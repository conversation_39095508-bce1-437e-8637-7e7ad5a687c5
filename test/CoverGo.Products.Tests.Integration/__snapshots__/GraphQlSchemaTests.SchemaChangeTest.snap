﻿schema {
  query: Query
  mutation: Mutation
}

interface Error {
  message: String!
}

interface products_Issue {
  path: [String]
  message: String
  code: products_IssueCode!
}

interface products_PricingEffect {
  meta: Any
  premium: products_Premium
  difference: products_Premium
  type: products_PricingEffectType!
}

interface products_ScriptEvaluationError {
  message: String
  type: products_ScriptEvaluationErrorType!
}

type Mutation {
  cloneProductVersion(input: products_CloneProductVersionInput!): products_CloneProductVersionPayload!
  createProductVersion(input: products_CreateProductVersionInput!): products_CreateProductVersionPayload!
  productMutationSetTermsAndConditions(input: products_SetTermsAndConditionsInput): products_Result
  productMutationRemoveTermsAndConditions(input: products_RemoveTermsAndConditionsInput): products_Result
  productMutationSetRatingFactorsTable(input: products_SetRatingFactorsTableInput): products_Result
  productMutationRemoveRatingFactorsTable(input: products_RemoveRatingFactorsTableInput): products_Result
  productMutationSetSegments(input: products_SetSegmentsInput): products_Result
  productMutationAttachDocuments(input: products_AttachDocumentsInput): products_Result
  productMutationRemoveAttachedDocument(input: products_RemoveAttachedDocumentInput): products_Result
  updateProductVersion(input: products_UpdateProductVersionInput!): products_UpdateProductVersionPayload!
  sbusMutationCreate(create: products_SbuUpsertInput): products_ResultOfCreatedStatus
  sbusMutationUpdate(update: products_SbuUpsertInput): products_Result
  sbusMutationDelete(delete: products_SbuUpsertInput): products_Result
  sbusMutationBatch(batch: products_GenericSbu3BatchInput): products_Result @authorize(apply: BEFORE_RESOLVER)
  exportProduct(productId: products_ProductIdInput!): products_FilePathResult
  importProduct(fromFilePath: String!): products_Result
  discountCodesMutationAddEligibleProduct(input: products_DiscountCodeProductsUpsertInput): products_Result
  discountCodesMutationRemoveEligibleProduct(input: products_DiscountCodeProductsUpsertInput): products_Result
  discountCodesMutationBatch(batch: products_GenericDiscountCode3BatchInput): products_Result
  discountCodesMutationCreate(create: products_DiscountCodeUpsertInput): products_ResultOfCreatedStatus
  discountCodesMutationUpdate(update: products_DiscountCodeUpsertInput): products_Result
  discountCodesMutationDelete(delete: products_DiscountCodeUpsertInput): products_Result
  discountCodesMutationApplyCode(input: products_DiscountCodeApplyInput): products_Result
}

type ProductField {
  value: Any
  key: String!
}

type ProductVersioningError implements Error {
  message: String!
  code: String
}

type Query {
  _schemaDefinition(configuration: String!): _SchemaDefinition
  product(productId: products_ProductIdInput!): products_Product @authorize(apply: BEFORE_RESOLVER)
  issuerProductIdGenerateNext: products_ResultOfString
  productPricing(productId: products_ProductIdInput! dataInput: String! distributorID: String campaignCodes: [String] startDate: DateTime scriptType: products_ScriptTypeEnum): products_ProductPricingResult @deprecated(reason: "Use 'CalculatePricing' API instead")
  productCalculatePricing(input: products_CalculatePricingInput): products_PricingResult
  productEvaluateUnderwriting(input: products_EvaluateUnderwritingInput): products_UnderwritingResult
  productLatestPricingScriptType(productId: products_ProductIdInput): products_ResultOfScriptTypeEnum
  productActiveNetworks(searchInput: String): [products_Network] @authorize(apply: BEFORE_RESOLVER)
  productProductPlanDetails(input: products_ProductPlanDetailFactorsInput!): products_ProductPlanDetails! @authorize(apply: BEFORE_RESOLVER)
  sbusQuery(where: products_GenericSbuQueryInput): products_GenericSbu8QueryInterface
  productImportInfo(filePath: String!): products_ProductImportInfo
  productImportHistoryQuery(where: products_GenericProductImportHistoryRecordQueryInput): products_GenericProductImportHistoryRecord8QueryInterface
  productEventLogs(productId: products_ProductIdInput fromDate: DateTime toDate: DateTime orderBy: products_OrderByInput skip: Int first: Int): [products_ProductEventLog]
  discountCodesQuery(where: products_GenericDiscountCodeQueryInput): products_GenericDiscountCode8QueryInterface
}

type _SchemaDefinition {
  name: String!
  document: String!
  extensionDocuments: [String!]!
}

type products_AdjustmentValue {
  type: products_AdjustmentValueType!
}

type products_AdjustmentValueDiscount {
  amount: Decimal!
  type: products_AdjustmentValueType!
}

type products_AdjustmentValueFactor {
  factor: Decimal!
  type: products_AdjustmentValueType!
}

type products_AdjustmentValueFlat {
  value: Decimal!
  type: products_AdjustmentValueType!
}

type products_AdjustmentValuePercentage {
  percentage: Decimal!
  type: products_AdjustmentValueType!
}

type products_AdjustmentValuePremium {
  value: Decimal!
  frequency: products_BillingFrequency!
  type: products_AdjustmentValueType!
}

type products_AdjustmentValuePrimaryCommission {
  from: products_AdjustmentValuePrimaryCommissionFrom!
  unit: products_AdjustmentValuePrimaryCommissionUnit!
  type: products_AdjustmentValueType!
}

type products_AdjustmentValueRound {
  precision: Decimal!
  mode: products_AdjustmentValueRoundMode!
  fields: [String]
  type: products_AdjustmentValueType!
}

type products_AdjustmentValueSecondaryCommission {
  from: products_AdjustmentValueSecondaryCommissionFrom!
  unit: products_AdjustmentValueSecondaryCommissionUnit!
  type: products_AdjustmentValueType!
}

type products_AmountCoinsuranceMaxLimitValue {
  amount: Decimal
  currency: String
}

type products_AmountCopaymentValue {
  amount: Decimal
  currency: String
}

type products_AmountDeductibleValue {
  amount: Decimal
  currency: String
  perTimeUnit: String
  perVariableUnit: String
}

type products_AmountFormulaCoinsuranceMaxLimitValue {
  currency: String
}

type products_AmountFormulaCopaymentValue {
  currency: String
}

type products_AmountFormulaDeductibleValue {
  currency: String
  perTimeUnit: String
  perVariableUnit: String
}

type products_AmountFormulaLimitValue {
  currency: String
  perTimeUnit: String
  perVariableUnit: String
}

type products_AmountFormulaWaitingPeriodValue {
  unit: String
}

type products_AmountLimitValue {
  amount: Decimal
  currency: String
  perTimeUnit: String
  perVariableUnit: String
}

type products_AmountPanelCopaymentValue {
  amount: Decimal
  currency: String
  perTimeUnit: String
  perVariableUnit: String
}

type products_AmountWaitingPeriodValue {
  amount: Decimal
  unit: String
}

type products_AttachedDocument {
  id: String
  logicalId: String
  name: String
  type: String
  category: products_AttachedDocumentCategory
}

type products_Benefit {
  meta: Any
  annualPricing: products_Pricing
}

type products_BenefitBreakdown {
  benefitId: String
  benefitCode: String
  premiumRequired: Decimal!
  premiumElements: [products_PremiumElement]
}

type products_BenefitDefinition {
  id: String
  businessId: String
  name: String
  description: String
  status: String
  benefitDefinitionTypeIds: [String]
  createdAt: DateTime!
  lastModifiedAt: DateTime!
  createdById: String
  lastModifiedById: String
}

type products_BenefitDefinitionType {
  id: String
  businessId: String
  name: String
  description: String
  status: String
  createdAt: DateTime!
  lastModifiedAt: DateTime!
  createdById: String
  lastModifiedById: String
}

type products_BenefitPremiums {
  premiumBeforeTax: Decimal!
  tax: Decimal!
  premium: Decimal!
  additionalProperties: [products_KeyValuePairOfStringAndObject!]
}

type products_BenefitUnderwritingResult {
  id: String
  name: String
  decision: products_UnderwritingResultDecision!
  referToUnderwriter: Boolean!
  loadings: [products_UnderwritingLoading]
  type: products_UnderwritingType!
}

type products_Billing {
  billingMode: products_BillingMode!
  billingFrequency: products_BillingFrequency!
  modalFactor: Decimal
  modalFee: Decimal
  modalRounding: Boolean
  modalPrecision: Float
}

type products_BillingInsuredSummary {
  id: String
  memberId: String
  premiumBeforeTax: Decimal!
  tax: Decimal!
  fee: Decimal!
  premium: Decimal!
}

type products_BillingOutput {
  policyPremium: Decimal!
  periods: [products_BillingPeriod]
  summary: products_BillingSummary
}

type products_BillingPeriod {
  fromDate: DateTime!
  toDate: DateTime!
  premium: Decimal!
  premiumBeforeTax: Decimal!
  tax: Decimal!
  fee: Decimal!
  rate: Decimal!
  premiumBreakdown: products_PremiumBreakdown
}

type products_BillingSummary {
  premiumBeforeTax: Decimal!
  tax: Decimal!
  fee: Decimal!
  premium: Decimal!
  commissionTotals: products_CommissionTotals
  insureds: [products_BillingInsuredSummary]
}

type products_CloneProductVersionPayload {
  clonedProductId: products_ProductId
}

type products_CommissionEffect {
  type: products_CommissionEffectType!
  source: products_CommissionSource
  total: Decimal!
  value: products_AdjustmentValue
}

type products_CommissionSource {
  net: Decimal
  gross: Decimal
  primaryCommissions: Decimal
}

type products_CommissionTotals {
  primary: Decimal!
  secondary: Decimal!
  total: Decimal!
}

type products_CommissionsOutput {
  primary: [products_CommissionEffect]
  secondary: [products_CommissionEffect]
  commissionTotals: products_CommissionTotals
}

type products_CreateProductVersionCommandResult {
  clonedProductId: products_ProductId!
  clonedProductName: String!
}

type products_CreateProductVersionPayload {
  result: products_CreateProductVersionCommandResult
  errors: [products_CreateProductVersionError!]
}

type products_CreatedStatus {
  id: String
  ids: [String]
}

type products_CustomIssue implements products_Issue {
  path: [String]
  message: String
  code: products_IssueCode!
}

type products_DetailedBreakdown {
  insureds: [products_DetailedBreakdownInsured]
  policyPremium: products_PolicyPremium
  totalPolicyPremium: products_TotalPolicyPremium
}

type products_DetailedBreakdownBenefit {
  benefitId: String
  benefitCode: String
  benefitPremiums: products_BenefitPremiums
}

type products_DetailedBreakdownInsured {
  id: String
  memberId: String
  benefits: [products_DetailedBreakdownBenefit]
  insuredPremium: products_InsuredPremium
  totalInsuredPremium: products_TotalInsuredPremium
}

type products_Details {
  errorType: String
  errorMessage: String
  trace: String
}

type products_DiscountCode {
  id: String
  name: String
  description: String
  productTypeId: String
  type: products_DiscountType!
  value: Decimal!
  validFrom: DateTime
  validTo: DateTime
  redemptionLimit: Int
  productIds: [products_ProductId]
  createdAt: DateTime!
  lastModifiedAt: DateTime!
  createdById: String
  lastModifiedById: String
}

type products_DiscountEffect implements products_PricingEffect {
  value: products_AdjustmentValueFlatFactorType
  meta: Any
  premium: products_Premium
  difference: products_Premium
  type: products_PricingEffectType!
}

type products_Effects {
  benefits: [products_TaxEffect]
  insureds: [products_TaxEffect]
  mode: [products_TaxEffect]
  global: [products_TaxEffect]
}

type products_Error {
  code: String
  message: String
}

type products_Exclusion {
  id: String
  name: String
  description: String
  reUnderwritingFlag: Boolean
  exclusionPeriod: products_ExclusionPeriod
}

type products_ExclusionPeriod {
  unit: products_ExclusionPeriodUnit!
  value: Float!
}

type products_FactorEffect implements products_PricingEffect {
  value: products_AdjustmentValueFlatFactorType
  meta: Any
  premium: products_Premium
  difference: products_Premium
  type: products_PricingEffectType!
}

type products_FeeEffect implements products_PricingEffect {
  value: products_AdjustmentValueFlatFactorType
  meta: Any
  premium: products_Premium
  difference: products_Premium
  type: products_PricingEffectType!
}

type products_FilePathResult {
  filePath: String
  status: String
  errors: [String]
  errors_2: [products_Error]
  isSuccess: Boolean!
}

type products_GenericDiscountCode8QueryInterface {
  totalCount: Long!
  list: [products_DiscountCode]
}

type products_GenericProductImportHistoryRecord8QueryInterface {
  totalCount: Long!
  list: [products_ProductImportHistoryRecord]
}

type products_GenericSbu8QueryInterface {
  totalCount: Long!
  list: [products_Sbu]
}

type products_GroupedBenefit {
  value: products_BenefitDefinition!
}

type products_GroupedBenefitType {
  value: products_BenefitDefinitionType!
}

type products_HealthQuestionnaire {
  id: String
  name: String
}

type products_InNetwork {
  organizations: [products_InNetworkOrganization!]!
}

type products_InNetworkAndOutOfNetwork {
  description: String
}

type products_InNetworkOrganization {
  id: String
  name: String
}

type products_InputDataValidationError implements products_ScriptEvaluationError {
  message: String
  type: products_ScriptEvaluationErrorType!
  issues: [products_Issue]
}

type products_InstallmentBreakdown {
  policyPremium: Decimal!
  periods: [products_BillingPeriod]
}

type products_Insured {
  meta: Any
  benefits: [products_Benefit]
  annualPricing: products_Pricing
}

type products_InsuredBreakdown {
  id: String
  memberId: String
  premiumRequired: Decimal!
  commissions: products_CommissionsOutput
  premiumElements: [products_PremiumElement]
}

type products_InsuredPremium {
  premiumBeforeTax: Decimal!
  tax: Decimal!
  premium: Decimal!
  additionalProperties: [products_KeyValuePairOfStringAndObject!]
}

type products_InsuredUnderwritingResult {
  id: String
  decision: products_UnderwritingResultDecision!
  type: products_UnderwritingType!
  referToUnderwriter: Boolean!
  healthQuestionnaire: products_HealthQuestionnaire
  loadings: [products_UnderwritingLoading]
  remarks: [products_UnderwritingRemark]
  preExistingConditions: [products_PreExistingCondition]
  exclusions: [products_Exclusion]
  benefitsUnderwriting: [products_BenefitUnderwritingResult]
}

type products_InvalidEnumValueIssue implements products_Issue {
  received: String
  options: [String]
  path: [String]
  message: String
  code: products_IssueCode!
}

type products_InvalidLiteralIssue implements products_Issue {
  expected: String
  received: String
  path: [String]
  message: String
  code: products_IssueCode!
}

type products_InvalidStringIssue implements products_Issue {
  validation: products_Validation!
  path: [String]
  message: String
  code: products_IssueCode!
}

type products_InvalidTypeIssue implements products_Issue {
  expected: String
  received: String
  path: [String]
  message: String
  code: products_IssueCode!
}

type products_InvalidUnionDiscriminatorIssue implements products_Issue {
  options: [String]
  path: [String]
  message: String
  code: products_IssueCode!
}

type products_KeyValuePairOfStringAndObject {
  key: String!
}

type products_KeyValuePairOfStringAndScriptSchemaPlanField {
  key: String!
  value: products_ScriptSchemaPlanField!
}

type products_LoadingEffect implements products_PricingEffect {
  meta: Any
  value: products_AdjustmentValue
  premium: products_Premium
  difference: products_Premium
  type: products_PricingEffectType!
}

type products_Mode {
  meta: Any
  properties: products_ModeProperties
  annualPricing: products_Pricing
  insureds: [products_Insured]
  billing: products_BillingOutput
  installmentBreakdown: products_InstallmentBreakdown
  detailedBreakdown: products_DetailedBreakdown
}

type products_ModeProperties {
  meta: Any
  count: Int!
  billing: products_Billing
}

type products_Network {
  uuid: UUID!
  id: String
  name: String
  status: products_NetworkStatus!
}

type products_NotMultipleOfIssue implements products_Issue {
  multipleOf: Float!
  path: [String]
  message: String
  code: products_IssueCode!
}

type products_NumberDeferredPeriodValue {
  length: Long
  period: String
}

type products_NumberFormulaDeferredPeriodValue {
  period: String
}

type products_NumberFormulaQualificationPeriodValue {
  period: String
}

type products_NumberLimitValue {
  amount: Long
  unit: String
  perTimeUnit: String
  perVariableUnit: String
}

type products_NumberQualificationPeriodValue {
  length: Long
  period: String
}

type products_OutOfNetwork {
  description: String
}

type products_PercentageCoinsuranceValue {
  amount: Decimal
}

type products_PercentageFormulaCoinsuranceValue {
  description: String
}

type products_PercentageFormulaReimbursementValue {
  description: String
}

type products_PercentageReimbursementValue {
  amount: Decimal
}

type products_PlanAddOn {
  id: String
  name: String
  benefitTypes: [products_PlanBenefitType!]!
  benefits: [products_PlanBenefit!]!
}

type products_PlanBenefit {
  name: String
  benefitTypes: [products_PlanBenefitType!]!
  benefits: [products_PlanBenefit!]!
  limits: [products_PlanBenefitLimit!]!
  overseasAccidentalLimits: [products_PlanBenefitOverseasAccidentalLimit!]!
  groupedBenefitLimits: [products_PlanBenefitGroupedBenefitLimit!]!
  deductibles: [products_PlanBenefitDeductible!]!
  copayments: [products_PlanBenefitCopayment!]!
  panelCopayments: [products_PlanBenefitPanelCopayment!]!
  coinsurances: [products_PlanBenefitCoinsurance!]!
  reimbursements: [products_PlanBenefitReimbursement!]!
  geographicalLimits: [products_PlanBenefitGeographicalLimit!]!
  waitingPeriods: [products_PlanBenefitWaitingPeriod!]!
  deferredPeriods: [products_PlanBenefitDeferredPeriod!]!
  qualificationPeriods: [products_PlanBenefitQualificationPeriod!]!
}

type products_PlanBenefitCoinsurance {
  coinsuranceValue: products_CoinsuranceValue
  maxLimit: products_CoinsuranceMaxLimitValue
  description: String
}

type products_PlanBenefitCopayment {
  copaymentValue: products_CopaymentValue
  description: String
}

type products_PlanBenefitDeductible {
  deductibleValue: products_DeductibleValue
  description: String
}

type products_PlanBenefitDeferredPeriod {
  deferredPeriodValue: products_DeferredPeriodValue
}

type products_PlanBenefitGeographicalLimit {
  region: String
}

type products_PlanBenefitGroupedBenefitLimit {
  limitValue: products_LimitValue
  groupedBenefits: [products_GroupedBenefitItem!]!
  description: String
}

type products_PlanBenefitLimit {
  limitValue: products_LimitValue
  network: products_NetworkType
  description: String
}

type products_PlanBenefitOverseasAccidentalLimit {
  percentage: Long
}

type products_PlanBenefitPanelCopayment {
  panelCopaymentValue: products_PanelCopaymentValue
}

type products_PlanBenefitQualificationPeriod {
  qualificationPeriodValue: products_QualificationPeriodValue
}

type products_PlanBenefitReimbursement {
  reimbursementValue: products_ReimbursementValue
  description: String
}

type products_PlanBenefitType {
  name: String
  benefitTypes: [products_PlanBenefitType!]!
  benefits: [products_PlanBenefit!]!
  code: String
  limits: [products_PlanBenefitLimit!]!
  overseasAccidentalLimits: [products_PlanBenefitOverseasAccidentalLimit!]!
  groupedBenefitLimits: [products_PlanBenefitGroupedBenefitLimit!]!
  deductibles: [products_PlanBenefitDeductible!]!
  copayments: [products_PlanBenefitCopayment!]!
  panelCopayments: [products_PlanBenefitPanelCopayment!]!
  coinsurances: [products_PlanBenefitCoinsurance!]!
  reimbursements: [products_PlanBenefitReimbursement!]!
  geographicalLimits: [products_PlanBenefitGeographicalLimit!]!
  waitingPeriods: [products_PlanBenefitWaitingPeriod!]!
  deferredPeriods: [products_PlanBenefitDeferredPeriod!]!
  qualificationPeriods: [products_PlanBenefitQualificationPeriod!]!
}

type products_PlanBenefitWaitingPeriod {
  waitingPeriodValue: products_WaitingPeriodValue
  description: String
}

type products_PolicyBreakdown {
  commissions: products_CommissionsOutput
  premiumRequired: Decimal!
  premiumElements: [products_PremiumElement]
}

type products_PolicyPremium {
  premiumBeforeTax: Decimal!
  tax: Decimal!
  premium: Decimal!
  additionalProperties: [products_KeyValuePairOfStringAndObject!]
}

type products_PreExistingCondition {
  id: String
  name: String
  code: String
  description: String
}

type products_Premium {
  base: Decimal!
  premiums: Decimal!
  discounts: Decimal!
  loadings: Decimal!
  roundings: Decimal!
  total: Decimal!
  fees: Decimal!
  limits: Decimal!
  factors: Decimal!
}

type products_PremiumBreakdown {
  policyBreakdown: products_PolicyBreakdown
  insuredBreakdown: [products_InsuredBreakdown]
  benefitBreakdown: [products_BenefitBreakdown]
}

type products_PremiumEffect implements products_PricingEffect {
  meta: Any
  value: products_AdjustmentValuePremium
  premium: products_Premium
  difference: products_Premium
  type: products_PricingEffectType!
}

type products_PremiumElement {
  element: String
  description: String
  amount: Decimal!
}

type products_Pricing {
  effects: [products_PricingEffect]
  taxes: products_Taxes
  premium: products_Premium
  summary: products_Summary
  commissions: products_CommissionsOutput
}

type products_PricingData {
  meta: Any
  modes: [products_Mode]
  annualPricing: products_Pricing
}

type products_PricingFactorsEffect implements products_PricingEffect {
  value: products_AdjustmentValueFlatFactorType
  meta: Any
  premium: products_Premium
  difference: products_Premium
  type: products_PricingEffectType!
}

type products_PricingResult {
  data: products_PricingData
  errors: products_ScriptEvaluationError
}

type products_Product {
  id: products_ProductId
  lifecycleStage: String
  policyIssuanceMethod: products_PolicyIssuanceMethod
  offerValidityPeriod: TimeSpan
  autoRenewal: Boolean!
  renewalNotification: Boolean!
  taxConfiguration: products_TaxConfiguration
  fields: [ProductField!]!
  termsAndConditionsTemplateId: String
  termsAndConditionsJacketId: String
  name: String
  scripts(where: products_ScriptWhereInput): [products_Script!]!
}

type products_ProductEventLog {
  id: String
  nodeId: String
  productId: products_ProductId
  ref: String
  alias: String
  type: String
  values: String
  timestamp: DateTime!
  updatedBy: products_User
}

type products_ProductId {
  plan: String
  version: String
  type: String
}

type products_ProductImportHistoryPackage {
  productId: products_ProductId
  productName: String
  productLifecycleStage: String
  productLastModifiedAt: DateTime
  productLastModifiedBy: String
  tenant: String
  exportedAt: DateTime!
  exportedBy: String
  includedEntries: [String]
  requestId: String
  relativeFilePath: String
}

type products_ProductImportHistoryRecord {
  id: String
  importedAt: DateTime!
  importedById: String
  importedBy: String
  package: products_ProductImportHistoryPackage
}

type products_ProductImportInfo {
  productId: products_ProductId
  productName: String
  productLifecycleStage: String
  productLastModifiedAt: DateTime
  productLastModifiedBy: String
  tenant: String
  exportedAt: DateTime!
  exportedBy: String
  includedEntries: [String]
  validationMessages: [products_ProductImportValidationMessage]
}

type products_ProductImportValidationMessage {
  level: products_ProductImportValidationMessageLevel!
  entry: String
  code: String
  meta: products_ProductImportValidationMessageMeta
}

type products_ProductImportValidationMessageMeta {
  lockedBy: String
}

type products_ProductPlanDetail {
  productId: products_ProductId!
  id: String
  name: String
  benefitTypes: [products_PlanBenefitType!]!
  benefits: [products_PlanBenefit!]!
  addOns: [products_PlanAddOn!]!
  fields: [products_KeyValuePairOfStringAndScriptSchemaPlanField!]
}

type products_ProductPlanDetails {
  productId: products_ProductId!
  planDetails: [products_ProductPlanDetail!]!
  addOns: [products_PlanAddOn!]!
  masterPlan: [products_ProductRepresentationNode!]!
  allPlans: [products_ProductRepresentationNode!]!
  attachedDocuments: [products_AttachedDocument!]
  insuredsFields: [products_KeyValuePairOfStringAndScriptSchemaPlanField!]
  memberCustomSchema: [products_KeyValuePairOfStringAndScriptSchemaPlanField!]
  policyFields: [products_KeyValuePairOfStringAndScriptSchemaPlanField!]
}

type products_ProductPricingResult {
  pricing: String
  status: String
  errors: [String]
  errors_2: [products_Error]
  isSuccess: Boolean!
}

type products_ProductRepresentationNode {
  percentage: Any
  resolved: Any
  order: Any
  summary: Any
  extraSummary: Any
  masterPlan: Any
  children: [products_ProductRepresentationNode!]
  componentName: String
  editor: products_ProductRepresentationNodeEditor
  id: String
  minified: Boolean
  name: String
  nodeName: String
  parameters: [products_ProductRepresentationNodeParameter!]
  parentId: String
  parentIds: [String!]
  premiumBaseFrequency: String
  productCurrency: String
  productType: String
  props: products_ProductRepresentationNodeProps
  readonly: Boolean
  resolvedValue: Int
  label: String
  index: Int
  indent: Int
  originalAmount: Int
  path: String
  planId: String
  isExpanded: Boolean
  sortedPath: String
  directParent: String
  identify: String
  sortedIdentify: String
  code: String
  desc: String
  descIdx: Int
  limitType: String
  limitPerTime: String
  limitPerVariable: String
  amount: Int
  number: Int
  value: String
  htmlLabel: String
  parent: products_ProductRepresentationNode
  childrenIds: [String!]
}

type products_ProductRepresentationNodeEditor {
  componentName: String
}

type products_ProductRepresentationNodeParameter {
  value: Any
  component: String
  dataType: String
  description: String
  display: String
  displayValue: String
  label: String
  name: String
  props: products_ProductRepresentationNodeParameterProps
  segments: [products_ProductRepresentationNodeParameterSegment!]
  table: products_ProductRepresentationNodeParameterTable
  values: [products_ProductRepresentationNodeParameterValue!]
  isUsingFormula: Boolean
  unit: String
  unitType: String
}

type products_ProductRepresentationNodeParameterProps {
  disabled: Boolean
  placeholder: String
  maxlength: Int
  rows: Int
  theme: String
  allowSelectNothing: Boolean
  options: [products_ProductRepresentationNodeParameterPropsOption!]
}

type products_ProductRepresentationNodeParameterPropsOption {
  name: String
  value: String
}

type products_ProductRepresentationNodeParameterSegment {
  name: String
  options: [products_ProductRepresentationNodeParameterSegmentOption!]
  path: String
  segId: String
}

type products_ProductRepresentationNodeParameterSegmentOption {
  isToggle: Boolean
  value: String
}

type products_ProductRepresentationNodeParameterTable {
  columns: [products_ProductRepresentationNodeParameterTableColumn!]
  rows: [products_ProductRepresentationNodeParameterTableRow!]
}

type products_ProductRepresentationNodeParameterTableColumn {
  value: [String!]
}

type products_ProductRepresentationNodeParameterTableRow {
  value: [String!]
}

type products_ProductRepresentationNodeParameterValue {
  condition: [String!]
  value: Int
}

type products_ProductRepresentationNodeProps {
  value: String
  theme: String
  id: String
  name: String
  formulaName: String
  path: String
  tableName: String
  options: [products_ProductRepresentationNodePropsOption!]
}

type products_ProductRepresentationNodePropsOption {
  name: String
  value: String
}

type products_Result {
  status: String
  errors: [String]
  errors_2: [products_Error]
  isSuccess: Boolean!
}

type products_ResultOfCreatedStatus {
  status: String
  errors: [String]
  errors_2: [products_Error]
  value: products_CreatedStatus
  isSuccess: Boolean!
}

type products_ResultOfScriptTypeEnum {
  status: String
  errors: [String]
  errors_2: [products_Error]
  value: products_ScriptTypeEnum!
  isSuccess: Boolean!
}

type products_ResultOfString {
  status: String
  errors: [String]
  errors_2: [products_Error]
  value: String
  isSuccess: Boolean!
}

type products_RoundEffect implements products_PricingEffect {
  meta: Any
  value: products_AdjustmentValueRound
  premium: products_Premium
  difference: products_Premium
  type: products_PricingEffectType!
}

type products_Sbu {
  id: String
  startDate: DateTime!
  endDate: DateTime!
  amount: Decimal!
  createdAt: DateTime!
  lastModifiedAt: DateTime!
  createdById: String
  lastModifiedById: String
}

type products_Script {
  id: String
  name: String
  inputSchema: String
  outputSchema: String
  sourceCode: String
  referenceSourceCodeUrl: String
  checksum: String
  externalTableDataUrl: String
  externalTableDataUrls: [String]
  includeDiscountCodes: Boolean
  type: products_ScriptTypeEnum
  createdAt: DateTime
  lastModifiedAt: DateTime
  lastModifiedById: String
}

type products_ScriptExecutionError implements products_ScriptEvaluationError {
  message: String
  type: products_ScriptEvaluationErrorType!
  details: products_Details
}

type products_ScriptSchemaPlanField {
  type: String!
  meta: products_ScriptSchemaPlanFieldMeta!
}

type products_ScriptSchemaPlanFieldMeta {
  required: Boolean!
  component: String
  fieldType: String
  label: String
  validations: String
  minLength: Int
  maxLength: Int
  numeric: Boolean
  options: [products_ScriptSchemaPlanFieldOption!]
  condition: String
}

type products_ScriptSchemaPlanFieldOption {
  key: String
  name: String!
  value: String
}

type products_Summary {
  gross: Decimal!
  grossPlusTaxes: Decimal!
  net: Decimal!
  taxes: Decimal!
  discounts: Decimal!
  commissions: Decimal!
}

type products_TaxConfiguration {
  referenceMnemonic: String!
  mnemonicCode: String!
  allowOverrideAtOfferStage: Boolean!
  allowOverrideAtProposalStage: Boolean!
}

type products_TaxEffect {
  value: products_AdjustmentValueFlatFactorPercentageType
  type: products_TaxEffectType!
  label: String
  total: Decimal!
}

type products_Taxes {
  total: Decimal!
  effects: products_Effects
}

type products_TooBigIssue implements products_Issue {
  maximum: Float!
  inclusive: Boolean!
  exact: Boolean
  type: products_TooBigIssueType!
  path: [String]
  message: String
  code: products_IssueCode!
}

type products_TooSmallIssue implements products_Issue {
  minimum: Float!
  inclusive: Boolean!
  exact: Boolean
  type: products_TooSmallIssueType!
  path: [String]
  message: String
  code: products_IssueCode!
}

type products_TotalInsuredPremium {
  premiumBeforeTax: Decimal!
  tax: Decimal!
  premium: Decimal!
  additionalProperties: [products_KeyValuePairOfStringAndObject!]
}

type products_TotalPolicyPremium {
  premiumBeforeTax: Decimal!
  tax: Decimal!
  premium: Decimal!
  additionalProperties: [products_KeyValuePairOfStringAndObject!]
}

type products_UnderwritingData {
  policy: products_InsuredUnderwritingResult
  insureds: [products_InsuredUnderwritingResult]
}

type products_UnderwritingLoading {
  id: String
  name: String
  method: products_UnderwritingLoadingMethod!
  value: Float!
  description: String
}

type products_UnderwritingRemark {
  timestamp: String
  underwriter: String
  content: String
}

type products_UnderwritingResult {
  data: products_UnderwritingData
  error: products_ScriptEvaluationError
}

type products_UnrecognizedKeysIssue implements products_Issue {
  keys: [String]
  path: [String]
  message: String
  code: products_IssueCode!
}

type products_UpdateProductVersionPayload {
  product: products_Product
}

type products_User {
  email: String
  username: String
  name: String
}

union products_AdjustmentValueFlatFactorPercentageType = products_AdjustmentValueFlat | products_AdjustmentValueFactor | products_AdjustmentValuePercentage

union products_AdjustmentValueFlatFactorType = products_AdjustmentValueFlat | products_AdjustmentValueFactor

union products_CoinsuranceMaxLimitValue = products_AmountCoinsuranceMaxLimitValue | products_AmountFormulaCoinsuranceMaxLimitValue

union products_CoinsuranceValue = products_PercentageCoinsuranceValue | products_PercentageFormulaCoinsuranceValue

union products_CopaymentValue = products_AmountCopaymentValue | products_AmountFormulaCopaymentValue

union products_CreateProductVersionError = ProductVersioningError

union products_DeductibleValue = products_AmountDeductibleValue | products_AmountFormulaDeductibleValue

union products_DeferredPeriodValue = products_NumberDeferredPeriodValue | products_NumberFormulaDeferredPeriodValue

union products_GroupedBenefitItem = products_GroupedBenefit | products_GroupedBenefitType

union products_LimitValue = products_AmountLimitValue | products_NumberLimitValue | products_AmountFormulaLimitValue

union products_NetworkType = products_InNetworkAndOutOfNetwork | products_OutOfNetwork | products_InNetwork

union products_PanelCopaymentValue = products_AmountPanelCopaymentValue

union products_QualificationPeriodValue = products_NumberQualificationPeriodValue | products_NumberFormulaQualificationPeriodValue

union products_ReimbursementValue = products_PercentageReimbursementValue | products_PercentageFormulaReimbursementValue

union products_WaitingPeriodValue = products_AmountWaitingPeriodValue | products_AmountFormulaWaitingPeriodValue

input products_AttachDocumentsInput {
  productId: products_ProductIdInput
  attachedDocumentsIds: [products_AttachedDocumentIdInput]
}

input products_AttachedDocumentIdInput {
  templateId: String
  category: products_AttachedDocumentCategory!
}

input products_BenefitUwLoadingInput {
  benefitId: String
  benefitName: String
  loadings: [products_UwLoadingInput]
}

input products_BillingInput {
  billingMode: products_BillingMode!
  billingFrequency: products_BillingFrequency!
  modalFactor: Decimal
  modalFee: Decimal
}

input products_CalculatePricingInput {
  productId: products_ProductIdInput
  dataInput: String!
  modes: [products_ModePropertiesInput]
  distributorID: String
  agentIds: [String!]
  campaignCodes: [String!]
  startDate: DateTime
  isRenewal: Boolean
  uwLoadings: products_UwLoadingsInput
  taxes: [products_TaxInput]
  pricingFactors: products_PricingFactorsInput
}

input products_CloneProductVersionInput {
  originalProductId: products_ProductIdInput!
  cloneProductVersion: String!
  isReadonly: Boolean! = true
}

input products_CreateProductVersionInput {
  originalProductId: products_ProductIdInput!
}

input products_DiscountCodeApplyInput {
  discountCode: String
}

input products_DiscountCodeFilterInput {
  id: String
  id_in: [String]
  name: String
  name_contains: String
  name_in: [String]
  productTypeId: String
  productTypeId_in: [String]
  type: products_DiscountType
  validFrom_gt: DateTime
  validFrom_lt: DateTime
  validFrom_gte: DateTime
  validFrom_lte: DateTime
  validTo_gt: DateTime
  validTo_lt: DateTime
  validTo_gte: DateTime
  validTo_lte: DateTime
  validTo_exists: Boolean
  productIds_exists: Boolean
  productIds_in: [products_ProductIdInput]
  productIds_contains: products_ProductIdWhereInput
}

input products_DiscountCodeProductsUpsertInput {
  discountCodeId: String
  productIds: [products_ProductIdInput]
  byId: String
}

input products_DiscountCodeUpsertInput {
  id: String
  name: String
  description: String
  productTypeId: String
  type: products_DiscountType
  value: Decimal
  validFrom: DateTime
  validTo: DateTime
  redemptionLimit: Int
  byId: String
}

input products_EvaluateUnderwritingInput {
  productId: products_ProductIdInput
  dataInput: String
  insuredUnderwritingInputs: [products_InsuredUnderwritingInput]
  modes: products_UnderwritingModesInput!
}

input products_GenericDiscountCode3BatchInput {
  create: [products_DiscountCodeUpsertInput]
  update: [products_DiscountCodeUpsertInput]
  delete: [products_DiscountCodeUpsertInput]
}

input products_GenericDiscountCodeFilterInput {
  and: [products_GenericDiscountCodeFilterInput]
  or: [products_GenericDiscountCodeFilterInput]
  where: products_DiscountCodeFilterInput
}

input products_GenericDiscountCodeQueryInput {
  where: products_GenericDiscountCodeFilterInput
  asOf: DateTime
  orderBy: products_OrderByInput
  orderBy2: [products_OrderByInput!]
  first: Int
  skip: Int
  includeEvents: Boolean
  groupBy: products_GroupByInput
}

input products_GenericProductImportHistoryRecordFilterInput {
  and: [products_GenericProductImportHistoryRecordFilterInput]
  or: [products_GenericProductImportHistoryRecordFilterInput]
  where: products_ProductImportHistoryRecordFilterInput
}

input products_GenericProductImportHistoryRecordQueryInput {
  where: products_GenericProductImportHistoryRecordFilterInput
  asOf: DateTime
  orderBy: products_OrderByInput
  orderBy2: [products_OrderByInput!]
  first: Int
  skip: Int
  includeEvents: Boolean
  groupBy: products_GroupByInput
}

input products_GenericSbu3BatchInput {
  create: [products_SbuUpsertInput]
  update: [products_SbuUpsertInput]
  delete: [products_SbuUpsertInput]
}

input products_GenericSbuFilterInput {
  and: [products_GenericSbuFilterInput]
  or: [products_GenericSbuFilterInput]
  where: products_SbuFilterInput
}

input products_GenericSbuQueryInput {
  where: products_GenericSbuFilterInput
  asOf: DateTime
  orderBy: products_OrderByInput
  orderBy2: [products_OrderByInput!]
  first: Int
  skip: Int
  includeEvents: Boolean
  groupBy: products_GroupByInput
}

input products_GroupByInput {
  fieldName: String
}

input products_HealthQuestionnaireResponseInput {
  template: products_TemplateInput
  answers: Any
}

input products_InsuredUnderwritingInput {
  id: String
  type: String
  dateOfBirth: Date
  insuredSimplifiedMedicalUnderwritingResponse: products_HealthQuestionnaireResponseInput
  insuredNonMedicalMaterialFacts: products_NonMedicalMaterialFactsInput
  fields: Any
  meta: Any
}

input products_MemberUwLoadingInput {
  id: String
  memberId: String
  loadings: [products_UwLoadingInput]
  benefits: [products_BenefitUwLoadingInput]
}

input products_ModePropertiesInput {
  count: Int!
  meta: Any
  billing: products_BillingInput
}

input products_MoneyInput {
  amount: Decimal!
  currencyCode: String
}

input products_NonMedicalMaterialFactsInput {
  id: String
  meta: Any
}

input products_OrderByInput {
  fieldName: String
  type: products_OrderByType!
}

input products_PricingFactorCommercialAdjustmentInput {
  value: Decimal!
  type: products_PricingFactorCommercialAdjustmentInputType!
}

input products_PricingFactorCommissionInput {
  value: Decimal!
  type: products_PricingFactorCommissionInputType!
}

input products_PricingFactorContingencyMarginsInput {
  value: Decimal!
  type: products_PricingFactorContingencyMarginsInputType!
}

input products_PricingFactorExpensesInput {
  value: Decimal!
  type: products_PricingFactorExpensesInputType!
}

input products_PricingFactorProfitInput {
  value: Decimal!
  type: products_PricingFactorProfitInputType!
}

input products_PricingFactorsInput {
  commission: products_PricingFactorCommissionInput
  profit: products_PricingFactorProfitInput
  expenses: products_PricingFactorExpensesInput
  contingencyMargins: products_PricingFactorContingencyMarginsInput
  commercialAdjustment: products_PricingFactorCommercialAdjustmentInput
}

input products_ProductIdInput {
  plan: String
  version: String
  type: String
}

input products_ProductIdWhereInput {
  type: String
  type_in: [String]
  plan: String
  plan_in: [String]
  plan_contains: String
  version: String
  version_in: [String]
  version_contains: String
  version_ncontains: String
}

input products_ProductImportHistoryRecordFilterInput {
  productId_in: [products_ProductIdInput]
}

input products_ProductPlanDetailFactorsInput {
  productId: products_ProductIdInput
}

input products_RemoveAttachedDocumentInput {
  productId: products_ProductIdInput
  attachedDocumentId: String
}

input products_RemoveRatingFactorsTableInput {
  productId: products_ProductIdInput
}

input products_RemoveTermsAndConditionsInput {
  productId: products_ProductIdInput
}

input products_SbuFilterInput {
  id: String
  id_in: [String]
}

input products_SbuUpsertInput {
  id: String
  startDate: DateTime
  endDate: DateTime
  amount: Decimal
  byId: String
}

input products_ScriptWhereInput {
  or: [products_ScriptWhereInput]
  and: [products_ScriptWhereInput]
  id: String
  id_in: [String]
  type: products_ScriptTypeEnum
}

input products_SetRatingFactorsTableInput {
  productId: products_ProductIdInput
  ratingFactorsTable: String
}

input products_SetSegmentsInput {
  productId: products_ProductIdInput
  segments: String
}

input products_SetTermsAndConditionsInput {
  productId: products_ProductIdInput
  templateId: String
  jacketId: String
}

input products_SettableOfNullableOfTimeSpanInput {
  value: TimeSpan
}

input products_TaxInput {
  id: String
  name: String
  value: Decimal!
  type: products_TaxInputType!
}

input products_TemplateInput {
  id: String
  name: String
}

input products_UnderwritingModesInput {
  benefitLevel: Boolean!
}

input products_UpdateProductVersionInput {
  productId: products_ProductIdInput!
  offerValidityPeriod: products_SettableOfNullableOfTimeSpanInput
}

input products_UwLoadingInput {
  id: String
  factor: Decimal
  amount: products_MoneyInput
}

input products_UwLoadingsInput {
  loadings: [products_UwLoadingInput!]
  members: [products_MemberUwLoadingInput!]
}

enum ApplyPolicy {
  BEFORE_RESOLVER
  AFTER_RESOLVER
}

enum products_AdjustmentValuePrimaryCommissionFrom {
  GROSS
  NET
  FLAT
}

enum products_AdjustmentValuePrimaryCommissionUnit {
  PERCENTAGE
  FLAT
  FACTOR
}

enum products_AdjustmentValueRoundMode {
  UP
  DOWN
  NEAREST
}

enum products_AdjustmentValueSecondaryCommissionFrom {
  GROSS
  NET
  PRIMARY_COMMISSIONS
  FLAT
}

enum products_AdjustmentValueSecondaryCommissionUnit {
  PERCENTAGE
  FLAT
  FACTOR
}

enum products_AdjustmentValueType {
  FLAT
  FACTOR
  ROUND
  PREMIUM
  PRIMARY_COMMISSION
  SECONDARY_COMMISSION
  DISCOUNT
  PERCENTAGE
}

enum products_AttachedDocumentCategory {
  OFFER
  PROPOSAL
  POLICY
  POLICY_CANCELLATION
  POLICY_ENDORSEMENT
}

enum products_BillingFrequency {
  MONTHLY
  QUARTERLY
  ANNUALLY
  SEMI_ANNUALLY
  SINGLE_PAYMENT
}

enum products_BillingMode {
  CALENDAR_YEAR
  POLICY_YEAR
  DAILY_PRORATA
}

enum products_CommissionEffectType {
  COMMISSION
}

enum products_DiscountType {
  AMOUNT
  PERCENTAGE
}

enum products_ExclusionPeriodUnit {
  DAYS
  WEEKS
  MONTHS
  YEARS
}

enum products_IssueCode {
  INVALID_TYPE
  INVALID_LITERAL
  CUSTOM
  INVALID_UNION
  INVALID_UNION_DISCRIMINATOR
  INVALID_ENUM_VALUE
  UNRECOGNIZED_KEYS
  INVALID_ARGUMENTS
  INVALID_RETURN_TYPE
  INVALID_DATE
  INVALID_STRING
  TOO_SMALL
  TOO_BIG
  INVALID_INTERSECTION_TYPES
  NOT_MULTIPLE_OF
  NOT_FINITE
}

enum products_NetworkStatus {
  PENDING_APPROVAL
  SUBMITED_FOR_APPROVAL
  APPROVED
  REJECTED
}

enum products_OrderByType {
  ASC
  DSC
}

enum products_PolicyIssuanceMethod {
  MANUAL_NO_PREMIUM_DEPENDENCY
  MANUAL_AFTER_PREMIUM_RECEIPT
  AUTOMATIC_AFTER_PREMIUM_RECEIPT
}

enum products_PricingEffectType {
  FACTOR
  LOADING
  FEE
  DISCOUNT
  ROUND
  PREMIUM
  FLAT
  PRIMARY_COMMISSION
  SECONDARY_COMMISSION
  PERCENTAGE
  PRICING_FACTORS
}

enum products_PricingFactorCommercialAdjustmentInputType {
  FLAT
  FACTOR
}

enum products_PricingFactorCommissionInputType {
  FLAT
  FACTOR
}

enum products_PricingFactorContingencyMarginsInputType {
  FLAT
  FACTOR
}

enum products_PricingFactorExpensesInputType {
  FLAT
  FACTOR
}

enum products_PricingFactorProfitInputType {
  FLAT
  FACTOR
}

enum products_ProductImportValidationMessageLevel {
  ERROR
  WARNING
}

enum products_ScriptEvaluationErrorType {
  SCRIPT_ERROR
  INPUT_DATA_VALIDATION_ERROR
}

enum products_ScriptTypeEnum {
  NONE
  PRICING
  UNDERWRITING
  CANCELLATION
  CLAIM
  PRICING_STANDARD
  UNDERWRITING_STANDARD
}

enum products_TaxEffectType {
  TAX
}

enum products_TaxInputType {
  FLAT
  FACTOR
}

enum products_TooBigIssueType {
  ARRAY
  STRING
  NUMBER
  SET
  DATE
  BIGINT
}

enum products_TooSmallIssueType {
  ARRAY
  STRING
  NUMBER
  SET
  DATE
  BIGINT
}

enum products_UnderwritingLoadingMethod {
  FACTOR
  FIXED_AMOUNT
}

enum products_UnderwritingResultDecision {
  ACCEPTED
  REJECTED
  PENDING
}

enum products_UnderwritingType {
  MANUAL
  AUTO
}

enum products_Validation {
  EMAIL
  URL
  EMOJI
  UUID
  NANOID
  REGEX
  CUID
  CUID2
  ULID
  DATETIME
  DATE
  TIME
  DURATION
  IP
  BASE64
}

directive @authorize("The name of the authorization policy that determines access to the annotated resource." policy: String "Roles that are allowed to access the annotated resource." roles: [String!] "Defines when when the resolver shall be executed.By default the resolver is executed after the policy has determined that the current user is allowed to access the field." apply: ApplyPolicy! = BEFORE_RESOLVER) repeatable on SCHEMA | OBJECT | FIELD_DEFINITION

"The `@defer` directive may be provided for fragment spreads and inline fragments to inform the executor to delay the execution of the current fragment to indicate deprioritization of the current fragment. A query with `@defer` directive will cause the request to potentially return multiple responses, where non-deferred data is delivered in the initial response and data deferred is delivered in a subsequent response. `@include` and `@skip` take precedence over `@defer`."
directive @defer("If this argument label has a value other than null, it will be passed on to the result of this defer directive. This label is intended to give client applications a way to identify to which fragment a deferred result belongs to." label: String "Deferred when true." if: Boolean) on FRAGMENT_SPREAD | INLINE_FRAGMENT

"The `@specifiedBy` directive is used within the type system definition language to provide a URL for specifying the behavior of custom scalar definitions."
directive @specifiedBy("The specifiedBy URL points to a human-readable specification. This field will only read a result for scalar types." url: String!) on SCALAR

"The `@stream` directive may be provided for a field of `List` type so that the backend can leverage technology such as asynchronous iterators to provide a partial list in the initial response, and additional list items in subsequent responses. `@include` and `@skip` take precedence over `@stream`."
directive @stream("If this argument label has a value other than null, it will be passed on to the result of this stream directive. This label is intended to give client applications a way to identify to which fragment a streamed result belongs to." label: String "The initial elements that shall be send down to the consumer." initialCount: Int! = 0 "Streamed when true." if: Boolean) on FIELD

scalar Any

"The `Date` scalar represents an ISO-8601 compliant date type."
scalar Date

"The `DateTime` scalar represents an ISO-8601 compliant date time type."
scalar DateTime @specifiedBy(url: "https:\/\/www.graphql-scalars.com\/date-time")

"The built-in `Decimal` scalar type."
scalar Decimal

"The `Long` scalar type represents non-fractional signed whole 64-bit numeric values. Long can represent values between -(2^63) and 2^63 - 1."
scalar Long

"The `TimeSpan` scalar represents an ISO-8601 compliant duration type."
scalar TimeSpan

scalar UUID @specifiedBy(url: "https:\/\/tools.ietf.org\/html\/rfc4122")
