using CoverGo.Gateway.Client;
using CoverGo.Products.Client;
using CoverGo.Products.Tests.Integration.Utils;

using GraphQL.Client.Http;

using IdentityModel.Client;

using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration
{
    [Collection("Sequential")]
    public class ProductCrudTests : TestBase
    {
        [Fact]
        public async Task GIVEN_product_added_WHEN_query_with_events_THEN_contain_event_logs()
        {
            productId productId = await Products.Create();

            await Products.AddFact(productId);
            await Products.AddInternalReview(productId);

            product product = await Products.SearchProductById(productId);

            List<detailedEventLog?>? eventsList = product.events?.ToList();
            eventsList?.Count().Should().Be(3);
            eventsList?[2]!.type.Should().Be("creation");
            eventsList?[1]!.type.Should().Be("addFact");
            eventsList?[0]!.type.Should().Be("addInternalReview");
            eventsList?.Should().Contain(e => e!.valueAsString!.Contains(@"""productId"""));
        }

        public async Task GIVEN_products_created_WHEN_search_by_version_ncontains_THEN_returns_product_satisfying_condition()
        {
            productId productId1 = await Products.Create();
            productId productId2 = await Products.Create(new createProductInput
            {
                productId = new productIdInput
                {
                    type = CreateNewGuid(),
                    plan = CreateNewGuid(),
                    version = "test-version"
                }
            });

            productId?[] productsIds = (await Products.QueryProductsAsync()).Select(p => p.productId).ToArray();

            productsIds.Any(id => id == productId1).Should().BeTrue();
            productsIds.Any(id => id == productId2).Should().BeTrue();

            productsIds = (await Products.QueryProductsAsync(new productWhereInput
            {
                productId = new productIdWhereInput
                {
                    version_ncontains = "est-versio"
                }
            })).Select(p => p.productId).ToArray();

            productsIds.Any(id => id == productId1).Should().BeTrue();
            productsIds.Any(id => id == productId2).Should().BeFalse();
        }

        [Fact]
        public async Task GIVEN_products_added_WHEN_query_with_events_THEN_contain_event_logs()
        {
            productId pid1 = await Products.Create();
            productId pid2 = await Products.Create();
            (string, addFactInput) addFactTask = await Products.AddFact(pid1);
            string addReviewTask = await Products.AddInternalReview(pid2);
            //await Task.WhenAll(addFactTask, addReviewTask);

            product s1 = await Products.SearchProductById(pid1, _gatewayGraphQlHttpClient);
            product s2 = await Products.SearchProductById(pid2, _gatewayGraphQlHttpClient);
            List<detailedEventLog?> peList1 = s1.events.ToList();
            List<detailedEventLog?> peList2 = s2.events.ToList();

            peList1.Count().Should().Be(2);
            peList1[0]!.type.Should().Be("addFact");
            peList1[1]!.type.Should().Be("creation");
            peList2.Count().Should().Be(2);
            peList2[0]!.type.Should().Be("addInternalReview");
            peList2[1]!.type.Should().Be("creation");
        }

        [Fact]
        public async Task GIVEN_productTreeId_WHEN_createProduct_THEN_product_contains_productTreeID_reference()
        {
            string productTreeId = CreateNewGuid();
            createProductInput input = new()
            {
                productId = new productIdInput
                {
                    plan = CreateNewGuid(),
                    type = CreateNewGuid()
                },
                productTreeId = productTreeId
            };

            string mutation = new covergoMutationBuilder()
                .createProduct(new covergoMutationBuilder.createProductArgs(input), new productBuilder()
                    .productId(new productIdBuilder()
                        .WithAllFields()))
                .Build();
            product response = await _gatewayGraphQlHttpClient.SendMutationAndEnsureAsync<product>(mutation);
            productId productId = response.productId!;

            product product = await Products.SearchProductById(productId, _gatewayGraphQlHttpClient);
            product.productTreeId.Should().Be(productTreeId);
        }

        [Fact]
        public async Task GIVEN_product_with_productTreeId_WHEN_updateProduct_THEN_product_productTreeID_is_updated()
        {
            string productTreeId = CreateNewGuid();
            createProductInput createInput = new()
            {
                productId = new productIdInput { plan = CreateNewGuid(), type = CreateNewGuid() },
                productTreeId = productTreeId
            };

            string createMutation = new covergoMutationBuilder()
                .createProduct(new covergoMutationBuilder.createProductArgs(createInput), new productBuilder()
                    .productId(new productIdBuilder()
                        .WithAllFields()))
                .Build();
            product createResponse = await _gatewayGraphQlHttpClient.SendMutationAndEnsureAsync<product>(createMutation);
            productId productId = createResponse.productId!;

            string productTreeId2 = CreateNewGuid();
            var updateInput = new updateProductInput { productTreeId = productTreeId2 };
            string mutation = new covergoMutationBuilder()
                .updateProduct(new covergoMutationBuilder.updateProductArgs(
                    new productIdInput { plan = productId.plan, version = productId.version, type = productId.type }, updateInput),
                    new productBuilder()
                    .productId(new productIdBuilder()
                        .WithAllFields()))
                .Build();
            await _gatewayGraphQlHttpClient.SendMutationAndEnsureAsync<product>(mutation);

            product product = await Products.SearchProductById(productId, _gatewayGraphQlHttpClient);
            product.productTreeId.Should().Be(productTreeId2);
        }

        [Fact]
        public async Task GIVEN_product_with_pdtId_for_validation_enabled_tenant_WHEN_creating_product_with_same_pdtId_THEN_error_is_returned()
        {
            var gatewayGraphQlHttpClient = await InitializeTenant();
            (string pdtId, string productType, string productPlan) = await CreateProductWithId(gatewayGraphQlHttpClient);

            createProductInput input = new()
            {
                productId = new productIdInput { plan = CreateNewGuid(), type = productType, version = "1.0" },
                fields = JsonConvert.SerializeObject(new { pdtID = pdtId }).Escape()
            };

            string mutation = new covergoMutationBuilder()
                .createProduct(new covergoMutationBuilder.createProductArgs(input), new productBuilder()
                    .productId(new productIdBuilder()
                        .WithAllFields()))
                .Build();
            var response = await gatewayGraphQlHttpClient.SendMutationAsync<JToken>(new GraphQLHttpRequest(mutation));
            response.Errors.Should().NotBeNullOrEmpty();
            response.Errors?.First().Message.Should().Be("This pdtID already exists");
        }

        [Fact]
        public async Task GIVEN_product_with_pdtId_for_validation_disabled_tenant_WHEN_creating_product_with_same_pdtId_THEN_creation_is_successful()
        {
            (string pdtId, string productType, string productPlan) = await CreateProductWithId(_gatewayGraphQlHttpClient);

            createProductInput input = new()
            {
                productId = new productIdInput { plan = CreateNewGuid(), type = productType },
                fields = JsonConvert.SerializeObject(new { pdtID = pdtId }).Escape()
            };

            string mutation = new covergoMutationBuilder()
                .createProduct(new covergoMutationBuilder.createProductArgs(input), new productBuilder()
                    .productId(new productIdBuilder()
                        .WithAllFields()))
                .Build();
            var response = await _gatewayGraphQlHttpClient.SendMutationAsync<JToken>(new GraphQLHttpRequest(mutation));
            response.Errors.Should().BeNullOrEmpty();
        }

        [Fact]
        public async Task GIVEN_product_with_pdtId_for_validation_enabled_tenant_WHEN_creating_product_with_same_pdtId_different_version_THEN_creation_is_successful()
        {
            var gatewayGraphQlHttpClient = await InitializeTenant();
            (string pdtId, string productType, string productPlan) = await CreateProductWithId(gatewayGraphQlHttpClient);

            createProductInput input = new()
            {
                productId = new productIdInput { plan = productPlan, type = productType, version = "1.0_custom_AAA" },
                fields = JsonConvert.SerializeObject(new { pdtID = pdtId }).Escape()
            };

            string mutation = new covergoMutationBuilder()
                .createProduct(new covergoMutationBuilder.createProductArgs(input), new productBuilder()
                    .productId(new productIdBuilder()
                        .WithAllFields()))
                .Build();
            var response = await gatewayGraphQlHttpClient.SendMutationAsync<JToken>(new GraphQLHttpRequest(mutation));
            response.Errors.Should().BeNullOrEmpty();
        }

        [Fact]
        public async Task GIVEN_products_with_different_pdtId_for_validation_enabled_tenant_WHEN_updating_product_to_same_pdtId_THEN_error_is_returned()
        {
            var gatewayGraphQlHttpClient = await InitializeTenant();
            (string product1PdtId, string product1ProductType, string product1Plan) = await CreateProductWithId(gatewayGraphQlHttpClient);
            (string product2PdtId, string product2ProductType, string product2Plan) = await CreateProductWithId(gatewayGraphQlHttpClient,
                productType: product1ProductType);

            string updateMutation = new covergoMutationBuilder()
                .updateProduct(new covergoMutationBuilder.updateProductArgs(
                    new productIdInput { plan = product2Plan, type = product2ProductType, version = "1.0" },
                    new updateProductInput { fields = JsonConvert.SerializeObject(new { pdtID = product1PdtId }).Escape() }),
                    new productBuilder()
                    .productId(new productIdBuilder()
                        .WithAllFields()))
                .Build();
            var response = await gatewayGraphQlHttpClient.SendMutationAsync<JToken>(new GraphQLHttpRequest(updateMutation));
            response.Errors.Should().NotBeNullOrEmpty();
            response.Errors?.First().Message.Should().Be("This pdtID already exists");
        }

        [Fact]
        public async Task GIVEN_products_with_different_pdtId_for_validation_enabled_tenant_WHEN_updating_product_to_same_pdtId_with_fieldsPatch_THEN_error_is_returned()
        {
            var gatewayGraphQlHttpClient = await InitializeTenant();
            (string product1PdtId, string product1ProductType, string product1Plan) = await CreateProductWithId(gatewayGraphQlHttpClient);
            (string product2PdtId, string product2ProductType, string product2Plan) = await CreateProductWithId(gatewayGraphQlHttpClient,
                productType: product1ProductType);

            string updateMutation = new covergoMutationBuilder()
                .updateProduct(new covergoMutationBuilder.updateProductArgs(
                    new productIdInput { plan = product2Plan, type = product2ProductType, version = "1.0" },
                    new updateProductInput
                    {
                        fieldsPatch = JsonConvert.SerializeObject(new[]
                    {
                        new
                        {
                            op = "replace",
                            path = "/pdtID",
                            value = product1PdtId
                        }
                    }).Escape()
                    }),
                    new productBuilder()
                    .productId(new productIdBuilder()
                        .WithAllFields()))
                .Build();
            var response = await gatewayGraphQlHttpClient.SendMutationAsync<JToken>(new GraphQLHttpRequest(updateMutation));
            response.Errors.Should().NotBeNullOrEmpty();
            response.Errors?.First().Message.Should().Be("This pdtID already exists");
        }

        [Fact]
        public async Task GIVEN_products_with_different_pdtId_for_validation_disabled_tenant_WHEN_updating_product_to_same_pdtId_THEN_update_is_successful()
        {
            (string product1PdtId, string product1ProductType, string product1Plan) = await CreateProductWithId(_gatewayGraphQlHttpClient);
            (string product2PdtId, string product2ProductType, string product2Plan) = await CreateProductWithId(_gatewayGraphQlHttpClient,
                productType: product1ProductType);

            string updateMutation = new covergoMutationBuilder()
                .updateProduct(new covergoMutationBuilder.updateProductArgs(
                    new productIdInput { plan = product2Plan, type = product2ProductType, version = "1.0" },
                    new updateProductInput { fields = JsonConvert.SerializeObject(new { pdtID = product1PdtId }).Escape() }),
                    new productBuilder()
                    .productId(new productIdBuilder()
                        .WithAllFields()))
                .Build();
            var response = await _gatewayGraphQlHttpClient.SendMutationAsync<JToken>(new GraphQLHttpRequest(updateMutation));
            response.Errors.Should().BeNullOrEmpty();
        }

        [Fact]
        public async Task GIVEN_product_with_unique_pdtId_WHEN_updating_product_THEN_update_is_successful()
        {
            var gatewayGraphQlHttpClient = await InitializeTenant();
            (string pdtId, string productType, string plan) = await CreateProductWithId(gatewayGraphQlHttpClient);

            string updateMutation = new covergoMutationBuilder()
                .updateProduct(new covergoMutationBuilder.updateProductArgs(
                    new productIdInput { plan = plan, type = productType, version = "1.0" },
                    new updateProductInput { lifecycleStage = CreateNewGuid() }),
                    new productBuilder()
                    .productId(new productIdBuilder()
                        .WithAllFields()))
                .Build();
            var response = await gatewayGraphQlHttpClient.SendMutationAsync<JToken>(new GraphQLHttpRequest(updateMutation));
            response.Errors.Should().BeNullOrEmpty();
        }

        [Fact]
        public async Task GIVEN_product_with_unique_pdtId_WHEN_updating_product_with_empty_fieldsPatch_THEN_update_is_successful()
        {
            var gatewayGraphQlHttpClient = await InitializeTenant();
            (string pdtId, string productType, string plan) = await CreateProductWithId(gatewayGraphQlHttpClient);

            string updateMutation = new covergoMutationBuilder()
                .updateProduct(new covergoMutationBuilder.updateProductArgs(
                    new productIdInput { plan = plan, type = productType, version = "1.0" },
                    new updateProductInput { lifecycleStage = CreateNewGuid(), fieldsPatch = "[]" }),
                    new productBuilder()
                    .productId(new productIdBuilder()
                        .WithAllFields()))
                .Build();
            var response = await gatewayGraphQlHttpClient.SendMutationAsync<JToken>(new GraphQLHttpRequest(updateMutation));
            response.Errors.Should().BeNullOrEmpty();
        }

        private static async Task<(string pdtId, string productType, string plan)> CreateProductWithId(GraphQLHttpClient gatewayGraphQlHttpClient,
            string? pdtId = default, string? productType = default, string? version = "1.0")
        {
            pdtId ??= CreateNewGuid();
            productType ??= CreateNewGuid();
            string plan = CreateNewGuid();
            createProductInput input = new()
            {
                productId = new productIdInput { plan = plan, type = productType, version = version },
                fields = JsonConvert.SerializeObject(new { pdtID = pdtId }).Escape()
            };

            string mutation = new covergoMutationBuilder()
                .createProduct(new covergoMutationBuilder.createProductArgs(input), new productBuilder()
                    .productId(new productIdBuilder()
                        .WithAllFields()))
                .Build();
            await gatewayGraphQlHttpClient.SendMutationAndEnsureAsync<product>(mutation);

            return (pdtId, productType, plan);
        }

        private async Task<GraphQLHttpClient> InitializeTenant()
        {
            string tenantId = "validatePdtId_tenant";
            string mutation = new covergoMutationBuilder()
                .initializeTenantAuth(new covergoMutationBuilder.initializeTenantAuthArgs(tenantId),
                    new resultBuilder().WithAllFields())
                .Build();
            var result = await _gatewayGraphQlHttpClient.SendMutationAndEnsureAsync<JToken>(mutation);

            mutation = new covergoMutationBuilder()
                .initializeTenant(new covergoMutationBuilder.initializeTenantArgs(tenantId, new createAdminInput
                {
                    email = UserCredentials.Admin.UserName,
                    username = UserCredentials.Admin.UserName,
                    password = UserCredentials.Admin.Password
                },
                null,
                null,
                new createAppInput[]
                {
                    new createAppInput()
                    {
                        appId = UserCredentials.Admin.ClientId,
                        appName = UserCredentials.Admin.ClientId,
                        appConfig = JToken.Parse("{}").ToString(Newtonsoft.Json.Formatting.None).Escape()
                    }
                }),
                    new resultBuilder().WithAllFields())
                .Build();
            result = await _gatewayGraphQlHttpClient.SendMutationAndEnsureAsync<JToken>(mutation);
            result.Should().NotBeNullOrEmpty();

            var accessToken = await GetAccessToken(tenantId);
            return Setup.BuildGatewaysGraphQlClient(accessToken);
        }

        private static async Task<string> GetAccessToken(string tenantId)
        {
            var client = new HttpClient();
            var config = Utils.Config.Load();

            var response = await client.RequestPasswordTokenAsync(new()
            {
                Address = $"{config.AuthUrl}/{tenantId}/connect/token",
                ClientId = UserCredentials.Admin.ClientId,
                UserName = UserCredentials.Admin.UserName,
                Password = UserCredentials.Admin.Password,
                Scope = "custom_profile offline_access",
            });

            return response.AccessToken;
        }

        async Task<ICollection<Product>> GetProducts(ProductWhere where, bool loadRepresentation = false)
        {
            ProductQuery q = new ProductQuery { Where = where, LoadRepresentation = loadRepresentation };

            return await Products.GetProducts(q);
        }

        public static string GetPricingScript()
        {
            const string pricingScriptResourceName = "CoverGo.Products.Tests.Integration.Resources.pricing-script.txt";
            using var streamReader = new StreamReader(typeof(ProductCrudTests).Assembly.GetManifestResourceStream(pricingScriptResourceName)!);
            string script = streamReader.ReadToEnd();
            return script;
        }
    }
}
