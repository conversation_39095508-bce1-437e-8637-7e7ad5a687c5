using System.Net.Http;
using System.Threading.Tasks;
using CoverGo.L10n.Client;

namespace CoverGo.Products.Tests.Integration.Utils;

public class L10n
{
    private readonly IL10nClient _client;
    private readonly string _tenantId;

    public L10n(HttpClient client, string tenantId)
    {
        _client = new L10nClient(client);
        _tenantId = tenantId;
    }

    public async Task Upsert(string key, string value) => await _client.L10n2Async(_tenantId, new UpsertL10nCommand
    {
        Locale = "en-US",
        Key = key,
        Value = value
    });
}