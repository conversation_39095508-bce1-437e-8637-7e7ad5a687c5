using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.FileSystem.Client;

namespace CoverGo.Products.Tests.Integration.Utils;

public class FileSystem
{
    private readonly FileSystemClient _client;
    private readonly string _tenantId;

    public FileSystem(HttpClient client, string tenantId)
    {
        _client = new FileSystemClient(client);
        _tenantId = tenantId;
    }

    public async Task Initialize()
    {
        await _client.FileSystem_InitializeTenantAsync(_tenantId, new InitializeTenantFileSystemCommand
        {
            Config = new FileSystemConfig
            {
                ProviderId = "local",
                AccessKeyId = "-",
                AccessKeySecret = "-",
                BucketName = "-",
                Endpoint = "-"
            }
        });
    }

    public async Task Upload(string key, string content)
    {
        UploadFileCommand uploadFileCommand = new()
        {
            Key = key,
            Content = Encoding.UTF8.GetBytes(content)
        };

        Result result = await _client.FileSystem_AddFileAsync(_tenantId, null, uploadFileCommand);
        if (!result.IsSuccess) throw new InvalidOperationException(string.Join(", ", result.Errors));
    }

    public async Task Delete(List<string> keys)
    {
        DeleteFileCommand deleteFileCommand = new() { Keys = keys };
        Result result = await _client.FileSystem_DeleteAsync(_tenantId, null, deleteFileCommand);
        if (!result.IsSuccess) throw new InvalidOperationException(string.Join(", ", result.Errors));
    }

    public async Task<string> Download(string key)
    {
        GetFileCommand getFileCommand = new() { Key = key };
        ResultOfByteOf result = await _client.FileSystem_GetAsync(_tenantId, null, getFileCommand);
        if (!result.IsSuccess) throw new InvalidOperationException(string.Join(", ", result.Errors));
        return Encoding.UTF8.GetString(result.Value);
    }
}