﻿using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration.Utils
{
    public class Token
    {
        private readonly GraphQLHttpClient _gatewayGraphQlClient;
        public Token(GraphQLHttpClient gatewayGraphQlClient) 
        { 
            _gatewayGraphQlClient = gatewayGraphQlClient;
        }

        public async Task<string> Fetch(covergoQueryBuilder.token_2Args tokenArgs)
        {
            string query = new covergoQueryBuilder()
                .token_2(tokenArgs, new tokenBuilder()
                    .error()
                    .errorDescription()
            .accessToken())
            .Build();

            token token = await _gatewayGraphQlClient.SendQueryAndEnsureAsync<token>(query);

            if (token.error != null)
                throw new Exception(token.errorDescription);

            return token.accessToken!;
        }
    }
}
