using CoverGo.Gateway.Client;

using GraphQL.Client.Http;

using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration.Utils
{
    public sealed class Benefits
    {
        private readonly GraphQLHttpClient _client;
        public Benefits(GraphQLHttpClient client)
        {
            _client = client;
        }


        public async Task<string> CreateBenefitDefinitionType(string name, string? fields = null)
        {
            string mutation = new covergoMutationBuilder().createBenefitDefinitionType(new covergoMutationBuilder.createBenefitDefinitionTypeArgs(input: new createBenefitDefinitionTypeInput
            {
                businessId = CreateNewGuid(),
                description = CreateNewGuid(),
                name = name,
                status = CreateNewGuid(),
                fields = fields
            }), new createdStatusResultBuilder()
                .createdStatus(new createdStatusBuilder()
                    .id()
                    .ids())
                .status()
                .errors()).Build();

            createdStatusResult result = await _client.SendMutationAndEnsureAsync<createdStatusResult>(mutation);
            result!.errors.Should().BeNullOrEmpty();
            return result!.createdStatus!.id!;
        }

        public async Task<string> CreateBenefitDefinition(string typeId, string? fields = null)
        {
            string? mutation = new covergoMutationBuilder()
                .createBenefitDefinition(new covergoMutationBuilder.createBenefitDefinitionArgs(input: new createBenefitDefinitionInput
                {
                    benefitDefinitionTypeIds = new List<string> { typeId },
                    businessId = CreateNewGuid(),
                    description = CreateNewGuid(),
                    name = CreateNewGuid(),
                    status = CreateNewGuid(),
                    fields = fields
                }), new createdStatusResultBuilder()
                    .createdStatus(new createdStatusBuilder()
                        .id()
                        .ids())
                    .status()
                    .errors())
                .Build();

            createdStatusResult result = await _client.SendMutationAndEnsureAsync<createdStatusResult>(mutation);
            result!.errors.Should().BeNullOrEmpty();
            return result!.createdStatus!.id!;
        }

        public async Task<benefitDefinitions> GetBenefitDefinitions(benefitDefinitionWhereInput filterInput)
        {
            var query = new covergoQueryBuilder().benefitDefinitions(new covergoQueryBuilder.benefitDefinitionsArgs(where: filterInput), new benefitDefinitionsBuilder()
                .totalCount()
                .list(new benefitDefinitionBuilder()
                .id()
                .fields()
                .benefitDefinitionTypes(
                            new benefitDefinitionTypeBuilder()
                                .fields())))
            .Build();

            return await _client.SendQueryAndEnsureAsync<benefitDefinitions>(query);
        }


        private static string CreateNewGuid() =>
            Guid.NewGuid().ToString();
    }
}
