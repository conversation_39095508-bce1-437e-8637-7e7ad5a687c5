﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration.Utils
{
    static class TypesExtensionMethods
    {
        /// <summary>
        /// Converts object to a json string
        /// Note that for now it takes into consideration a bug
        /// in the graphql client that requires
        /// to escape all quotes in input strings
        /// </summary>
        /// <param name="object"></param>
        /// <returns></returns>
        public static string ToEscapedJsonString(this object input) =>
            JsonConvert.SerializeObject(input).Escape();

        /// <summary>
        /// Escape strings so it can be passed to graphql client as an argument
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static string Escape(this string input) =>
            input.Replace(@"""", @"\""").Replace("\n", "").Replace("\r", "");

        /// <summary>
        /// This method is to address issue in graphql client
        /// that it only allow escaped strings as input.
        /// So we need to unescape them to be able to compare later.
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static string Unescape(this string input) =>
            input.Replace(@"\""", @"""");

        public static string RemoveWhitespaces(this string input) =>
            Regex.Replace(input, @"\s", "");

        public static string RemoveLineBreaks(this string input) =>
            string.IsNullOrEmpty(input) ? input : input.Replace("\r", "").Replace("\n", "");
    }
}
