using System;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using CoverGo.ProductBuilder.Client;
using GraphQL;
using GraphQL.Client.Http;

namespace CoverGo.Products.Tests.Integration.Utils;

public class ProductBuilder
{
    private readonly GraphQLHttpClient _client;

    public ProductBuilder(GraphQLHttpClient client) => _client = client;

    public Task<string> CreateNode(CreateNodeInput input) => SendMutation<string>(new MutationBuilder()
        .createNode(input)
        .Build());

    public Task<string> Subtree(Guid nodeId, NodeBuilder nodeBuilder) => SendQueryRaw(new QueryBuilder()
        .subtree(new(nodeId), nodeBuilder)
        .Build());

    public Task<ProductSchemaDescription> GetProductSchema(Guid productTreeId) => SendQuery<ProductSchemaDescription>(new QueryBuilder()
        .productSchema(new QueryBuilder.productSchemaArgs(null, productTreeId), new ProductSchemaDescriptionBuilder()
            .dataSchema()
            .uiSchemas(new UiSchemaBuilder()
                .name()
                .schema()))
        .Build());

    public Task<ResultOfGuid> CreateProductSchema(ProductSchemaInput input) => SendMutation<ResultOfGuid>(new MutationBuilder()
        .createProductSchema(new MutationBuilder.createProductSchemaArgs(input), new ResultOfGuidBuilder()
            .WithAllFields())
        .Build());

    public Task<Result> AddUISchemaToProductSchema(Guid productSchemaId, UiSchemaInput input) => SendMutation<Result>(new MutationBuilder()
        .addUiSchemaToProductSchema(new MutationBuilder.addUiSchemaToProductSchemaArgs(productSchemaId, input), new ResultBuilder()
            .WithAllFields())
        .Build());

    public Task<Result> AttachNodeScript(Guid nodeId, NodeScriptInput input) => SendMutation<Result>(new MutationBuilder()
        .attachNodeScript(new MutationBuilder.attachNodeScriptArgs(nodeId, input), new ResultBuilder()
            .WithAllFields())
        .Build());

    public Task<ResolutionResult> Resolve(Guid nodeId, string fieldName, ExpressionInput input) => SendQuery<ResolutionResult>(new QueryBuilder()
        .resolve(new QueryBuilder.resolveArgs(fieldName, nodeId, null, input), new ResolutionResultBuilder()
            .value())
        .Build());

    public Task<LockStatus> LockNode(Guid nodeId) => SendMutation<LockStatus>(new MutationBuilder()
        .lockNode(new MutationBuilder.lockNodeArgs(nodeId), new LockStatusBuilder()
            .WithAllFields())
        .Build());

    private async Task<TResponse> SendMutation<TResponse>(string mutation)
    {
        GraphQLResponse<JsonElement> response = await _client.SendMutationAsync<JsonElement>(new GraphQLHttpRequest(mutation));
        JsonSerializerOptions jsonOptions = new();
        jsonOptions.Converters.Add(new JsonConverterGuid());
        return response.Data.EnumerateObject().First().Value.Deserialize<TResponse>(jsonOptions)
           ?? throw new Exception("Cannot deserialize result");
    }

    private async Task<TResponse> SendQuery<TResponse>(string query)
    {
        GraphQLResponse<JsonElement> response = await _client.SendQueryAsync<JsonElement>(new GraphQLHttpRequest(query));
        JsonElement value = response.Data.EnumerateObject().First().Value;
        return value.ValueKind == JsonValueKind.Null ? default : value.Deserialize<TResponse>();
    }

    private async Task<string> SendQueryRaw(string query)
    {
        GraphQLResponse<JsonElement> response = await _client.SendQueryAsync<JsonElement>(new GraphQLHttpRequest(query));
        return response.Data.EnumerateObject().First().Value.ToString();
    }
}