﻿using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration.Utils
{
    public class ProductType
    {
        private readonly GraphQLHttpClient _gatewayGraphQlClient;
        public ProductType(GraphQLHttpClient gatewayGraphQlClient)
        {
            _gatewayGraphQlClient = gatewayGraphQlClient;
        }
        public async Task<string> Create()
        {
            string typeId = CreateNewGuid();
            await Create(new createProductTypeInput { typeId = typeId });
            return typeId;
        }

        public Task Create(string typeId) =>
            Create(new createProductTypeInput { typeId = typeId });

        public Task Create(createProductTypeInput input)
        {
            string mutation = new covergoMutationBuilder()
                  .createProductType(new covergoMutationBuilder.createProductTypeArgs(input), new resultBuilder()
            .WithAllFields())
            .Build();

            return _gatewayGraphQlClient.SendMutationAndEnsureAsync<Result>(mutation);
        }

        public async Task<productType> FindById(string id, productTypeBuilder fields)
        {
            string query = new covergoQueryBuilder()
                .productTypes(new covergoQueryBuilder.productTypesArgs(new productTypeWhereInput { typeId = id }), fields)
            .Build();

            return (await _gatewayGraphQlClient.SendQueryAndEnsureAsync<ICollection<productType>>(query)).Single();
        }

        protected static string CreateNewGuid() =>
Guid.NewGuid().ToString();

    }
}
