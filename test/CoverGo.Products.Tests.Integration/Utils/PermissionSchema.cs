﻿using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration.Utils
{
    public sealed class PermissionSchema
    {
        private readonly GraphQLHttpClient _gatewayGraphQlClient;
        public PermissionSchema(GraphQLHttpClient gatewayGraphQlClient)
        { 
            _gatewayGraphQlClient = gatewayGraphQlClient;
        }

        public Task<string> Create(createPermissionSchemaInput input)
        {
            string mutation = new covergoMutationBuilder()
                .createPermissionSchema(new covergoMutationBuilder.createPermissionSchemaArgs(input), new createdStatusResultBuilder()
            .WithAllFields())
            .Build();

            return _gatewayGraphQlClient.CreateAndReturnId(mutation);
        }

        public async Task<(string id, createPermissionSchemaInput input)> Create()
        {
            createPermissionSchemaInput input = new()
            {
                name = CreateNewGuid(),
                objectType = CreateNewGuid(),
                actionType = permissionSchemaActionType.READ,
                description = CreateNewGuid(),
                schema = "",
                stateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.EQUALS,
                    path = "foo.bar.bar",
                    value = new scalarValueInput { stringValue = CreateNewGuid() }
                },
                updateCondition = new fieldsWhereInput
                {
                    condition = fieldsWhereCondition.EQUALS,
                    path = "foo.bar.test.test",
                    value = new scalarValueInput { stringValue = CreateNewGuid() }
                }
            };

            return (await Create(input), input);
        }

        private static string CreateNewGuid() =>
        Guid.NewGuid().ToString();

    }
}
