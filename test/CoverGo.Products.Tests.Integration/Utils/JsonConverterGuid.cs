using System;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace CoverGo.Products.Tests.Integration.Utils;

public class JsonConverterGuid : JsonConverter<Guid>
{
    public override Guid Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TryGetGuid(out Guid value)) return value;
        if (Guid.TryParse(reader.GetString(), out value)) return value;
        throw new FormatException("The JSON value is not in a supported Guid format.");
    }

    public override void Write(Utf8JsonWriter writer, Guid value, JsonSerializerOptions options) =>
        writer.WriteStringValue(value);
}