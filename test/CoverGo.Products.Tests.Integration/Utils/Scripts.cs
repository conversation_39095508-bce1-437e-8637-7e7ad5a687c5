#nullable enable

using System.Threading.Tasks;
using CoverGo.Gateway.Client;
using covergoMutationBuilder = CoverGo.Gateway.Client.covergoMutationBuilder;
using CoverGo.Products.Client;
using GraphQL.Client.Http;

namespace CoverGo.Products.Tests.Integration.Utils;

public sealed class Scripts
{
    private readonly ProductsClient _httpClient;
    private readonly GraphQLHttpClient _graphQlClient;
    private readonly string _tenantId;

    public Scripts(ProductsClient httpClient, GraphQLHttpClient graphQlClient, string tenantId)
    {
        _httpClient = httpClient;
        _graphQlClient = graphQlClient;
        _tenantId = tenantId;
    }

    public Task<ResultOfCreatedStatus> Create(CreateScriptCommand command)
    {
        return _httpClient.Scripts_CreateAsync(_tenantId, command)!;
    }

    public Task<string> Create(createScriptInput input)
    {
        string mutation = new covergoMutationBuilder()
            .createScript(new covergoMutationBuilder.createScriptArgs(input), new createdStatusResultBuilder()
                .WithAllFields())
        .Build();

        return _graphQlClient.CreateAndReturnId(mutation);
    }


    public async Task<ScriptResult?> Evaluate(string clientId, EvaluateProductScriptCommand command)
    {
        return await _httpClient.ScriptEvaluation_Evaluate2Async(_tenantId, clientId, command);
    }

    public async Task<ScriptResult?> EvaluateV2(string? clientId, EvaluateProductScriptCommand command)
    {
        return await _httpClient.ScriptEvaluationControllerV2_EvaluateAsync(_tenantId, clientId, command);
    }
}
