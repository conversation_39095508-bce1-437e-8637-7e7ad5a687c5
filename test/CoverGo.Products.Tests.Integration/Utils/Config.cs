﻿using Microsoft.Extensions.Configuration;

namespace CoverGo.Products.Tests.Integration.Utils;

public class Config
{
    public string GatewayUrl { get; set; }
    public string AuthUrl { get; set; }
    public string ProductsUrl { get; set; }
    public string ProductBuilderUrl { get; set; }
    public string FileSystemUrl { get; set; }
    public string L10nUrl { get; set; }
    public string ReferenceUrl { get; set; }
    public string ChannelManagementUrl { get; set; }

    public static readonly Config Local = new()
    {
        AuthUrl = "http://localhost:60000",
        ProductsUrl = "http://localhost:60020",
        ProductBuilderUrl = "http://localhost:60180",
        FileSystemUrl = "http://localhost:61872",
        L10nUrl = "http://localhost:60040",
        ReferenceUrl = "http://localhost:61910/graphql",
        ChannelManagementUrl = "http://localhost:64483",
        GatewayUrl = "http://localhost:60060"
    };

    private const string EnvironmentVariablePrefix = "PRODUCTS_INTEGRATION_TEST-";

    public static Config Load(string prefix = EnvironmentVariablePrefix)
    {
        var cfg = new Config();
        var builder = new ConfigurationBuilder();

        builder.AddEnvironmentVariables(source =>
        {
            source.Prefix = prefix;
        });

        builder.Build().Bind(cfg);

        return string.IsNullOrWhiteSpace(cfg.ProductsUrl) ? Local : cfg;
    }
}
