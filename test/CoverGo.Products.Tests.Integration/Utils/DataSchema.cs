﻿using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration.Utils
{
    public class DataSchema
    {
        private readonly GraphQLHttpClient _gatewayGraphQlClient;
        public DataSchema(GraphQLHttpClient gatewayGraphQlClient)
        {
            _gatewayGraphQlClient = gatewayGraphQlClient;
        }

        public async Task<(string, createDataSchemaInput)> Create(List<string?>? tags = default)
        {
            createDataSchemaInput input = new()
            {
                name = Guid.NewGuid().ToString(),
                description = Guid.NewGuid().ToString(),
                schema = "{}",
                standard = new dataSchemaStandardInput
                {
                    type = dataSchemaStandardTypeEnum.STATE_CHART,
                    version = Guid.NewGuid().ToString()
                },
                type = Guid.NewGuid().ToString(),
                tags = tags ?? new List<string?>()
            };
            return (await Create(input), input);
        }

        public async Task<string> Create(createDataSchemaInput input)
        {
            string mutation = new covergoMutationBuilder()
                .createDataSchema(new covergoMutationBuilder.createDataSchemaArgs(input), new createdStatusResultBuilder()
                    .createdStatus(new createdStatusBuilder()
            .id()))
            .Build();

            return await _gatewayGraphQlClient.CreateAndReturnId(mutation);
        }
    }
}
