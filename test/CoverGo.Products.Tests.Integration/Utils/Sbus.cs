using CoverGo.Products.Client;
using GraphQL.Client.Http;
using System;
using System.Linq;
using System.Threading.Tasks;
namespace CoverGo.Products.Tests.Integration.Utils;

public sealed class Sbus
{
    private readonly GraphQLHttpClient _graphQlClient;
    private readonly DateTime _today;

    public Sbus(GraphQLHttpClient client)
    {
        _today = DateTime.UtcNow.Date;
        _graphQlClient = client;
    }

    public async Task<string> Create(products_SbuUpsertInput input)
    {
        string mutation = new MutationBuilder()
            .sbusMutationCreate(
                new MutationBuilder.sbusMutationCreateArgs(input),
                new products_ResultOfCreatedStatusBuilder().WithAllFields()
            )
            .Build();

        return (await _graphQlClient.SendMutationAndEnsureAsync<products_ResultOfCreatedStatus>(mutation))
            .value
            .id;
    }

    public Task<products_Result> Update(products_SbuUpsertInput input)
    {
        string mutation = new MutationBuilder()
            .sbusMutationUpdate(
                new MutationBuilder.sbusMutationUpdateArgs(input),
                new products_ResultBuilder().WithAllFields()
            )
            .Build();

        return _graphQlClient.SendMutationAndEnsureAsync<products_Result>(mutation);
    }

    public Task<products_Result> Delete(string sbuId)
    {
        string mutation = new MutationBuilder()
            .sbusMutationDelete(
                new MutationBuilder.sbusMutationDeleteArgs(
                    new products_SbuUpsertInput { id = sbuId }
                ),
                new products_ResultBuilder().WithAllFields()
            )
            .Build();

        return _graphQlClient.SendMutationAndEnsureAsync<products_Result>(mutation);
    }

    public async Task<products_Sbu> LastSbuInFutureOrNull()
    {
        string query = new QueryBuilder()
            .sbusQuery(
                new QueryBuilder.sbusQueryArgs(
                    new products_GenericSbuQueryInput
                    {
                        orderBy = new products_OrderByInput
                        {
                            fieldName = "endDate",
                            type = products_OrderByType.DSC,
                        }
                    }
                ),
                new products_GenericSbu8QueryInterfaceBuilder()
                    .list(
                        new products_SbuBuilder().WithAllFields()
                    )
            )
            .Build();

        var sbus = await _graphQlClient.SendQueryAndEnsureAsync<products_GenericSbu8QueryInterface>(query);

        return sbus.list?.FirstOrDefault(
            s => s!.startDate > _today
        );
    }

    public async Task<products_Sbu> LastSbuInFuture()
    {
        var last = await LastSbuInFutureOrNull();

        if (last != null)
        {
            return last;
        }

        products_SbuUpsertInput input = new()
        {
            startDate = _today.AddDays(1),
            endDate = _today.AddDays(3),
            amount = 0.01M,
        };

        string id = await Create(input);

        return (await LastSbuInFutureOrNull())!;
    }
}
