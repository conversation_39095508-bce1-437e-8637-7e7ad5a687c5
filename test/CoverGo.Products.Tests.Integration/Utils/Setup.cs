﻿using CoverGo.ChannelManagement.Client;
using CoverGo.Products.Client;
using CoverGo.Reference.Client;
using GraphQL.Client.Abstractions.Websocket;
using GraphQL.Client.Http;
using GraphQL.Client.Serializer.Newtonsoft;
using GraphQL.Client.Serializer.SystemTextJson;
using IdentityModel.Client;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json.Linq;
using System;
using System.Net.Http;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration.Utils;

static class Setup
{
    private static readonly Lazy<Config> Config = new(() => Utils.Config.Load());

    public static ProductsClient BuildProductsHttpClient(string accessToken) => new ProductsClient(CreateHttpClient(Config.Value.ProductsUrl, accessToken));

    public static HttpClient BuildFileSystemHttpClient(string accessToken) => CreateHttpClient(Config.Value.FileSystemUrl, accessToken);

    public static HttpClient BuildL10nHttpClient(string accessToken) => CreateHttpClient(Config.Value.L10nUrl, accessToken);

    private static HttpClient CreateHttpClient(string baseUrl, string accessToken)
    {
        HttpClient httpClient = new() { BaseAddress = new Uri(baseUrl) };
        httpClient.DefaultRequestHeaders.Authorization = new("Bearer", accessToken);
        return httpClient;
    }

    public static GraphQLHttpClient BuildGatewaysGraphQlClient(string accessToken) =>
        BuildGraphQlClient(Config.Value.GatewayUrl, accessToken, new NewtonsoftJsonSerializer());

    public static GraphQLHttpClient BuildProductsGraphQlClient(string accessToken) =>
        BuildGraphQlClient(Config.Value.ProductsUrl, accessToken, new NewtonsoftJsonSerializer());

    public static GraphQLHttpClient BuildProductBuilderGraphQlClient(string accessToken) =>
        BuildGraphQlClient(Config.Value.ProductBuilderUrl, accessToken, new SystemTextJsonSerializer());

    public static IChannelManagementClient GetChannelManagementClient(string accessToken)
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddChannelManagementClient().ConfigureHttpClient(client =>
        {
            client.BaseAddress = new UriBuilder(Config.Value.ChannelManagementUrl) { Path = "/graphql" }.Uri;
            client.DefaultRequestHeaders.Authorization = new("Bearer", accessToken);
        });
        var serviceProvider = serviceCollection.BuildServiceProvider();
        return serviceProvider.GetRequiredService<IChannelManagementClient>();
    }
    public static ReferenceClient GetReferenceClient(string accessToken)
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddReferenceGraphQlClient().ConfigureHttpClient((HttpClient client) =>
        {
            client.BaseAddress = new UriBuilder(Config.Value.ReferenceUrl) { Path = "/graphql" }.Uri;
            client.DefaultRequestHeaders.Authorization = new("Bearer", accessToken);
        });
        serviceCollection.AddTransient<ReferenceClient>();
        serviceCollection.AddTransient<ReferenceTypePropertyValidator>();

        var serviceProvider = serviceCollection.BuildServiceProvider();
        return serviceProvider.GetRequiredService<ReferenceClient>();
    }

    private static GraphQLHttpClient BuildGraphQlClient(string baseUrl, string accessToken, IGraphQLWebsocketJsonSerializer serializer)
    {
        var client = new GraphQLHttpClient(new UriBuilder(baseUrl) { Path = "/graphql" }.Uri, serializer);
        client.HttpClient.DefaultRequestHeaders.Authorization = new("Bearer", accessToken);
        return client;
    }

    public static async Task<string> GetAccessToken()
    {
        var client = new HttpClient();

        var response = await client.RequestPasswordTokenAsync(new()
        {
            Address = $"{Config.Value.AuthUrl}/{UserCredentials.Admin.TenantId}/connect/token",
            ClientId = UserCredentials.Admin.ClientId,
            UserName = UserCredentials.Admin.UserName,
            Password = UserCredentials.Admin.Password,
            Scope = "custom_profile offline_access",
        });

        return response.AccessToken;
    }


    public static string? GetLoginIdFromJwtAccessToken(string accessToken)
    {
        if (string.IsNullOrEmpty(accessToken)) return null;

        var jwtParts = accessToken.Split(".", StringSplitOptions.RemoveEmptyEntries);
        if (jwtParts.Length != 3) return null;

        var jwtBody = jwtParts[1];

        try
        {
            var jwtJsonString = Base64UrlEncoder.Decode(jwtBody);
            var jwtJson = JToken.Parse(jwtJsonString);
            return jwtJson["sub"]?.ToString();
        }
        catch
        {
            return null;
        }
    }
}
