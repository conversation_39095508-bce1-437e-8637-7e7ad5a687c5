using CoverGo.DomainUtils;
using CoverGo.Gateway.Client;
using CoverGo.Products.Client;
using GraphQL;
using GraphQL.Client.Http;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using _service_CoverGoMutationsRootBuilder = CoverGo.Products.Client.MutationBuilder;
using _service_CoverGoQueriesRootBuilder = CoverGo.Products.Client.QueryBuilder;
using products_CreatedStatusBuilder = CoverGo.Products.Client.products_CreatedStatusBuilder;
using products_DiscountCodeBuilder = CoverGo.Products.Client.products_DiscountCodeBuilder;
using products_DiscountCodeFilterInput = CoverGo.Products.Client.products_DiscountCodeFilterInput;
using products_DiscountCodeProductsUpsertInput = CoverGo.Products.Client.products_DiscountCodeProductsUpsertInput;
using products_DiscountCodeUpsertInput = CoverGo.Products.Client.products_DiscountCodeUpsertInput;
using products_FilePathResult = CoverGo.Products.Client.products_FilePathResult;
using products_FilePathResultBuilder = CoverGo.Products.Client.products_FilePathResultBuilder;
using products_GenericDiscountCode3BatchInput = CoverGo.Products.Client.products_GenericDiscountCode3BatchInput;
using products_GenericDiscountCode8QueryInterface = CoverGo.Products.Client.products_GenericDiscountCode8QueryInterface;
using products_GenericDiscountCode8QueryInterfaceBuilder = CoverGo.Products.Client.products_GenericDiscountCode8QueryInterfaceBuilder;
using products_GenericDiscountCodeFilterInput = CoverGo.Products.Client.products_GenericDiscountCodeFilterInput;
using products_GenericDiscountCodeQueryInput = CoverGo.Products.Client.products_GenericDiscountCodeQueryInput;
using products_GenericProductImportHistoryRecord8QueryInterface = CoverGo.Products.Client.products_GenericProductImportHistoryRecord8QueryInterface;
using products_GenericProductImportHistoryRecord8QueryInterfaceBuilder = CoverGo.Products.Client.products_GenericProductImportHistoryRecord8QueryInterfaceBuilder;
using products_GenericProductImportHistoryRecordQueryInput = CoverGo.Products.Client.products_GenericProductImportHistoryRecordQueryInput;
using products_ProductIdBuilder = CoverGo.Products.Client.products_ProductIdBuilder;
using products_ProductIdInput = CoverGo.Products.Client.products_ProductIdInput;
using products_ProductImportHistoryRecord = CoverGo.Products.Client.products_ProductImportHistoryRecord;
using products_ProductImportHistoryRecordBuilder = CoverGo.Products.Client.products_ProductImportHistoryRecordBuilder;
using products_ProductImportInfo = CoverGo.Products.Client.products_ProductImportInfo;
using products_ProductImportInfoBuilder = CoverGo.Products.Client.products_ProductImportInfoBuilder;
using products_ProductPricingResult = CoverGo.Products.Client.products_ProductPricingResult;
using products_ProductPricingResultBuilder = CoverGo.Products.Client.products_ProductPricingResultBuilder;
using products_Result = CoverGo.Products.Client.products_Result;
using products_ResultBuilder = CoverGo.Products.Client.products_ResultBuilder;
using products_ResultOfCreatedStatusBuilder = CoverGo.Products.Client.products_ResultOfCreatedStatusBuilder;
using Result = CoverGo.DomainUtils.Result;

namespace CoverGo.Products.Tests.Integration.Utils;

public sealed class Products
{
    private readonly GraphQLHttpClient _graphQlClient;
    private readonly GraphQLHttpClient _gatewayGraphQlClient;
    private readonly ProductsClient _httpClient;
    private readonly string _tenantId;

    public Products(GraphQLHttpClient graphQlClient, GraphQLHttpClient gatewayGraphQlClient, ProductsClient httpClient, string tenantId)
    {
        _graphQlClient = graphQlClient;
        _gatewayGraphQlClient = gatewayGraphQlClient;
        _httpClient = httpClient;
        _tenantId = tenantId;
    }
    public Products(GraphQLHttpClient gatewayGraphQlClient, string tenantId)
    {
        _gatewayGraphQlClient = gatewayGraphQlClient;
        _tenantId = tenantId;
    }

    public async Task<ResultOfCreatedStatus> AddInternalReview(string productId, AddInternalReviewCommand command)
    {
        return await _httpClient.Products_AddInternalReviewAsync(_tenantId, productId, command);
    }
    public async Task<Result> UpdateInternalReview(string productId, UpdateInternalReviewCommand command)
    {
        return await _httpClient.Products_UpdateInternalReviewAsync(_tenantId, productId, command);
    }
    public async Task<Result> RemoveInternalReview(string productId, RemoveCommand command)
    {
        return await _httpClient.Products_RemoveInternalReviewAsync(_tenantId, productId, command);
    }

    public async Task AddScriptToProduct(AddScriptToProductCommand command)
    {
        var result = await _httpClient.Products_AddScriptToProductAsync(_tenantId, command);
        if (!result.IsSuccess) throw new InvalidOperationException(result.Errors?.First());
    }
    public async Task CreateProduct(CreateProductCommand command)
    {
        var result = await _httpClient.Products_CreateAsync(_tenantId, command);
        if (!result.IsSuccess) throw new InvalidOperationException(result.Errors?.First());
    }
    public async Task UpdateProduct(UpdateProductCommand command)
    {
        var result = await _httpClient.Products_UpdateAsync(_tenantId, command);
        if (!result.IsSuccess) throw new InvalidOperationException(result.Errors?.First());
    }

    public async Task<CreatedStatus> CreateConfig(CreateProductConfigCommand command)
    {
        ResultOfCreatedStatus result = await _httpClient.Products_CreateConfigAsync(_tenantId, command);
        if (!result.IsSuccess) throw new InvalidOperationException(result.Errors?.First());
        return result.Value;
    }

    public async Task DeleteConfig(string id)
    {
        var command = new DeleteCommand() { DeletedById = "unknown" };
        var result = await _httpClient.Products_DeleteConfigAsync(_tenantId, id, command);
        if (!result.IsSuccess) throw new InvalidOperationException(result.Errors?.First());
    }

    public async Task<string> CreateScript(CreateScriptCommand command)
    {
        var result = await _httpClient.Scripts_CreateAsync(_tenantId, command);
        if (!result.IsSuccess) throw new InvalidOperationException(result.Errors?.First());
        return result.Value.Id;
    }

    public async Task UpdateScript(UpdateScriptCommand command)
    {
        var result = await _httpClient.Scripts_UpdateAsync(_tenantId, command);
        if (!result.IsSuccess) throw new InvalidOperationException(result.Errors?.First());
    }

    public async Task DeleteProduct(DeleteProductCommand command)
    {
        await _httpClient.Products_DeleteAsync(_tenantId, command);
    }
    public async Task<Product> GetProductByProductId(ProductId productId)
    {
        ProductQuery q = new ProductQuery
        {
            Where = new ProductWhere
            {
                ProductId = new ProductIdWhere { Plan = productId.Plan, Type = productId.Type, Version = productId.Version }
            }
        };
        ICollection<Product> products = await _httpClient.Products_GetAllAsync(_tenantId, q, null);

        return products.OfType<Product>().FirstOrDefault();
    }

    public Task<products> FindById(productId id) => FindById(id, new productBuilder()
    .productId(new productIdBuilder()
        .type()
        .version()
        .plan())
    .representation()
    .fields());


    public async Task<List<Product>> GetProducts(ProductQuery query, string? clientId = null)
    {
        return await _httpClient.Products_GetAllAsync(_tenantId, query, clientId);
    }

    public async Task<long> GetProductsTotalCount(ProductWhere where, string? clientId = null)
    {
        return await _httpClient.Products_GetTotalCountAsync(_tenantId, where, clientId);
    }

    public async Task CreateBenefitDefinition(CreateBenefitDefinitionCommand command)
    {
        await _httpClient.BenefitDefinitions_CreateAsync(_tenantId, command);
    }
    public async Task<ICollection<BenefitDefinition>> GetBenefits(BenefitQueryArguments args)
    {
        return await _httpClient.BenefitDefinitions_GetAllAsync(_tenantId, args);
    }

    public async Task<ResultOfCreatedStatus> AddFact(string productId, AddFactCommand command)
    {
        return await _httpClient.Products_AddFactAsync(_tenantId, productId, command);
    }
    public async Task<Result> UpdateFact(string productId, UpdateFactCommand command)
    {
        return await _httpClient.Products_UpdateFactAsync(_tenantId, productId, command);
    }
    public async Task<Result> FactBatch(string productId, FactCommandBatch command)
    {
        return await _httpClient.Products_FactBatchAsync(_tenantId, productId, command);
    }

    public async Task<string> GetProductPricing(ProductId productId, string dataInput, string distributorID = null, string[] campaignCodes = null, DateTimeOffset? startDate = null)
    {
        string query = new _service_CoverGoQueriesRootBuilder().productPricing(
                new _service_CoverGoQueriesRootBuilder.productPricingArgs(
                    new products_ProductIdInput
                    {
                        plan = productId.Plan,
                        type = productId.Type,
                        version = productId.Version
                    }, dataInput, distributorID, campaignCodes, startDate?.DateTime), new products_ProductPricingResultBuilder().WithAllFields())
            .Build();

        return (await _graphQlClient.SendQueryAndEnsureAsync<products_ProductPricingResult>(query)).pricing;
    }

    public async Task<products_PricingResult> CalculatePricing(ProductId productId, string dataInput, List<products_ModePropertiesInput> modes = null, string distributorId = null, string[] campaignCodes = null, products_UwLoadingsInput uwLoadings = null, string[] agentIds = null)
    {
        string query = new _service_CoverGoQueriesRootBuilder().productCalculatePricing(
                new _service_CoverGoQueriesRootBuilder.productCalculatePricingArgs(
                    new products_CalculatePricingInput
                    {
                        productId = new products_ProductIdInput
                        {
                            plan = productId.Plan,
                            type = productId.Type,
                            version = productId.Version
                        },
                        dataInput = dataInput,
                        modes = modes,
                        distributorID = distributorId,
                        agentIds = agentIds,
                        campaignCodes = campaignCodes,
                        uwLoadings = uwLoadings,
                    }), new products_PricingResultBuilder().WithAllFields())
            .Build();

        return await _graphQlClient.SendQueryAndEnsureAsync<products_PricingResult>(query);
    }

    public async Task<products_UnderwritingResult> EvaluateUnderwriting(ProductId productId, string dataInput, List<products_InsuredUnderwritingInput> insureds, string metaInput)
    {
        string query = new _service_CoverGoQueriesRootBuilder().productEvaluateUnderwriting(
                new _service_CoverGoQueriesRootBuilder.productEvaluateUnderwritingArgs(
                    new products_EvaluateUnderwritingInput
                    {
                        productId = new products_ProductIdInput
                        {
                            plan = productId.Plan,
                            type = productId.Type,
                            version = productId.Version
                        },
                        dataInput = dataInput,
                        insuredUnderwritingInputs = insureds,
                        modes = new products_UnderwritingModesInput
                        {
                            benefitLevel = false
                        }
                    }), new products_UnderwritingResultBuilder().data(new products_UnderwritingDataBuilder().WithAllFields()).error(new products_ScriptEvaluationErrorBuilder().WithAllFields()))
            .Build();

        //hack as Any type serialization is not working well with generated client we use
        query = query.Replace("\"{{placeholder}}\"", metaInput);

        return await _graphQlClient.SendQueryAndEnsureAsync<products_UnderwritingResult>(query);
    }

    public async Task<products_ResultOfScriptTypeEnum> GetLatestPricingScriptType(products_ProductIdInput productId)
    {
        string query = new _service_CoverGoQueriesRootBuilder().productLatestPricingScriptType(
            new _service_CoverGoQueriesRootBuilder.productLatestPricingScriptTypeArgs(productId),
            new products_ResultOfScriptTypeEnumBuilder().WithAllFields()).Build();
        return await _graphQlClient.SendQueryAndEnsureAsync<products_ResultOfScriptTypeEnum>(query);
    }

    public async Task<products_FilePathResult> ExportProduct(ProductId productId)
    {
        string mutation = new _service_CoverGoMutationsRootBuilder().exportProduct(
            new _service_CoverGoMutationsRootBuilder.exportProductArgs(
                new products_ProductIdInput
                {
                    plan = productId.Plan,
                    type = productId.Type,
                    version = productId.Version
                }),
            new products_FilePathResultBuilder()
                .WithAllFields()
        ).Build();

        products_FilePathResult result = await _graphQlClient.SendMutationAndEnsureAsync<products_FilePathResult>(mutation);

        return result;
    }

    public async Task<products_Result> ImportProduct(string fromFilePath)
    {
        string mutation = new _service_CoverGoMutationsRootBuilder().importProduct(
            new _service_CoverGoMutationsRootBuilder.importProductArgs(fromFilePath),
            new products_ResultBuilder()
                .WithAllFields()
        ).Build();

        products_Result result = await _graphQlClient.SendMutationAndEnsureAsync<products_Result>(mutation);

        return result;
    }

    public async Task<products_ProductImportInfo> GetProductImportInfo(string filePath)
    {
        string query = new _service_CoverGoQueriesRootBuilder().productImportInfo(
            new _service_CoverGoQueriesRootBuilder.productImportInfoArgs(filePath),
            new products_ProductImportInfoBuilder()
                .WithAllFields()
        ).Build();

        products_ProductImportInfo result = await _graphQlClient.SendQueryAndEnsureAsync<products_ProductImportInfo>(query);

        return result;
    }

    public async Task<GraphQLResponse<JToken>> GetProductImportInfoRaw(string filePath)
    {
        string query = new _service_CoverGoQueriesRootBuilder().productImportInfo(
            new _service_CoverGoQueriesRootBuilder.productImportInfoArgs(filePath),
            new products_ProductImportInfoBuilder()
                .WithAllFields()
        ).Build();

        GraphQLResponse<JToken> response = await _graphQlClient.SendQueryAsync<JToken>(new GraphQLHttpRequest(query));

        return response;
    }

    public async Task<ICollection<products_ProductImportHistoryRecord>> GetProductImportHistory(products_GenericProductImportHistoryRecordQueryInput where)
    {
        string query = new _service_CoverGoQueriesRootBuilder().productImportHistoryQuery(
            new _service_CoverGoQueriesRootBuilder.productImportHistoryQueryArgs(where),
            new products_GenericProductImportHistoryRecord8QueryInterfaceBuilder()
                .totalCount()
                .list(new products_ProductImportHistoryRecordBuilder()
                    .WithAllFields())
        ).Build();

        products_GenericProductImportHistoryRecord8QueryInterface result = await _graphQlClient.SendQueryAndEnsureAsync<products_GenericProductImportHistoryRecord8QueryInterface>(query);

        return result.list;
    }

    public async Task<Result> CreateDiscountCode(products_DiscountCodeUpsertInput create)
    {
        string mutation = new _service_CoverGoMutationsRootBuilder()
            .discountCodesMutationCreate(new _service_CoverGoMutationsRootBuilder.discountCodesMutationCreateArgs(create: create), new products_ResultOfCreatedStatusBuilder()
                .status()
                .errors()
                .value(new products_CreatedStatusBuilder()
                    .id()))
            .Build();

        Result result = await _graphQlClient.SendMutationAndEnsureAsync<Result>(mutation);

        return result;

    }

    public async Task<products_GenericDiscountCode8QueryInterface> GetDiscountCodes(string id)
    {
        //products_GenericDiscountCode8QueryInterface
        string query = new _service_CoverGoQueriesRootBuilder()
            .discountCodesQuery(new _service_CoverGoQueriesRootBuilder.discountCodesQueryArgs(@where: new products_GenericDiscountCodeQueryInput { @where = new products_GenericDiscountCodeFilterInput { @where = new products_DiscountCodeFilterInput { id = id } } }), new products_GenericDiscountCode8QueryInterfaceBuilder()
                .totalCount()
                .list(new products_DiscountCodeBuilder()
                    .id()
                    .name()
                    .type()
                    .description()
                    .value()
                    .validFrom()
                    .validTo()
                    .productTypeId()
                    .productIds(new products_ProductIdBuilder()
                        .plan()
                        .type()
                        .version())))
            .Build();

        products_GenericDiscountCode8QueryInterface result = await _graphQlClient.SendQueryAndEnsureAsync<products_GenericDiscountCode8QueryInterface>(query);

        return result;
    }

    public async Task<products_GenericDiscountCode8QueryInterface> DiscountCodesGet(products_DiscountCodeFilterInput filterInput)
    {
        string query = new _service_CoverGoQueriesRootBuilder()
            .discountCodesQuery(new _service_CoverGoQueriesRootBuilder.discountCodesQueryArgs(@where: new products_GenericDiscountCodeQueryInput { @where = new products_GenericDiscountCodeFilterInput { @where = filterInput } }), new products_GenericDiscountCode8QueryInterfaceBuilder()
                .totalCount()
                .list(new products_DiscountCodeBuilder()
                    .id()
                    .name()
                    .type()
                    .description()
                    .value()
                    .validFrom()
                    .validTo()
                    .productTypeId()
                    .productIds(new products_ProductIdBuilder()
                        .plan()
                        .type()
                        .version())))
            .Build();

        return await _graphQlClient.SendQueryAndEnsureAsync<products_GenericDiscountCode8QueryInterface>(query);
    }

    public async Task<products_GenericDiscountCode8QueryInterface> DiscountCodesGet(string id)
    {
        string query = new _service_CoverGoQueriesRootBuilder()
            .discountCodesQuery(new _service_CoverGoQueriesRootBuilder.discountCodesQueryArgs(@where: new products_GenericDiscountCodeQueryInput { @where = new products_GenericDiscountCodeFilterInput { @where = new products_DiscountCodeFilterInput { id = id } } }), new products_GenericDiscountCode8QueryInterfaceBuilder()
                .totalCount()
                .list(new products_DiscountCodeBuilder()
                    .id()
                    .name()
                    .type()
                    .description()
                    .value()
                    .validFrom()
                    .validTo()
                    .productTypeId()
                    .productIds(new products_ProductIdBuilder()
                        .plan()
                        .type()
                        .version())))
            .Build();

        return await _graphQlClient.SendQueryAndEnsureAsync<products_GenericDiscountCode8QueryInterface>(query);
    }

    public async Task<Result> DiscountCodesBatch(products_GenericDiscountCode3BatchInput batch)
    {
        string mutation = new _service_CoverGoMutationsRootBuilder()
            .discountCodesMutationBatch(new _service_CoverGoMutationsRootBuilder.discountCodesMutationBatchArgs(batch: batch), new products_ResultBuilder()
                .status()
                .errors())
            .Build();

        return await _graphQlClient.SendMutationAndEnsureAsync<Result>(mutation);
    }

    public async Task<Result> AddEligibleProductsToDiscountCode(products_DiscountCodeProductsUpsertInput input)
    {
        string mutation = new _service_CoverGoMutationsRootBuilder()
            .discountCodesMutationAddEligibleProduct(new _service_CoverGoMutationsRootBuilder.discountCodesMutationAddEligibleProductArgs(input: input), new products_ResultBuilder()
                .status()
                .errors())
            .Build();

        var result = await _graphQlClient.SendMutationAndEnsureAsync<Result>(mutation);
        return result;
    }



    public Task<productId> Create() =>
            Create(new createProductInput
            {
                productId = new productIdInput
                {
                    plan = CreateNewGuid(),
                    type = CreateNewGuid(),
                    version = CreateNewGuid()
                }
            });

    public productIdInput CreateProductId() => new()
    {
        plan = CreateNewGuid(),
        version = CreateNewGuid(),
        type = CreateNewGuid()
    };

    public async Task<productId> Create(createProductInput input)
    {
        string mutation = new covergoMutationBuilder()
            .createProduct(new covergoMutationBuilder.createProductArgs(input), new productBuilder()
                .productId(new productIdBuilder()
                    .WithAllFields()))
            .Build();

        product response = await _gatewayGraphQlClient.SendMutationAndEnsureAsync<product>(mutation);
        return response.productId!;
    }

    public async Task<productId> SetTermsAndConditions(products_SetTermsAndConditionsInput input)
    {
        string mutation = new _service_CoverGoMutationsRootBuilder()
        .productMutationSetTermsAndConditions(new _service_CoverGoMutationsRootBuilder.productMutationSetTermsAndConditionsArgs(input: input), new products_ResultBuilder()
            .status()
            .errors())
        .Build();

        product response = await _graphQlClient.SendMutationAndEnsureAsync<product>(mutation);
        return response.productId!;
    }

    public async Task<productId> RemoveTermsAndConditions(products_RemoveTermsAndConditionsInput input)
    {
        string mutation = new _service_CoverGoMutationsRootBuilder()
        .productMutationRemoveTermsAndConditions(new _service_CoverGoMutationsRootBuilder.productMutationRemoveTermsAndConditionsArgs(input: input), new products_ResultBuilder()
            .status()
            .errors())
        .Build();

        product response = await _graphQlClient.SendMutationAndEnsureAsync<product>(mutation);
        return response.productId!;
    }

    public async Task<productId> SetRatingFactorsTable(products_SetRatingFactorsTableInput input)
    {
        string mutation = new _service_CoverGoMutationsRootBuilder()
        .productMutationSetRatingFactorsTable(new _service_CoverGoMutationsRootBuilder.productMutationSetRatingFactorsTableArgs(input: input), new products_ResultBuilder()
            .status()
            .errors())
        .Build();

        product response = await _graphQlClient.SendMutationAndEnsureAsync<product>(mutation);
        return response.productId!;
    }

    public async Task<productId> RemoveRatingFactorsTable(products_RemoveRatingFactorsTableInput input)
    {
        string mutation = new _service_CoverGoMutationsRootBuilder()
         .productMutationRemoveRatingFactorsTable(new _service_CoverGoMutationsRootBuilder.productMutationRemoveRatingFactorsTableArgs(input: input), new products_ResultBuilder()
            .status()
            .errors())
        .Build();


        product response = await _graphQlClient.SendMutationAndEnsureAsync<product>(mutation);
        return response.productId!;
    }

    public async Task<productId> SetSegments(products_SetSegmentsInput input)
    {
        string mutation = new _service_CoverGoMutationsRootBuilder()
          .productMutationSetSegments(new _service_CoverGoMutationsRootBuilder.productMutationSetSegmentsArgs(input: input), new products_ResultBuilder()
            .status()
            .errors())
        .Build();

        product response = await _graphQlClient.SendMutationAndEnsureAsync<product>(mutation);
        return response.productId!;
    }

    public async Task<benefitInput> AddBenefit(productId productId)
    {
        benefitInput input = new()
        {
            typeId = CreateNewGuid(),
            optionKey = CreateNewGuid(),
            value = new scalarValueInput { stringValue = "test value" },
        };

        await AddBenefitToProduct(productId, input);

        return input;
    }

    Task AddBenefitToProduct(productId productId, benefitInput benefitInput)
    {
        string mutation = new covergoMutationBuilder()
            .addBenefit(new covergoMutationBuilder.addBenefitArgs(ProductIdToInput(productId), benefitInput), new resultBuilder()
                .WithAllFields())
        .Build();

        return _gatewayGraphQlClient.SendMutationAndEnsureAsync<Result>(mutation);
    }

    public productIdInput ProductIdToInput(productId productId) =>
            new()
            {
                plan = productId.plan,
                type = productId.type,
                version = productId.version
            };

    private static string CreateNewGuid() =>
            Guid.NewGuid().ToString();

    public async Task<result> CloneProduct(productId productId, productId cloneProductId, upsertL10nCloneProductNameInput? name = null)
    {
        cloneProductInput cloneProductInput = new()
        {
            cloneProductId = new productIdInput
            {
                plan = cloneProductId.plan,
                type = cloneProductId.type,
                version = cloneProductId.version
            }
        };

        string mutation = new covergoMutationBuilder()
            .cloneProduct(new covergoMutationBuilder.cloneProductArgs(ProductIdToInput(productId), cloneProductInput, name), new resultBuilder()
            .WithAllFields())
            .Build();

        return await _gatewayGraphQlClient.SendMutationAndEnsureAsync<result>(mutation);

    }


    public Task<product> Update(productIdInput productId, updateProductInput update)
    {
        string? mutation = new covergoMutationBuilder().updateProduct(new covergoMutationBuilder.updateProductArgs(productId, update), new productBuilder()
            .productId(new productIdBuilder()
                .plan()
                .version()
                .type())
            .representation())
        .Build();

        return _gatewayGraphQlClient.SendMutationAndEnsureAsync<product>(mutation);
    }

    public async Task<(string, addFactInput)> AddFact(productId productId)
    {
        addFactInput addFactInput = new()
        {
            type = "test type",
            value = new scalarValueInput { stringValue = "test value" }
        };

        return (await AddFactToProduct(productId, addFactInput), addFactInput);
    }

    Task<string> AddFactToProduct(productId productId, addFactInput addFactInput)
    {
        string mutation = new covergoMutationBuilder()
            .addFactToProduct(new covergoMutationBuilder.addFactToProductArgs(ProductIdToInput(productId), addFactInput), new createdStatusResultBuilder()
                .WithAllFields())
            .Build();

        return _gatewayGraphQlClient.CreateAndReturnId(mutation);
    }


    public Task<string> AddInternalReview(productId productId)
    {
        internalReviewInput addInternalReviewCommand = new()
        {
            status = CreateNewGuid(),
            comment = CreateNewGuid()
        };

        return AddInternalReviewToProduct(productId, addInternalReviewCommand);
    }


    Task<string> AddInternalReviewToProduct(productId productId, internalReviewInput input)
    {
        string mutation = new covergoMutationBuilder()
            .addInternalReviewToProduct(new covergoMutationBuilder.addInternalReviewToProductArgs(ProductIdToInput(productId), input), new createdStatusResultBuilder()
                .WithAllFields())
            .Build();

        return _gatewayGraphQlClient.CreateAndReturnId(mutation);
    }

    public async Task<IEnumerable<product>> QueryProductsAsync(productWhereInput? where = null)
    {
        string query = new covergoQueryBuilder()
            .products_2(new covergoQueryBuilder.products_2Args(where: where), new productsBuilder()
                .list(new productBuilder()
                    .productId(new productIdBuilder()
                        .WithAllFields())))
            .Build();

        products result = await _gatewayGraphQlClient.SendQueryAndEnsureAsync<products>(query);
        return result.list!;
    }

    public async Task<product> SearchProductById(productId id, GraphQLHttpClient client)
    {
        productBuilder fields = new productBuilder()
            .facts(new factBuilder()
                .id()
                .type()
                .value(new scalarValueBuilder()
                    .stringValue()))
            .name()
            .events(new detailedEventLogBuilder()
                .type()
                .valueAsString())
            .productTreeId();

        return (await FindById(id, fields)).list!.First()!;
    }

    public async Task<products_ProductPlanDetails> GetProductPlanDetails(products_ProductIdInput productId)
    {
        string query = new _service_CoverGoQueriesRootBuilder().productProductPlanDetails(new _service_CoverGoQueriesRootBuilder.productProductPlanDetailsArgs(new products_ProductPlanDetailFactorsInput
        {
            productId = productId
        }), new products_ProductPlanDetailsBuilder().WithAllFields()).Build();

        return await _graphQlClient.SendQueryAndEnsureAsync<products_ProductPlanDetails>(query);
    }

    public async Task<List<products_Network>> GetActiveNetworks()
    {
        string query = new _service_CoverGoQueriesRootBuilder().productActiveNetworks(new(), new products_NetworkBuilder().WithAllFields()).Build();

        return await _graphQlClient.SendQueryAndEnsureAsync<List<products_Network>>(query);
    }

    #region Benefits

    public Task ProductBenefitBatch(productId productId, benefitBatchInput benefitBatchInput)
    {
        string mutation = new covergoMutationBuilder()
            .productBenefitBatch(new covergoMutationBuilder.productBenefitBatchArgs(ProductIdToInput(productId), benefitBatchInput), new resultBuilder()
                .WithAllFields())
            .Build();
        return _gatewayGraphQlClient.SendMutationAndEnsureAsync<Result>(mutation);
    }

    public async Task<product> SearchProductById(productId id)
    {
        productBuilder fields = new productBuilder()
            .benefitGraph(new benefitGraphBuilder()
                .typeId()
                .optionKey()
                .value()
                .value2(new scalarValueBuilder()
                    .stringValue()));

        return (await FindById(id, fields)).list!.First()!;
    }

    public Task AddScriptToProduct(productIdInput productId, string scriptId)
    {
        string mutation = new covergoMutationBuilder()
            .addScriptToProduct(new covergoMutationBuilder.addScriptToProductArgs(new addScriptToProductInput { productId = productId, scriptId = scriptId }), new resultBuilder()
                .WithAllFields())
            .Build();

        return _gatewayGraphQlClient.SendMutationAndEnsureAsync<Result>(mutation);
    }

    public Task RemoveScriptFromProduct(productIdInput productId, string scriptId)
    {
        string mutation = new covergoMutationBuilder()
            .removeScriptFromProduct(new covergoMutationBuilder.removeScriptFromProductArgs(new removeScriptFromProductInput { productId = productId, scriptId = scriptId }), new resultBuilder()
                .WithAllFields())
            .Build();

        return _gatewayGraphQlClient.SendMutationAndEnsureAsync<Result>(mutation);
    }

    public async Task<products> FindById(productId id, productBuilder fields)
    {
        string query = new covergoQueryBuilder()
            .products_2(new covergoQueryBuilder.products_2Args(where: new productWhereInput
            {
                productId = new productIdWhereInput
                {
                    plan = id.plan,
                    type = id.type,
                    version = id.version
                }
            }), new productsBuilder()
                .list(fields))
            .Build();

        return await _gatewayGraphQlClient.SendQueryAndEnsureAsync<products>(query);
    }



    public string ProductIdToString(productId productId) =>
            $"{productId.plan}|{productId.version}|{productId.type}";

    #endregion
}
