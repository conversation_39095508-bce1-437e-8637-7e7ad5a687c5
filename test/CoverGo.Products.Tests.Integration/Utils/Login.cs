﻿using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration.Utils
{
    public class Login
    {
        private readonly GraphQLHttpClient _gatewayGraphQlClient;
        public Login(GraphQLHttpClient gatewayGraphQlClient)
        {
            _gatewayGraphQlClient = gatewayGraphQlClient;
        }

        public Task<string> Create(createLoginInput input)
        {
            string mutation = new covergoMutationBuilder()
                .createLogin(new covergoMutationBuilder.createLoginArgs(input), new createdStatusResultBuilder()
            .WithAllFields())
            .Build();

            return _gatewayGraphQlClient.CreateAndReturnId(mutation);
        }

        public Task<string> Create(string name, string clientId)
        {
            createLoginInput input = new()
            {
                username = name,
                isEmailConfirmed = true,
                clientId = clientId,
                password = CreateNewGuid(),
                email = $"{name}@covergo.com"
            };

            return Create(input);
        }

        public Task<string> Create() =>
            Create(CreateNewGuid());

        public Task<string> Create(string name) =>
                    Create(name, CreateNewGuid());

        public Task AddToGroup(string loginId, string groupId)
        {
            string mutation = new covergoMutationBuilder()
                .addToPermissionGroup(new covergoMutationBuilder.addToPermissionGroupArgs(loginId, groupId), new resultBuilder()
                    .WithAllFields())
            .Build();

            return _gatewayGraphQlClient.SendMutationAndEnsureAsync<Result>(mutation);
        }

        public Task AddPermission(string loginId, string permission, string value)
        {
            string mutation = new covergoMutationBuilder()
                .addTargettedPermission(new covergoMutationBuilder.addTargettedPermissionArgs(loginId, new addTargettedPermissionInput { type = permission, value = value }), new resultBuilder()
                    .WithAllFields())
            .Build();

            return _gatewayGraphQlClient.SendMutationAndEnsureAsync<Result>(mutation);
        }

        public Task AddPermissions(string loginId, List<(string permission, string value)> permissions)
        {
            addTargettedPermissionInput[] inputs =
                permissions.Select(p => new addTargettedPermissionInput { type = p.permission, value = p.value }).ToArray();

            string mutation = new covergoMutationBuilder()
                .addTargettedPermissions(new covergoMutationBuilder.addTargettedPermissionsArgs(loginId, inputs), new resultBuilder()
                    .WithAllFields())
            .Build();

            return _gatewayGraphQlClient.SendMutationAndEnsureAsync<Result>(mutation);
        }

        public Task RemovePermissions(string loginId, List<(string permission, string value)> permissions)
        {
            removeTargettedPermissionInput[] inputs =
                permissions.Select(p => new removeTargettedPermissionInput() { type = p.permission, value = p.value }).ToArray();

            string mutation = new covergoMutationBuilder()
                .removeTargettedPermissions(new covergoMutationBuilder.removeTargettedPermissionsArgs(loginId, inputs), new resultBuilder()
                    .WithAllFields())
            .Build();

            return _gatewayGraphQlClient.SendMutationAndEnsureAsync<Result>(mutation);
        }

        public Task AddPermissionToGroup(string groupId, string permission, string value)
        {
            string mutation = new covergoMutationBuilder()
                .addPermissionToPermissionGroup(new covergoMutationBuilder.addPermissionToPermissionGroupArgs(groupId, permission, value), new resultBuilder()
                    .WithAllFields())
            .Build();

            return _gatewayGraphQlClient.SendMutationAndEnsureAsync<Result>(mutation);
        }

        public Task RemovePermissionToGroup(string groupId, string permission, string value)
        {
            string mutation = new covergoMutationBuilder()
                .removePermissionFromPermissionGroup(new covergoMutationBuilder.removePermissionFromPermissionGroupArgs(groupId, permission, value), new resultBuilder()
                    .WithAllFields())
            .Build();

            return _gatewayGraphQlClient.SendMutationAndEnsureAsync<Result>(mutation);
        }

        public Task RemovePermission(string loginId, string permission, string value)
        {
            string mutation = new covergoMutationBuilder()
                .removeTargettedPermission(new covergoMutationBuilder.removeTargettedPermissionArgs(loginId, permission, value), new resultBuilder()
                    .WithAllFields())
            .Build();

            return _gatewayGraphQlClient.SendMutationAndEnsureAsync<Result>(mutation);
        }

        public Task AddPermissionSchema(string loginId, string permissionSchemaId, ICollection<string> targetIds)
        {
            string mutation = new covergoMutationBuilder()
                .addTargetedPermissionSchemaToLogin(new covergoMutationBuilder.addTargetedPermissionSchemaToLoginArgs(new addTargetedPermissionSchemaToLoginInput { loginId = loginId, permissionSchemaId = permissionSchemaId, targetIds = targetIds }), new resultBuilder()
                    .WithAllFields())
            .Build();

            return _gatewayGraphQlClient.SendMutationAndEnsureAsync<Result>(mutation);
        }

        public async Task InviteEntityToLogin(string clientId, inviteEntityInput input)
        {
            string mutation = new covergoMutationBuilder()
                .inviteEntityToLogin(
                    new covergoMutationBuilder.inviteEntityToLoginArgs(clientId, input),
                    new createdStatusResultBuilder().WithAllFields())
            .Build();

            await _gatewayGraphQlClient.SendQueryAndEnsureAsync<createdStatusResult>(mutation);
        }

        public async Task<login> GetLogin(string userName)
        {
            string query = new covergoQueryBuilder()
                .login(new covergoQueryBuilder.loginArgs(userName),
                    new loginBuilder()
                        .id()
                        .associatedUser(new entityInterfaceBuilder()
                            .id())
                        .permissionGroups(new permissionGroupBuilder().id())
                        .targettedPermissions(new targettedPermissionBuilder().WithAllFields()))
                .Build();

            return await _gatewayGraphQlClient.SendQueryAndEnsureAsync<login>(query);
        }

        public async Task<(string loginId, createLoginInput input)> CreateValidLoginAndReturnIdAndInput(string? entityId = null)
        {
            string name = CreateNewGuid();
            createLoginInput input = new()
            {
                username = name,
                isEmailConfirmed = true,
                clientId = "admin",
                password = CreateNewGuid(),
                email = $"{name}@covergo.com",
                entityId = entityId
            };

            return (await Create(input), input);
        }

        public Task<login> Me()
        {
            var query = new covergoQueryBuilder()
                .me(new loginBuilder()
                    .id()
                    .targettedPermissions(new targettedPermissionBuilder()
                        .WithAllFields()))
                .Build();
            return _gatewayGraphQlClient.SendQueryAndEnsureAsync<login>(query);
        }

        protected static string CreateNewGuid() =>
    Guid.NewGuid().ToString();
    }

}
