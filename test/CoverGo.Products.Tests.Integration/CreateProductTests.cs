#nullable enable

using System.Threading.Tasks;
using CoverGo.Products.Tests.Integration.Support;

namespace CoverGo.Products.Tests.Integration;

[Collection("Sequential")]
[Trait("Category", "Integration")]
public class CreateProductTests(ProductsWebApplicationFactory webApplicationFactory) : ApiTestBase(webApplicationFactory), IClassFixture<ProductsWebApplicationFactory>
{
    [Theory]
    [InlineData(null, null, false, false)]
    [InlineData(false, false, false, false)]
    [InlineData(true, true, true, true)]
    [Trait("Ticket", "CH-22964")]
    public async Task When_CreateProduct_Then_ProductIsCreated_Correctly(bool? autoRenewal, bool? renewalNotification, bool pAutoRenewal, bool pRenewalNotification)
    {
        // Arrange (Given)
        Client.ProductId productId = new() { Plan = CreateNewGuid(), Type = CreateNewGuid(), Version = CreateNewGuid() };
        Client.CreateProductCommand command = new() { ProductId = productId, AutoRenewal = autoRenewal, RenewalNotification = renewalNotification };

        // Act (When)
        await Products.CreateProduct(command);

        // Assert (Then)
        var createdProduct = await Products.GetProductByProductId(productId);
        createdProduct.Should().NotBeNull();
        createdProduct.AutoRenewal.Should().Be(pAutoRenewal);
        createdProduct.RenewalNotification.Should().Be(pRenewalNotification);
    }

    [Theory]
    [InlineData(false, "", "", false, false)]
    [InlineData(true, "", "", false, false)]
    [InlineData(true, "X-FIN-TAXRATE", "", false, false)]
    [InlineData(true, "X-FIN-CUSTOMTAX", "VAT", true, true)]
    [Trait("Ticket", "CH-19045")]
    public async Task When_CreateProductWithTaxConfiguration_Then_ProductIsCreated_Correctly(bool hasTaxConfig, string referenceMnemonic, string mnemonicCode, bool allowOverrideAtOfferStage, bool allowOverrideAtProposalStage)
    {
        // Arrange (Given)
        Client.TaxConfiguration taxConfiguration = hasTaxConfig ? new() { ReferenceMnemonic = referenceMnemonic, MnemonicCode = mnemonicCode, AllowOverrideAtOfferStage = allowOverrideAtOfferStage, AllowOverrideAtProposalStage = allowOverrideAtProposalStage } : null;
        Client.ProductId productId = new() { Plan = CreateNewGuid(), Type = CreateNewGuid(), Version = CreateNewGuid() };

        // Act (When)
        await Products.CreateProduct(new() { ProductId = productId, TaxConfiguration = taxConfiguration });

        // Assert (Then)
        var createdProduct = await Products.GetProductByProductId(productId);
        createdProduct.Should().NotBeNull();
        createdProduct.TaxConfiguration.Should().BeEquivalentTo(new { ReferenceMnemonic = referenceMnemonic, MnemonicCode = mnemonicCode, AllowOverrideAtOfferStage = allowOverrideAtOfferStage, AllowOverrideAtProposalStage = allowOverrideAtProposalStage });
    }
}
