using CoverGo.BuildingBlocks.Auth.SSO;

using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace CoverGo.Products.Tests.Integration.Support;

public class ProductsWebApplicationFactory : WebApplicationFactory<Program>
{
    public ProductsWebApplicationFactory()
    {
        Program.UseBootstrapLogger = false;
    }

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureTestServices(services =>
        {
            services.Configure<JwtBearerOptions>(JwtBearerDefaults.AuthenticationScheme, it =>
            {
                it.TokenValidationParameters.ValidateIssuer = false;
                it.ForwardDefaultSelector = null;
            });
            services.RemoveAll<SSOJwtBearerEvents>();
        });

        builder.UseEnvironment(TestEnvironment.GetEnvironment());
    }
}
