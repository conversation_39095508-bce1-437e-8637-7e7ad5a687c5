using System;
using System.Net.Http;
using System.Net.Http.Headers;

using CoverGo.Products.Client;
using CoverGo.Products.Tests.GatewayClient;
using CoverGo.Products.Tests.Integration.ProductsStrawberryClient;
using CoverGo.Products.Tests.Integration.Utils;

using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace CoverGo.Products.Tests.Integration.Support;

public abstract class ApiTestBase : TestBase
{
    protected WebApplicationFactory<Program> _applicationFactory;
    private readonly Lazy<IConfiguration> _configuration;
    private readonly Lazy<ITestProductsClient> _productsClient;
    private readonly Lazy<ITestGatewayClient> _gatewayClient;
    private readonly Lazy<HttpClient> _productsHttpClient;
    private readonly Lazy<ProductsClient> _productsRestClient;

    private IConfiguration Configuration => _configuration.Value;
    protected ITestGatewayClient GatewayClient => _gatewayClient.Value;
    protected ITestProductsClient ProductsClient => _productsClient.Value;
    protected HttpClient ProductsHttpClient => _productsHttpClient.Value;
    protected ProductsClient ProductsRestClient => _productsRestClient.Value;

    protected ApiTestBase(ProductsWebApplicationFactory applicationFactory)
    {
        _applicationFactory = applicationFactory;
        _configuration = new(CreateConfiguration);
        _gatewayClient = new(CreateTestGatewayClient);
        _productsClient = new(GetTestProductsClient);
        _productsHttpClient = new(CreateProductsHttpClient);
        _productsRestClient = new(() => new ProductsClient(ProductsHttpClient));
    }

    protected ApiTestBase() : this(new ProductsWebApplicationFactory())
    {
    }

    private ITestGatewayClient CreateTestGatewayClient()
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton(Configuration);
        serviceCollection.AddTestGatewayClient()
            .ConfigureHttpClient((sp, client) =>
            {
                client.BaseAddress = GetGatewayUri(sp.GetRequiredService<IConfiguration>());

                var authToken = Setup.GetAccessToken().GetAwaiter().GetResult();
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", authToken);
            });

        var serviceProvider = serviceCollection.BuildServiceProvider();
        return serviceProvider.GetRequiredService<ITestGatewayClient>();
    }

    private ITestProductsClient GetTestProductsClient()
    {
        var serviceCollection = new ServiceCollection();
        var httpClientFactory = new TestHttpClientFactory
        {
            Clients =
            {
                ["TestProductsClient"] = () =>
                {
                    HttpClient client = _applicationFactory.CreateClient(new WebApplicationFactoryClientOptions { BaseAddress = GetProductsUri(Configuration) });
                    var authToken = Setup.GetAccessToken().GetAwaiter().GetResult();
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", authToken);
                    return client;
                }
            }
        };

        serviceCollection.AddSingleton<IHttpClientFactory>(httpClientFactory);
        serviceCollection.AddTestProductsClient();
        var serviceProvider = serviceCollection.BuildServiceProvider();
        return serviceProvider.GetRequiredService<ITestProductsClient>();
    }

    private IConfiguration CreateConfiguration()
    {
        IConfiguration configuration = TestConfigFactory.Create();
        return configuration;
    }

    private static Uri GetGatewayUri(IConfiguration configuration) => GetServiceUri(configuration, "gateway");

    private static Uri GetProductsUri(IConfiguration configuration) => GetServiceUri(configuration, "products");

    private static Uri GetServiceUri(IConfiguration configuration, string serviceName) => new UriBuilder(configuration.GetConnectionString(serviceName)) { Path = "graphql" }.Uri;

    private HttpClient CreateProductsHttpClient() => _applicationFactory.CreateClient();

    protected void ConfigureApiServices(Action<IServiceCollection> servicesConfiguration) =>
        _applicationFactory = _applicationFactory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureTestServices(servicesConfiguration);
        });
}
