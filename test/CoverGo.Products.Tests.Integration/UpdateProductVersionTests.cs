#nullable enable

using System;
using System.IO;
using System.Threading.Tasks;

using CoverGo.Products.Client;
using CoverGo.Products.Tests.Integration.Support;

using FluentAssertions.Execution;

using ProductId = CoverGo.Products.Domain.Products.ProductId;

namespace CoverGo.Products.Tests.Integration;

[Trait("Ticket", "CH-17302")]
[Trait("Ticket", "CH-2216")]
[Collection("Sequential")]
public class UpdateProductVersionTests : ApiTestBase, IClassFixture<ProductsWebApplicationFactory>
{
    public UpdateProductVersionTests(ProductsWebApplicationFactory webApplicationFactory) : base(webApplicationFactory)
    {
    }

    [Fact]
    public async Task UpdateProductVersionOfferValidityPeriod()
    {
        // Arrange (Given)
        var createProductResult = await GatewayClient.CreateProduct.ExecuteAsync(new()
        {
            ProductId = new()
            {
                Plan = CreateNewGuid(),
                Type = CreateNewGuid(),
                Version = CreateNewGuid()
            },
            Representation = File.OpenText("ProductPlanDetailsTests_MultiplePlansRepresentation.json").ReadToEnd(),
        });
        createProductResult.Errors.Should().BeEmpty();
        var productId = createProductResult.Data!.CreateProduct!.ProductId;

        // Act (When)
        var result = await ProductsClient.UpdateProductVersion.ExecuteAsync(
            new ProductsStrawberryClient.products_UpdateProductVersionInput()
            {
                ProductId = new() { Plan = productId.Plan, Type = productId.Type, Version = productId.Version },
                OfferValidityPeriod = new() { Value = TimeSpan.FromSeconds(30) }
            });
        result.Errors.Should().BeEmpty();

        // Assert (Then)
        var productResult = await ProductsClient.GetProduct.ExecuteAsync(productId.Plan, productId.Type, productId.Version);
        productResult.Errors.Should().BeEmpty();
        productResult.Data!.Product.OfferValidityPeriod.Should().Be(TimeSpan.FromSeconds(30));
    }
}
