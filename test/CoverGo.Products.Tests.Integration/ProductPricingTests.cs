﻿using CoverGo.ChannelManagement.Client;
using CoverGo.Products.Client;
using Newtonsoft.Json.Linq;
using StrawberryShake;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration;

[Collection("Sequential")]
public sealed class ProductPricingTests : TestBase
{
    [Fact]
    public async Task GIVEN_product_WHEN_request_price_THEN_returns_calculated_product_price()
    {
        ProductId productId = await CreateProductWithPricingScript();

        const string dataInput = """{\"policy\":{\"startDate\":\"2022-05-01\",\"endDate\":\"2023-04-30\"},\"insuredGroups\":[{\"numberOfInsureds\":1,\"planSelected\":\"PLAN_20220425\",\"key\":1651423061618}],\"insureds\":[{\"planId\":\"PLAN_20220425\",\"startDate\":\"2022-05-01\",\"endDate\":\"2023-04-30\",\"isQuoting\":true}],\"segments\":[]}""";
        string result = await Products.GetProductPricing(productId, dataInput);

        var jObject = JObject.Parse(result);
        jObject.Value<bool>("isSuccess").Should().Be(true);
        jObject.SelectToken("data.totalPremium.amount")!.Value<decimal>().Should().Be(2502.5M);
    }
    [Fact]
    public async Task GIVEN_product_WHEN_request_price_THEN_returns_calculated_product_price2()
    {
        ProductId productId = await CreateProductWithPricingScript();
        var (salesChannelID, distributorID, commissionID, productAvailabilityID) = await this.SetupCommission(productId);

        const string dataInput = """{\"policy\":{\"startDate\":\"2022-05-01\",\"endDate\":\"2023-04-30\"},\"insuredGroups\":[{\"numberOfInsureds\":1,\"planSelected\":\"PLAN_20220425\",\"key\":1651423061618}],\"insureds\":[{\"planId\":\"PLAN_20220425\",\"startDate\":\"2022-05-01\",\"endDate\":\"2023-04-30\",\"isQuoting\":true}],\"segments\":[]}""";
        string result = await Products.GetProductPricing(productId, dataInput, distributorID);

        var jObject = JObject.Parse(result);
        jObject.Value<bool>("isSuccess").Should().Be(true);
        jObject.SelectToken("data.totalPremium.amount")!.Value<decimal>().Should().Be(2502.5M);
    }
    [Fact]
    public async Task GIVEN_product_WHEN_request_price_THEN_should_send_VAT_config_from_Reference()
    {
        ProductId productId = await CreateProductWithPricingScript("function execute(input) { return { result:{ isSuccess: false, data: { vat:input.vat } }, shouldSerialize: true } }");

        const string dataInput = """{\"policy\":{\"startDate\":\"2022-05-01\",\"endDate\":\"2023-04-30\"},\"insuredGroups\":[{\"numberOfInsureds\":1,\"planSelected\":\"PLAN_20220425\",\"key\":1651423061618}],\"insureds\":[{\"planId\":\"PLAN_20220425\",\"startDate\":\"2022-05-01\",\"endDate\":\"2023-04-30\",\"isQuoting\":true}],\"segments\":[]}""";
        string result = await Products.GetProductPricing(productId, dataInput);
        var jObject = JObject.Parse(result);
        jObject["data"]!["vat"]!.Value<string>().Should().Be("""[{"id":"group_tax","name":"Group tax","type":"factor","value": 0}]""");
    }

    private async Task<ProductId> CreateProductWithPricingScript(string? pricingScript = null)
    {
        ProductId productId = GenerateProductId();

        await Products.CreateProduct(new()
        {
            ProductId = productId,
            Representation = """[{"id":"030067989066408396","props":{"theme":"#e4b917"},"productType":"gm","minified":true,"ch":[{"id":"27852147936577465","readonly":false,"ch":[{"id":"2983000711101427","ch":[],"n":"unitPrice","p":[{"n":"amount","l":"Amount","c":"Number","d":"number","v":1000},{"n":"currency","l":"Currency","c":"Select","d":"string","v":"HKD"},{"n":"unit","l":"Unit","c":"Select","d":"string","v":"perPolicy"}]},{"id":"4678364068681702","n":"unitPrice","p":[{"n":"amount","l":"Amount","c":"Number","d":"number","v":1500},{"n":"currency","l":"Currency","c":"Select","d":"string","v":"HKD"},{"n":"unit","l":"Unit","c":"Select","d":"string","v":"perInsured"}]},{"id":"5709595095072255","n":"unitPrice","p":[{"n":"amount","l":"Amount","c":"Number","d":"number","v":900},{"n":"currency","l":"Currency","c":"Select","d":"string","v":"HKD"},{"n":"unit","l":"Unit","c":"Select","d":"string","v":"perEmployee"}]},{"id":"0005106318174872282","ch":[],"n":"unitPrice","p":[{"n":"amount","l":"Amount","c":"Number","d":"number","v":100},{"n":"currency","l":"Currency","c":"Select","d":"string","v":"HKD"},{"n":"unit","l":"Unit","c":"Select","d":"string","v":"perChild"}]}],"n":"plan","p":[{"n":"id","l":"ID","c":"Text","d":"string","v":"PLAN_20220425"},{"n":"name","l":"Name","c":"Text","d":"string","v":"PRODUCT_20220425_01_PLAN"}]}],"n":"root","pc":"HKD"}]""",
        });

        var scriptId = await Products.CreateScript(new CreateScriptCommand
        {
            Name = "pricing",
            InputSchema = """{\"properties\":{},\"type\":\"object\",\"policyFields\":{\"properties\":{\"startDate\":{\"type\":\"string\",\"meta\":{\"label\":\"Start Date\",\"component\":\"CDatePicker\",\"required\":true,\"fixed\":true,\"order\":1}},\"endDate\":{\"type\":\"string\",\"meta\":{\"label\":\"End Date\",\"component\":\"CDatePicker\",\"required\":false,\"fixed\":true,\"order\":2}}}},\"insuredsFields\":{\"properties\":{\"numberOfInsureds\":{\"type\":\"number\",\"meta\":{\"label\":\"Number of Insureds\",\"component\":\"JInputNumber\",\"required\":true,\"order\":1,\"fixed\":true}},\"planSelected\":{\"type\":\"string\",\"meta\":{\"label\":\"Plan Selected\",\"component\":\"JSelect\",\"required\":true,\"options\":[{\"key\":\"27852147936577465\",\"name\":\"PRODUCT_20220425_01_PLAN\",\"value\":\"MSIG_20220425\"}],\"order\":2,\"fixed\":true}}}}}""",
            OutputSchema = "",
            SourceCode = pricingScript ?? GetPricingScript(),
            Type = ScriptTypeEnum2.Pricing
        });

        await Products.AddScriptToProduct(new AddScriptToProductCommand
        {
            ProductId = productId,
            ScriptId = scriptId
        });

        return productId;
    }

    private string GetPricingScript()
    {
        const string pricingScriptResourceName = "CoverGo.Products.Tests.Integration.Resources.pricing-script.txt";
        using var streamReader = new StreamReader(GetType().Assembly.GetManifestResourceStream(pricingScriptResourceName)!);
        string script = streamReader.ReadToEnd();
        return script;
    }
}
public static class TestExtensions
{
    public static async Task<string> SetupSaleChannel(this TestBase test)
    {
        var resSaleChannel = await test._channelManagementClient.CreateSalesChannel.ExecuteAsync(new()
        {
            ChannelType = "SLS",
            ActiveFromDateTime = DateTimeOffset.UtcNow,
            HasPortalAccess = true
        });
        resSaleChannel.EnsureNoErrors();
        return resSaleChannel.Data.CreateSalesChannel.SalesChannelState.SalesChannelID;
    }
    public static async Task<(string salesChannelID, string distributorID)> SetupDistributor(this TestBase test)
    {
        var salesChannelID = await test.SetupSaleChannel();
        var resDistributor = await test._channelManagementClient.CreateDistributor.ExecuteAsync(new()
        {
            Class = "SLS",
            SalesChannelID = salesChannelID,
            ActiveFromDateTime = DateTimeOffset.UtcNow,
            HasPortalAccess = true,
            HasPortalAccessDefault = true,
            UmbrellaPaymentRule = false,
            Party = new OrganizationPartyFieldsInput
            {
                DistributorName = Guid.NewGuid().ToString("N"),
                AddrLine1 = Guid.NewGuid().ToString("N"),
                PostCode = Guid.NewGuid().ToString("N"),
                CountryCode = "HKG"
            },
        });
        resDistributor.EnsureNoErrors();
        var distributorID = resDistributor.Data.CreateDistributor.DistributorState.DistributorId;
        return (salesChannelID, distributorID);
    }
    public static async Task<(string salesChannelID, string distributorID, string campaignCode, string campaignID, DateTimeOffset effectiveFrom, DateTimeOffset effectiveTo)> SetupCampaign(this TestBase test, ProductIdInput productId)
    {
        var (salesChannelID, distributorID) = await test.SetupDistributor();
        var campaignCode = Guid.NewGuid().ToString("N");
        var effectiveFrom = DateTimeOffset.UtcNow.AddDays(-1);
        var effectiveTo = DateTimeOffset.UtcNow.AddDays(5);

        var res = await test._channelManagementClient.CreateCampaign.ExecuteAsync(new()
        {
            CampaignCode = campaignCode,
            Discount = 1,
            SalesChannelID = salesChannelID,
            DistributorIDs = [distributorID],
            EffectiveFrom = effectiveFrom,
            EffectiveTo = effectiveTo,
            ProductID = productId,
            PremiumCalculation = PremiumCalculationType.Gross
        });
        res.EnsureNoErrors();
        var campaignID = res.Data.CreateCampaign.Campaign.CampaignID;
        return (salesChannelID, distributorID, campaignCode, campaignID, effectiveFrom, effectiveTo);
    }
    public static async Task<(string salesChannelID, string distributorID, string commissionID, string productAvailabilityID)> SetupCommission(this TestBase test, ProductId productId)
    {
        var (salesChannelID, distributorID) = await test.SetupDistributor();
        var resCommission = await test._channelManagementClient.CreateCommission.ExecuteAsync(new()
        {
            Title = Guid.NewGuid().ToString("N"),
            Description = Guid.NewGuid().ToString("N"),
            Rules = [
                new() { Amount = 2, AmountUnit = "flat", PercentageUnit = "PER_POLICY_ISSUED" },
                new() { Amount = 1, AmountUnit = "flat", PercentageUnit = "PER_POLICY_ISSUED" }
            ],
            RenewalRules = [],
        });
        resCommission.EnsureNoErrors();
        var commissionID = resCommission.Data.CreateCommission.Commission.CommissionID;
        var resProductAvailability = await test._channelManagementClient.CreateProductAvailability.ExecuteAsync(new()
        {
            CommissionID = commissionID,
            ProductID = new() { Type = productId.Type, Plan = productId.Plan, Version = productId.Version },
            DistributorIDs = [distributorID],
            EffectiveFrom = DateTimeOffset.UtcNow.AddDays(-1),
            EffectiveTo = DateTimeOffset.UtcNow.AddDays(1),
            SalesChannelID = salesChannelID,
            BuyFlow = ProductBuyFlow.BrokerBuyFlow,
        });
        resProductAvailability.EnsureNoErrors();
        var productAvailabilityID = resProductAvailability.Data.CreateProductAvailability.ProductAvailability.ProductAvailabilityID;
        return (salesChannelID, distributorID, commissionID, productAvailabilityID);
    }
}
