using System;
using System.Threading.Tasks;

using CoverGo.ProductBuilder.Client;
using CoverGo.Products.Client;
using CoverGo.Products.Tests.Integration.Utils;

namespace CoverGo.Products.Tests.Integration;

public partial class ProductExchangeTests
{
    [Fact]
    public async Task GIVEN_a_locked_source_product_WHEN_exporting_THEN_expected_error_is_returned()
    {
        string sourceProductTreeId = await CreateProductTree();
        ProductId productId = await CreateProduct(sourceProductTreeId);
        await FileSystem.Initialize();
        LockStatus lockStatus = await LockProduct(sourceProductTreeId);
        lockStatus.Should().NotBeNull();
        lockStatus.isLocked.Should().BeTrue();

        Func<Task> func = () => ExportProduct(productId);

        await func.Should().ThrowAsync<InvalidOperationException>().Where(e => e.Message.StartsWith($"locked_product: The product is locked by {UserCredentials.Admin.UserName}"));
    }

    [Fact]
    public async Task GIVEN_a_locked_target_product_WHEN_getting_product_import_info_and_importing_THEN_it_is_just_a_warning()
    {
        string sourceProductTreeId = await CreateProductTree();
        ProductId productId = await CreateProduct(sourceProductTreeId);
        await FileSystem.Initialize();
        string filePath = await ExportProduct(productId);
        LockStatus lockStatus = await LockProduct(sourceProductTreeId);
        lockStatus.Should().NotBeNull();
        lockStatus.isLocked.Should().BeTrue();

        products_ProductImportInfo productImportInfo = await GetProductImportInfo(filePath);
        await ImportProduct(filePath);

        productImportInfo.validationMessages.Should().BeEquivalentTo(new[] {
            new products_ProductImportValidationMessage
            {
                level = products_ProductImportValidationMessageLevel.WARNING,
                entry = "productTree.json",
                code = "product_tree_exists",
                meta = null
            },
            new products_ProductImportValidationMessage
            {
                level = products_ProductImportValidationMessageLevel.ERROR,
                entry = null,
                code = "locked_product",
                meta = new products_ProductImportValidationMessageMeta
                {
                    lockedBy = UserCredentials.Admin.UserName
                }
            }
        });
    }

    private Task<LockStatus> LockProduct(string productTreeId) => ProductBuilder.LockNode(Guid.Parse(productTreeId));
}