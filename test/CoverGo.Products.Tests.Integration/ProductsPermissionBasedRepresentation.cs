﻿using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration
{
    [Collection("Sequential")]
    public class ProductsPermissionBasedRepresentation : TestBase
    {
        [Fact]
        public async Task GIVEN_permissionSchema_set_WHEN_fetch_product_THEN_returns_product_with_allowed_field()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "product",
                actionType = permissionSchemaActionType.READ,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape()
            });

            productId productId = await Products.Create(new createProductInput
            {
                productId = Products.CreateProductId(),

                representation = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            });

            const string expectedFields = @"{""field1"":""Value1""}";

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "readProducts", productId.AsString());

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { productId.AsString() });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            products products = await new Utils.Products(client, TenantId).FindById(productId);
            products.Should().NotBeNull();

            product? product = products!.list!.FirstOrDefault();
            product.Should().NotBeNull();

            product!.representation!.RemoveWhitespaces().Should().Be(expectedFields);
        }

        [Fact]
        public async Task GIVEN_permissionSchema_with_all_fields_allowed_set_WHEN_fetch_product_THEN_returns_product_with_allowed_fields()
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "product",
                actionType = permissionSchemaActionType.READ,
                schema = GetResource("PermissionsJsonSchema").Escape()
            });

            productId productId = await Products.Create(new createProductInput
            {
                productId = Products.CreateProductId(),

                representation = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            });

            const string expectedFields = @"{""field1"":""Value1"",""field2"":""Value2""}";

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, "readProducts", productId.AsString());

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { productId.AsString() });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            products products = await new Utils.Products(client, TenantId).FindById(productId);

            products.Should().NotBeNull();

            product? product = products!.list!.FirstOrDefault();
            product.Should().NotBeNull();

            product!.representation!.RemoveWhitespaces().Should().Be(expectedFields);
        }

        [Theory]
        [InlineData("updateProducts")]
        [InlineData("writeProducts")]
        public async Task GIVEN_permissionSchema_set_WHEN_patch_product_fields_THEN_updates_product_fields(string productPermissionName)
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "product",
                actionType = permissionSchemaActionType.WRITE,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape()
            });

            productId productId = await Products.Create(new createProductInput
            {
                productId = Products.CreateProductId(),

                representation = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            });

            string representationPatch = @"
[
 {
  ""op"": ""replace"",
  ""path"": ""/field1"",
  ""value"": ""Value3""
 }
]".Escape();

            const string expectedFields = "{\"field1\":\"Value3\",\"field2\":\"Value2\"}";

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, productPermissionName, productId.AsString());

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { productId.AsString() });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            await new Utils.Products(client, TenantId).Update(productId.AsInput(), new updateProductInput { representationPatch = representationPatch });

            products products = await Products.FindById(productId);
            products.Should().NotBeNull();

            product? product = products!.list!.FirstOrDefault();

            product!.representation!.RemoveWhitespaces().Should().Be(expectedFields);
        }

        [Theory]
        [InlineData("updateProducts")]
        [InlineData("writeProducts")]
        public async Task GIVEN_permissionSchema_set_WHEN_patch_product_fields_without_permission_THEN_fails(string productPermissionName)
        {
            string permissionSchemaId = await PermissionSchema.Create(new createPermissionSchemaInput
            {
                name = CreateNewGuid(),
                objectType = "product",
                actionType = permissionSchemaActionType.WRITE,
                schema = GetResource("PermissionsJsonSchema_Field1").Escape()
            });

            productId productId = await Products.Create(new createProductInput
            {
                productId = Products.CreateProductId(),

                representation = @"
{
    ""field1"": ""Value1"",
    ""field2"": ""Value2""
}".Escape()
            });

            string representationPatch = @"
[
 {
  ""op"": ""replace"",
  ""path"": ""/field2"",
  ""value"": ""Value3""
 }
]".Escape();

            (string loginId, createLoginInput credentials) = await Login.CreateValidLoginAndReturnIdAndInput();
            await Login.AddPermission(loginId!, productPermissionName, productId.AsString());

            await Login.AddPermissionSchema(loginId, permissionSchemaId, new[] { productId.AsString() });

            GraphQLHttpClient client = await CreateGraphQLHttpClient(credentials.clientId!, credentials.username!, credentials.password!);
            Func<Task> method = () => new Utils.Products(client, TenantId).Update(productId.AsInput(), new updateProductInput { representationPatch = representationPatch });

            await method.Should().ThrowAsync<Exception>();
        }
    }
}
