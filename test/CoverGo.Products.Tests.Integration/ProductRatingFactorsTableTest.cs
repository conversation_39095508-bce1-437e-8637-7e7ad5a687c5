using CoverGo.Gateway.Client;
using CoverGo.Products.Client;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration;

[Collection("Sequential")]
public class ProductRatingFactorsTableTests : TestBase
{
    [Fact]
    public async Task GIVEN_product_WHEN_setting_rating_factors_table_THEN_rating_factors_table_should_be_set()
    {
        productId productId = await Products.Create();

        string tableValue = "testRatingFactorsTableValue";
        await Products.SetRatingFactorsTable(new products_SetRatingFactorsTableInput { productId = new() { plan = productId.plan, type = productId.type, version = productId.version }, ratingFactorsTable = tableValue });

        Product product = await Products.GetProductByProductId(new ProductId { Plan = productId.plan, Type = productId.type, Version = productId.version });

        product.RatingFactorsTable.Should().Be(tableValue);
    }

    [Fact(Skip = "Flaky test, pending review. https://covergo.slack.com/archives/C060X0AS05Q/p1727884312315759")]
    public async Task GIVEN_product_with_rating_factors_table_WHEN_removing_rating_factors_table_THEN_rating_factors_table_should_be_null()
    {
        productId productId = await Products.Create();

        string tableValue = "testRatingFactorsTableValue";
        await Products.SetRatingFactorsTable(new products_SetRatingFactorsTableInput { productId = new() { plan = productId.plan, type = productId.type, version = productId.version }, ratingFactorsTable = tableValue });
        await Products.RemoveRatingFactorsTable(new products_RemoveRatingFactorsTableInput { productId = new() { plan = productId.plan, type = productId.type, version = productId.version } });

        Product product = await Products.GetProductByProductId(new ProductId { Plan = productId.plan, Type = productId.type, Version = productId.version });

        product.RatingFactorsTable.Should().Be(null);
    }
}