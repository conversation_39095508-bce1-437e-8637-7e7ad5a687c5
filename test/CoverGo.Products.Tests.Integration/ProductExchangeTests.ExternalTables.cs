using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using CoverGo.ProductBuilder.Client;
using CoverGo.Products.Client;

namespace CoverGo.Products.Tests.Integration;

public partial class ProductExchangeTests
{
    [Fact]
    public async Task GIVEN_an_exported_product_with_referenced_external_tables_WHEN_importing_THEN_the_product_is_imported_with_the_external_tables()
    {
        string externalTableName1 = Guid.NewGuid().ToString();
        string externalTableName2 = Guid.NewGuid().ToString();
        string sourceProductTreeId = await CreateProductTreeReferencedToExternalTables(externalTableName1, externalTableName2);
        ProductId productId = await CreateProduct(sourceProductTreeId);
        await FileSystem.Initialize();
        string externalTableContent1 = $"{{\"index\":\"{Guid.NewGuid().ToString()}\"}}";
        string externalTableContent2 = $"{{\"index\":\"{Guid.NewGuid().ToString()}\"}}";
        await FileSystem.Upload($"externalTables/{externalTableName1}.txt", externalTableContent1);
        await FileSystem.Upload($"externalTables/{externalTableName2}.txt", externalTableContent2);
        string filePath = await ExportProduct(productId);

        products_ProductImportInfo productImportInfo = await GetProductImportInfo(filePath);
        await ImportProduct(filePath);
        string actualExternalTableContent1 = await FileSystem.Download($"externalTables/{externalTableName1}.txt");
        string actualExternalTableContent2 = await FileSystem.Download($"externalTables/{externalTableName2}.txt");

        productImportInfo.includedEntries.Should().HaveCount(3);
        productImportInfo.includedEntries.ElementAt(0).Should().Be("productTree.json");
        productImportInfo.includedEntries.ElementAt(1).Should().BeOneOf($"externalTables/{externalTableName1}.json", $"externalTables/{externalTableName2}.json");
        productImportInfo.includedEntries.ElementAt(2).Should().BeOneOf($"externalTables/{externalTableName1}.json", $"externalTables/{externalTableName2}.json");

        List<products_ProductImportValidationMessage> expectedValidationMessages = new()
        {
            new products_ProductImportValidationMessage
            {
                level = products_ProductImportValidationMessageLevel.WARNING,
                entry = "productTree.json",
                code = "product_tree_exists"
            }
        };

        expectedValidationMessages.AddRange(productImportInfo.includedEntries.Where(e => e.StartsWith("externalTables/")).Select(e => new products_ProductImportValidationMessage
        {
            level = products_ProductImportValidationMessageLevel.WARNING,
            entry = e,
            code = "external_table_exists"
        }));

        productImportInfo.validationMessages.Should().BeEquivalentTo(expectedValidationMessages);

        actualExternalTableContent1.Should().Be(externalTableContent1);
        actualExternalTableContent2.Should().Be(externalTableContent2);
    }

    [Fact]
    public async Task GIVEN_an_exported_product_with_referenced_external_tables_and_no_target_external_tables_WHEN_importing_THEN_the_product_is_imported_with_the_external_tables()
    {
        string externalTableName1 = Guid.NewGuid().ToString();
        string externalTableName2 = Guid.NewGuid().ToString();
        string sourceProductTreeId = await CreateProductTreeReferencedToExternalTables(externalTableName1, externalTableName2);
        ProductId productId = await CreateProduct(sourceProductTreeId);
        await FileSystem.Initialize();
        string externalTableContent1 = $"{{\"index\":\"{Guid.NewGuid().ToString()}\"}}";
        string externalTableContent2 = $"{{\"index\":\"{Guid.NewGuid().ToString()}\"}}";
        await FileSystem.Upload($"externalTables/{externalTableName1}.txt", externalTableContent1);
        await FileSystem.Upload($"externalTables/{externalTableName2}.txt", externalTableContent2);
        string filePath = await ExportProduct(productId);
        await FileSystem.Delete(new List<string>
        {
            $"externalTables/{externalTableName1}.txt",
            $"externalTables/{externalTableName2}.txt"
        });

        products_ProductImportInfo productImportInfo = await GetProductImportInfo(filePath);
        await ImportProduct(filePath);
        string actualExternalTableContent1 = await FileSystem.Download($"externalTables/{externalTableName1}.txt");
        string actualExternalTableContent2 = await FileSystem.Download($"externalTables/{externalTableName2}.txt");

        productImportInfo.includedEntries.Should().HaveCount(3);
        productImportInfo.includedEntries.ElementAt(0).Should().Be("productTree.json");
        productImportInfo.includedEntries.ElementAt(1).Should().BeOneOf($"externalTables/{externalTableName1}.json", $"externalTables/{externalTableName2}.json");
        productImportInfo.includedEntries.ElementAt(2).Should().BeOneOf($"externalTables/{externalTableName1}.json", $"externalTables/{externalTableName2}.json");

        productImportInfo.validationMessages.Should().BeEquivalentTo(new[]
        {
            new products_ProductImportValidationMessage
            {
                level = products_ProductImportValidationMessageLevel.WARNING,
                entry = "productTree.json",
                code = "product_tree_exists"
            }
        });

        actualExternalTableContent1.Should().Be(externalTableContent1);
        actualExternalTableContent2.Should().Be(externalTableContent2);
    }

    [Fact]
    public async Task GIVEN_an_exported_product_referenced_to_missing_external_tables_WHEN_exporting_THEN_it_throws()
    {
        string externalTableName1 = Guid.NewGuid().ToString();
        string externalTableName2 = Guid.NewGuid().ToString();
        string sourceProductTreeId = await CreateProductTreeReferencedToExternalTables(externalTableName1, externalTableName2);
        ProductId productId = await CreateProduct(sourceProductTreeId);
        await FileSystem.Initialize();

        Func<Task> func = () => ExportProduct(productId);

        await func.Should().ThrowAsync<InvalidOperationException>().Where(e => e.Message.StartsWith(": Unable to export the product"));
    }

    private Task<string> CreateProductTreeReferencedToExternalTables(string externalTableName1, string externalTableName2) => ProductBuilder.CreateNode(new CreateNodeInput
    {
        @ref = "product1",
        fields = new NodeFieldInput[]
        {
            new NodeFieldInput
            {
                @ref = "table1",
                type = "String",
                resolver = new ExpressionInput
                {
                    text = $"IMPORT_WORKSHEET(\\\"{externalTableName1}\\\")",
                    language = Language.MATHJS
                }
            },
            new NodeFieldInput
            {
                @ref = "table2",
                type = "String",
                resolver = new ExpressionInput
                {
                    text = $"IMPORT_WORKSHEET(\\\"{externalTableName2}\\\")",
                    language = Language.MATHJS
                }
            }
        }
    });
}