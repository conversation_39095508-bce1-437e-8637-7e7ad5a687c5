﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Client;
using CoverGo.Products.Client;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration;

[Collection("Sequential")]
public sealed class BenefitDefinitionTests : TestBase
{
    [Fact]
    public async Task GIVEN_benefitDefinitions_WHEN_request_this_benefitDefinition_by_BusinessIdContains_THEN_return_it()
    {
        BenefitDefinition benefit = await CreateSingleBenefitDefinition();
        await CreateSingleBenefitDefinition();

        var businessIdContainsWhere = new BenefitDefinitionWhere { BusinessId_contains = benefit.BusinessId };

        var args = new BenefitQueryArguments { Where = businessIdContainsWhere };
        ICollection<BenefitDefinition> filteredProduct = await Products.GetBenefits(args);


        filteredProduct.Count.Should().Be(1);
        filteredProduct.FirstOrDefault()?.BusinessId.Should().Be(benefit.BusinessId);
    }

    [Fact]
    public async Task GIVEN_benefitDefinition_WHEN_request_this_benefitDefinition_by_OR_BusinessIdContains_and_Name_contains_THEN_return_it()
    {
        string benefitName = Guid.NewGuid().ToString();
        BenefitDefinition benefit = await CreateSingleBenefitDefinition(benefitName);
        BenefitDefinition benefit2 = await CreateSingleBenefitDefinition();

        var nameContainsWhere = new BenefitDefinitionWhere { Name_contains = benefit.Name };
        var businessIdContainsWhere = new BenefitDefinitionWhere { BusinessId_contains = benefit2.BusinessId };

        var businessContainsAndNameContainsWhere = new BenefitDefinitionWhere { Or = new List<BenefitDefinitionWhere> { businessIdContainsWhere, nameContainsWhere } };

        var args = new BenefitQueryArguments { Where = businessContainsAndNameContainsWhere };

        ICollection<BenefitDefinition> filteredProduct = await Products.GetBenefits(args);

        filteredProduct.Count.Should().Be(2);
        filteredProduct.First(b => b.Id == benefit.Id).Name.Should().Be(benefitName);
        filteredProduct.First(b => b.Id == benefit2.Id).BusinessId.Should().Be(benefit2.BusinessId);
    }

    [Fact]
    public async Task GIVEN_benefitDefinition_WHEN_request_this_benefitDefinition_by_NameContains_THEN_return_it()
    {
        string benefitName = Guid.NewGuid().ToString();
        BenefitDefinition benefit = await CreateSingleBenefitDefinition(benefitName);

        var nameContainsWhere = new BenefitDefinitionWhere { Name_contains = benefit.Name };

        var args = new BenefitQueryArguments { Where = nameContainsWhere };
        ICollection<BenefitDefinition> filteredProducts = await Products.GetBenefits(args);

        filteredProducts.FirstOrDefault()?.Name.Should().Be(benefitName);
    }

    [Fact]
    public async Task GIVEN_benefitDefinition_WHEN_sort_by_name_asc_THEN_return_sorted_list_by_name()
    {
        const string benefitName = "A Insurance";
        const string benefitName2 = "B Insurance";
        BenefitDefinition benefit = await CreateSingleBenefitDefinition(benefitName);
        BenefitDefinition benefit2 = await CreateSingleBenefitDefinition(benefitName2);

        var idInConditionWhere = new BenefitDefinitionWhere { Id_in = new List<string> { benefit.Id, benefit2.Id } };
        var args = new BenefitQueryArguments { Where = idInConditionWhere, OrderBy = new OrderBy { FieldName = "name", Type = OrderByType.ASC } };

        ICollection<BenefitDefinition> filteredProduct = await Products.GetBenefits(args);

        filteredProduct.Count.Should().Be(2);
        filteredProduct.First().Name.Should().Be(benefitName);
        filteredProduct.Skip(1).First()?.Name.Should().Be(benefitName2);
    }

    [Fact]
    public async Task GIVEN_benefitDefinition_WHEN_sort_by_name_dsc_THEN_return_sorted_list_by_name()
    {
        const string benefitName = "A Insurance";
        const string benefitName2 = "B Insurance";
        BenefitDefinition benefit = await CreateSingleBenefitDefinition(benefitName);
        BenefitDefinition benefit2 = await CreateSingleBenefitDefinition(benefitName2);

        BenefitDefinitionWhere idInConditionWhere = new BenefitDefinitionWhere { Id_in = new List<string> { benefit.Id, benefit2.Id } };
        BenefitQueryArguments args = new BenefitQueryArguments { Where = idInConditionWhere, OrderBy = new OrderBy { FieldName = "name", Type = OrderByType.DSC } };

        ICollection<BenefitDefinition> filteredProduct = await Products.GetBenefits(args);

        filteredProduct.Count.Should().Be(2);
        filteredProduct.First()?.Name.Should().Be(benefitName2);
        filteredProduct.Skip(1).First()?.Name.Should().Be(benefitName);
    }

    [Fact]
    public async Task GIVEN_benefitDefinitions_WHEN_sort_by_businessId_asc_THEN_return_sorted_list_by_businessId()
    {
        BenefitDefinition benefit = await CreateSingleBenefitDefinition();
        BenefitDefinition benefit2 = await CreateSingleBenefitDefinition();

        var idInConditionWhere = new BenefitDefinitionWhere { Id_in = new List<string> { benefit.Id, benefit2.Id } };
        var args = new BenefitQueryArguments { Where = idInConditionWhere, OrderBy = new OrderBy { FieldName = "businessId", Type = OrderByType.ASC } };

        ICollection<BenefitDefinition> filteredProduct = await Products.GetBenefits(args);

        var orderedBusinessId = new List<string> { benefit.BusinessId, benefit2.BusinessId };
        orderedBusinessId.Sort();

        filteredProduct.Count.Should().Be(2);
        filteredProduct.First()?.BusinessId.Should().Be(orderedBusinessId[0]);
        filteredProduct.Skip(1).First()?.BusinessId.Should().Be(orderedBusinessId[1]);
    }

    [Fact]
    public async Task GIVEN_benefitDefinitions_WHEN_sort_by_businessId_dsc_THEN_return_sorted_list_by_businessId()
    {
        BenefitDefinition benefit = await CreateSingleBenefitDefinition();
        BenefitDefinition benefit2 = await CreateSingleBenefitDefinition();

        var idInConditionWhere = new BenefitDefinitionWhere { Id_in = new List<string> { benefit.Id, benefit2.Id } };
        var args = new BenefitQueryArguments { Where = idInConditionWhere, OrderBy = new OrderBy { FieldName = "businessId", Type = OrderByType.DSC } };

        ICollection<BenefitDefinition> filteredProduct = await Products.GetBenefits(args);

        var orderedBusinessId = new List<string> { benefit.BusinessId, benefit2.BusinessId };
        orderedBusinessId.Sort((a, b) => string.Compare(b, a, StringComparison.Ordinal));

        filteredProduct.Count.Should().Be(2);
        filteredProduct.First()?.BusinessId.Should().Be(orderedBusinessId[0]);
        filteredProduct.Skip(1).First()?.BusinessId.Should().Be(orderedBusinessId[1]);
    }

    [Fact]
    public async Task GIVEN_product_added_WHEN_add_and_update_multiple_benefits_of_it_THEN_product_contains_new_and_updated_benefits_content()
    {
        productId productId = await Products.Create();

        benefitInput benefit1 = await Products.AddBenefit(productId);
        benefitInput benefit2 = await Products.AddBenefit(productId);

        benefitInput addBenefitInput1 = new()
        {
            typeId = CreateNewGuid(),
            optionKey = CreateNewGuid(),
            value = new scalarValueInput { stringValue = "test value" }
        };
        benefitInput addBenefitInput2 = new()
        {
            typeId = CreateNewGuid(),
            optionKey = CreateNewGuid(),
            value = new scalarValueInput { stringValue = "test value" }
        };
        benefitInput updateBenefitInput1 = new()
        {
            typeId = benefit1.typeId!,
            optionKey = benefit1.optionKey!,
            value = new scalarValueInput { stringValue = "test update batch 1" }
        };
        benefitInput updateBenefitInput2 = new()
        {
            typeId = benefit2.typeId!,
            optionKey = benefit2.optionKey!,
            value = new scalarValueInput { stringValue = "test update batch 2" }
        };
        benefitBatchInput benefitBatchInput = new()
        {
            addBenefitInputs = new List<benefitInput?> { addBenefitInput1, addBenefitInput2 },
            updateBenefitInputs = new List<benefitInput?> { updateBenefitInput1, updateBenefitInput2 }
        };

        await Products.ProductBenefitBatch(productId, benefitBatchInput);
        product product = await Products.SearchProductById(productId);

        product.benefitGraph!.Count.Should().Be(4);

        benefitGraph finalBenefit1 = product.benefitGraph.First(b => b!.typeId == benefit1.typeId && b.optionKey == benefit1.optionKey)!;
        benefitGraph finalBenefit2 = product.benefitGraph.First(b => b!.typeId == benefit2.typeId && b.optionKey == benefit2.optionKey)!;

        finalBenefit1.value2!.stringValue.Should().Be(updateBenefitInput1.value.stringValue);
        finalBenefit2.value2!.stringValue.Should().Be(updateBenefitInput2.value.stringValue);
    }

    private async Task<BenefitDefinition> CreateSingleBenefitDefinition(string benefitName = "")
    {
        BenefitDefinition benefitDefinition = GenerateBenefitDefinition(benefitName);

        await Products.CreateBenefitDefinition(new CreateBenefitDefinitionCommand
        {
            BusinessId = benefitDefinition.BusinessId,
            Name = benefitDefinition.Name,
            CreatedById = benefitDefinition.CreatedById
        });

        var where = new BenefitDefinitionWhere()
        {
            BusinessId_in = new List<string>() { benefitDefinition.BusinessId },
        };

        var args = new BenefitQueryArguments { Where = where };
        ICollection<BenefitDefinition> product = await Products.GetBenefits(args);

        return product.FirstOrDefault();
    }
    private static BenefitDefinition GenerateBenefitDefinition(string benefitName)
    {
        return new()
        {
            Name = benefitName,
            CreatedById = Guid.NewGuid().ToString(),
            BusinessId = Guid.NewGuid().ToString()
        };
    }
}