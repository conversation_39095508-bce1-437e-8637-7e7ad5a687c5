﻿using CoverGo.DomainUtils;
using CoverGo.Gateway.Client;
using CoverGo.Products.Client;
using CoverGo.Products.Tests.Integration.Utils;

using System;
using System.Linq;
using System.Threading.Tasks;
using Result = CoverGo.DomainUtils.Result;

namespace CoverGo.Products.Tests.Integration;

[Collection("Sequential")]
public sealed class ProductInternalReviewTests : TestBase
{
    [Fact]
    public async Task GIVEN_add_internal_review_command__WHEN_adding_internal_review_to_product_THEN_return_success()
    {
        ProductId productId = await CreateProduct();

        AddInternalReviewCommand addIRCommand = new AddInternalReviewCommand
        {
            Status = Guid.NewGuid().ToString(),
            Comment = Guid.NewGuid().ToString(),
            AddedById = Guid.NewGuid().ToString()
        };
        ResultOfCreatedStatus result = await Products.AddInternalReview(productId.ToProductIdString(), addIRCommand);

        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });

        result.Status.Should().Be("success");
        result.Errors.Should().BeNullOrEmpty();
    }

    [Fact]
    public async Task GIVEN_add_internal_review_command__WHEN_product_not_exist_THEN_return_failure()
    {
        var productId = GenerateProductId();

        AddInternalReviewCommand addIRCommand = new AddInternalReviewCommand
        {
            Status = Guid.NewGuid().ToString(),
            Comment = Guid.NewGuid().ToString(),
            AddedById = Guid.NewGuid().ToString()
        };
        ResultOfCreatedStatus result = await Products.AddInternalReview(productId.ToProductIdString(), addIRCommand);

        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });

        result.Status.Should().Be("failure");
        result.Errors.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task GIVEN_update_internal_review_command__WHEN_updating_internal_review_to_product_THEN_update_internal_review_content()
    {
        ProductId productId = await CreateProduct();
        string reviewId = await AddInternalReview(productId);

        var status = Guid.NewGuid().ToString();
        var comment = Guid.NewGuid().ToString();
        var updatedBy = Guid.NewGuid().ToString();
        UpdateInternalReviewCommand updateIRCommand = new UpdateInternalReviewCommand
        {
            Id = reviewId,
            Status = status,
            IsStatusChanged = true,
            Comment = comment,
            IsCommentChanged = true,
            ModifiedById = updatedBy
        };
        Result result = await Products.UpdateInternalReview(productId.ToProductIdString(), updateIRCommand);
        result.Status.Should().Be("success");
        result.Errors.Should().BeNullOrEmpty();

        Product product = await Products.GetProductByProductId(productId);
        InternalReview review = product.InternalReviews.FirstOrDefault();

        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });

        review.Status.Should().Be(status);
        review.Comment.Should().Be(comment);
        review.LastModifiedById.Should().Be(updatedBy);

    }

    [Fact]
    public async Task GIVEN_update_internal_review_command__WHEN__product_not_exist_THEN_return_failure()
    {
        ProductId productId = GenerateProductId();

        var status = Guid.NewGuid().ToString();
        var comment = Guid.NewGuid().ToString();
        var updatedBy = Guid.NewGuid().ToString();
        UpdateInternalReviewCommand updateIRCommand = new UpdateInternalReviewCommand
        {
            Id = Guid.NewGuid().ToString(),
            Status = status,
            IsStatusChanged = true,
            Comment = comment,
            IsCommentChanged = true,
            ModifiedById = updatedBy
        };
        Result result = await Products.UpdateInternalReview(productId.ToProductIdString(), updateIRCommand);

        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });

        result.Status.Should().Be("failure");
        result.Errors.Should().NotBeNullOrEmpty();
        result.Errors.FirstOrDefault().Should().Be($"The product {productId.ToProductIdString()} was not found.");
    }

    [Fact]
    public async Task GIVEN_update_internal_review_command__WHEN__internal_review_not_exist_THEN_return_failure()
    {
        ProductId productId = await CreateProduct();

        var status = Guid.NewGuid().ToString();
        var comment = Guid.NewGuid().ToString();
        var updatedBy = Guid.NewGuid().ToString();
        UpdateInternalReviewCommand updateIRCommand = new UpdateInternalReviewCommand
        {
            Id = Guid.NewGuid().ToString(),
            Status = status,
            IsStatusChanged = true,
            Comment = comment,
            IsCommentChanged = true,
            ModifiedById = updatedBy
        };
        Result result = await Products.UpdateInternalReview(productId.ToProductIdString(), updateIRCommand);

        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });

        result.Status.Should().Be("failure");
        result.Errors.Should().NotBeNullOrEmpty();
        result.Errors.FirstOrDefault().Should().Be($"The internal review with Id '{updateIRCommand.Id}' was not found for this product.");
    }

    [Fact]
    public async Task GIVEN_remove_internal_review_command__WHEN_removing_internal_review_from_product_THEN_return_success()
    {
        ProductId productId = await CreateProduct();
        string reviewId = await AddInternalReview(productId);

        RemoveCommand updateIRCommand = new RemoveCommand
        {
            Id = reviewId,
        };
        Result result = await Products.RemoveInternalReview(productId.ToProductIdString(), updateIRCommand);

        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });

        result.Status.Should().Be("success");
        result.Errors.Should().BeNullOrEmpty();
    }

    [Fact]
    public async Task GIVEN_remove_internal_review_command__WHEN_product_not_exist_THEN_return_failure()
    {
        ProductId productId = await CreateProduct();
        string reviewId = Guid.NewGuid().ToString();

        RemoveCommand removeIRCommand = new RemoveCommand
        {
            Id = reviewId,
        };
        Result result = await Products.RemoveInternalReview(productId.ToProductIdString(), removeIRCommand);

        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });

        result.Status.Should().Be("failure");
        result.Errors.Should().NotBeNullOrEmpty();
        result.Errors.FirstOrDefault().Should().Be($"The internal review with Id '{removeIRCommand.Id}' was not found for this product.");
    }

    [Fact]
    public async Task GIVEN_remove_internal_review_command__WHEN_internal_review_not_exit_THEN_return_failure()
    {
        ProductId productId = await CreateProduct();
        string reviewId = Guid.NewGuid().ToString();

        RemoveCommand removeIRCommand = new RemoveCommand
        {
            Id = reviewId,
        };
        Result result = await Products.RemoveInternalReview(productId.ToProductIdString(), removeIRCommand);

        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });

        result.Status.Should().Be("failure");
        result.Errors.Should().NotBeNullOrEmpty();
        result.Errors.FirstOrDefault().Should().Be($"The internal review with Id '{removeIRCommand.Id}' was not found for this product.");
    }

    [Fact]
    public async Task GIVEN_product_added_WHEN_add_internal_review_to_it_THEN_succeed_and_product_contains_internal_review()
    {
        productId productId = await Products.Create();

        string internalReviewId = await AddInternalReview(productId);

        product product = await SearchProductById(productId);

        product.internalReviews!.Single()!.id.Should().Be(internalReviewId);
    }

    async Task<product> SearchProductById(productId id)
    {
        productBuilder fields = new productBuilder()
            .internalReviews(new internalReviewBuilder()
                .id()
                .comment()
                .status());

        return (await Products.FindById(id, fields)).list!.First()!;
    }

    async Task<string> AddInternalReview(ProductId productId)
    {
        AddInternalReviewCommand addIRCommand = new AddInternalReviewCommand
        {
            Status = Guid.NewGuid().ToString(),
            Comment = Guid.NewGuid().ToString(),
            AddedById = Guid.NewGuid().ToString()
        };
        ResultOfCreatedStatus result = await Products.AddInternalReview(productId.ToProductIdString(), addIRCommand);
        return result.Value.Id;
    }

    Task<string> AddInternalReview(productId productId)
    {
        internalReviewInput addInternalReviewCommand = new()
        {
            status = CreateNewGuid(),
            comment = CreateNewGuid()
        };

        return AddInternalReviewToProduct(productId, addInternalReviewCommand);
    }

    Task<string> AddInternalReviewToProduct(productId productId, internalReviewInput input)
    {
        string mutation = new covergoMutationBuilder()
            .addInternalReviewToProduct(new covergoMutationBuilder.addInternalReviewToProductArgs(Products.ProductIdToInput(productId), input), new createdStatusResultBuilder()
                .WithAllFields())
            .Build();

        return _gatewayGraphQlHttpClient.CreateAndReturnId(mutation);
    }

    async Task<ProductId> CreateProduct()
    {
        var productId = GenerateProductId();
        await Products.CreateProduct(new CreateProductCommand { ProductId = productId });
        return productId;
    }
}