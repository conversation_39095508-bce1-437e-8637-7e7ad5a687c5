﻿/**
 * @name typecast
 * @description it will convert value into particular JavaScript data type based on provided dataType
 * @example
 * value='2' dataType='number' --> value=2
 * value='true' dataType='boolean' --> value=true
 */
function typecast(value, dataType) {
	if (value === null || value === undefined) return value
	if (!isNaN(value)) return Number(value)
	if (value === "true") return true
	if (value === "false") return false
	return value // fallback just return value, for example if array or object there is no conversion needed
}

/**
 * @name execute
 * @param {
 *  representation: <from product>
 *  dataInput: {
 *    policy: {
 *      startDate: "2021-05-01"
 *      endDate: "2022-04-30"
 *    },
 *    plans: {
 *      <planId>: {
 *        <plan's fields>
 *      }
 *    },
 *    insureds: [
 *      startDate (optional)
 *      endDate (optional)
 *      <indured's fields>
 *    ]
 *   }
 * }
 * @returns {
 *  isSuccess,
 *  data,
 *  error {
 *    message
 *  }
 * }
 * @description this is main function of the script
 * Reference for input formar: https://covergo.atlassian.net/wiki/spaces/COVERHEALT/pages/231800833/Member+movements+Pricing
 */

/**
 * 
 Node property mapping
 const keyMapping = {
		children: "ch",
		name: "n",
		label: "l",
		parameters: "p",
		component: "c",
		displayValue: "dv",
		description: "de",
		dataType: "d",
		value: "v",
	}
 */
export function execute({ representation, dataInput, externalTableData }) {
	const formulaLogs = { warns: [] }
	if (typeof externalTableData === "string") {
		externalTableData = JSON.parse(externalTableData)
	}
	// Helpers
	const deepClone = (input) => JSON.parse(JSON.stringify(input))
	const getParameterValue = (parameters, property) => parameters?.find((item) => item.n === property)?.v
	const handlingNodeTypes = ["root", "plan", "benefitType", "benefit", "ifCondition", "unitPrice", "bandedPremium"]

	/**
		 * This function will returned the detail for each direct price node under a certain node, 
		 * even no insured applied
		 * @param {*} node 
		 * @returns 
		 * [
				{
					currency: "HKD",
					unit: "perEmployee",
					amount: 1000,
					proRate: 1,
				}
			]
			*/
	const getDirectPricesDefinition = (node, activeDayRate = 1) => {
		const handlingNodeTypes = ["plan", "benefitType", "benefit"]
		if (!handlingNodeTypes.includes(node.n)) return null
		const directPriceNodes = node?.ch?.filter((i) => i.n === "unitPrice") || []
		const conditionPriceNodes =
			node?.ch?.reduce((acc, _node) => {
				const pricingNodes =
					_node?.ch
						?.filter((i) => i.n === "unitPrice")
						?.map((priceNode) => ({ ...priceNode, condition: getParameterValue(_node.p, "description") })) || []
				if (_node.n !== "ifCondition" || !pricingNodes?.length === 0) return acc
				return [...acc, ...pricingNodes]
			}, []) || []
		const allDirectPriceNodes = [...directPriceNodes, ...conditionPriceNodes]
		return allDirectPriceNodes.map((priceNode) => {
			const amount = getParameterValue(priceNode.p, "amount")
			return {
				condition: priceNode.condition,
				currency: getParameterValue(priceNode.p, "currency"),
				unit: getParameterValue(priceNode.p, "unit"),
				amount,
				proRate: amount * activeDayRate,
			}
		})
	}

	/**
	 * @name generateParentId
	 * @desciption set the parentId for the nodes
	 */
	function generateParentId(node, parentNode) {
		node.parentId = parentNode?.id
		if (node.ch?.length) {
			node.ch.forEach((cNode) => generateParentId(cNode, node))
		}
	}

	function roundToTwo(num) {
		const number = +(Math.round(num + "e+4") + "e-4")
		return +(Math.round(number + "e+2") + "e-2")
	}

	function pickPricingDetails(node, flattenPriceNodes) {
		return {
			id: node.id,
			name: node.n,
			directPriceDetails: node.directPriceDetails,
			directBandedPriceDetails: node.directBandedPriceDetails,
			directBandedPremiumNodes: node.directBandedPremiumNodes,
			totalPrice: node.totalPrice,
			totalDirectPrice: node.totalDirectPrice,
			insuredCount: node.insuredCount,
			directPricesDefinition: node.directPricesDefinition,
			nodeName: getParameterValue(node.p, "name"),
			parentId: node.parentId,
			conditionalPrices: getConditionalPrices(flattenPriceNodes, node),
			conditions: node.conditions,
		}
	}

	/**
	 * @param {*} tree
	 * @returns pricingNodes
	 * @description Bring all the nested unitPrice node to parent
	 * (parent will have the list of ids those are pricing node id)
	 * -- In each unitPrice node have list of conditions to the price become affected
	 * -- This function will change the tree as relative param and return flatten array of all unitPrice nodes
	 */
	function buildPricingTree(tree) {
		const flattenTree = []
		const pricingNodes = []
		const bandedPremiumNodes = []

		let stack = [...tree]
		while (stack.length) {
			const node = stack.pop()
			flattenTree.push(node)
			if (node.n === "unitPrice" || node.n === "bandedPremium") {
				const parentNodes = getParents(flattenTree, node)
				const conditionNodes = parentNodes
					.filter((pNode) => {
						return pNode.n === "ifCondition"
					})
					// remove childNode to avoid circular structure
					.map((cn) => {
						return { ...cn, ch: null }
					})
				const planNode = parentNodes.find((pNode) => {
					return pNode.n === "plan"
				})

				node.planId = getParameterValue(planNode.p, "id")
				node.conditionNodes = conditionNodes

				// for unit price node
				if (node.n === "unitPrice") {
					parentNodes.forEach((pNode) => {
						if (!pNode.pricingNodes) {
							pNode.pricingNodes = []
						}
						pNode.pricingNodes.push(node.id)
					})
					pricingNodes.push(node)
				}

				// for banded premium node
				if (node.n === "bandedPremium") {
					parentNodes.forEach((pNode) => {
						if (!pNode.bandedPremiumNodes) {
							pNode.bandedPremiumNodes = []
						}
						pNode.bandedPremiumNodes.push(node.id)
					})
					bandedPremiumNodes.push(node)
				}
			}

			if (node?.ch?.length) {
				const children = node.ch
					.filter((cNode) => cNode.__isGhost !== true)
					.filter((cNode) => handlingNodeTypes.includes(cNode.n))
					.reverse()
				stack = [...stack, ...children]
			}
		}

		return {
			pricingNodes,
			bandedPremiumNodes,
		}
	}

	function findNode(nodes, nodeId) {
		return nodes.find((n) => n.id === nodeId)
	}

	function getParents(flattenTree, childNode, parentType) {
		const parentNodes = []
		let node = childNode
		let parentLevel = 1
		while (node.parentId && node.n !== "root") {
			const parent = findNode(flattenTree, node.parentId)
			parent.parentLevel = parentLevel
			parentNodes.push(parent)
			parentLevel++
			node = parent
		}
		if (parentType) {
			return parentNodes.filter((pNode) => pNode.n === parentType)
		}
		return parentNodes
	}

	function getConditionalPrices(flattenPriceNodes, node) {
		if (!["plan", "benefitType", "benefit"].includes(node.n)) return []
		const conditionNodes = node?.ch?.filter((cNode) => {
			// just care condition node that have direct unitPrice node
			const directPriceNodes = cNode?.ch?.filter((n) => {
				return n.n === "unitPrice"
			})
			const directBandedNodes = cNode?.ch?.filter((n) => {
				return n.n === "bandedPremium"
			})
			return cNode.n === "ifCondition" && (directPriceNodes?.length || directBandedNodes?.length)
		})
		if (!conditionNodes || !conditionNodes.length) return []
		const conditionNodeIds = conditionNodes.map((cn) => cn.id)
		return flattenPriceNodes
			.filter((fNode) => {
				return conditionNodeIds.includes(fNode.id)
			})
			.map((fNode) => {
				return {
					id: fNode.id,
					insuredCount: fNode.insuredCount,
					description: getParameterValue(fNode.p, "description"),
					totalPrice: fNode.totalDirectPrice,
					directPriceDetails: fNode.directPriceDetails,
					directPricesDefinition: fNode.directPricesDefinition,
					directBandedPriceDetails: fNode.directBandedPriceDetails,
				}
			})
	}

	function getConditions(flattenTree, node) {
		const conditionNodes = getParents(flattenTree, node, "ifCondition")
		if (conditionNodes.length) {
			return conditionNodes.map((condNode) => {
				return {
					description: getParameterValue(condNode.p, "description"),
					parentLevel: condNode.parentLevel,
				}
			})
		}
		return []
	}

	function getActiveDays(startDate, endDate) {
		if (!startDate || !endDate) return null
		const start = new Date(startDate)
		const end = new Date(endDate)
		const diff = end.getTime() - start.getTime()
		const diffDays = diff / (1000 * 3600 * 24)
		return Math.floor(diffDays) + 1
	}

	function getPolicyDays(initialStartDate) {
		const startDate = initialStartDate ? new Date(initialStartDate) : new Date()
		const year = startDate.getFullYear()
		const month = startDate.getMonth()
		const day = startDate.getDate()
		const endDate = new Date(year + 1, month, day)
		return getActiveDays(startDate, endDate)
	}

	function isLeapYear(year) {
		return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0
	}

	function countPolicyYearDay(date) {
		const dateObj = new Date(date)
		const startYear = dateObj.getFullYear()
		const startMonth = dateObj.getMonth() + 1
		const startDate = dateObj.getDate()
		const expectEndYear = startYear + 1
		const expectEndDate = new Date(`${expectEndYear}-${startMonth}-${startDate}`)
		let numberOfDate = (expectEndDate.getTime() - dateObj.getTime()) / (24 * 60 * 60 * 1000)
		if (isLeapYear(startYear) && startMonth === 2 && startDate === 29) numberOfDate -= 1
		return Math.ceil(numberOfDate)
	}

	function getToday() {
		const dayObj = new Date()
		const year = dayObj.getFullYear()
		const month = (dayObj.getMonth() + 1).toString().padStart(2, "0")
		const day = dayObj.getDate().toString().padStart(2, "0")
		return `${year}-${month}-${day}`
	}

	function parseDate(date) {
		if (!date) return {}
		const parts = date.split("-")
		return {
			year: parseInt(parts[0]),
			month: parseInt(parts[1]),
			day: parseInt(parts[2]),
		}
	}

	function getPolicyMonths(startDate, endDate) {
		if (!startDate || !endDate) return null
		const parsedStartDate = parseDate(startDate)
		const parsedEndDate = parseDate(endDate)
		const additionMonth = parsedEndDate.day + 1 > parsedStartDate.day ? 1 : 0
		return parsedEndDate.year * 12 + parsedEndDate.month - (parsedStartDate.year * 12 + parsedStartDate.month) + additionMonth
	}

	function getIAlevy(startDate, endDate) {
		let levy = {}
		if (startDate <= "2017-12-31") {
			levy = {
				rate: 0,
				annualMax: 0,
			}
		} else if (startDate >= "2018-01-01" && startDate <= "2019-03-31") {
			levy = {
				rate: 0.0004,
				annualMax: 2000, // HKD
			}
		} else if (startDate >= "2019-04-01" && startDate <= "2020-03-31") {
			levy = {
				rate: 0.0006,
				annualMax: 3000, // HKD
			}
		} else if (startDate >= "2020-04-01" && startDate <= "2021-03-31") {
			levy = {
				rate: 0.00085,
				annualMax: 4250, // HKD
			}
		} else if (startDate >= "2021-04-01") {
			levy = {
				rate: 0.001,
				annualMax: 5000, // HKD
			}
		}
		const defaultPolicyMonths = 12
		const policyMonths = getPolicyMonths(startDate, endDate) || defaultPolicyMonths
		levy.max = policyMonths <= defaultPolicyMonths ? levy.annualMax : (levy.annualMax * policyMonths) / defaultPolicyMonths
		return levy
	}

	const formulasSource = {
		SUM(...args) {
			args = args.map((item) => Number(item)) // expecting each item to be a number
			const result = args.reduce((item, acc) => acc + item, 0)
			console.log({ formula: "SUM", result, args })
			return result
		},

		MULTIPLY(...args) {
			args = args.map((item) => Number(item)) // expecting each item to be a number
			const result = args.reduce((item, acc) => acc * item, 1)
			console.log({ formula: "MULTIPLY", result, args })
			return result
		},

		DIVIDE(divisor, dividend) {
			if (dividend === 0) return NaN
			const result = Number(divisor / dividend).toFixed(2)
			console.log({ formula: "DIVIDE", result, args: [divisor, dividend] })
			return result
		},

		DOB_TO_AGE(...args) {
			const dob = new Date(args?.[0])
			const today = args?.[1] ? new Date(args?.[1]) : new Date()
			const age = today.getFullYear() - dob.getFullYear()
			const mDiff = today.getMonth() - dob.getMonth()
			const dDiff = today.getDate() - dob.getDate()
			if (mDiff < 0 || (mDiff === 0 && dDiff < 0)) return age - 1

			console.log({ formula: "DOB_TO_AGE", result: age, args })
			return age
		},

		IF(condition, truthyValue, falsyValue) {
			if (condition) return truthyValue
			else return falsyValue
		},

		VLOOKUP_EXT({ args, externalTableData } = {}) {
			const tableName = args[0] // table name
			const data = externalTableData?.find((table) => table.name === tableName)?.payload ?? []
			const columnToReturn = args[1]

			const map = args.reduce((acc, arg, i) => {
				if (i < 2) return // skip first 2 args

				return { ...acc, [arg?.[0]]: arg?.[1] }
			}, {})

			const resolved = data?.rows?.find?.((row) => {
				let isMatch = true
				for (const column in map) {
					const valueToMatch = map[column]
					const valueOfRow = row?.[column]

					if (typeof valueOfRow === "string" && valueOfRow.startsWith("$range(")) {
						const rowArgs = valueOfRow
							.replaceAll("$range(", "")
							.replaceAll(")", "")
							.split(",")
							.map((i) => i.trim())
						const minRange = +rowArgs[0]
						const maxRange = +rowArgs[1]
						if (valueToMatch < minRange || valueToMatch > maxRange) isMatch = false
						// eslint-disable-next-line eqeqeq
					} else if (valueOfRow != valueToMatch) isMatch = false
				}
				return isMatch
			})?.[columnToReturn]
			if (!tableName || data.length < 1) {
				const warnMessage = `table named ${tableName} has ${data.length} item`
				!formulaLogs.warns.includes(warnMessage) && formulaLogs.warns.push(warnMessage)
			}
			console.log({ formula: "VLOOKUP_EXT", result: resolved, args })
			return resolved
		},

		VLOOKUP_MATCH(...args) {
			return args
		},

		JOIN(...args) {
			const separator = args.pop() || ""
			const result = args.join(separator)
			console.log({ formula: "JOIN", result, args })
			return result
		},

		DOB_TO_DAY(...args) {
			const dob = new Date(args?.[0])
			const today = new Date()
			const result = Math.floor((today.getTime() - dob.getTime()) / (1000 * 60 * 60 * 24))
			// console.log({ formula: "DOB_TO_DAY", result, args })
			return result
		},

		EQUAL(...args) {
			const firstArg = args?.[0]
			const result = args.every((arg) => arg === firstArg)
			// console.log({ formula: "EQUAL", result, args })
			return result
		},

		GREATER_THAN(...args) {
			console.log({ formula: "GREATER_THAN", args })
			return Boolean(Number(args?.[0]) > Number(args?.[1]))
		},

		GREATER_THAN_OR_EQUAL(...args) {
			console.log({ formula: "GREATER_THAN_OR_EQUAL", args })
			return Boolean(Number(args?.[0]) >= Number(args?.[1]))
		},

		LESS_THAN(...args) {
			console.log({ formula: "LESS_THAN", args })
			return Boolean(Number(args?.[0]) < Number(args?.[1]))
		},

		LESS_THAN_OR_EQUAL(...args) {
			console.log({ formula: "LESS_THAN_OR_EQUAL", args })
			return Boolean(Number(args?.[0]) <= Number(args?.[1]))
		},
		DAYS(...args) {
			const fromDate = new Date(args?.[0])
			const toDate = new Date(args?.[1])
			const calculateTheTime = Math.abs(fromDate?.getTime() - toDate?.getTime())
			const result = calculateTheTime / (1000 * 3600 * 24) + 1
			console.log({ formula: "DAYS", result, args })
			return result
		},
		AND(...args) {
			const truthyArgs = args.filter((arg) => !!arg)
			return truthyArgs.length === args.length
		},

		OR(...args) {
			const truthyArgs = args?.find((arg) => !!arg)
			return !!truthyArgs
		},

		COUNT_INSURED({ insureds, context, fn }) {
			const result =
				(insureds || []).filter((insured) =>
					formulaSimpleResolver({
						formula: fn,
						context: { ...insured, insureds },
						resolvers: formulasSource,
					})
				)?.length || 0
			console.log("COUNT_INSURED", { result })
			return result
		},
		COUNT_DEPENDENT({ context, fn }) {
			if (context.memberType === "dependent") return -1

			const { insureds } = context
			// for quoting run (no real members)
			if ((insureds || []).every((ins) => ins.isQuoting)) {
				return -1
			}

			// for real data
			const dependentsOfInsured = (insureds || []).filter(
				(ins) => ins.memberType === "dependent" && ins.dependentOf.find((emp) => emp.memberId === context.memberId)
			)
			const result = fn.length
				? dependentsOfInsured.filter((insured) =>
						formulaSimpleResolver({
							formula: fn,
							context: { ...insured, insureds: dependentsOfInsured },
							resolvers: formulasSource,
						})
				  )?.length
				: dependentsOfInsured.length
			console.log("COUNT_DEPENDENT", { result })

			return result
		},
		INSURED_COVERED_DURATION(insuredStartDate, insuredEndDate) {
			const fromDate = new Date(insuredStartDate)
			const toDate = new Date(insuredEndDate)
			const calculateTheTime = Math.abs(fromDate?.getTime() - toDate?.getTime())
			console.log("INSURED_COVERED_DURATION", calculateTheTime / (1000 * 3600 * 24) + 1)
			return calculateTheTime / (1000 * 3600 * 24) + 1
		},
	}

	function formulaSimpleResolver({ formula = [], context = {}, resolvers = {} }) {
		/**
		 * getProp utility - an alternative to lodash.get
		 * <AUTHOR> @muffypl, @pi0
		 * @param {Object} object
		 * @param {String|Array} path
		 * @param {*} defaultVal
		 */
		function getProp(object, path, defaultVal) {
			const _path = Array.isArray(path) ? path : path.split(".").filter((i) => i.length)

			if (!_path.length) {
				return object === undefined ? defaultVal : object
			}

			return getProp(object[_path.shift()], _path, defaultVal)
		}

		const resolvedTree = JSON.parse(JSON.stringify(formula)) // sanitise and create a copy of the formula tree

		function walk(node = {}) {
			// eslint-disable-next-line no-unused-expressions
			node?.children?.forEach((child) => walk(child))

			// Resolve based on node name
			switch (node?.name) {
				case "formula": {
					const resolver = resolvers?.[node?.props?.formulaName]
					if (node?.props?.formulaName === "DOB_TO_AGE") {
						node.resolved = resolver
							? resolver(...(node?.children?.map((item) => item.resolved) || []), context.policy.startDate)
							: null
					} else if (node?.props?.formulaName === "VLOOKUP_EXT") {
						node.resolved = resolver
							? resolver({ args: node?.children?.map((item) => item.resolved), externalTableData: context?.externalTableData })
							: null
					} else if (node?.props?.formulaName === "COUNT_INSURED") {
						node.resolved = resolver ? resolver({ insureds: context.insureds, fn: node?.children?.map((item) => item) }) : null
					} else if (node?.props?.formulaName === "COUNT_DEPENDENT") {
						node.resolved = resolver ? resolver({ context, fn: node?.children?.map((item) => item) }) : null
					} else if (node?.props?.formulaName === "INSURED_COVERED_DURATION") {
						const insuredStartDate = context.startDate || context?.policy?.startDate
						const insuredEndDate = context.endDate || context?.policy?.endDate
						if (context.startDate === undefined || context.endDate === undefined) {
							const warnMessage = "insured not having information about startDate or endDate"
							!formulaLogs.warns.includes(warnMessage) && formulaLogs.warns.push(warnMessage)
						}
						node.resolved = resolver ? resolver(insuredStartDate, insuredEndDate) : null
					} else {
						node.resolved = resolver ? resolver(...(node?.children?.map((item) => item.resolved) || [])) : null
					}
					break
				}
				case "data": {
					// TODO: find data within context object based on path of props.path
					// If no data there, return error
					// If data there, use it
					const resolved = getProp(context, node?.props?.path)
					if (resolved === undefined) {
						const warnMessage = `property named ${node?.props?.path} not exist in context`
						!formulaLogs.warns.includes(warnMessage) && formulaLogs.warns.push(warnMessage)
					}
					node.resolved = resolved
					break
				}
				case "value": {
					node.resolved = typecast(node?.props?.value)
					break
				}
				case "table": {
					node.resolved = node?.props?.tableName
					break
				}
				default:
					node.resolved = null
			}
		}

		walk(resolvedTree?.[0])

		return resolvedTree?.[0]?.resolved
	}

	/**
	 * @name makeResponse
	 * @description Convention for the reponse of the script
	 */
	function makeResponse({ isSuccess = true, data = null, error = { message: "" } }) {
		return JSON.stringify({
			isSuccess,
			data,
			error: error?.message ? error : undefined,
		})
	}

	// return true if pass all conditions
	function checkConditions(node, context) {
		let passed = true
		node.conditionNodes.forEach((condition) => {
			const expression = getParameterValue(condition.p, "expression")
			if (
				!formulaSimpleResolver({
					formula: expression,
					context: context,
					resolvers: formulasSource,
				})
			) {
				passed = false
			}
		})
		return passed
	}

	function compareArray(arr1, arr2) {
		if (!arr1 || !arr2 || arr1.length !== arr2.length) return false
		for (let i = 0; i < arr1.length; i++) {
			if (arr1[i] !== arr2[i]) return false
		}
		return true
	}

	function getAmountFromBandedPremium(node, definedSegments, context) {
		const bandedPremiumConfig = node.p?.find((p) => p.n === "bandedPremium")
		const bandedPremiumSegments = bandedPremiumConfig?.segments || []
		const mappedOptions = bandedPremiumSegments.map((seg) => {
			const segment = definedSegments?.find((s) => s.id === seg.segId)
			const options = segment?.meta?.options || []
			for (const opt of options) {
				if (
					formulaSimpleResolver({
						formula: opt.formula,
						context: context,
						resolvers: formulasSource,
					})
				) {
					return {
						...opt,
						segId: segment?.id,
						label: segment?.meta?.label,
					}
				}
			}
			return null
		})
		const mappedOptionNames = mappedOptions.map((o) => o?.name || null)
		const amount = bandedPremiumConfig.values?.find((v) => compareArray(v.conditions, mappedOptionNames))?.value || 0
		return { amount, options: mappedOptions }
	}

	try {
		let levy = {
			rate: 0.001,
			max: 5000,
		}
		let tree = representation
		if (typeof tree === "string") {
			tree = JSON.parse(tree)
		}
		let input = dataInput
		if (typeof input === "string") {
			input = JSON.parse(input)
		}

		let policy = null
		let policyDays = getPolicyDays()
		let activeDayRate = 1
		let defaultPolicyDays = countPolicyYearDay(getToday())
		const definedSegments = input.segments || []

		if (input?.policy) {
			policy = input.policy
			defaultPolicyDays = countPolicyYearDay(policy.startDate)
			policyDays = getActiveDays(policy.startDate, policy.endDate) || defaultPolicyDays
			activeDayRate = parseFloat(policyDays / defaultPolicyDays)
			levy = getIAlevy(policy.startDate, policy.endDate)
		}

		let insureds = []
		if (input && input.insureds) {
			insureds = input.insureds.map((insured) => {
				return {
					...insured,
					policy: input.policy,
					plan: input.plans?.[insured.planId] || {},
				}
			})
		}

		// Prepare data
		const formattedInsureds = { insureds }
		const copyOfTree = deepClone(tree)
		copyOfTree.forEach((treeNode) => generateParentId(treeNode, null))
		const { pricingNodes, bandedPremiumNodes } = buildPricingTree(copyOfTree)

		// Get currency and unit
		const firstPricingNode = pricingNodes?.[0]
		const currency = tree[0]?.pc || (firstPricingNode && getParameterValue(firstPricingNode.p, "currency")) || "HKD"
		const unit = firstPricingNode && getParameterValue(firstPricingNode.p, "unit")
		const unitObj = {
			unit,
			currency,
		}
		const intl = new Intl.NumberFormat("zh-HK", {
			style: "currency",
			currency: currency,
			maximumFractionDigits: 2,
			minimumFractionDigits: 2,
		})

		// for each insured => get affected pricing
		// a unit price will be affected when all parent conditional nodes be satisfied
		const pricePerInsureds = formattedInsureds.insureds.map((insured) => {
			//
			// Unit Price Affects
			//
			const priceAffects = pricingNodes
				.map((node) => {
					// For each unit price node, checking if it is applied to insured
					let affected = true
					affected = insured.planId === node.planId
					const unit = getParameterValue(node.p, "unit")
					switch (unit) {
						case "perInsured":
							// affect to all of members
							break
						case "perEmployee":
							// affect to employee
							if (insured.memberType !== "employee") {
								affected = false
							}
							break
						case "perDependent":
							if (insured.memberType !== "dependent") {
								affected = false
							}
							break
						case "perAdult":
							if (formulasSource.DOB_TO_AGE(insured.dateOfBirth, policy.startDate) < 18) {
								affected = false
							}
							break
						case "perChild":
							if (!(insured.memberType === "dependent" && insured.relationshipToEmployee === "child")) {
								affected = false
							}
							break
						case "perPolicy":
							if (node.applied) {
								affected = false
							} else {
								node.tempApplied = true
							}
							break
					}
					if (affected && node.conditionNodes && node.conditionNodes.length) {
						if (!checkConditions(node, { ...insured, insureds })) {
							affected = false
						}
					}
					let amount = getParameterValue(node.p, "amount")
					const stdAmount = amount
					const amountIsAnExpression = Array.isArray(amount)
					if (amountIsAnExpression) {
						const resolved = formulaSimpleResolver({
							formula: amount,
							context: { ...insured, insureds, externalTableData },
							resolvers: formulasSource,
						})
						amount = Number(resolved) || 0
					}
					let activeDays = policyDays
					if (policy) {
						const insuredEndDate = insured.endDate || policy.endDate
						activeDays = getActiveDays(insured.startDate, insuredEndDate)
						if (activeDays < 0) activeDays = 0
						if (!insured.startDate) activeDays = policyDays
						amount = (amount / defaultPolicyDays) * activeDays
					}
					if (node.tempApplied && affected) {
						node.applied = true
					}
					return {
						id: node.id,
						amount,
						stdAmount,
						activeDays,
						affected,
						parentId: node.parentId,
						unit,
					}
				})
				.filter((node) => node.affected)

			// Priority pricing node
			// group by parentId
			const priceAffectGroups = priceAffects.reduce((groups, node) => {
				if (!groups[node.parentId]) groups[node.parentId] = []
				groups[node.parentId].push(node)
				return groups
			}, {})
			const finalPriceAffect = []
			Object.keys(priceAffectGroups).forEach((parentId) => {
				const nodes = priceAffectGroups[parentId]
				// 0. perPolicy
				const perPolicyNode = nodes.find((node) => node.unit === "perPolicy")
				if (perPolicyNode) {
					finalPriceAffect.push(perPolicyNode)
				}
				// 1. perInsured
				const perInsuredNode = nodes.find((node) => node.unit === "perInsured")
				if (perInsuredNode) {
					finalPriceAffect.push(perInsuredNode)
					return
				}
				// 2: perChild
				const perChildNode = nodes.find((node) => node.unit === "perChild")
				if (perChildNode) {
					finalPriceAffect.push(perChildNode)
					return
				}
				// 3: perAdult
				const perAdultNode = nodes.find((node) => node.unit === "perAdult")
				if (perAdultNode) {
					finalPriceAffect.push(perAdultNode)
					return
				}
				// 4: perEmployee
				const perEmployeeNode = nodes.find((node) => node.unit === "perEmployee")
				if (perEmployeeNode) {
					finalPriceAffect.push(perEmployeeNode)
					return
				}
				// 5: perDependent
				const perDependentNode = nodes.find((node) => node.unit === "perDependent")
				if (perDependentNode) {
					finalPriceAffect.push(perDependentNode)
				}
			})

			//
			// Banded Premium Affects
			//
			const bandedPremiumAffects = bandedPremiumNodes
				.map((node) => {
					let affected = true
					affected = insured.planId === node.planId

					if (affected && node.conditionNodes && node.conditionNodes.length) {
						if (!checkConditions(node, { ...insured, insureds })) {
							affected = false
						}
					}

					return {
						...node,
						affected,
					}
				})
				.filter((node) => node.affected)
				// decide the premium that take affect
				.map((node) => {
					const { amount, options } = getAmountFromBandedPremium(node, definedSegments, { ...insured, insureds })
					return {
						...node,
						amount,
						options,
					}
				})
				// exclude banded premium node which have amount = 0
				.filter((node) => node.amount)
				// prorata
				.map((node) => {
					let amount = node.amount
					node.stdAmount = node.amount
					let activeDays = policyDays
					if (policy) {
						const insuredEndDate = insured.endDate || policy.endDate
						activeDays = getActiveDays(insured.startDate, insuredEndDate)
						if (activeDays < 0) activeDays = 0
						if (!insured.startDate) activeDays = policyDays
						amount = (amount / defaultPolicyDays) * activeDays
					}
					node.amount = amount
					return {
						...node,
						activeDays,
					}
				})
				// pick needed fields
				.map((node) => {
					return {
						id: node.id,
						amount: node.amount,
						stdAmount: node.stdAmount,
						activeDays: node.activeDays,
						parentId: node.parentId,
						bandedLabels: node.options?.map((o) => o?.label),
						bandedOptionNames: node.options?.map((o) => o?.name),
					}
				})

			// Calculate total price per insured
			let totalAmountOfInsured = finalPriceAffect.reduce((sum, priceTag) => {
				return sum + priceTag.amount
			}, 0)
			totalAmountOfInsured = bandedPremiumAffects.reduce((sum, priceTag) => {
				return sum + priceTag.amount
			}, totalAmountOfInsured)
			const accurateTotalAmountOfInsured = totalAmountOfInsured

			// Calculate Std premium of each insured (member activity)
			let stdPremium = finalPriceAffect.reduce((sum, priceTag) => {
				return sum + priceTag.stdAmount
			}, 0)
			stdPremium = bandedPremiumAffects.reduce((sum, priceTag) => {
				return sum + priceTag.stdAmount
			}, stdPremium)

			return {
				...insured,
				priceAffects: finalPriceAffect,
				bandedPremiumAffects,
				amount: roundToTwo(totalAmountOfInsured),
				accurateAmount: accurateTotalAmountOfInsured,
				stdPremium: roundToTwo(stdPremium),
			}
		})

		// Build Output
		let stack = [...copyOfTree].map((n) => {
			return {
				...n,
				level: 0,
			}
		})
		const flattenPriceNodes = []
		const flattenTree = []
		// Traversal the tree
		while (stack.length) {
			const node = stack.pop()
			flattenTree.push(node)

			let insuredCount = 0
			let totalPrice = 0
			let totalDirectPrice = 0
			let directPriceDetails = []
			let directBandedPriceDetails = []

			// Find direct unit price
			// - plan1
			//  |- unit price (this is direct unit price of 'plan1' node)
			//  |- benefitType
			//    |- unit price (this is not direct unit price of 'plan1' node, but 'benefitType')
			let directPriceNodes = []
			if (node.ch) {
				directPriceNodes = node.ch
					.filter((cNode) => {
						return cNode.n === "unitPrice"
					})
					.map((cNode) => cNode.id)
			}
			let directBandedNodes = []
			if (node.ch) {
				directBandedNodes = node.ch
					.filter((cNode) => {
						return cNode.n === "bandedPremium"
					})
					.map((cNode) => cNode.id)
			}

			pricePerInsureds.forEach((insured) => {
				// Total price
				// Sum of all nested unitPrice node deeply
				const priceTags = insured.priceAffects.filter((pa) => {
					return node?.pricingNodes?.includes(pa.id) || false
				})
				const bandedPriceTags = insured.bandedPremiumAffects.filter((b) => {
					return node?.bandedPremiumNodes?.includes(b.id) || false
				})

				if (priceTags.length || bandedPriceTags.length) {
					insuredCount = insuredCount + 1
					const priceTagsAmount = priceTags.reduce((acc, ptag) => {
						return +parseFloat(acc) + ptag.amount
					}, 0)
					const bandedPriceTagsAmount = bandedPriceTags.reduce((acc, btag) => {
						return +parseFloat(acc) + btag.amount
					}, 0)
					totalPrice = totalPrice + roundToTwo(priceTagsAmount + bandedPriceTagsAmount)
				}

				//
				// Direct price
				// Sum of price of direct nested unitPrice node
				//

				// Direct unit prices
				const directPriceTags = insured.priceAffects.filter((pa) => {
					return directPriceNodes?.includes(pa.id) || false
				})
				if (directPriceTags.length) {
					totalDirectPrice = directPriceTags.reduce((acc, ptag) => {
						return +parseFloat(acc) + ptag.amount
					}, totalDirectPrice)
					directPriceDetails = directPriceTags.reduce((acc, ptag) => {
						acc.push({
							nodeId: ptag.id,
							amount: ptag.amount,
							unit: ptag.unit,
							activeDays: ptag.activeDays,
						})
						return acc
					}, directPriceDetails)
				}

				// Direct banded prices
				const directBandedTags = insured.bandedPremiumAffects.filter((b) => {
					return directBandedNodes?.includes(b.id) || false
				})
				if (directBandedTags.length) {
					totalDirectPrice = directBandedTags.reduce((acc, btag) => {
						return +parseFloat(acc) + btag.amount
					}, totalDirectPrice)
					directBandedPriceDetails = directBandedTags.reduce((acc, btag) => {
						acc.push({
							nodeId: btag.id,
							amount: btag.amount,
							activeDays: btag.activeDays,
							bandedLabels: btag.bandedLabels,
							bandedOptionNames: btag.bandedOptionNames,
						})
						return acc
					}, directBandedPriceDetails)
				}
			})

			let directBandedPremiumNodes = bandedPremiumNodes?.filter((n) => directBandedNodes.includes(n.id)) || []
			directBandedPremiumNodes = directBandedPremiumNodes.map((bpn) => {
				const clonedNode = deepClone(bpn)
				const bandedPremiumConfig = clonedNode.p?.find((p) => p.n === "bandedPremium")
				const bandedPremiumSegments = bandedPremiumConfig?.segments || []
				const segments = bandedPremiumSegments.map((seg) => {
					const segment = definedSegments?.find((s) => s.id === seg.segId)
					return segment
				})
				return {
					labels: segments.map((s) => s?.meta?.label),
					values: bandedPremiumConfig.values.map((v) => {
						const insuredCount =
							directBandedPriceDetails.filter((d) => {
								return d.nodeId === clonedNode.id && compareArray(d.bandedOptionNames, v.conditions)
							})?.length || 0
						return {
							...v,
							prorataValue: roundToTwo(v.value * activeDayRate),
							insuredCount,
						}
					}),
				}
			})

			node.directBandedPremiumNodes = directBandedPremiumNodes
			node.totalPrice = roundToTwo(totalPrice)
			node.insuredCount = insuredCount
			node.totalDirectPrice = roundToTwo(totalDirectPrice)
			node.directPriceDetails = directPriceDetails
			node.directBandedPriceDetails = directBandedPriceDetails
			node.directPricesDefinition = getDirectPricesDefinition(node, activeDayRate)
			if (["plan", "benefitType", "benefit"].includes(node.n)) {
				node.conditions = getConditions(flattenTree, node)
			}
			flattenPriceNodes.push(node)

			if (node.ch && node.ch.length) {
				const children = node.ch
					.filter((cNode) => cNode.__isGhost !== true)
					.filter((cNode) => handlingNodeTypes.includes(cNode.n))
					.reverse()
					.map((n) => ({
						...n,
						level: node.level + 1,
					}))
				stack = [...stack, ...children]
			}
		}

		// Total Price - from 'product' node
		const productNode = flattenPriceNodes.find((node) => node.n === "root") || flattenPriceNodes[0]
		const totalPriceAllPlans = {
			amount: productNode.totalPrice,
			...unitObj,
		}
		const formatedTotalPriceAllPlans = intl.format(totalPriceAllPlans.amount)
		const totalInsureds = productNode.insuredCount

		// Total Premium
		let levyAmount = roundToTwo(totalPriceAllPlans.amount * levy.rate)
		levyAmount = levyAmount > levy.max ? levy.max : levyAmount
		const totalPremium = {
			amount: roundToTwo(totalPriceAllPlans.amount + levyAmount),
			...unitObj,
		}
		const formatedTotalPremium = intl.format(totalPremium.amount)

		// Levy flat
		const levyFlat = {
			amount: levyAmount,
			...unitObj,
		}
		const levyPercentage = parseFloat(levy.rate * 100).toFixed(3)
		const formatedLevyFlat = intl.format(levyFlat.amount)

		// Price Details
		// The flatten price nodes look like
		// [
		//   planNode1,
		//   benefitTypeNode1,
		//   benefitNode1,
		//   benefitNode2,
		//   planNode2,
		//   benefitTypeNode2,
		//   benefitNode3,
		//   benefitNode4,
		// ]

		// The pricingDetails will put the plan node under benefit/benefitTypeNode
		// [
		//   benefitTypeNode1,
		//   benefitNode1,
		//   benefitNode2,
		//   planNode1, (subTotalEachPlan)
		//   benefitTypeNode2,
		//   benefitNode3,
		//   benefitNode4,
		//   planNode2, (subTotalEachPlan)
		// ]
		let tempPlan = null
		const priceDetails = flattenPriceNodes
			.filter((fpNode) => {
				return ["plan", "benefitType", "benefit"].includes(fpNode.n)
			})
			.reduce((res, fpNode, idx) => {
				if (fpNode.n === "plan") {
					if (tempPlan) {
						res = [
							...res,
							{
								...pickPricingDetails(tempPlan, flattenPriceNodes),
								label: tempPlan?.p?.find((i) => i.n === "name")?.v + " Subtotal",
								name: "subTotalEachPlan",
								nodeName: "subTotalEachPlan",
							},
						]
						tempPlan = fpNode
					} else {
						tempPlan = fpNode
					}
					const label = fpNode?.p?.find((i) => i.n === "name")?.v
					res = [...res, { ...pickPricingDetails(fpNode, flattenPriceNodes), label }]
				}
				if (fpNode.n === "benefitType" || fpNode.n === "benefit") {
					const label = fpNode?.p?.find((i) => i.n === "name")?.dv
					res = [...res, { ...pickPricingDetails(fpNode, flattenPriceNodes), label }]
				}
				return res
			}, [])
		priceDetails.push({
			...pickPricingDetails(tempPlan, flattenPriceNodes),
			label: tempPlan?.p?.find((i) => i.n === "name")?.v + " Subtotal",
			name: "subTotalEachPlan",
			nodeName: "subTotalEachPlan",
		})

		// Plans
		const plans = priceDetails.filter((pdNode) => pdNode.name === "plan")
		const output = {
			// Totals
			totalPriceAllPlans,
			formatedTotalPriceAllPlans,
			totalPremium,
			formatedTotalPremium,
			totalInsureds,
			// Levy
			levy: levy.rate,
			levyPercentage,
			levyFlat,
			formatedLevyFlat,
			// others
			priceDetails,
			pricePerInsureds,
			plans,
			currency,
			unit,
			summary: {
				totalPriceAllPlans,
				formatedTotalPriceAllPlans,
				totalPremium,
				formatedTotalPremium,
				totalInsureds,
				levy: levy.rate,
				levyPercentage,
				levyFlat,
				formatedLevyFlat,
				currency,
				unit,
			},
			formulaLogs,
		}
		return makeResponse({
			isSuccess: true,
			data: output,
		})
	} catch (error) {
		console.log(error)
		return makeResponse({
			isSuccess: false,
			formulaLogs,
			error: {
				message: error.message,
			},
		})
	}
}
