{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "title": "The root schema", "description": "The root schema comprises the entire JSON document.", "default": {}, "examples": [{"field1": "Value1", "field2": "Value2"}], "required": ["field1", "field2"], "properties": {"field1": {"$id": "#/properties/field1", "type": "string", "title": "The field1 schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["Value1"]}, "field2": {"$id": "#/properties/field2", "type": "string", "title": "The field2 schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["Value2"]}}, "additionalProperties": true}