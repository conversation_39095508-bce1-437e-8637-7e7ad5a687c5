using CoverGo.Products.Client;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration;

[Collection("Sequential")]
public class ScriptTests : TestBase
{
    #region LatestPricingScriptType
    [Fact]
    public async Task GIVEN_product_with_standardPricing_WHEN_get_latestScriptType_THEN_pricing_standard_is_returned()
    {
        ProductId productId = await SetupSampleStandardPricingScenario();
        products_ResultOfScriptTypeEnum result = await Products.GetLatestPricingScriptType(new products_ProductIdInput { plan = productId.Plan, version = productId.Version, type = productId.Type });
        result.isSuccess.Should().BeTrue();
        result.value.Should().Be(products_ScriptTypeEnum.PRICING_STANDARD);
    }

    [Fact]
    public async Task GIVEN_product_with_old_pricing_version_WHEN_get_latestScriptType_THEN_pricing_is_returned()
    {
        ProductId productId = await SetupSampleStandardPricingScenario(scriptTypeEnum: ScriptTypeEnum2.Pricing);
        products_ResultOfScriptTypeEnum result = await Products.GetLatestPricingScriptType(new products_ProductIdInput { plan = productId.Plan, version = productId.Version, type = productId.Type });
        result.isSuccess.Should().BeTrue();
        result.value.Should().Be(products_ScriptTypeEnum.PRICING);
    }

    [Fact]
    public async Task GIVEN_product_with_no_pricing_script_WHEN_get_latestScriptType_THEN_error_is_returned()
    {
        ProductId productId = new()
        {
            Plan = Guid.NewGuid().ToString(),
            Version = "1",
            Type = Guid.NewGuid().ToString()
        };
        const string representation = "";
        await Products.CreateProduct(new CreateProductCommand
        {
            ProductId = productId,
            Representation = representation
        });

        products_ResultOfScriptTypeEnum result = await Products.GetLatestPricingScriptType(new products_ProductIdInput { plan = productId.Plan, version = productId.Version, type = productId.Type });
        result.isSuccess.Should().BeFalse();
    }
    #endregion

    #region evaluateScript
    [Fact]
    public async Task GIVEN_product_is_missing_WHEN_evaluate_script_THEN_ProductMissingErrorReturns()
    {
        var command = new EvaluateProductScriptCommand();
        var result = await Scripts.Evaluate(
            Guid.NewGuid().ToString(), command);
        result.Errors.Should().Contain("Product with this id does not exist");
    }

    [Fact]
    public async Task GIVEN_product_is_missing_WHEN_evaluate_script_v2_THEN_ProductMissingErrorReturns()
    {
        var command = new EvaluateProductScriptCommand();
        var result = await Scripts.EvaluateV2(
            Guid.NewGuid().ToString(), command);
        result.Errors.Should().Contain("Product with this id does not exist");
    }
    #endregion

    #region CalculatePricing
    [Fact(Skip = "Need to update after new billing changes")]
    public async Task GIVEN_existing_product_and_script_WHEN_calculatePricing_THEN_result_is_returned()
    {
        ProductId productId = await SetupSampleStandardPricingScenario();

        string dataInput = "{\\\"insureds\\\":[{\\\"age\\\":20}],\\\"plan\\\":{\\\"planId\\\":2},\\\"policy\\\":{\\\"startDate\\\":\\\"2024-05-13T00:00:00.000Z\\\",\\\"endDate\\\":\\\"2024-05-14T00:00:00.000Z\\\"}}";
        var modes = new List<products_ModePropertiesInput> {
            new products_ModePropertiesInput {
                meta = null,
                count = 1,
                billing = new products_BillingInput
                {
                    billingFrequency = products_BillingFrequency.ANNUALLY,
                    billingMode = products_BillingMode.POLICY_YEAR,
                    modalFactor = 1,
                    modalFee = 1,
                }
            }
        };
        products_PricingResult pricing = await Products.CalculatePricing(productId, dataInput, modes);
        pricing.data.Should().NotBeNull();
    }

    [Fact]
    public async Task GIVEN_existing_product_and_script_WHEN_calculatePricing_with_uwLoadings_THEN_it_should_send_uwLoadings()
    {
        var productId = await SetupSampleStandardPricingScenario("export default function(input) { return { errors:{message:JSON.stringify(input.uwLoadings), type:'SCRIPT_ERROR'}, data: { meta:{}, modes:[{ meta:{}, properties:{meta:{}, count:0, billing:{}}, pricing:{ effects:[], totals:{}, summary:{}, commissions:{} }, insureds:[], billing:{}  }], totals:{} } } }");
        var pricing = await Products.CalculatePricing(productId, dataInput: "{}", modes: [], uwLoadings: new() { loadings = [new() { id = "pl1", factor = 1d, amount = null }] });
        pricing.errors.message.Should().Be("{\"loadings\":[{\"id\":\"pl1\",\"factor\":1}],\"members\":[]}");
    }
    [Fact]
    public async Task GIVEN_existing_product_and_script_and_campaign_WHEN_calculatePricing_with_campaignCode_THEN_pricing_should_be_input_with_campaign()
    {
        var productId = await SetupSampleStandardPricingScenario("export default function(input) { return { errors:{message:JSON.stringify(input.campaigns), type:'SCRIPT_ERROR'}, data: { meta:{}, modes:[{ meta:{}, properties:{meta:{}, count:0, billing:{}}, pricing:{ effects:[], totals:{}, summary:{}, commissions:{} }, insureds:[], billing:{}  }], totals:{} } } }");
        var (_, distributorID, campaignCode, campaignID, effectiveFrom, effectiveTo) = await this.SetupCampaign(new() { Type = productId.Type, Plan = productId.Plan, Version = productId.Version });
        var startDate = DateTimeOffset.UtcNow.ToString("yyyy-MM-dd");
        var endDate = DateTimeOffset.UtcNow.AddYears(1).ToString("yyyy-MM-dd");
        var dataInput = EscapeJsonString(JsonSerializer.Serialize(new { policy = new { startDate, endDate } }));

        var pricing = await Products.CalculatePricing(productId, dataInput, modes: [], campaignCodes: [campaignCode], distributorId: distributorID);

        pricing.errors.message.Should().Be("[{\"campaignID\":\"" + campaignID + "\",\"campaignCode\":\"" + campaignCode + "\",\"discount\":1,\"premiumCalculationType\":\"gross\",\"effectiveFrom\":\"" + effectiveFrom.ToString("yyyy-MM-dd") + "\",\"effectiveTo\":\"" + effectiveTo.ToString("yyyy-MM-dd") + "\"}]");
    }
    static readonly JsonSerializerOptions options = new() { PropertyNamingPolicy = JsonNamingPolicy.CamelCase, Converters = { new JsonEnumMemberStringEnumConverter() } };
    [Fact]
    public async Task GIVEN_existing_product_and_script_WHEN_calculatePricing_with_agentId_THEN_pricing_should_be_input_with_commission()
    {
        var productId = await SetupSampleStandardPricingScenario("export default function(input) { return { errors:{message:JSON.stringify(input.commissions), type:'SCRIPT_ERROR'}, data: { meta:{}, modes:[{ meta:{}, properties:{meta:{}, count:0, billing:{}}, pricing:{ effects:[], totals:{}, summary:{}, commissions:{} }, insureds:[], billing:{}  }], totals:{} } } }");
        var (_, distributorID, commissionID, productAvailabilityID) = await this.SetupCommission(new() { Type = productId.Type, Plan = productId.Plan, Version = productId.Version });
        var startDate = DateTimeOffset.UtcNow.ToString("yyyy-MM-dd");
        var endDate = DateTimeOffset.UtcNow.AddYears(1).ToString("yyyy-MM-dd");
        var dataInput = EscapeJsonString(JsonSerializer.Serialize(new { policy = new { startDate, endDate } }));

        // Act (When)
        var pricingN = await Products.CalculatePricing(productId, dataInput, modes: [], distributorId: distributorID, agentIds: null);
        var pricing0 = await Products.CalculatePricing(productId, dataInput, modes: [], distributorId: distributorID, agentIds: []);
        var pricing1 = await Products.CalculatePricing(productId, dataInput, modes: [], distributorId: distributorID, agentIds: ["PrimaryAgentId"]);
        var pricing2 = await Products.CalculatePricing(productId, dataInput, modes: [], distributorId: distributorID, agentIds: ["PrimaryAgentId", "SecondaryAgentId"]);
        var commissionsN = JsonSerializer.Deserialize<List<Commission>>(pricingN.errors.message, options);
        var commissions0 = JsonSerializer.Deserialize<List<Commission>>(pricing0.errors.message, options);
        var commissions1 = JsonSerializer.Deserialize<List<Commission>>(pricing1.errors.message, options);
        var commissions2 = JsonSerializer.Deserialize<List<Commission>>(pricing2.errors.message, options);

        commissionsN[0].Rules.Should().HaveCount(2, "if agentId is null, all commissions should be returned (for backward compatibility)");
        commissions0.Should().BeEmpty("if no agentIds is provided, no commission should be returned");
        commissions1[0].Rules.Should().HaveCount(1, "if only primary agentId is provided, only that agent's commission should be returned");
        commissions2[0].Rules.Should().HaveCount(2, "if both agentIds are provided, all commissions should be returned");
    }
    #endregion

    #region EvaluateUnderwriting
    [Fact]
    public async Task GIVEN_existing_product_and_UnderwritingScript_WHEN_evaluatingUnderwriting_THEN_result_is_returned()
    {
        ProductId productId = new()
        {
            Plan = Guid.NewGuid().ToString(),
            Version = "1",
            Type = Guid.NewGuid().ToString()
        };
        string representation = "";
        await Products.CreateProduct(new CreateProductCommand
        {
            ProductId = productId,
            Representation = representation
        });

        string scriptId = await Products.CreateScript(new CreateScriptCommand
        {
            Type = ScriptTypeEnum2.Underwriting_standard,
            SourceCode = "import G from\"decimal.js\";var d=class extends Error{type=\"SCRIPT_ERROR\";details;constructor(e={errorType:\"\",errorMessage:\"\"}){super(e.errorMessage),this.details=e}},p=class extends Error{type=\"INPUT_DATA_VALIDATION_ERROR\";issues;constructor(e){super(\"Input data validation error\"),this.issues=e}};import A from\"decimal.js\";import{ZodError as N}from\"zod\";function l(t,e){return o=>{let n,{dataInput:s,modes:r,options:c,externalTableData:u,representationInput:i}=o;try{n=e?e(s):JSON.parse(s),n.externalTableData=u}catch(a){return a instanceof N?{error:new p(a.issues)}:{error:new d({errorType:typeof a,errorMessage:a instanceof Error?a.message:\"Unknown Error\"})}}if(i){let a;try{a=JSON.parse(i)}catch(f){return{error:new d({errorType:typeof f,errorMessage:f instanceof Error?f.message:\"Unknown Error\"})}}return t({representation:a,context:n,modes:r,options:c})}return t({context:n,modes:r,options:c})}}function E(t){let{insured:e,context:o,options:n,uwInsuredHandlers:s}=t,r={id:e.id,decision:\"Accepted\",referToUnderwriter:!1,remarks:[],preExistingConditions:[],exclusions:[],type:\"auto\"};n!=null&&n.benefitUnderwriting?r.benefitsUnderwriting=[]:r.loadings=[];for(let c of s){let u=c({insured:e,context:o});if(u){let i=u.next();for(;i.done!==!0;)T(r,i,n),i=u.next()}}return r}function y(t){let{uwInsuredHandlers:e}=t;return n=>{let{context:s,options:r}=n,{insureds:c}=s,u=[],i;for(let a of c)u.push(E({insured:a,context:s,options:r,uwInsuredHandlers:e}));return{data:{insureds:i?[]:u},errors:i}}}import m from\"decimal.js\";function x(t,e){return{type:t,value:e}}function w(t,e){let o=t.benefitsUnderwriting.find(n=>{var s;return(n==null?void 0:n.id)===((s=e.value)==null?void 0:s.benefitId)});return o||(o=I(e),t.benefitsUnderwriting.push(o)),o}function I(t){return{id:t.value.benefitId,name:t.value.id,decision:\"Accepted\",referToUnderwriter:!1,loadings:[],type:\"auto\"}}function g(t){return{...t.value}}function b(t,e,o){var s;let n=g(e);o!=null&&o.benefitUnderwriting?(s=w(t,e).loadings)==null||s.push(n):t.loadings.push(n)}function C(t,e,o){if(o!=null&&o.benefitUnderwriting){let n=w(t,e);n.loadings=[{...g(e),value:m.sum(...n.loadings.map(s=>new m(s.value))).toNumber()}]}else t.loadings=[{...g(e),value:m.sum(...t.loadings.map(n=>new m(n.value))).toNumber()}]}function v(t,e,o){let{value:n}=e;function s(r){r.decision!==\"Rejected\"&&(n.type===\"Rejected\"?(r.decision=n.type,r.type=\"auto\",r.referToUnderwriter=!1):r.decision!==\"Manual\"&&n.type===\"Manual\"&&(r.referToUnderwriter=!0,r.type=\"manual\",delete r.decision))}if(o!=null&&o.benefitUnderwriting){let r=w(t,e);s(r)}else s(t)}function T(t,e,o){({loading:b,aggregateLoadings:C,decision:v})[e.value.type](t,e.value,o)}var P=function*(t){let{insured:e}=t;yield x(\"loading\",{id:\"Test\",name:\"Test\",method:\"factor\",value:e.manualUWValue?200:100,benefitId:\"benefitId\"}),e.manualUWValue&&(yield x(\"decision\",{id:\"benefitId\",name:\"Decision\",type:\"Manual\",benefitId:\"benefitId\"}))},D=y({uwInsuredHandlers:[P]}),ve=l(D);export{ve as default,D as execute};"
        });

        await Products.AddScriptToProduct(new AddScriptToProductCommand
        {
            ProductId = productId,
            ScriptId = scriptId
        });

        var insuredsInput = new List<products_InsuredUnderwritingInput>{new products_InsuredUnderwritingInput
        {
            id = "1",
            meta = "{{placeholder}}"
        } };

        string metaInput = "{ manualUWValue : false}";

        products_UnderwritingResult underwriting = await Products.EvaluateUnderwriting(productId, null, insuredsInput, metaInput);
        underwriting.data.insureds.Should().NotBeNull();
        underwriting.data.insureds.First().referToUnderwriter.Should().BeFalse();
        underwriting.data.insureds.First().loadings.First().value.Should().Be(100);
    }

    [Fact(Skip = "Need to update after new billing changes")]
    public async Task GIVEN_existing_product_and_script_and_nonexistent_campaign_WHEN_calculatePricing_THEN_error_is_returned()
    {
        ProductId productId = await SetupSampleStandardPricingScenario();

        string dataInput = "{\\\"insureds\\\":[{\\\"age\\\":20}],\\\"plan\\\":{\\\"planId\\\":2},\\\"policy\\\":{\\\"startDate\\\":\\\"2024-05-13T00:00:00.000Z\\\",\\\"endDate\\\":\\\"2024-05-14T00:00:00.000Z\\\"}}";
        var modes = new List<products_ModePropertiesInput> {
            new products_ModePropertiesInput {
                meta = null,
                count = 1,
                billing = new products_BillingInput
                {
                    billingFrequency = products_BillingFrequency.ANNUALLY,
                    billingMode = products_BillingMode.POLICY_YEAR,
                    modalFactor = 1,
                    modalFee = 1,
                }
            }
        };
        products_PricingResult pricing = await Products.CalculatePricing(productId, dataInput, modes, Guid.NewGuid().ToString(), [Guid.NewGuid().ToString()]);
        pricing.errors.Should().NotBeNull();
        pricing.errors.type.Should().Be(products_ScriptEvaluationErrorType.INPUT_DATA_VALIDATION_ERROR);
    }
    #endregion

    private const string defaultStandardPricingScript =
        """import e from"zod";import t from"decimal.js";var ScriptError2=class extends Error{type="SCRIPT_ERROR";details;constructor(e={errorType:"",errorMessage:""}){super(e.errorMessage),this.details=e}},InputDataValidationError2=class extends Error{type="INPUT_DATA_VALIDATION_ERROR";issues;constructor(e){super("Input data validation error"),this.issues=e}};import n from"decimal.js";var exhaustiveMatchingGuard=e=>t=>{throw Error(e)},DateOutOfRangeError=class extends Error{modeDates;effectDates;constructor(e,t){let[n,s]=e,[r,i]=t,a=`${n.toISOString().substring(0,10)} - ${s.toISOString().substring(0,10)}`,o=`${r.toISOString().substring(0,10)} - ${i.toISOString().substring(0,10)}`;super(`Dates do not overlap. ${a} and ${o}`),this.modeDates=e,this.effectDates=t}};function millisecondsToDays(e){return Math.ceil(e/864e5)}function daysBetween(e,t){let n=e.getTime(),s=t.getTime();return millisecondsToDays(Math.abs(s-n))}function getOverlappingDays(e,t){let[n,s]=e,[r,i]=t,a=new Date(Math.max(n.getTime(),r.getTime())),o=new Date(Math.min(s.getTime(),i.getTime()));if(a>o)throw new DateOutOfRangeError(e,t);let u=Math.abs(o.getTime()-a.getTime()),l=millisecondsToDays(u);return l}var frequencyMultiplier={perMonth:12,perQuarter:4,perAnnum:1},daysInPaymentFrequency={perMonth:30,perQuarter:90,perAnnum:365};function translateValueFrequency(e,t,n,s){let r=e.times(frequencyMultiplier[t]).dividedBy(365),i;if("range"===n.type&&(null==s?void 0:s.type)==="range")i=getOverlappingDays([n.startDate,n.endDate],[s.startDate,s.endDate]);else{let a;switch(n.type){case"range":a=daysBetween(n.startDate,n.endDate);break;case"days":a=n.days;break;case"frequency":a=daysInPaymentFrequency[n.value]}let o=1/0;switch(null==s?void 0:s.type){case"range":o=daysBetween(s.startDate,s.endDate);break;case"days":o=s.days;break;case"frequency":o=daysInPaymentFrequency[s.value]}i=Math.min(a,o)}return r.times(i)}function applyFactorAdjustment(e,t,s){let r=new n(e.total),i=new n(e[s]),a=r.times(t.factor),o=a.minus(r);e[s]=i.plus(o).toNumber(),"commissions"!==s&&(e.total=r.plus(o).toNumber())}function applyFlatAdjustment(e,t,s,r){let i=new n(e[s]),a=new n(e.total),o=new n(t.value),u=r?translateValueFrequency(o,"perAnnum",r):o;e[s]=i.plus(u).toNumber(),"commissions"!==s&&(e.total=a.plus(u).toNumber())}function premiumEffect(e,t,s){let r=new n(e.premiums),i=new n(e.total),a=translateValueFrequency(new n(t.value),t.frequency,s.period,t.period);e.premiums=r.plus(a).toNumber(),e.total=i.plus(a).toNumber()}function discountEffect(e,t){if("factor"===t.type){applyFactorAdjustment(e,t,"discounts");return}if("flat"===t.type){t.value=-Math.abs(t.value),applyFlatAdjustment(e,t,"discounts");return}exhaustiveMatchingGuard(`Missing levy type ${t}.`)(t)}function taxEffect(e,t){return"factor"===t.type?applyFactorAdjustment(e,t,"taxes"):"flat"===t.type?applyFlatAdjustment(e,t,"taxes"):exhaustiveMatchingGuard(`Missing levy type ${t}.`)(t)}function vatEffect(e,t){return applyFactorAdjustment(e,t,"vat")}function feeEffect(e,t){return"factor"===t.type?applyFactorAdjustment(e,t,"fees"):"flat"===t.type?applyFlatAdjustment(e,t,"fees"):exhaustiveMatchingGuard(`Missing levy type ${t}.`)(t)}function factorEffect(e,t){return applyFactorAdjustment(e,t,"factors")}function loadingEffect(e,t){return"factor"===t.type?applyFactorAdjustment(e,t,"loadings"):"flat"===t.type?applyFlatAdjustment(e,t,"loadings"):exhaustiveMatchingGuard(`Missing loading type ${t}.`)(t)}function roundEffect(e,t){let{mode:s,precision:r,fields:i}=t,a="all"===i?Object.keys(e):i,o=new n(0);for(let u of a){if("roundings"===u||"total"===u)continue;let l=new n(e[u]),c={up:n.ROUND_UP,down:n.ROUND_DOWN,nearest:n.ROUND_HALF_UP},f=l.toDecimalPlaces(r,c[s]),m=l.minus(f);e.roundings=new n(e.roundings).plus(m).toNumber(),e[u]=f.toNumber(),o=o.plus(e[u])}return e.total=o.toNumber(),e}function commissionEffect(e,t){if("factor"===t.type){let s=new n(e.total),r=new n(e.commissions);e.commissions=r.plus(s.times(t.factor)).toNumber();return}if("flat"===t.type){let i=new n(e.commissions);e.commissions=i.plus(t.value).toNumber();return}return exhaustiveMatchingGuard(`Missing levy type ${t}.`)(t)}function applyEffect(e,t,s){let r={premium:premiumEffect,discount:discountEffect,tax:taxEffect,vat:vatEffect,fee:feeEffect,loading:loadingEffect,round:roundEffect,commission:commissionEffect,factor:factorEffect},i=Object.assign({},t.totals);for(let a of e)r[t.type](a,t.value,s);e[e.length-1]&&(t.difference.premiums=new n(t.totals.premiums).minus(i.premiums).toNumber(),t.difference.total=new n(t.totals.total).minus(i.total).toNumber(),t.difference.factors=new n(t.totals.factors).minus(i.factors).toNumber(),t.difference.taxes=new n(t.totals.taxes).minus(i.taxes).toNumber(),t.difference.fees=new n(t.totals.fees).minus(i.fees).toNumber(),t.difference.loadings=new n(t.totals.loadings).minus(i.loadings).toNumber(),t.difference.roundings=new n(t.totals.roundings).minus(i.roundings).toNumber(),t.difference.discounts=new n(t.totals.discounts).minus(i.discounts).toNumber(),t.difference.commissions=new n(t.totals.commissions).minus(i.commissions).toNumber())}function newPricingEffect(e,t,n){return{type:e,value:t,meta:n}}function newTotals(){return{premiums:0,total:0,factors:0,vat:0,taxes:0,fees:0,loadings:0,roundings:0,discounts:0,commissions:0,limits:0}}function sumTotals(e,t){e.premiums=new n(e.premiums).plus(t.premiums).toNumber(),e.total=new n(e.total).plus(t.total).toNumber(),e.factors=new n(e.factors).plus(t.factors).toNumber(),e.taxes=new n(e.taxes).plus(t.taxes).toNumber(),e.vat=new n(e.vat).plus(t.vat).toNumber(),e.fees=new n(e.fees).plus(t.fees).toNumber(),e.loadings=new n(e.loadings).plus(t.loadings).toNumber(),e.roundings=new n(e.roundings).plus(t.roundings).toNumber(),e.discounts=new n(e.discounts).plus(t.discounts).toNumber(),e.commissions=new n(e.commissions).plus(t.commissions).toNumber()}import{ZodError as s}from"zod";function shouldShowLevel(e,t){return!!("modes"===t.detail&&["modes"].includes(e)||"insureds"===t.detail&&["modes","insureds"].includes(e)||"benefits"===t.detail&&["modes","insureds","benefits"].includes(e))}function*runBenefitHandlers(e,t){if(t)for(let n of t){let s=newTotals(),r={meta:{},pricing:{totals:newTotals()}},{options:i,...a}=e;i.showPricingEffects&&(r.pricing.effects=[]);let o=n(a);if(o){let u=o.next();for(;;){if(!0===u.done){u.value&&(r.meta=u.value instanceof Function?u.value(structuredClone(r.pricing)):u.value);break}let l={type:u.value.type,value:u.value.value,totals:Object.assign({},s),difference:newTotals()};applyEffect([r.pricing.totals,l.totals],l,e.mode),s=l.totals,l.meta=u.value.meta instanceof Function?u.value.meta(Object.assign({},l.totals)):u.value.meta,i.showPricingEffects&&r.pricing.effects&&r.pricing.effects.push(l),u=o.next()}}summerizeTotals(r.pricing),yield r}}function runInsuredHandlers(e,t){let{mode:n,totals:s,benefits:r,context:i,insured:a,representation:o,options:u}=e,l={pricing:{totals:s}};if(u.showPricingEffects&&(l.pricing.effects=[]),!t)return l;let c=l.pricing.totals;for(let f of t){let m=f({mode:n,context:i,insured:a,nodes:o});if(void 0===m)continue;let p=m.next();for(;;){if(!0===p.done){let d=p.value;if(d){let g=d instanceof Function?d(structuredClone(l.pricing),structuredClone(r)):d;l.meta={insured:a,...g}}break}let y={type:p.value.type,value:p.value.value,totals:Object.assign({},c),difference:newTotals()};applyEffect([l.pricing.totals,y.totals],y,n),c=y.totals,y.meta=p.value.meta instanceof Function?p.value.meta(Object.assign({},l.pricing.totals)):p.value.meta,u.showPricingEffects&&l.pricing.effects&&l.pricing.effects.push(y),p=m.next()}}return shouldShowLevel("benefits",u)&&(l.benefits=r),summerizeTotals(l.pricing),l}function calculateInsureds(e){let t=[],{modeTotals:n,representation:s,benefitHandlers:r,insuredHandlers:i,modeProperty:a,context:o,options:u}=e,l=o.insureds??Array(a.count);for(let c of l){let f=newTotals(),m=runBenefitHandlers({mode:a,representation:s,context:o,insured:c,options:u},r),p=[];for(let d of m)sumTotals(f,d.pricing.totals),shouldShowLevel("benefits",u)&&p.push(d);let g=runInsuredHandlers({totals:f,benefits:p,insured:c,mode:a,representation:s,context:o,options:u},i);t.push(g),sumTotals(n,g.pricing.totals)}return t}function runPolicyHandlers(e,t){let{modeProperty:n,context:s,modeTotals:r,insureds:i,options:a}=e,o={pricing:{totals:r},properties:n};if(a.showPricingEffects&&(o.pricing.effects=[]),shouldShowLevel("insureds",a)&&(o.insureds=i),!t)return o;for(let u of t){let l=o.pricing.totals,c=u({mode:o,context:s});if(c){let f=c.next();for(;;){if(!0===f.done){let m=f.value;m&&(o.meta=m instanceof Function?m(structuredClone(o.pricing),i):m);break}let p={type:f.value.type,value:f.value.value,totals:newTotals(),difference:newTotals()};p.totals=Object.assign({},l),applyEffect([o.pricing.totals,p.totals],p,n),l=p.totals,p.meta=f.value.meta instanceof Function?f.value.meta(Object.assign({},o.pricing.totals)):f.value.meta,a.showPricingEffects&&o.pricing.effects&&o.pricing.effects.push(p),f=c.next()}}}return summerizeTotals(o.pricing),o}function summerizeTotals(e){let n=e.totals,s=new t(n.total).minus(n.vat).minus(n.fees).minus(n.taxes);e.summary={vat:n.vat,gross:n.total,net:s.toNumber(),taxes:n.taxes,discounts:n.discounts,commissions:n.commissions}}function exportPricingScript(e,t){return n=>{let r,{dataInput:i,modes:a,options:o,externalTableData:u,representationInput:l}=n;try{(r=t?t(i):JSON.parse(i)).externalTableData=u}catch(c){if(c instanceof s){let f=new InputDataValidationError2(c.issues);return{error:f}}return{error:new ScriptError2({errorType:typeof c,errorMessage:c instanceof Error?c.message:"Unknown Error"})}}if(l){let m;try{m=JSON.parse(l)}catch(p){return{error:new ScriptError2({errorType:typeof p,errorMessage:p instanceof Error?p.message:"Unknown Error"})}}return e({representation:m,context:r,modes:a,options:o})}return e({context:r,modes:a,options:o})}}function createPricingExecutionFunction(e){let{insuredHandlers:t,benefitHandlers:n,policyHandlers:s}=e,r=e=>{let{modes:r,context:i,representation:a,options:o}=e,u=newTotals(),l=[],c={},f;for(let m of r)try{let p=newTotals(),d=calculateInsureds({modeTotals:p,representation:a,insuredHandlers:t,benefitHandlers:n,modeProperty:m,context:i,options:o}),g=runPolicyHandlers({modeTotals:p,modeProperty:m,insureds:d,options:o,context:i},s);l.push(g),sumTotals(u,g.pricing.totals)}catch(y){y instanceof ScriptError2&&(f=y),y instanceof Error&&(f=new ScriptError2({errorType:typeof y,trace:JSON.stringify(m),errorMessage:y.message}))}let w={totals:u,meta:c};return shouldShowLevel("modes",o)&&(w.modes=l),{data:f?{totals:newTotals(),meta:c}:w,errors:f}};return r}var schema=e.object({insureds:e.array(e.object({age:e.number()})),plan:e.object({planId:e.union([e.literal(1),e.literal(2)])}),policy:e.object({startDate:e.coerce.date(),endDate:e.coerce.date()})}),parseInput=function(e){let t=JSON.parse(e);return schema.parse(t)};function assertInsured(e){if(!e)throw Error("Insured is required")}var VAT=newPricingEffect("tax",{type:"factor",factor:1.2},{type:"VAT"}),plan1Premium=newPricingEffect("premium",{type:"premium",value:1e3,frequency:"perAnnum"},{name:"plan 1 base premium"}),plan2Premium=newPricingEffect("premium",{type:"premium",value:2e3,frequency:"perAnnum"},{name:"plan 2 base premium"}),demoPremium=e=>newPricingEffect("premium",{type:"premium",value:e,frequency:"perAnnum"},{name:"plan 2 base premium"}),ageFactor=e=>(assertInsured(e),newPricingEffect("factor",{type:"factor",factor:e.age,frequency:"perAnnum"},{name:"age factor"})),insuredDiscountFactor=e=>{let t=Math.abs(1-e/100);return newPricingEffect("discount",{type:"factor",factor:t},{description:"15% Discount"})},benefit1=function*(e){let{context:t,insured:n}=e;return 1===t.plan.planId?yield plan1Premium:2===t.plan.planId&&(yield plan2Premium),yield ageFactor(n),yield VAT,{name:"Benefit 1"}},benefit2=function*(){yield demoPremium(500),yield VAT},benefit3=function*(){yield demoPremium(1500),yield VAT},insured1=function*(){yield insuredDiscountFactor(15)},billingsAndCollectionsMeta=function*(){yield insuredDiscountFactor(10)},execute=createPricingExecutionFunction({benefitHandlers:[benefit1,benefit2,benefit3],insuredHandlers:[insured1],policyHandlers:[billingsAndCollectionsMeta]}),basic_default=exportPricingScript(execute,parseInput);export{basic_default as default,execute};""";
    private async Task<ProductId> SetupSampleStandardPricingScenario(string pricingScript = defaultStandardPricingScript, ScriptTypeEnum2 scriptTypeEnum = ScriptTypeEnum2.Pricing_standard)
    {
        ProductId productId = new()
        {
            Plan = Guid.NewGuid().ToString(),
            Version = "1",
            Type = Guid.NewGuid().ToString()
        };
        const string representation = "";
        await Products.CreateProduct(new CreateProductCommand
        {
            ProductId = productId,
            Representation = representation
        });

        string scriptId = await Products.CreateScript(new CreateScriptCommand
        {
            Type = scriptTypeEnum,
            SourceCode = pricingScript
        });

        await Products.AddScriptToProduct(new AddScriptToProductCommand
        {
            ProductId = productId,
            ScriptId = scriptId
        });
        return productId;
    }

    // `{"a":1}` → `{\"a\":1}` (the \ is actually a character, not an escape character ~ C# "{\\\"a\\\":1}")
    static string EscapeJsonString(string s)
    {
        s = JsonSerializer.Serialize(s);
        // remove the double ""{...}"" → "{...}"
        return s[1..^1];
    }
}
