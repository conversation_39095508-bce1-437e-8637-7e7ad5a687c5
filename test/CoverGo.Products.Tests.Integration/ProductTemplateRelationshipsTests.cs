﻿using CoverGo.Gateway.Client;
using CoverGo.Products.Tests.Integration.Utils;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration
{
    [Collection("Sequential")]
    public class ProductTemplateRelationshipsTests : TestBase
    {
        [Fact]
        public async Task GIVEN_product_added_WHEN_add_templateRelationship_to_it_THEN_succeed_and_product_contains_it()
        {
            productId productId = await Products.Create();

            string templateId = await CreateTemplate();

            const string action = "test action";

            await AddTemplateRelationshipToProduct(productId, templateId, action);

            templateRelationship templateRelationship = (await SearchProductById(productId))
                .list!.Single()!.templateRelationships!.First()!;

            templateRelationship.template!.id.Should().BeEquivalentTo(templateId);
            templateRelationship.action.Should().BeEquivalentTo(action);
            templateRelationship.id.Should().NotBeNullOrWhiteSpace();
        }

        [Fact]
        public async Task GIVEN_product_with_few_templates_WHEN_filter_THEN_returns_product_with_right_template()
        {
            productId productId = await Products.Create();

            string templateId = await CreateTemplate();

            await AddTemplateRelationshipToProduct(productId, templateId, "test action");

            const string updatedAction = "test action 2";

            await AddTemplateRelationshipToProduct(productId, templateId, updatedAction);

            templateRelationship templateRelationship = (await SearchProductByIdAndFilterTemplates(productId, updatedAction))
                .templateRelationships!.First()!;

            templateRelationship.template!.id.Should().BeEquivalentTo(templateId);
            templateRelationship.action.Should().BeEquivalentTo(updatedAction);
            templateRelationship.id.Should().NotBeNullOrWhiteSpace();
        }

        [Fact]
        public async Task GIVEN_product_with_templateRelationship_added_WHEN_remove_templateRelationship_THEN_succeed_and_product_does_not_contain_templateRelationship()
        {
            productId productId = await Products.Create();

            string templateId = await CreateTemplate();

            await AddTemplateRelationshipToProduct(productId, templateId, "test action");

            templateRelationship templateRelationship = (await SearchProductById(productId))
                .list!.First()!.templateRelationships!.First()!;

            await RemoveTemplateRelationshipFromProduct(productId, templateRelationship.id!);

            ICollection<templateRelationship> templateRelationships = (await SearchProductById(productId))
                .list!.First()!.templateRelationships!;

            templateRelationships.Should().BeNullOrEmpty();
        }


        async Task<string> CreateTemplate()
        {
            string name = CreateNewGuid();

            string mutation = new covergoMutationBuilder()
                .createSmsTemplate(new covergoMutationBuilder.createSmsTemplateArgs(new createSmsTemplateInput { name = name }), new resultBuilder()
                    .WithAllFields())
                .Build();

            await _gatewayGraphQlHttpClient.SendMutationAndEnsureAsync<result>(mutation);

            string query = new covergoQueryBuilder()
                .templates(new covergoQueryBuilder.templatesArgs(where: new templateWhereInput { name = name }), new templatesBuilder()
                    .list(new templateInterfaceBuilder()
                        .id()))
                    .Build();

            templates response = await _gatewayGraphQlHttpClient.SendQueryAndEnsureAsync<templates>(query);
            return response.list!.Single()!.id!;
        }

        Task AddTemplateRelationshipToProduct(productId productId, string templateId, string action)
        {
            addTemplateRelationshipToProductInput input = new()
            {
                productId = Products.ProductIdToInput(productId),
                templateId = templateId,
                action = action
            };

            string mutation = new covergoMutationBuilder()
                .addTemplateRelationshipToProduct(new covergoMutationBuilder.addTemplateRelationshipToProductArgs(input), new resultBuilder()
                    .WithAllFields())
                .Build();

            return _gatewayGraphQlHttpClient.SendMutationAndEnsureAsync<result>(mutation);
        }

        Task RemoveTemplateRelationshipFromProduct(productId productId, string templateRelationshipId)
        {
            removeTemplateRelationshipFromProductInput input = new()
            {
                productId = Products.ProductIdToInput(productId),
                templateRelationshipId = templateRelationshipId,
            };

            string mutation = new covergoMutationBuilder()
                .removeTemplateRelationshipFromProduct(new covergoMutationBuilder.removeTemplateRelationshipFromProductArgs(input), new resultBuilder()
                    .WithAllFields())
                .Build();

            return _gatewayGraphQlHttpClient.SendMutationAndEnsureAsync<result>(mutation);
        }


        Task<products> SearchProductById(productId id)
        {
            string query = new covergoQueryBuilder()
                .products_2(new covergoQueryBuilder.products_2Args(where: new productWhereInput
                {
                    productId = new productIdWhereInput
                    {
                        plan = id.plan,
                        type = id.type,
                        version = id.version
                    }
                }), new productsBuilder()
                    .list(new productBuilder()
                        .templateRelationships(new productBuilder.templateRelationshipsArgs(), new templateRelationshipBuilder()
                            .id()
                            .template(new templateInterfaceBuilder()
                                .id())
                            .action())))
                .Build();

            return _gatewayGraphQlHttpClient.SendQueryAndEnsureAsync<products>(query);
        }

        async Task<product> SearchProductByIdAndFilterTemplates(productId id, string templateAction)
        {
            string query = new covergoQueryBuilder()
                .products_2(new covergoQueryBuilder.products_2Args(where: new productWhereInput { productId = new productIdWhereInput { plan = id.plan, type = id.type, version = id.version } }), new productsBuilder()
                    .list(new productBuilder()
                        .templateRelationships(new productBuilder.templateRelationshipsArgs(new templateRelationshipWhereInput { action = templateAction }), new templateRelationshipBuilder()
                            .id()
                            .template(new templateInterfaceBuilder()
                                .id())
                            .action())))
                .Build();

            products response = await _gatewayGraphQlHttpClient.SendQueryAndEnsureAsync<products>(query);

            return response.list!.First()!;
        }



    }
}
