fragment fieldInfo on products_ScriptSchemaPlanField {
  type
  meta {
    required
    fieldType
    label
    validations
    minLength
    maxLength
    numeric
    options {
      key
      name
      value
    }
  }
}

query GetProductPlanDetails($plan: String! $type: String! $version: String!) {
  productProductPlanDetails(input: {
    productId: {
      plan:  $plan
      type: $type
      version: $version
    }
  }) {
    planDetails {
      id
      name
      fields {
        key
        value {
          ...fieldInfo
        }
      }
    }
    insuredsFields {
      key
      value {
        ...fieldInfo
      }
    }
    memberCustomSchema {
      key
      value {
        ...fieldInfo
      }
    }
    policyFields {
      key
      value {
        ...fieldInfo
      }
    }
    attachedDocuments {
      id
      name
      type
      logicalId
      category
    }
  }
}
