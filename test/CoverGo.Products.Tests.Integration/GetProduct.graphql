query GetProduct($plan: String!, $type: String!, $version: String!) {
  product(productId: { plan: $plan, type: $type, version: $version }) {
    id {
      plan
      type
      version
    }
    name
    lifecycleStage
    policyIssuanceMethod
    offerValidityPeriod
    autoRenewal
    renewalNotification
    taxConfiguration {
      referenceMnemonic
      mnemonicCode
      allowOverrideAtOfferStage
      allowOverrideAtProposalStage
    }
    fields {
      key
      value
    }
    termsAndConditionsTemplateId
    termsAndConditionsJacketId
  }
}
