using System.Net;
using System.Threading.Tasks;

using CoverGo.Products.Tests.Integration.Support;

namespace CoverGo.Products.Tests.Integration;

public class ApiStartupTest(ProductsWebApplicationFactory webApplicationFactory) : IClassFixture<ProductsWebApplicationFactory>
{
    [Fact]
    public async Task ApiCanStart()
    {
        // Arrange (Given)
        var client = webApplicationFactory.CreateClient();
        
        // Act (When)
        var response = await client.GetAsync("/startupz");

        // Assert (Then)
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }
}
