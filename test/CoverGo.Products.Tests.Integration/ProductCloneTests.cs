#nullable enable

using System;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

using CoverGo.Gateway.Client;
using CoverGo.Products.Client;
using CoverGo.Products.Tests.Integration.Support;

using FluentAssertions.Execution;

using ProductId = CoverGo.Products.Domain.Products.ProductId;

namespace CoverGo.Products.Tests.Integration;

[Trait("Ticket", "CH-7546")]
[Collection("Sequential")]
public class ProductCloneTests : ApiTestBase, IClassFixture<ProductsWebApplicationFactory>
{
    public ProductCloneTests(ProductsWebApplicationFactory webApplicationFactory) : base(webApplicationFactory)
    {
    }

    [Fact]
    public async Task CloneProductSavesProduct()
    {
        // Arrange (Given)
        var createProductResult = await GatewayClient.CreateProduct.ExecuteAsync(new()
        {
            ProductId = new()
            {
                Plan = CreateNewGuid(),
                Type = CreateNewGuid(),
                Version = CreateNewGuid()
            },
            Representation = File.OpenText("ProductPlanDetailsTests_MultiplePlansRepresentation.json").ReadToEnd(),
        });
        createProductResult.Errors.Should().BeEmpty();
        var productId = createProductResult.Data!.CreateProduct!.ProductId;

        var scriptCreationResult = await Scripts.Create(new Client.CreateScriptCommand
        {
            Type = Client.ScriptTypeEnum2.Pricing,
            InputSchema = File.OpenText("ProductPlanDetailsTests_MultiplePlansSchema.json").ReadToEnd(),
            Name = "pricing",
            SourceCode = "var a = 0;"
        });
        await Products.AddScriptToProduct(new Client.AddScriptToProductCommand
        {
            ProductId = new Client.ProductId
            {
                Plan = productId.Plan,
                Type = productId.Type,
                Version = productId.Version
            },
            ScriptId = scriptCreationResult.Value.Id
        });

        // Act (When)
        var result = await ProductsClient.CloneProduct.ExecuteAsync(
            new()
            {
                Plan = productId.Plan,
                Type = productId.Type,
                Version = productId.Version,
            },
            "1.0_custom",
            true);
        result.Errors.Should().BeEmpty();

        // Assert (Then)
        var clonedProductId = new ProductId
        {
            Plan = result.Data!.CloneProductVersion!.ClonedProductId.Plan,
            Type = result.Data!.CloneProductVersion!.ClonedProductId.Type,
            Version = result.Data!.CloneProductVersion!.ClonedProductId.Version,
        };
        clonedProductId.Should().Be(new ProductId
        {
            Type = productId.Type,
            Plan = productId.Plan,
            Version = "1.0_custom",
        });
        var clonedProductResult = await GatewayClient.GetProducts.ExecuteAsync(
            new()
            {
                Plan = clonedProductId.Plan,
                Type = clonedProductId.Type,
                Version = clonedProductId.Version,
            });
        clonedProductResult.Errors.Should().BeEmpty();

        using var scope = new AssertionScope();
        clonedProductResult.Data!.Products_2!.List.Count.Should().Be(1);
        var clonedProductObj = clonedProductResult.Data!.Products_2!.List[0];
        clonedProductObj.Name.Should().Be(productId.Plan);
        clonedProductObj.Scripts.Count.Should().Be(1);
    }

    [Fact]
    public async Task GIVEN_product_added_WHEN_clone_THEN_create_product_with_same_content()
    {
        // Arrange (Given)
        productId productId = await Products.Create();
        var status = CreateNewGuid();

        var changeEffectiveDate = DateTime.Parse("2021-05-01", CultureInfo.InvariantCulture);
        var productUpdateResult = await ProductsRestClient.Products_UpdateAsync(
            TenantId,
            new()
            {
                ProductId = new()
                {
                    Plan = productId.plan,
                    Type = productId.type,
                    Version = productId.version,
                },
                ChangeEffectiveDate = changeEffectiveDate,
                IsChangeEffectiveDateChanged = true,
                Status = status,
                IsStatusChanged = true,
                OfferValidityPeriod = TimeSpan.FromDays(30),
                PolicyIssuanceMethod = PolicyIssuanceMethod.ManualAfterPremiumReceipt,
                IsPolicyIssuanceMethodChanged = true,
                IsOfferValidityPeriodChanged = true
            });
        productUpdateResult.Errors_2.Should().BeNullOrEmpty();

        productId cloneProductId = new()
        {
            plan = "Clone " + productId.plan,
            type = productId.type
        };

        // Act (When)
        await ProductsRestClient.Products_CloneAsync(TenantId, new()
        {
            CloneProductId = new()
            {
                Plan = "Clone " + productId.plan,
                Type = productId.type,
            },
            ProductId = new()
            {
                Plan = productId.plan,
                Type = productId.type,
                Version = productId.version,
            }
        });

        // Assert (Then)
        var clonedProductId = new ProductId
        {
            Plan = cloneProductId.plan,
            Type = cloneProductId.type,
            Version = cloneProductId.version,
        };
        var clonedProductResult = await GatewayClient.GetProducts.ExecuteAsync(
            new()
            {
                Plan = clonedProductId.Plan,
                Type = clonedProductId.Type,
                Version = clonedProductId.Version,
            });
        clonedProductResult.Errors.Should().BeEmpty();

        using var scope = new AssertionScope();
        clonedProductResult.Data!.Products_2!.List.Count.Should().Be(1);
        var clonedProductObj = clonedProductResult.Data!.Products_2!.List[0];
        clonedProductObj.Should().NotBeNull();
        clonedProductObj.Status.Should().Be(status);
        clonedProductObj.ChangeEffectiveDate.Should().Be(changeEffectiveDate);


        var clonedProductFromRestEndpoint = await Products.GetProductByProductId(
            new Client.ProductId
            {
                Plan = clonedProductId.Plan,
                Type = clonedProductId.Type,
                Version = clonedProductId.Version
            });

        clonedProductFromRestEndpoint.OfferValidityPeriod.Should().Be(TimeSpan.FromDays(30));
        clonedProductFromRestEndpoint.PolicyIssuanceMethod.Should().Be(PolicyIssuanceMethod.ManualAfterPremiumReceipt);
    }

    [Fact]
    public async Task GIVEN_product_with_scripts_added_WHEN_clone_THEN_create_product_with_same_content_and_scripts_with_new_scriptId()
    {
        // Arrange (Given)
        var productId = GenerateProductId();

        await Products.CreateProduct(new()
        {
            ProductId = productId,
            Representation = "[{\"id\":\"030067989066408396\",\"props\":{\"theme\":\"#e4b917\"},\"productType\":\"gm\",\"minified\":true,\"ch\":[{\"id\":\"27852147936577465\",\"readonly\":false,\"ch\":[{\"id\":\"2983000711101427\",\"ch\":[],\"n\":\"unitPrice\",\"p\":[{\"n\":\"amount\",\"l\":\"Amount\",\"c\":\"Number\",\"d\":\"number\",\"v\":1000},{\"n\":\"currency\",\"l\":\"Currency\",\"c\":\"Select\",\"d\":\"string\",\"v\":\"HKD\"},{\"n\":\"unit\",\"l\":\"Unit\",\"c\":\"Select\",\"d\":\"string\",\"v\":\"perPolicy\"}]},{\"id\":\"4678364068681702\",\"n\":\"unitPrice\",\"p\":[{\"n\":\"amount\",\"l\":\"Amount\",\"c\":\"Number\",\"d\":\"number\",\"v\":1500},{\"n\":\"currency\",\"l\":\"Currency\",\"c\":\"Select\",\"d\":\"string\",\"v\":\"HKD\"},{\"n\":\"unit\",\"l\":\"Unit\",\"c\":\"Select\",\"d\":\"string\",\"v\":\"perInsured\"}]},{\"id\":\"5709595095072255\",\"n\":\"unitPrice\",\"p\":[{\"n\":\"amount\",\"l\":\"Amount\",\"c\":\"Number\",\"d\":\"number\",\"v\":900},{\"n\":\"currency\",\"l\":\"Currency\",\"c\":\"Select\",\"d\":\"string\",\"v\":\"HKD\"},{\"n\":\"unit\",\"l\":\"Unit\",\"c\":\"Select\",\"d\":\"string\",\"v\":\"perEmployee\"}]},{\"id\":\"0005106318174872282\",\"ch\":[],\"n\":\"unitPrice\",\"p\":[{\"n\":\"amount\",\"l\":\"Amount\",\"c\":\"Number\",\"d\":\"number\",\"v\":100},{\"n\":\"currency\",\"l\":\"Currency\",\"c\":\"Select\",\"d\":\"string\",\"v\":\"HKD\"},{\"n\":\"unit\",\"l\":\"Unit\",\"c\":\"Select\",\"d\":\"string\",\"v\":\"perChild\"}]}],\"n\":\"plan\",\"p\":[{\"n\":\"id\",\"l\":\"ID\",\"c\":\"Text\",\"d\":\"string\",\"v\":\"PLAN_20220425\"},{\"n\":\"name\",\"l\":\"Name\",\"c\":\"Text\",\"d\":\"string\",\"v\":\"PRODUCT_20220425_01_PLAN\"}]}],\"n\":\"root\",\"pc\":\"HKD\"}]",
            OfferValidityPeriod = TimeSpan.FromDays(30)
        });

        var scriptId = await Products.CreateScript(new CreateScriptCommand
        {
            Name = "pricing",
            InputSchema = "{\\\"properties\\\":{},\\\"type\\\":\\\"object\\\",\\\"policyFields\\\":{\\\"properties\\\":{\\\"startDate\\\":{\\\"type\\\":\\\"string\\\",\\\"meta\\\":{\\\"label\\\":\\\"Start Date\\\",\\\"component\\\":\\\"CDatePicker\\\",\\\"required\\\":true,\\\"fixed\\\":true,\\\"order\\\":1}},\\\"endDate\\\":{\\\"type\\\":\\\"string\\\",\\\"meta\\\":{\\\"label\\\":\\\"End Date\\\",\\\"component\\\":\\\"CDatePicker\\\",\\\"required\\\":false,\\\"fixed\\\":true,\\\"order\\\":2}}}},\\\"insuredsFields\\\":{\\\"properties\\\":{\\\"numberOfInsureds\\\":{\\\"type\\\":\\\"number\\\",\\\"meta\\\":{\\\"label\\\":\\\"Number of Insureds\\\",\\\"component\\\":\\\"JInputNumber\\\",\\\"required\\\":true,\\\"order\\\":1,\\\"fixed\\\":true}},\\\"planSelected\\\":{\\\"type\\\":\\\"string\\\",\\\"meta\\\":{\\\"label\\\":\\\"Plan Selected\\\",\\\"component\\\":\\\"JSelect\\\",\\\"required\\\":true,\\\"options\\\":[{\\\"key\\\":\\\"27852147936577465\\\",\\\"name\\\":\\\"PRODUCT_20220425_01_PLAN\\\",\\\"value\\\":\\\"MSIG_20220425\\\"}],\\\"order\\\":2,\\\"fixed\\\":true}}}}}",
            OutputSchema = "",
            SourceCode = ProductCrudTests.GetPricingScript(),
            Type = ScriptTypeEnum2.Pricing
        });

        await Products.AddScriptToProduct(new Client.AddScriptToProductCommand
        {
            ProductId = productId,
            ScriptId = scriptId
        });


        productId cloneProductId = new()
        {
            plan = "Clone " + productId.Plan,
            type = productId.Type,
            version = productId.Version
        };

        Gateway.Client.productId productIdToClone = new Gateway.Client.productId()
        {
            plan = productId.Plan,
            type = productId.Type,
            version = productId.Version
        };

        // Act (When)
        await Products.CloneProduct(productIdToClone, cloneProductId);

        // Assert (Then)
        var clonedProductId = new ProductId
        {
            Plan = cloneProductId.plan,
            Type = cloneProductId.type,
            Version = cloneProductId.version,
        };
        var clonedProductResult = await GatewayClient.GetProducts.ExecuteAsync(
            new()
            {
                Plan = clonedProductId.Plan,
                Type = clonedProductId.Type,
                Version = clonedProductId.Version,
            });
        clonedProductResult.Errors.Should().BeEmpty();

        using var scope = new AssertionScope();
        clonedProductResult.Data!.Products_2!.List.Count.Should().Be(1);
        var clonedProductObj = clonedProductResult.Data!.Products_2!.List[0];
        clonedProductObj.Should().NotBeNull();
        clonedProductObj.Scripts.Should().NotBeNull();
        clonedProductObj.Scripts.First().Should().NotBe(null);
        clonedProductObj.Scripts.First().Should().NotBe(scriptId);
    }

    [Fact]
    public async Task GIVEN_product_added_WHEN_clone_with_name_THEN_create_product_with_localised_name()
    {
        // Arrange (Given)
        productId productId = await Products.Create();
        productId cloneProductId = new()
        {
            plan = "Clone " + productId.plan,
            type = productId.type
        };
        upsertL10nCloneProductNameInput name = new()
        {
            value = CreateNewGuid(),
            locale = "en-US"
        };

        // Act (When)
        await Products.CloneProduct(productId, cloneProductId, name);

        // Assert (Then)
        var clonedProductResult = await GatewayClient.GetProducts.ExecuteAsync(
            new()
            {
                Plan = cloneProductId.plan,
                Type = cloneProductId.type,
                Version = cloneProductId.version,
            });
        clonedProductResult.Errors.Should().BeEmpty();

        using var scope = new AssertionScope();
        clonedProductResult.Data!.Products_2!.List.Count.Should().Be(1);
        var clonedProductObj = clonedProductResult.Data!.Products_2!.List[0];
        clonedProductObj.Name.Should().Be(name.value);
    }

    [Fact]
    public async Task GIVEN_product_added_WHEN_clone_with_duplicate_productId_THEN_fails()
    {
        // Arrange (Given)
        productId productId = await Products.Create();
        productId cloneProductId = new()
        {
            plan = productId.plan,
            type = "Clone " + productId.type,
            version = productId.version
        };

        var cloneResult = await Products.CloneProduct(productId, cloneProductId);

        // Act (When)
        result result = await Products.CloneProduct(productId, cloneProductId);

        // Assert (Then)
        result.status.Should().Be("failure");
        result.errors?.First().Should().Be("This plan with this version already exists");
    }
}
