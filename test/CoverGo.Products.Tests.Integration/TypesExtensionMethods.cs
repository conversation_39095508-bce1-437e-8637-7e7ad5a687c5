﻿using System.Text.RegularExpressions;

namespace CoverGo.Products.Tests.Integration
{
    static class TypesExtensionMethods
    {
        /// <summary>
        /// Escape strings so it can be passed to graphql client as an argument
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static string Escape(this string input) =>
            input.Replace(@"""", @"\""").Replace("\n", "").Replace("\r", "");

        public static string RemoveWhitespaces(this string input) =>
    Regex.Replace(input, @"\s", "");

    }
}
