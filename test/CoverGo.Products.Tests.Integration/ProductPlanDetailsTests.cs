using System.IO;
using System.Threading.Tasks;

using CoverGo.Products.Client;
using CoverGo.Products.Tests.GatewayClient;
using CoverGo.Products.Tests.Integration.ProductsStrawberryClient;
using CoverGo.Products.Tests.Integration.Support;

using StrawberryShake;

namespace CoverGo.Products.Tests.Integration;

[Collection("Sequential")]
[Trait("Ticket", "CH-1644")]
[Trait("Ticket", "CH-1645")]
[Trait("Ticket", "CH-1646")]
public class ProductPlanDetailsTests : ApiTestBase, IClassFixture<ProductsWebApplicationFactory>
{
    public ProductPlanDetailsTests(ProductsWebApplicationFactory webApplicationFactory) : base(webApplicationFactory)
    {
    }

    [Fact]
    public async Task ProductWithMultiplePlansReturnsPlanFields()
    {
        // Arrange (Given)
        var productId = await GivenProduct();

        // Act (When)
        var productPlanDetails = await WhenGetProductPlanDetails(productId);

        // Assert (Then)
        productPlanDetails.PlanDetails.Should()
            .HaveCount(4).And
            .AllSatisfy(x => x.Fields.Should()
                .NotBeNullOrEmpty()
                .And.AllSatisfy(field => field.Value.Meta.Should().NotBeNull()));
    }

    [Fact]
    public async Task ProductReturnsPolicyFields()
    {
        // Arrange (Given)
        var productId = await GivenProduct();

        // Act (When)
        var productPlanDetails = await WhenGetProductPlanDetails(productId);

        // Assert (Then)
        productPlanDetails.PolicyFields.Should()
            .NotBeNullOrEmpty()
            .And.HaveCount(2)
            .And.AllSatisfy(field => field.Value.Meta.Should().NotBeNull());
    }

    [Fact]
    public async Task ProductReturnsInsuredsFields()
    {
        // Arrange (Given)
        var productId = await GivenProduct();

        // Act (When)
        var productPlanDetails = await WhenGetProductPlanDetails(productId);

        // Assert (Then)
        productPlanDetails.InsuredsFields.Should()
            .NotBeNullOrEmpty()
            .And.HaveCount(13)
            .And.AllSatisfy(field => field.Value.Meta.Should().NotBeNull());
    }

    [Fact]
    public async Task ProductReturnsMemberCustomSchema()
    {
        // Arrange (Given)
        var productId = await GivenProduct();

        // Act (When)
        var productPlanDetails = await WhenGetProductPlanDetails(productId);

        // Assert (Then)
        productPlanDetails.MemberCustomSchema.Should()
            .NotBeNullOrEmpty()
            .And.HaveCount(11)
            .And.AllSatisfy(field => field.Value.Meta.Should().NotBeNull());
    }

    [Fact]
    public async Task ProductWithoutScriptsReturnsEmptyPlanFields()
    {
        // Arrange (Given)
        var productId = await GivenProductWithoutScripts();

        // Act (When)
        var productPlanDetails = await WhenGetProductPlanDetails(productId);

        // Assert (Then)
        productPlanDetails.PlanDetails.Should()
            .HaveCount(4).And
            .AllSatisfy(x => x.Fields.Should().BeEmpty());
    }

    [Fact]
    public async Task ProductWithoutScriptsReturnsEmptyPolicyFields()
    {
        // Arrange (Given)
        var productId = await GivenProductWithoutScripts();

        // Act (When)
        var productPlanDetails = await WhenGetProductPlanDetails(productId);

        // Assert (Then)
        productPlanDetails.PolicyFields.Should().BeEmpty();
    }

    [Fact]
    public async Task ProductWithoutScriptsReturnsEmptyInsuredsFields()
    {
        // Arrange (Given)
        var productId = await GivenProductWithoutScripts();

        // Act (When)
        var productPlanDetails = await WhenGetProductPlanDetails(productId);

        // Assert (Then)
        productPlanDetails.InsuredsFields.Should().BeEmpty();
    }

    private async Task<ICreateProduct_CreateProduct_ProductId> GivenProduct()
    {
        var productId = await GivenProductWithoutScripts();

        var scriptCreationResult = await Scripts.Create(new Client.CreateScriptCommand
        {
            Type = Client.ScriptTypeEnum2.Pricing,
            InputSchema = File.OpenText("ProductPlanDetailsTests_MultiplePlansSchema.json").ReadToEnd(),
            Name = "pricing",
            SourceCode = "var a = 0;"
        });
        await Products.AddScriptToProduct(new Client.AddScriptToProductCommand
        {
            ProductId = new Client.ProductId
            {
                Plan = productId.Plan,
                Type = productId.Type,
                Version = productId.Version
            },
            ScriptId = scriptCreationResult.Value.Id
        });
        return productId;
    }

    private async Task<ICreateProduct_CreateProduct_ProductId> GivenProductWithoutScripts()
    {
        var createProductResult = await GatewayClient.CreateProduct.ExecuteAsync(new()
        {
            ProductId = new()
            {
                Plan = CreateNewGuid(),
                Type = CreateNewGuid(),
                Version = CreateNewGuid()
            },
            Representation = File.OpenText("ProductPlanDetailsTests_MultiplePlansRepresentation.json").ReadToEnd(),
        });
        createProductResult.EnsureNoErrors();
        var productId = createProductResult.Data!.CreateProduct!.ProductId;
        return productId;
    }

    private async Task<IGetProductPlanDetails_ProductProductPlanDetails> WhenGetProductPlanDetails(ICreateProduct_CreateProduct_ProductId productId)
    {
        var result = await this.ProductsClient.GetProductPlanDetails.ExecuteAsync(productId.Plan, productId.Type, productId.Version);
        result.Errors.Should().BeEmpty();
        return result.Data!.ProductProductPlanDetails!;
    }
}
