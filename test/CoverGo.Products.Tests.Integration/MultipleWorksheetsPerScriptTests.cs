using System;
using System.Threading.Tasks;

using CoverGo.Products.Client;

namespace CoverGo.Products.Tests.Integration;

[Collection("Sequential")]
public class MultipleWorksheetsPerScriptTests : TestBase
{
    [Fact]
    public async Task GIVEN_script_with_multiple_worksheets_WHEN_evaluating_THEN_expected_result_returned()
    {
        string externalTableUrl1 = $"externalTables/{Guid.NewGuid().ToString()}.txt";
        string externalTableUrl2 = $"externalTables/{Guid.NewGuid().ToString()}.txt";
        await FileSystem.Initialize();
        await FileSystem.Upload(externalTableUrl1, "{\"message\":\"hello\"}");
        await FileSystem.Upload(externalTableUrl2, "{\"message\":\"world\"}");

        var (productId, _) = await CreateProductWithScriptAsync(new CreateScriptCommand
        {
            SourceCode = $@"export function execute({{ externalTablesData }}) {{
                const {{ message: message1 }} = JSON.parse(externalTablesData['{externalTableUrl1}']);
                const {{ message: message2 }} = JSON.parse(externalTablesData['{externalTableUrl2}']);
                return `${{message1}} ${{message2}}`;
            }}",
            ExternalTableDataUrls = new() { externalTableUrl1, externalTableUrl2 }
        });

        ScriptResult result = await EvaluateProductScript(productId);

        result.Should().NotBeNull();
        result.Status.Should().Be("success");
        result.Value.Should().Be("hello world");
    }

    [Fact]
    public async Task GIVEN_script_with_multiple_worksheets_v2_WHEN_evaluating_THEN_expected_result_returned()
    {
        string externalTableUrl1 = $"externalTables/{Guid.NewGuid().ToString()}.txt";
        string externalTableUrl2 = $"externalTables/{Guid.NewGuid().ToString()}.txt";
        await FileSystem.Initialize();
        await FileSystem.Upload(externalTableUrl1, "{\"message\":\"hello\"}");
        await FileSystem.Upload(externalTableUrl2, "{\"message\":\"world\"}");

        var (productId, _) = await CreateProductWithScriptAsync(new CreateScriptCommand
        {
            SourceCode = $@"export function execute({{ externalTableData, externalTablesData }}) {{
                const {{ message: message1 }} = JSON.parse(externalTableData);
                const {{ message: message2 }} = JSON.parse(externalTablesData['{externalTableUrl2}']);
                return `${{message1}} ${{message2}}`;
            }}",
            ExternalTableDataUrl = externalTableUrl1,
            ExternalTableDataUrls = new() { externalTableUrl2 }
        });

        ScriptResult result = await EvaluateProductScript(productId);

        result.Should().NotBeNull();
        result.Status.Should().Be("success");
        result.Value.Should().Be("hello world");
    }

    [Fact]
    public async Task GIVEN_an_existent_product_with_a_worksheet_WHEN_adding_an_additional_worksheet_and_evaluating_THEN_expected_result_returned()
    {
        string externalTableUrl1 = $"externalTables/{Guid.NewGuid().ToString()}.txt";
        string externalTableUrl2 = $"externalTables/{Guid.NewGuid().ToString()}.txt";
        await FileSystem.Initialize();
        await FileSystem.Upload(externalTableUrl1, "{\"message\":\"hello\"}");
        await FileSystem.Upload(externalTableUrl2, "{\"message\":\"world\"}");

        var (productId, scriptId) = await CreateProductWithScriptAsync(new CreateScriptCommand
        {
            SourceCode = $@"export function execute({{ externalTableData, externalTablesData }}) {{
                const {{ message: message1 }} = JSON.parse(externalTableData);
                const {{ message: message2 }} = JSON.parse(externalTablesData['{externalTableUrl2}']);
                return `${{message1}} ${{message2}}`;
            }}",
            ExternalTableDataUrl = externalTableUrl1
        });

        await Products.UpdateScript(new UpdateScriptCommand
        {
            ScriptId = scriptId,
            ExternalTableDataUrls = new() { externalTableUrl2 }
        });

        ScriptResult result = await EvaluateProductScript(productId);

        result.Should().NotBeNull();
        result.Status.Should().Be("success");
        result.Value.Should().Be("hello world");
    }

    private async Task<(ProductId, string)> CreateProductWithScriptAsync(CreateScriptCommand command)
    {
        command.Name = Guid.NewGuid().ToString();
        command.InputSchema = "";
        command.OutputSchema = "";
        command.Type = ScriptTypeEnum2.None;

        ProductId productId = GenerateProductId();
        await Products.CreateProduct(new CreateProductCommand { ProductId = productId });

        string scriptId = await Products.CreateScript(command);

        await Products.AddScriptToProduct(new AddScriptToProductCommand
        {
            ScriptId = scriptId,
            ProductId = productId
        });

        return (productId, scriptId);
    }

    private Task<ScriptResult> EvaluateProductScript(ProductId productId)
        => Scripts.EvaluateV2(null, new EvaluateProductScriptCommand { ProductId = productId });
}