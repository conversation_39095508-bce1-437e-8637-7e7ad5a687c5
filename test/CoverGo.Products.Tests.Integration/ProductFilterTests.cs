﻿using CoverGo.Gateway.Client;
using CoverGo.Products.Tests.Integration.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration
{
    [Collection("Sequential")]
    public class ProductFilterTests : TestBase
    {

        [Fact]
        public async Task GIVEN_product_WHEN_filter_by_name_contains_or_productId_type_in_THEN_returns_products_with_both_name_contains_and_type_in()
        {
            string locale = "en-US";
            string l10nName = CreateNewGuid();

            _gatewayGraphQlHttpClient.HttpClient.DefaultRequestHeaders.Add("Accept-language", locale);

            createProductInput input1 = BuildCreateProductInputWithUniqueProductId();
            createProductInput input2 = BuildCreateProductInputWithUniqueProductId();
            createProductInput input3 = BuildCreateProductInputWithUniqueProductId();
            productId productId1 = await Products.Create(input1);
            productId productId2 = await Products.Create(input2);
            await Products.Create(input3);

            upsertL10nInput upsertInput = new()
            {
                key = $"products-{Products.ProductIdToString(productId2)}-name",
                locale = locale,
                value = l10nName
            };
            await AddL10nName(upsertInput);

            productWhereInput where = new()
            {
                or = new[]
                {
                    new productWhereInput
                    {
                        name_contains = l10nName
                    },
                    new productWhereInput
                    {
                        productId= new productIdWhereInput
                        {
                            type_in = new List<string>{ productId1.type }!
                        }
                    }
                }
            };

            List<product> productsFound = (await FindAll(where)).ToList();

            productsFound.Count().Should().Be(2);
            productsFound.Count(p => p.productId!.type == productId1.type).Should().Be(1);
            productsFound.Count(p => p.name == l10nName).Should().Be(1);
        }

        [Fact]
        public async Task GIVEN_product_WHEN_filter_by_lifecycleStage_THEN_returns_products_filtered_by_lifecycleStage()
        {
            createProductInput input1 = new()
            {
                lifecycleStage = CreateNewGuid(),
                productId = new productIdInput
                {
                    type = CreateNewGuid(),
                    plan = CreateNewGuid(),
                    version = CreateNewGuid()
                }
            };
            createProductInput input2 = BuildCreateProductInputWithUniqueProductId();
            createProductInput input3 = BuildCreateProductInputWithUniqueProductId();

            await Products.Create(input1);
            await Products.Create(input2);
            await Products.Create(input3);

            productWhereInput where = new()
            {
                lifecycleStage = input1.lifecycleStage
            };

            List<product> allProducts = (await FindAll(null)).ToList();
            List<product> filteredProducts = (await FindAll(where)).ToList();

            allProducts.Count().Should().BeGreaterThan(1);
            filteredProducts.Count().Should().Be(1);
            filteredProducts.FirstOrDefault()?.lifecycleStage.Should().Be(input1.lifecycleStage);
        }

        [Fact]
        public async Task GIVEN_product_with_productTreeId_WHEN_filter_by_productTreeId_exists_true_THEN_returns_products_with_productTreeId_not_null()
        {
            createProductInput input = new()
            {
                productTreeId = CreateNewGuid(),
                productId = new productIdInput
                {
                    type = CreateNewGuid(),
                    plan = CreateNewGuid(),
                    version = CreateNewGuid()
                }
            };

            await Products.Create(input);

            productWhereInput where = new()
            {
                productTreeId_exists = true
            };

            List<product> filteredProducts = (await FindAll(where)).ToList();

            filteredProducts.Select(p => p.productTreeId).Should().NotContainNulls();
        }

        [Fact]
        public async Task GIVEN_product_with_productTreeId_WHEN_filter_by_productTreeId_exists_false_THEN_returns_no_products_with_productTreeId()
        {
            createProductInput input = new()
            {
                productTreeId = CreateNewGuid(),
                productId = new productIdInput
                {
                    type = CreateNewGuid(),
                    plan = CreateNewGuid(),
                    version = CreateNewGuid()
                }
            };

            await Products.Create(input);

            productWhereInput where = new()
            {
                productTreeId_exists = false
            };

            List<product> filteredProducts = (await FindAll(where)).ToList();
            bool? anyProductsWithTreeIdFound = filteredProducts?.Any(p => p.productTreeId != null);
            anyProductsWithTreeIdFound.GetValueOrDefault().Should().BeFalse();
        }

        async Task<ICollection<product>> FindAll(productWhereInput? where)
        {
            productBuilder fields = new productBuilder()
                    .productId(new productIdBuilder()
                        .plan()
                        .type()
                        .version())
                    .name()
                    .lifecycleStage()
                    .productTreeId();

            string query = new covergoQueryBuilder()
                .products_2(new covergoQueryBuilder.products_2Args(where: where), new productsBuilder()
                    .list(fields))
                .Build();

            return (await _gatewayGraphQlHttpClient.SendQueryAndEnsureAsync<products>(query)).list!;
        }

        async Task<result> AddL10nName(upsertL10nInput input)
        {
            string mutation = new covergoMutationBuilder().upsertL10n(new covergoMutationBuilder.upsertL10nArgs(input), new resultBuilder()
                    .status()
                    .errors()
                    .errors_2(new errorsBuilder())
                )
                .Build();

            return await _gatewayGraphQlHttpClient.SendMutationAndEnsureAsync<result>(mutation);
        }

        createProductInput BuildCreateProductInputWithUniqueProductId() =>
            new()
            {
                productId = new productIdInput
                {
                    type = CreateNewGuid(),
                    plan = CreateNewGuid(),
                    version = CreateNewGuid()
                }
            };

    }
}
