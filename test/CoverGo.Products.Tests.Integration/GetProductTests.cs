#nullable enable

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.L10n.Client;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Tests.Integration.ProductsStrawberryClient;
using CoverGo.Products.Tests.Integration.Support;
using Microsoft.Extensions.DependencyInjection;
using ProductId = CoverGo.Products.Domain.Products.ProductId;

namespace CoverGo.Products.Tests.Integration;

[Collection("Sequential")]
[Trait("Category", "Integration")]
[Trait("Ticket", "CH-15303")]
public class GetProductTests : ApiTestBase, IClassFixture<ProductsWebApplicationFactory>
{
    public GetProductTests(ProductsWebApplicationFactory webApplicationFactory) : base(webApplicationFactory)
    {
    }

    [Fact]
    public async Task ProductIsReturned()
    {
        // Arrange (Given)
        var productId = await GivenProduct();

        // Act (When)
        var product = await WhenGetProduct(productId);

        // Assert (Then)
        product.Id.Should().Be(new GetProduct_Product_Id_products_ProductId(productId.Plan, productId.Type, productId.Version));
        product.OfferValidityPeriod.Should().Be(TimeSpan.FromDays(30));
        product.PolicyIssuanceMethod.Should().Be(Products_PolicyIssuanceMethod.AutomaticAfterPremiumReceipt);
        product.LifecycleStage.Should().Be("alpha");
    }

    [Fact]
    [Trait("Ticket", "CH-17484")]
    [Trait("Ticket", "CH-23985")]
    public async Task ProductFieldsAreReturned()
    {
        // Arrange (Given)
        var productId = await GivenProduct(fields: JsonSerializer.Serialize(new Dictionary<string, object?> { ["currency"] = "USD" }));

        // Act (When)
        var product = await WhenGetProduct(productId);

        // Assert (Then)
        product.Fields.ToDictionary(it => it.Key, it => it.Value.RootElement.GetRawText()).Should().BeEquivalentTo(new Dictionary<string, string>
        {
            ["currency"] = "\"USD\"",
        });
    }

    [Fact]
    [Trait("Ticket", "CH-17484")]
    [Trait("Ticket", "CH-23986")]
    public async Task ProductTermsAndConditionsAreReturned()
    {
        // Arrange (Given)
        var productId = await GivenProduct(termsAndConditionsJacketId: "1", termsAndConditionsTemplateId: "2");

        // Act (When)
        var product = await WhenGetProduct(productId);

        // Assert (Then)
        product.TermsAndConditionsJacketId.Should().Be("1");
        product.TermsAndConditionsTemplateId.Should().Be("2");
    }

    [Fact]
    [Trait("Ticket", "CH-17484")]
    [Trait("Ticket", "CH-23987")]
    public async Task ProductNameIsReturned()
    {
        // Arrange (Given)
        var productId = await GivenProduct(name: "Cool Insurance Product");

        // Act (When)
        var product = await WhenGetProduct(productId);

        // Assert (Then)
        product.Name.Should().Be("Cool Insurance Product");
    }

    private async Task<ProductId> GivenProduct(
        string? fields = null,
        string? termsAndConditionsTemplateId = null,
        string? termsAndConditionsJacketId = null,
        string? name = null,
        CancellationToken cancellationToken = default)
    {
        var repo = _applicationFactory.Services.GetRequiredService<IProductRepository>();
        var productId = new ProductId()
        {
            Plan = CreateNewGuid(),
            Type = CreateNewGuid(),
            Version = CreateNewGuid()
        };
        var result = await repo.CreateAsync(
            "covergo",
            new()
            {
                ProductId = productId,
                OfferValidityPeriod = TimeSpan.FromDays(30),
                LifecycleStage = "alpha",
                PolicyIssuanceMethod = PolicyIssuanceMethod.AutomaticAfterPremiumReceipt,
                Fields = fields,
                TermsAndConditionsJacketId = termsAndConditionsJacketId,
                TermsAndConditionsTemplateId = termsAndConditionsTemplateId,
            },
            cancellationToken);
        result.IsSuccess.Should().BeTrue();
        if (name is not null)
        {
            var l10nClient = _applicationFactory.Services.GetRequiredService<IL10nClient>();
            await l10nClient.L10n2Async(TenantId, new()
            {
                Key = $"products-{productId}-name",
                Locale = !string.IsNullOrEmpty(CultureInfo.CurrentCulture.Name) ? CultureInfo.CurrentCulture.Name : "en-US",
                Value = name
            });
        }
        return productId;
    }

    private async Task<IGetProduct_Product> WhenGetProduct(ProductId productId)
    {
        var result = await ProductsClient.GetProduct.ExecuteAsync(productId.Plan, productId.Type, productId.Version);
        result.Errors.Should().BeEmpty();
        return result.Data!.Product!;
    }
}
