<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>

    <NoWarn>8600,8602,8603,8604,8632</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <GraphQL Remove="GetProductPlanDetails.graphql" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="ProductPlanDetailsTests_MultiplePlansRepresentation.json" />
    <None Remove="ProductPlanDetailsTests_MultiplePlansSchema.json" />
    <None Remove="Resources\Attachment.txt" />
    <None Remove="Resources\PermissionsJsonSchema.json" />
    <None Remove="Resources\PermissionsJsonSchema_Field1.json" />
    <None Remove="Resources\pricing-script.txt" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="ProductPlanDetailsTests_MultiplePlansRepresentation.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="ProductPlanDetailsTests_MultiplePlansSchema.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Resources\Attachment.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\PermissionsJsonSchema.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\PermissionsJsonSchema_Field1.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Resources\pricing-script.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CoverGo.GraphQL.Client" />
    <PackageReference Include="CoverGo.FileSystem.Client" />
    <PackageReference Include="CoverGo.L10n.Client" />
    <PackageReference Include="CoverGo.Gateway.Client" />
    <PackageReference Include="CoverGo.Reference.Client" />
    <PackageReference Include="CoverGo.ChannelManagement.Client" />
    <PackageReference Include="CoverApp.ProductBuilder.Client" />
    <PackageReference Include="GraphQL.Client" />
    <PackageReference Include="GraphQL.Client.Serializer.Newtonsoft" />
    <PackageReference Include="IdentityModel" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" />
    <PackageReference Include="StrawberryShake.Server" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\CoverGo.Products.Client\CoverGo.Products.Client.csproj" />
    <ProjectReference Include="..\..\src\CoverGo.Products.Application\CoverGo.Products.Application.csproj" />
    <ProjectReference Include="..\..\src\CoverGo.Products.Infrastructure\CoverGo.Products.Infrastructure.csproj" />
    <ProjectReference Include="..\..\src\CoverGo.Products.Tests.GatewayClient\CoverGo.Products.Tests.GatewayClient.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="testsettings.json" />
    <None Remove="testsettings.Development.Tests.json" />
    <None Remove="testsettings.Staging.CI.json" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="testsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="testsettings.Development.Tests.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <DependentUpon>testsettings.json</DependentUpon>
    </Content>
    <Content Include="testsettings.Staging.CI.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <DependentUpon>testsettings.json</DependentUpon>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <GraphQL Include="*.graphql">
      <Generator>MSBuild:GenerateGraphQLCode</Generator>
    </GraphQL>
  </ItemGroup>

  <ItemGroup>
    <GraphQL Update="ProductClone.graphql">
      <Generator>MSBuild:GenerateGraphQLCode</Generator>
    </GraphQL>
  </ItemGroup>

   <ItemGroup>
    <None Update="referenceInputData.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Content Include="__snapshots__\*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

</Project>
