using System;
using System.Linq;
using System.Threading.Tasks;

using CoverGo.ProductBuilder.Client;
using CoverGo.Products.Client;

namespace CoverGo.Products.Tests.Integration;

public partial class ProductExchangeTests
{
    [Fact]
    public async Task GIVEN_a_product_with_a_product_tree_WHEN_exporting_importing_THEN_the_same_product_tree_is_created()
    {
        string sourceProductTreeId = await CreateProductTree();
        ProductId productId = await CreateProduct(sourceProductTreeId);
        Product product = await Products.GetProductByProductId(productId);
        DateTime lastModifiedAt = product.LastModifiedAt;

        await FileSystem.Initialize();
        string filePath = await ExportProduct(productId);
        await ImportProduct(filePath);
        product = await Products.GetProductByProductId(productId);

        filePath.Should().StartWith("exchange%2F").And.EndWith(".cgx");

        string targetProductTreeId = product.ProductTreeId;
        targetProductTreeId.Should().NotBe(sourceProductTreeId);

        product.LastModifiedAt.Should().NotBe(lastModifiedAt);
        //product.LastModifiedById.Should().NotBeNullOrEmpty().And.Be(LoginId);

        string targetProductTree = await GetProductTree(targetProductTreeId);
        targetProductTree.Should().Be("{\"ref\":\"a\",\"alias\":\"a\",\"type\":\"NodeAggregate\",\"children\":[{\"ref\":\"b\",\"alias\":\"b\",\"type\":\"NodeAggregate\",\"fields\":[{\"ref\":\"f2\",\"alias\":\"f2\",\"type\":\"String\",\"resolver\":{\"text\":\"2\",\"language\":\"MATHJS\"}}]},{\"ref\":\"c\",\"alias\":null,\"type\":\"NodeAggregate\",\"fields\":[{\"ref\":\"f3\",\"alias\":null,\"type\":\"String\",\"resolver\":null}]},{\"ref\":\"d\",\"alias\":null,\"type\":\"NodeAggregate\",\"fields\":null}],\"fields\":[{\"ref\":\"f1\",\"alias\":\"f1\",\"type\":\"String\",\"resolver\":{\"text\":\"1\",\"language\":\"MATHJS\"}}]}");
    }

    [Fact]
    public async Task GIVEN_a_non_existent_product_WHEN_exporting_it_THEN_it_throws()
    {
        ProductId productId = GenerateProductId();
        Func<Task> func = () => ExportProduct(productId);
        await func.Should().ThrowAsync<InvalidOperationException>().Where(e => e.Message == "product_not_found: Product does not exist");
    }

    [Fact]
    public async Task GIVEN_a_product_without_product_tree_WHEN_exporting_it_THEN_it_throws()
    {
        string sourceProductTreeId = null;
        ProductId productId = await CreateProduct(sourceProductTreeId);
        Func<Task> func = () => ExportProduct(productId);
        await func.Should().ThrowAsync<InvalidOperationException>().Where(e => e.Message == "product_tree_not_found: There is no tree associated with the product");
    }

    [Fact]
    public async Task GIVEN_a_product_with_non_existent_product_tree_WHEN_exporting_it_THEN_it_throws()
    {
        string sourceProductTreeId = Guid.NewGuid().ToString().Replace("-", "");
        ProductId productId = await CreateProduct(sourceProductTreeId);
        Func<Task> func = () => ExportProduct(productId);
        await func.Should().ThrowAsync<InvalidOperationException>().Where(e => e.Message == ": Product tree does not exist");
    }

    [Fact]
    public async Task GIVEN_an_exported_source_product_WHEN_importing_a_non_existent_target_product_THEN_it_throws()
    {
        string sourceProductTreeId = await CreateProductTree();
        ProductId productId = await CreateProduct(sourceProductTreeId);
        await FileSystem.Initialize();
        string filePath = await ExportProduct(productId);
        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });

        Func<Task> func = () => ImportProduct(filePath);
        await func.Should().ThrowAsync<InvalidOperationException>().Where(e => e.Message == "product_not_found: Product does not exist");
    }

    [Fact]
    public async Task GIVEN_a_non_existent_file_path_WHEN_importing_THEN_it_throws()
    {
        string sourceProductTreeId = await CreateProductTree();
        ProductId productId = await CreateProduct(sourceProductTreeId);
        string fileName = Guid.NewGuid().ToString();
        string filePath = $"exchange%2F{fileName}.cgx";
        await FileSystem.Initialize();

        Func<Task> func = () => ImportProduct(filePath);
        await func.Should().ThrowAsync<InvalidOperationException>().Where(e => e.Message.StartsWith(": Could not find file"));
    }

    [Fact]
    public async Task GIVEN_a_product_with_a_root_node_without_fields_WHEN_exporting_importing_THEN_the_root_node_with_default_fields_is_created()
    {
        string sourceProductTreeId = await CreateProductTreeWithRootNodeWithoutFields();
        ProductId productId = await CreateProduct(sourceProductTreeId);

        await FileSystem.Initialize();
        string filePath = await ExportProduct(productId);
        await ImportProduct(filePath);

        string targetProductTreeId = await GetProductTreeId(productId);
        targetProductTreeId.Should().NotBe(sourceProductTreeId);

        string targetProductTree = await GetProductTree(targetProductTreeId);
        targetProductTree.Should().Be("{\"ref\":\"a\",\"alias\":null,\"type\":\"NodeAggregate\",\"children\":null,\"fields\":[{\"ref\":\"meta\",\"alias\":\"Meta\",\"type\":\"String\",\"resolver\":{\"text\":\"\",\"language\":\"MATHJS\"}}]}");
    }

    [Theory]
    [InlineData("{0}.cgx")]
    [InlineData("exchange%2F{0}.zip")]
    [InlineData("exchange%2F{0}/{0}.cgx")]
    [InlineData("exchange%2F{0}%2F{0}.cgx")]
    public async Task GIVEN_an_invalid_file_path_WHEN_importing_THEN_it_throws(string filePathFormat)
    {
        string sourceProductTreeId = await CreateProductTree();
        ProductId productId = await CreateProduct(sourceProductTreeId);
        string fileName = Guid.NewGuid().ToString();
        string filePath = string.Format(filePathFormat, fileName);
        await FileSystem.Initialize();

        Func<Task> func = () => ImportProduct(filePath);
        await func.Should().ThrowAsync<InvalidOperationException>().Where(e => e.Message.StartsWith(": Please upload the file to '/api/v1/files/exchange%2Ffilename.cgx' and pass 'exchange%2Ffilename.cgx'."));
    }

    [Fact]
    public async Task GIVEN_an_invalid_file_WHEN_importing_THEN_it_throws()
    {
        string fileName = Guid.NewGuid().ToString();
        string filePath = $"exchange%2F{fileName}.cgx";
        await FileSystem.Initialize();
        await FileSystem.Upload(filePath.Replace("%2F", "/"), "Invalid ZIP");

        Func<Task> func = () => ImportProduct(filePath);
        await func.Should().ThrowAsync<InvalidOperationException>().Where(e => e.Message.StartsWith("invalid_file: The file is invalid"));
    }

    private Task<string> CreateProductTree() => ProductBuilder.CreateNode(new CreateNodeInput
    {
        @ref = "a",
        alias = "a",
        type = "NodeAggregate",
        children = new CreateNodeInput[]
        {
            new CreateNodeInput
            {
                @ref = "b",
                alias = "b",
                type = "NodeAggregate",
                fields = new NodeFieldInput[]
                {
                    new NodeFieldInput
                    {
                        @ref = "f2",
                        alias = "f2",
                        type = "String",
                        resolver = new ExpressionInput
                        {
                            text = "2",
                            language = Language.MATHJS
                        }
                    }
                }
            },
            new CreateNodeInput
            {
                @ref = "c",
                fields = new NodeFieldInput[]
                {
                    new NodeFieldInput
                    {
                        @ref = "f3",
                        type = "String"
                    }
                }
            },
            new CreateNodeInput
            {
                @ref = "d"
            }
        },
        fields = new NodeFieldInput[]
        {
            new NodeFieldInput
            {
                @ref = "f1",
                alias = "f1",
                type = "String",
                resolver = new ExpressionInput
                {
                    text = "1",
                    language = Language.MATHJS
                }
            }
        }
    });

    private Task<string> CreateProductTreeWithRootNodeWithoutFields() => ProductBuilder.CreateNode(new CreateNodeInput
    {
        @ref = "a"
    });

    private async Task<ProductId> CreateProduct(string productTreeId, string lifecycleStage = null)
    {
        ProductId productId = GenerateProductId();
        await Products.CreateProduct(new CreateProductCommand { ProductId = productId, CreatedById = LoginId, ProductTreeId = productTreeId, LifecycleStage = lifecycleStage });
        return productId;
    }

    private async Task<string> ExportProduct(ProductId productId)
    {
        products_FilePathResult result = await Products.ExportProduct(productId);
        if (result.isSuccess != true) throw new InvalidOperationException(result.errors_2?.Select(e => $"{e.code}: {e.message}").First());
        return result.filePath;
    }

    private async Task ImportProduct(string filePath)
    {
        products_Result result = await Products.ImportProduct(filePath);
        if (result.isSuccess != true) throw new InvalidOperationException(result.errors_2?.Select(e => $"{e.code}: {e.message}").First());
    }

    private async Task<string> GetProductTreeId(ProductId productId)
    {
        Product product = await Products.GetProductByProductId(productId);
        return product.ProductTreeId;
    }

    private Task<string> GetProductTree(string productTreeId) => ProductBuilder.Subtree(Guid.Parse(productTreeId), new NodeBuilder()
        .@ref()
        .alias()
        .type()
        .children(new NodeBuilder()
            .@ref()
            .alias()
            .type()
            .fields(new FieldDescriptionBuilder()
                .@ref()
                .alias()
                .type()
                .resolver(new ExpressionBuilder()
                    .text()
                    .language())))
        .fields(new FieldDescriptionBuilder()
            .@ref()
            .alias()
            .type()
            .resolver(new ExpressionBuilder()
                .text()
                .language())));
}