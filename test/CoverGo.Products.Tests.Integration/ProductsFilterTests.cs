﻿using CoverGo.Products.Client;
using CoverGo.Products.Tests.Integration.Utils;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration;

[Collection("Sequential")]
public sealed class ProductsFilterTest : TestBase
{
    [Fact]
    public async Task GIVEN_products_with_version_WHEN_getting_products_THEN_return_filtered_products_by_version()
    {
        ProductId productId = await CreateProduct(null, "1.0");
        ProductId productId2 = await CreateProduct(null, "1.0-custom");
        ProductId productId3 = await CreateProduct(null, "1.0");

        try
        {
            ICollection<Product> products = await GetProducts(new ProductWhere { ProductId = new ProductIdWhere { Version_contains = "custom" } });
            products.Count.Should().BeGreaterOrEqualTo(1);

            products = await GetProducts(new ProductWhere { ProductId = new ProductIdWhere { Version_ncontains = "custom" } });
            products.Count(product => product.Id!.Version!.Contains("custom")).Should().Be(0);

            products = await GetProducts(new ProductWhere { ProductId = new ProductIdWhere { Version_ncontains = ".*" } });
            products.Count().Should().Be(0);
        }
        finally
        {
            await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });
            await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId2 });
            await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId3 });
        }
    }

    [Fact]
    public async Task GIVEN_products_issuerProductId_filter_WHEN_getting_products_THEN_return_filtered_products_by_issuerProductId()
    {
        (ProductId productId, string issuerProductId) = await CreateDBSProduct(new() { IssuerProductId = "INS-123" });
        (ProductId productId2, string issuerProductId2) = await CreateDBSProduct(new() { IssuerProductId = "INS-124" });

        ProductWhere issuerProductIdWhere = new ProductWhere { IssuerProductId = issuerProductId };
        ICollection<Product> filteredProducts = await GetProducts(issuerProductIdWhere);

        ProductWhere issuerProductIdWhere2 = new ProductWhere { IssuerProductId = issuerProductId2 };
        ICollection<Product> filteredProducts2 = await GetProducts(issuerProductIdWhere2);

        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });
        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId2 });

        filteredProducts.Count.Should().Be(1);
        filteredProducts.FirstOrDefault().IssuerProductId.Should().Be(issuerProductId);
        filteredProducts2.Count.Should().Be(1);
        filteredProducts2.FirstOrDefault().IssuerProductId.Should().Be(issuerProductId2);
    }

    [Fact]
    public async Task GIVEN_products_internal_review_status_filter_WHEN_getting_products_THEN_return_filtered_products_by_internal_review_status()
    {
        ProductId productId = await CreateProduct();
        ProductId productId2 = await CreateProduct();
        ProductId productId3 = await CreateProduct();

        await AddInternalReview(productId, "rejected");
        await AddInternalReview(productId, "approved");
        await AddInternalReview(productId2, "rejected");
        await AddInternalReview(productId3, "rejected");
        await AddInternalReview(productId3, "approved");
        await AddInternalReview(productId3, "onHold");

        ProductWhere where = new ProductWhere { InternalReviews_some = new InternalReviewWhere { Status = "approved" } };
        ICollection<Product> products = await GetProducts(where);

        InternalReviewWhere irWhere = new InternalReviewWhere { Status_in = new List<string> { "rejected", "approved" } };
        ProductWhere where2 = new ProductWhere { InternalReviews_some = irWhere };
        ICollection<Product> products2 = await GetProducts(where2);

        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });
        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId2 });
        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId3 });

        var pp = products.ToList().Where(p => p.Id.ToProductIdString() == productId.ToProductIdString());

        products.Count().Should().Be(2);
        products.ToList().Where(p => p.Id.ToProductIdString() == productId.ToProductIdString()).Should().NotBeNullOrEmpty();
        products.ToList().Where(p => p.Id.ToProductIdString() == productId2.ToProductIdString()).Should().BeNullOrEmpty();
        products.ToList().Where(p => p.Id.ToProductIdString() == productId3.ToProductIdString()).Should().NotBeNullOrEmpty();
        products2.Count().Should().Be(3);
    }

    async Task<ICollection<Product>> GetProducts(ProductWhere where)
    {
        ProductQuery q = new ProductQuery { Where = where };

        return await Products.GetProducts(q);
    }

    async Task<(ProductId, string)> CreateDBSProduct(CreateProductCommand? command = null)
    {
        var productId = await CreateProduct(command);
        ProductWhere where = new ProductWhere
        {
            ProductId = new ProductIdWhere { Type = productId.Type, Plan = productId.Plan, Version = productId.Version }
        };
        ICollection<Product> product = await GetProducts(where);
        string issuerProductId = product.FirstOrDefault().IssuerProductId;

        return (productId, issuerProductId);
    }

    async Task<string> AddInternalReview(ProductId productId, string status)
    {
        AddInternalReviewCommand addIRCommand = new AddInternalReviewCommand
        {
            Status = status,
            Comment = Guid.NewGuid().ToString(),
            AddedById = Guid.NewGuid().ToString()
        };
        var result = await Products.AddInternalReview(productId.ToProductIdString(), addIRCommand);
        return result.Value.Id;
    }
    async Task<ProductId> CreateProduct(CreateProductCommand? command = null, string version = null)
    {
        command ??= new();
        command.ProductId = GenerateProductId(version);
        await Products.CreateProduct(command);
        return command.ProductId;
    }
}