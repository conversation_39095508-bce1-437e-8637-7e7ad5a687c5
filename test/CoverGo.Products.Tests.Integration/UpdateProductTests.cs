#nullable enable

using System.Threading.Tasks;
using CoverGo.Products.Tests.Integration.Support;

namespace CoverGo.Products.Tests.Integration;

[Collection("Sequential")]
[Trait("Category", "Integration")]
public class UpdateProductTests(ProductsWebApplicationFactory webApplicationFactory) : ApiTestBase(webApplicationFactory), IClassFixture<ProductsWebApplicationFactory>
{
    [Theory] // | old       | command   | after updated
    [InlineData(false, false, null, null, false, false)] // if not update (null), then no change
    [InlineData(true, true, null, null, true, true)]
    [InlineData(false, true, true, false, true, false)] // if update, then it should be updated
    [Trait("Ticket", "CH-22964")]
    public async Task When_UpdateProduct_Then_AutoRenewal_UpdatedCorrectly(
        bool oAutoRenewal, bool oRenewalNotification, // current product config
        bool? autoRenewal, bool? renewalNotification, // update command
        bool nAutoRenewal, bool nRenewalNotification) // after updated product config
    {
        // Arrange (Given)
        var productId = await GivenProduct(oAutoRenewal, oRenewalNotification);
        Client.UpdateProductCommand command = new() { ProductId = productId, AutoRenewal = autoRenewal, RenewalNotification = renewalNotification };

        // Act (When)
        await Products.UpdateProduct(command);

        // Assert (Then)
        var product = await Products.GetProductByProductId(productId);
        product.Should().NotBeNull();
        product.AutoRenewal.Should().Be(nAutoRenewal);
        product.RenewalNotification.Should().Be(nRenewalNotification);
    }

    [Theory]
    [InlineData("X-FIN-TAXRATE", "", false, false, "X-FIN-CUSTOMTAX", "VAT", true, true)]
    [Trait("Ticket", "CH-19045")]
    public async Task When_UpdateProductTaxConfig_Then_TaxConfig_UpdatedCorrectly(
        string oReferenceMnemonic, string oMnemonicCode, bool oAllowOverrideAtOfferStage, bool oAllowOverrideAtProposalStage, // current product config
        string nReferenceMnemonic, string nMnemonicCode, bool nAllowOverrideAtOfferStage, bool nAllowOverrideAtProposalStage) // update command
    {
        // Arrange (Given)
        var productId = await GivenProduct(oReferenceMnemonic, oMnemonicCode, oAllowOverrideAtOfferStage, oAllowOverrideAtProposalStage);
        Client.UpdateProductCommand command = new() { ProductId = productId, TaxConfiguration = new() { ReferenceMnemonic = nReferenceMnemonic, MnemonicCode = nMnemonicCode, AllowOverrideAtOfferStage = nAllowOverrideAtOfferStage, AllowOverrideAtProposalStage = nAllowOverrideAtProposalStage } };

        // Act (When)
        await Products.UpdateProduct(command);

        // Assert (Then)
        var product = await Products.GetProductByProductId(productId);
        product.Should().NotBeNull();
        product.TaxConfiguration.Should().BeEquivalentTo(new { ReferenceMnemonic = nReferenceMnemonic, MnemonicCode = nMnemonicCode, AllowOverrideAtOfferStage = nAllowOverrideAtOfferStage, AllowOverrideAtProposalStage = nAllowOverrideAtProposalStage });
    }

    private async Task<Client.ProductId> GivenProduct(bool autoRenewal, bool renewalNotification)
    {
        var productId = new Client.ProductId { Plan = CreateNewGuid(), Type = CreateNewGuid(), Version = CreateNewGuid() };
        var command = new Client.CreateProductCommand { ProductId = productId, AutoRenewal = autoRenewal, RenewalNotification = renewalNotification };
        await Products.CreateProduct(command);
        return productId;
    }
    private async Task<Client.ProductId> GivenProduct(string oReferencedType, string oMnemonicCode, bool oAllowOverrideAtOfferStage, bool oAllowOverrideAtProposalStage)
    {
        var productId = new Client.ProductId { Plan = CreateNewGuid(), Type = CreateNewGuid(), Version = CreateNewGuid() };
        var command = new Client.CreateProductCommand { ProductId = productId, TaxConfiguration = new() { ReferenceMnemonic = oReferencedType, MnemonicCode = oMnemonicCode, AllowOverrideAtOfferStage = oAllowOverrideAtOfferStage, AllowOverrideAtProposalStage = oAllowOverrideAtProposalStage } };
        await Products.CreateProduct(command);
        return productId;
    }
}
