using System;
using System.Linq;
using System.Threading.Tasks;

using CoverGo.Products.Client;
using CoverGo.Products.Tests.Integration.Utils;

using GraphQL;

using Newtonsoft.Json.Linq;

namespace CoverGo.Products.Tests.Integration;

public partial class ProductExchangeTests
{
    [Fact]
    public async Task GIVEN_a_product_with_a_product_tree_WHEN_getting_product_import_info_THEN_expected_result_is_returned()
    {
        DateTime startedAt = DateTime.UtcNow;
        string sourceProductTreeId = await CreateProductTree();
        ProductId productId = await CreateProduct(sourceProductTreeId, lifecycleStage: "Alpha");
        await FileSystem.Initialize();
        string productName = $"Test Product {productId.Plan}";
        await L10n.Upsert($"products-{productId.Plan}|{productId.Version}|{productId.Type}-name", productName);
        string filePath = await ExportProduct(productId);

        products_ProductImportInfo productImportInfo = await GetProductImportInfo(filePath);

        productImportInfo.productId.Should().NotBeNull();
        productImportInfo.productId.plan.Should().Be(productId.Plan);
        productImportInfo.productId.type.Should().Be(productId.Type);
        productImportInfo.productId.version.Should().Be(productId.Version);

        productImportInfo.productName.Should().Be(productName);

        productImportInfo.productLifecycleStage.Should().Be("Alpha");

        productImportInfo.productLastModifiedAt.Should().NotBeNull().And.BeAfter(startedAt);
        //productImportInfo.productLastModifiedBy.Should().Be(UserCredentials.Admin.UserName);

        productImportInfo.tenant.Should().Be(TenantId);

        productImportInfo.exportedAt.Should().BeAfter(productImportInfo.productLastModifiedAt.Value);
        //productImportInfo.exportedBy.Should().Be(UserCredentials.Admin.UserName);
    }

    [Fact]
    public async Task GIVEN_a_product_with_localized_name_WHEN_getting_product_import_info_THEN_expected_result_is_returned()
    {
        string sourceProductTreeId = await CreateProductTree();
        ProductId productId = await CreateProduct(sourceProductTreeId, lifecycleStage: "Alpha");
        await FileSystem.Initialize();
        string productName = $"Test Product {productId.Plan}";
        await L10n.Upsert($"products-{productId.Plan}-name", productName);
        string filePath = await ExportProduct(productId);

        products_ProductImportInfo productImportInfo = await GetProductImportInfo(filePath);

        productImportInfo.productName.Should().Be(productName);
    }

    [Fact]
    public async Task GIVEN_a_non_existent_file_WHEN_getting_product_import_info_THEN_it_throws()
    {
        string fileName = Guid.NewGuid().ToString();
        string filePath = $"exchange%2F{fileName}.cgx";
        Func<Task> func = () => GetProductImportInfo(filePath);
        await func.Should().ThrowAsync<Exception>().Where(e => e.Message.Contains("Could not find file"));
    }

    [Fact]
    public async Task GIVEN_an_exported_source_product_WHEN_getting_import_info_for_a_non_existent_target_product_THEN_it_returns_expected_validation_messages()
    {
        string sourceProductTreeId = await CreateProductTree();
        ProductId productId = await CreateProduct(sourceProductTreeId);
        await FileSystem.Initialize();
        string filePath = await ExportProduct(productId);
        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });

        products_ProductImportInfo productImportInfo = await GetProductImportInfo(filePath);

        productImportInfo.validationMessages.Should().ContainSingle();
        productImportInfo.validationMessages.First().level.Should().Be(products_ProductImportValidationMessageLevel.ERROR);
        productImportInfo.validationMessages.First().code.Should().Be("product_not_found");
    }

    [Fact]
    public async Task GIVEN_an_exported_source_product_WHEN_getting_import_info_for_a_target_product_with_different_lifecycle_stage_THEN_it_returns_expected_validation_messages()
    {
        string sourceProductTreeId = await CreateProductTree();
        ProductId productId = await CreateProduct(sourceProductTreeId, lifecycleStage: "Alpha");
        await FileSystem.Initialize();
        string filePath = await ExportProduct(productId);
        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });
        await Products.CreateProduct(new CreateProductCommand { ProductId = productId, LifecycleStage = "Pre-production" });

        products_ProductImportInfo productImportInfo = await GetProductImportInfo(filePath);

        productImportInfo.validationMessages.Should().ContainSingle();
        productImportInfo.validationMessages.First().level.Should().Be(products_ProductImportValidationMessageLevel.WARNING);
        productImportInfo.validationMessages.First().code.Should().Be("different_lifecycle_stages");
    }

    [Fact]
    public async Task GIVEN_a_target_product_without_product_tree_WHEN_importing_THEN_the_validation_messages_are_null()
    {
        string sourceProductTreeId = await CreateProductTree();
        ProductId productId = await CreateProduct(sourceProductTreeId);
        await FileSystem.Initialize();
        string filePath = await ExportProduct(productId);
        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });
        await Products.CreateProduct(new CreateProductCommand { ProductId = productId });

        products_ProductImportInfo productImportInfo = await GetProductImportInfo(filePath);

        productImportInfo.validationMessages.Should().BeNull();
    }

    [Theory]
    [InlineData("{0}.cgx")]
    [InlineData("exchange%2F{0}.zip")]
    [InlineData("exchange%2F{0}/{0}.cgx")]
    [InlineData("exchange%2F{0}%2F{0}.cgx")]
    public async Task GIVEN_an_invalid_file_path_WHEN_getting_product_import_info_THEN_it_throws(string filePathFormat)
    {
        string fileName = Guid.NewGuid().ToString();
        string filePath = string.Format(filePathFormat, fileName);
        Func<Task> func = () => GetProductImportInfo(filePath);
        await func.Should().ThrowAsync<Exception>().Where(e => e.Message == "Please upload the file to '/api/v1/files/exchange%2Ffilename.cgx' and pass 'exchange%2Ffilename.cgx'.");
    }

    [Fact]
    public async Task GIVEN_an_invalid_file_WHEN_getting_product_import_info_THEN_it_throws()
    {
        string fileName = Guid.NewGuid().ToString();
        string filePath = $"exchange%2F{fileName}.cgx";
        await FileSystem.Initialize();
        await FileSystem.Upload(filePath.Replace("%2F", "/"), "Invalid ZIP");

        GraphQLResponse<JToken> response = await Products.GetProductImportInfoRaw(filePath);
        response.Errors.Should().NotBeNull().And.HaveCount(1);
        response.Errors[0].Message.Should().Be("The file is invalid");
        response.Errors[0].Extensions.Should()
            .NotBeNull()
            .And.HaveCount(1)
            .And.ContainKey("code")
            .And.ContainValue("invalid_file");
    }

    private Task<products_ProductImportInfo> GetProductImportInfo(string filePath) => Products.GetProductImportInfo(filePath);
}