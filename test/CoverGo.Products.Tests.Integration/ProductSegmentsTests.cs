using CoverGo.Gateway.Client;
using CoverGo.Products.Client;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration;

[Collection("Sequential")]
public class ProductSegmentsTest : TestBase
{
    [Fact]
    public async Task GIVEN_product_WHEN_setting_segments_THEN_segments_should_be_set()
    {
        productId productId = await Products.Create();

        string tableValue = "someValue";
        await Products.SetSegments(new products_SetSegmentsInput { productId = new() { plan = productId.plan, type = productId.type, version = productId.version }, segments = tableValue });

        Product product = await Products.GetProductByProductId(new ProductId { Plan = productId.plan, Type = productId.type, Version = productId.version });

        product.Segments.Should().Be(tableValue);
    }
}