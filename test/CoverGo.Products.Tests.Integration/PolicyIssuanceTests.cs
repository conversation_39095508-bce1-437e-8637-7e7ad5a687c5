using System;
using System.Linq;
using System.Threading.Tasks;

using CoverGo.Products.Client;
using CoverGo.Products.Tests.Integration.Support;

namespace CoverGo.Products.Tests.Integration;

[Trait("Ticket", "CH-6275")]
[Trait("Ticket", "CH-13065")]
public class PolicyIssuanceTests(ProductsWebApplicationFactory webApplicationFactory) : IClassFixture<ProductsWebApplicationFactory>
{
    private string _tenantId = "covergo";

    [Fact]
    public async Task CreateProductWithIssuance()
    {
        // Arrange (Given)
        var client = webApplicationFactory.CreateClient();
        var rest = new ProductsClient(client);
        var productId = new ProductId()
        {
            Type = "gm",
            Version = "1.0",
            Plan = Guid.NewGuid().ToString(),
        };

        // Act (When)
        var createResult = await rest.Products_CreateAsync(_tenantId, new()
        {
            ProductId = productId,
            PolicyIssuanceMethod = PolicyIssuanceMethod.AutomaticAfterPremiumReceipt
        });

        // Assert (Then)
        createResult.IsSuccess.Should().BeTrue($"API Failed {string.Join("," , createResult.Errors_2?.Select(it => it.Message) ?? createResult.Errors?.Select(it => it) ?? [])}");
        await ThenIssuanceShouldBe(rest, productId, PolicyIssuanceMethod.AutomaticAfterPremiumReceipt);
    }

    [Fact]
    public async Task UpdateProductWithIssuance()
    {
        // Arrange (Given)
        var client = webApplicationFactory.CreateClient();
        var rest = new ProductsClient(client);
        var productId = new ProductId()
        {
            Type = "gm",
            Version = "1.0",
            Plan = Guid.NewGuid().ToString(),
        };
        await GivenProduct(rest, productId, PolicyIssuanceMethod.AutomaticAfterPremiumReceipt);

        // Act (When)
        var updateResult = await rest.Products_UpdateAsync(_tenantId, new()
        {
            ProductId = productId,
            IsPolicyIssuanceMethodChanged = true,
            PolicyIssuanceMethod = PolicyIssuanceMethod.ManualNoPremiumDependency,
        });

        // Assert (Then)
        updateResult.IsSuccess.Should().BeTrue($"API Failed {string.Join("," , updateResult.Errors_2?.Select(it => it.Message) ?? updateResult.Errors?.Select(it => it) ?? [])}");
        await ThenIssuanceShouldBe(rest, productId, PolicyIssuanceMethod.ManualNoPremiumDependency);
    }

    [Fact]
    public async Task UpdateProductWithoutIssuance()
    {
        // Arrange (Given)
        var client = webApplicationFactory.CreateClient();
        var rest = new ProductsClient(client);
        var productId = new ProductId()
        {
            Type = "gm",
            Version = "1.0",
            Plan = Guid.NewGuid().ToString(),
        };
        await GivenProduct(rest, productId, PolicyIssuanceMethod.AutomaticAfterPremiumReceipt);

        // Act (When)
        var updateResult = await rest.Products_UpdateAsync(_tenantId, new()
        {
            ProductId = productId,
            IsPolicyIssuanceMethodChanged = false,
            PolicyIssuanceMethod = null,
        });

        // Assert (Then)
        updateResult.IsSuccess.Should().BeTrue($"API Failed {string.Join("," , updateResult.Errors_2?.Select(it => it.Message) ?? updateResult.Errors?.Select(it => it) ?? [])}");
        await ThenIssuanceShouldBe(rest, productId, PolicyIssuanceMethod.AutomaticAfterPremiumReceipt);
    }

    private async Task GivenProduct(ProductsClient rest, ProductId productId, PolicyIssuanceMethod policyIssuanceMethod)
    {
        var createResult = await rest.Products_CreateAsync(_tenantId, new()
        {
            ProductId = productId,
            PolicyIssuanceMethod = policyIssuanceMethod,
        });
        createResult.Errors_2.Should().BeNullOrEmpty();
    }

    private async Task ThenIssuanceShouldBe(ProductsClient rest, ProductId productId, PolicyIssuanceMethod policyIssuanceMethod)
    {
        var product = await rest.Products_GetAllAsync(
            _tenantId,
            new()
            {
                Where = new() { ProductId = new() { Plan = productId.Plan, Type = productId.Type, Version = productId.Version } }
            },
            null);
        product[0].PolicyIssuanceMethod.Should().Be(policyIssuanceMethod);
    }
}
