using CoverGo.DomainUtils;
using CoverGo.Products.Client;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration
{
    [Collection("Sequential")]
    public class DiscountCodeCrudTests : TestBase
    {

        [Fact]
        public async Task GIVEN_discount_code_input_WHEN_create_THEN_success()
        {
            string id = CreateNewGuid();
            string code = $"COVERGO-{CreateNewGuid()}";
            DateTime now = DateTime.UtcNow;
            DateTime validFrom = now.AddDays(-1);
            DateTime validTo = now.AddDays(1);
            products_DiscountCodeUpsertInput createInput = new()
            {
                id = id,
                name = code,
                type = products_DiscountType.AMOUNT,
                value = 100,
                description = "Covergo discount code",
                validFrom = validFrom,
                validTo = validTo,
                productTypeId = "ih"

            };

            await Products.CreateDiscountCode(createInput);

            products_GenericDiscountCode8QueryInterface codes = await Products.GetDiscountCodes(id);

            codes.totalCount.Should().Be(1);
            codes.list.Should().NotBeEmpty();

            products_DiscountCode? discountCode = codes.list!.ElementAt(0);

            discountCode!.id.Should().Be(id);
            discountCode.name.Should().Be(code);
            discountCode.type.Should().Be(products_DiscountType.AMOUNT);
            discountCode.value.Should().Be(100);
            discountCode.description.Should().Be("Covergo discount code");
            discountCode.validFrom.Should().BeSameDateAs(validFrom);
            discountCode.validTo.Should().BeSameDateAs(validTo);
            discountCode.productTypeId.Should().Be("ih");
        }


        [Fact]
        public async Task GIVEN_discount_code_input_WHEN_update_THEN_success()
        {
            string id = CreateNewGuid();
            products_DiscountCodeUpsertInput createInput = new() { id = id, name = CreateNewGuid() };
            products_GenericDiscountCode3BatchInput batch = new()
            {
                create = new List<products_DiscountCodeUpsertInput?>
                { createInput }
            };

            await Products.DiscountCodesBatch(batch);

            string code = $"COVERGO-{CreateNewGuid()}";
            DateTime now = DateTime.UtcNow;
            DateTime validFrom = now.AddDays(-1);
            DateTime validTo = now.AddDays(1);
            products_DiscountCodeUpsertInput updateInput = new()
            {
                id = id,
                name = code,
                type = products_DiscountType.AMOUNT,
                value = 100,
                description = "Covergo discount code",
                validFrom = validFrom,
                validTo = validTo,
                productTypeId = "ih"

            };
            batch = new products_GenericDiscountCode3BatchInput { update = new List<products_DiscountCodeUpsertInput?> { updateInput } };

            await Products.DiscountCodesBatch(batch);

            products_GenericDiscountCode8QueryInterface discountCodes = await Products.DiscountCodesGet(id);

            discountCodes.totalCount.Should().Be(1);
            discountCodes.list.Should().NotBeEmpty();

            products_DiscountCode? discountCode = discountCodes.list!.ElementAt(0);

            discountCode!.id.Should().Be(id);
            discountCode.name.Should().Be(code);
            discountCode.type.Should().Be(products_DiscountType.AMOUNT);
            discountCode.value.Should().Be(100);
            discountCode.description.Should().Be("Covergo discount code");
            discountCode.validFrom.Should().BeSameDateAs(validFrom);
            discountCode.validTo.Should().BeSameDateAs(validTo);
            discountCode.productTypeId.Should().Be("ih");
        }

        [Fact]
        public async Task GIVEN_an_active_discount_code_WHEN_update_THEN_failure()
        {
            string id = CreateNewGuid();
            DateTime now = DateTime.UtcNow;
            DateTime validFrom = now.AddDays(-1);
            DateTime validTo = now.AddDays(1);
            products_DiscountCodeUpsertInput createInput = new()
            {
                id = id,
                name = CreateNewGuid(),
                validFrom = validFrom,
                validTo = validTo,
            };
            products_GenericDiscountCode3BatchInput batch = new()
            {
                create = new List<products_DiscountCodeUpsertInput?>
                { createInput }
            };

            await Products.DiscountCodesBatch(batch);

            string code = $"COVERGO-{CreateNewGuid()}";

            products_DiscountCodeUpsertInput updateInput = new()
            {
                id = id,
                name = code,
                type = products_DiscountType.AMOUNT,
                value = 100,
                description = "Covergo discount code",
                productTypeId = "ih"

            };
            batch = new products_GenericDiscountCode3BatchInput { update = new List<products_DiscountCodeUpsertInput?> { updateInput } };

            Result result = await Products.DiscountCodesBatch(batch);

            result.Status.Should().Be("failure");
            result.Errors.Should().Contain($"The discount code with id {id} is active/expired so it can't be updated.");
        }


        [Fact]
        public async Task GIVEN_an_active_discount_code_WHEN_add_products_THEN_success()
        {
            string id = CreateNewGuid();
            DateTime now = DateTime.UtcNow;
            DateTime validFrom = now.AddDays(-1);
            DateTime validTo = now.AddDays(1);
            products_DiscountCodeUpsertInput createInput = new()
            {
                id = id,
                name = CreateNewGuid(),
                validFrom = validFrom,
                validTo = validTo,
            };
            products_GenericDiscountCode3BatchInput batch = new()
            {
                create = new List<products_DiscountCodeUpsertInput?>
                { createInput }
            };

            await Products.DiscountCodesBatch(batch);

            string productType = $"product-type-{CreateNewGuid()}";
            products_ProductIdInput productId = new() { plan = "annually", type = productType, version = "1.0" };
            await Products.AddEligibleProductsToDiscountCode(new products_DiscountCodeProductsUpsertInput
            {
                discountCodeId = id,
                productIds = new List<products_ProductIdInput?> { productId }
            });

            products_GenericDiscountCode8QueryInterface codes = await Products.DiscountCodesGet(new products_DiscountCodeFilterInput
            {
                productIds_in = new List<products_ProductIdInput?>
            {
                new()
                {
                    type = productType,
                    plan = "annually",
                    version = "1.0"
                }
            }
            });

            codes.totalCount.Should().Be(1);
            codes.list.Should().NotBeEmpty();

            products_DiscountCode? discountCode = codes.list!.ElementAt(0);
            discountCode!.id.Should().Be(id);
            products_ProductId assignedProduct = discountCode.productIds!.First()!;
            assignedProduct.plan.Should().Be("annually");
            assignedProduct.type.Should().Be(productType);
            assignedProduct.version.Should().Be("1.0");
        }


        [Fact]
        public async Task GIVEN_an_expired_discount_code_WHEN_add_products_THEN_failure()
        {
            string id = CreateNewGuid();
            DateTime now = DateTime.UtcNow;
            DateTime validFrom = now.AddDays(-2);
            DateTime validTo = now.AddDays(-1);
            products_DiscountCodeUpsertInput createInput = new()
            {
                id = id,
                name = CreateNewGuid(),
                validFrom = validFrom,
                validTo = validTo,
            };
            products_GenericDiscountCode3BatchInput batch = new()
            {
                create = new List<products_DiscountCodeUpsertInput?>
                { createInput }
            };

            await Products.DiscountCodesBatch(batch);

            string productType = $"product-type-{CreateNewGuid()}";
            products_ProductIdInput productId = new() { plan = "annually", type = productType, version = "1.0" };

            Result result = await Products.AddEligibleProductsToDiscountCode(new products_DiscountCodeProductsUpsertInput
            {
                discountCodeId = id,
                productIds = new List<products_ProductIdInput?> { productId }
            });

            result.Status.Should().Be("failure");
            result.Errors.Should().Contain($"Can't add products into an expired discount code.");
        }

        [Fact]
        public async Task GIVEN_an_expired_discount_code_input_WHEN_update_THEN_failure()
        {
            string id = CreateNewGuid();
            DateTime now = DateTime.UtcNow;
            DateTime validFrom = now.AddDays(-2);
            DateTime validTo = now.AddDays(-1);
            products_DiscountCodeUpsertInput createInput = new()
            {
                id = id,
                name = CreateNewGuid(),
                validFrom = validFrom,
                validTo = validTo,
            };
            products_GenericDiscountCode3BatchInput batch = new()
            {
                create = new List<products_DiscountCodeUpsertInput?>
                { createInput }
            };

            await Products.DiscountCodesBatch(batch);

            string code = $"COVERGO-{CreateNewGuid()}";

            products_DiscountCodeUpsertInput updateInput = new()
            {
                id = id,
                name = code,
                type = products_DiscountType.AMOUNT,
                value = 100,
                description = "Covergo discount code",
                productTypeId = "ih"

            };
            batch = new products_GenericDiscountCode3BatchInput { update = new List<products_DiscountCodeUpsertInput?> { updateInput } };

            Result result = await Products.DiscountCodesBatch(batch);

            result.Status.Should().Be("failure");
            result.Errors.Should().Contain($"The discount code with id {id} is active/expired so it can't be updated.");
        }

        [Fact]
        public async Task GIVEN_an_active_discount_code_WHEN_delete_THEN_failure()
        {
            string id = CreateNewGuid();
            DateTime now = DateTime.UtcNow;
            DateTime validFrom = now.AddDays(-1);
            DateTime validTo = now.AddDays(1);
            products_DiscountCodeUpsertInput createInput = new()
            {
                id = id,
                name = CreateNewGuid(),
                validFrom = validFrom,
                validTo = validTo
            };
            products_GenericDiscountCode3BatchInput batch = new() { create = new List<products_DiscountCodeUpsertInput?> { createInput } };

            await Products.DiscountCodesBatch(batch);

            products_GenericDiscountCode8QueryInterface discountCodes = await Products.DiscountCodesGet(id);

            discountCodes.totalCount.Should().Be(1);
            discountCodes.list.Should().NotBeEmpty();

            products_DiscountCodeUpsertInput deleteInput = new() { id = id };
            batch = new products_GenericDiscountCode3BatchInput { delete = new List<products_DiscountCodeUpsertInput?> { deleteInput } };

            Result result = await Products.DiscountCodesBatch(batch);

            result.Status.Should().Be("failure");
            result.Errors.Should().Contain($"The discount code with id {id} is active/expired so it can't be deleted.");
        }

        [Fact]
        public async Task GIVEN_an_expired_discount_code_WHEN_delete_THEN_failure()
        {
            string id = CreateNewGuid();
            DateTime now = DateTime.UtcNow;
            DateTime validFrom = now.AddDays(-2);
            DateTime validTo = now.AddDays(-1);
            products_DiscountCodeUpsertInput createInput = new()
            {
                id = id,
                name = CreateNewGuid(),
                validFrom = validFrom,
                validTo = validTo
            };
            products_GenericDiscountCode3BatchInput batch = new() { create = new List<products_DiscountCodeUpsertInput?> { createInput } };

            await Products.DiscountCodesBatch(batch);

            products_GenericDiscountCode8QueryInterface discountCodes = await Products.DiscountCodesGet(id);

            discountCodes.totalCount.Should().Be(1);
            discountCodes.list.Should().NotBeEmpty();

            products_DiscountCodeUpsertInput deleteInput = new() { id = id };
            batch = new products_GenericDiscountCode3BatchInput { delete = new List<products_DiscountCodeUpsertInput?> { deleteInput } };

            Result result = await Products.DiscountCodesBatch(batch);

            result.Status.Should().Be("failure");
            result.Errors.Should().Contain($"The discount code with id {id} is active/expired so it can't be deleted.");
        }


        [Fact]
        public async Task GIVEN_an_new_discount_code_WHEN_delete_THEN_success()
        {
            string id = CreateNewGuid();
            products_DiscountCodeUpsertInput createInput = new() { id = id, name = CreateNewGuid() };
            products_GenericDiscountCode3BatchInput batch = new() { create = new List<products_DiscountCodeUpsertInput?> { createInput } };

            await Products.DiscountCodesBatch(batch);

            products_GenericDiscountCode8QueryInterface discountCodes = await Products.DiscountCodesGet(id);

            discountCodes.totalCount.Should().Be(1);
            discountCodes.list.Should().NotBeEmpty();

            products_DiscountCodeUpsertInput deleteInput = new() { id = id };
            batch = new products_GenericDiscountCode3BatchInput { delete = new List<products_DiscountCodeUpsertInput?> { deleteInput } };
            await Products.DiscountCodesBatch(batch);

            discountCodes = await Products.DiscountCodesGet(id);
            discountCodes.totalCount.Should().Be(0);
            discountCodes.list.Should().BeEmpty();
        }

        [Fact]
        public async Task GIVEN_discount_code_input_WHEN_delete_THEN_success()
        {
            string id = CreateNewGuid();
            products_DiscountCodeUpsertInput createInput = new() { id = id, name = CreateNewGuid() };
            products_GenericDiscountCode3BatchInput batch = new() { create = new List<products_DiscountCodeUpsertInput?> { createInput } };

            await Products.DiscountCodesBatch(batch);

            products_GenericDiscountCode8QueryInterface discountCodes = await Products.DiscountCodesGet(id);

            discountCodes.totalCount.Should().Be(1);
            discountCodes.list.Should().NotBeEmpty();

            products_DiscountCodeUpsertInput deleteInput = new() { id = id };
            batch = new products_GenericDiscountCode3BatchInput { delete = new List<products_DiscountCodeUpsertInput?> { deleteInput } };
            await Products.DiscountCodesBatch(batch);

            discountCodes = await Products.DiscountCodesGet(id);
            discountCodes.totalCount.Should().Be(0);
            discountCodes.list.Should().BeEmpty();
        }

        [Fact]
        public async Task GIVEN_discount_codes_with_products_WHEN_filter_by_product_ids_THEN_return_correct_discount_code()
        {
            string id = CreateNewGuid();
            string code = $"COVERGO-{CreateNewGuid()}";
            string productType = $"product-type-{CreateNewGuid()}";
            DateTime now = DateTime.UtcNow;
            DateTime validFrom = now.AddDays(-1);
            DateTime validTo = now.AddDays(1);
            products_DiscountCodeUpsertInput createInput = new()
            {
                id = id,
                name = code,
                type = products_DiscountType.AMOUNT,
                value = 100,
                description = "Covergo discount code",
                validFrom = validFrom,
                validTo = validTo,
                productTypeId = "ih"
            };
            products_GenericDiscountCode3BatchInput batch = new() { create = new List<products_DiscountCodeUpsertInput?> { createInput } };

            await Products.DiscountCodesBatch(batch);

            products_ProductIdInput productId = new() { plan = "annually", type = productType, version = "1.0" };
            await Products.AddEligibleProductsToDiscountCode(new products_DiscountCodeProductsUpsertInput()
            {
                discountCodeId = id,
                productIds = new List<products_ProductIdInput?> { productId }
            });

            products_GenericDiscountCode8QueryInterface codes = await Products.DiscountCodesGet(new products_DiscountCodeFilterInput
            {
                productIds_in = new List<products_ProductIdInput?>
            {
                new()
                {
                    type = productType,
                    plan = "annually",
                    version = "1.0"
                }
            }
            });

            codes.totalCount.Should().Be(1);
            codes.list.Should().NotBeEmpty();

            products_DiscountCode? discountCode = codes.list!.ElementAt(0);

            discountCode!.id.Should().Be(id);
            discountCode.name.Should().Be(code);
            discountCode.type.Should().Be(products_DiscountType.AMOUNT);
            discountCode.value.Should().Be(100);
            discountCode.description.Should().Be("Covergo discount code");
            discountCode.validFrom.Should().BeSameDateAs(validFrom);
            discountCode.validTo.Should().BeSameDateAs(validTo);
            discountCode.productTypeId.Should().Be("ih");
            discountCode.productIds.Should().HaveCount(1);
            products_ProductId assignedProduct = discountCode.productIds!.First()!;
            assignedProduct.plan.Should().Be("annually");
            assignedProduct.type.Should().Be(productType);
            assignedProduct.version.Should().Be("1.0");
        }

        protected static string CreateNewGuid() =>
            Guid.NewGuid().ToString();
    }
}
