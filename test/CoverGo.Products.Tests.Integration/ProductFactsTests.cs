﻿using CoverGo.Gateway.Client;
using CoverGo.Products.Client;
using CoverGo.Products.Tests.Integration.Utils;

using GraphQL.Client.Http;

using IdentityModel.Client;

using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration
{
    [Collection("Sequential")]
    public class ProductFactsTests : TestBase
    {
        [Fact]
        public async Task GIVEN_product_added_WHEN_add_fact_to_it_THEN_succeed_and_product_contains_fact()
        {
            productId productId = await Products.Create();

            (string factId, addFactInput addFactInput) = await AddFact(productId);

            product product = await SearchProductById(productId);

            product.facts.Should().NotBeNullOrEmpty();
            product.facts!.Single()!.id.Should().Be(factId);
            product.facts!.Single()!.ShouldHaveSameValuesAsInput(addFactInput);
        }

        [Fact]
        public async Task GIVEN_product_added_WHEN_update_fact_of_it_THEN_succeed_and_product_contains_updated_fact()
        {
            productId productId = await Products.Create();

            (string factId, _) = await AddFact(productId);

            updateFactInput updateFactInput = await UpdateFact(productId, factId);

            product product = await SearchProductById(productId);

            product.facts!.Single().ShouldHaveSameValuesAsInput(updateFactInput);
        }

        [Fact]
        public async Task GIVEN_product_added_WHEN_add_and_update_multiple_facts_of_it_THEN_product_contains_new_and_updated_facts_content()
        {
            productId productId = await Products.Create();

            (string factId1, _) = await AddFact(productId);
            (string factId2, _) = await AddFact(productId);

            addFactInput addFactInput1 = new()
            {
                type = "add test type 1",
                value = new scalarValueInput { stringValue = "test added value 1" }
            };
            addFactInput addFactInput2 = new()
            {
                type = "add test type 2",
                value = new scalarValueInput { stringValue = "test added value 2" }
            };
            updateFactInput updateFactInput1 = new()
            {
                id = factId1,
                type = "updated test type 1",
                value = new scalarValueInput { stringValue = "test updated value 1" }
            };
            updateFactInput updateFactInput2 = new()
            {
                id = factId2,
                type = "updated test type 2",
                value = new scalarValueInput { stringValue = "test updated value 2" },
            };
            factBatchInput factBatchInput = new()
            {
                addFactInputs = new List<addFactInput?> { addFactInput1, addFactInput2 },
                updateFactInputs = new List<updateFactInput?> { updateFactInput1, updateFactInput2 }
            };

            await ProductFactBatch(productId, factBatchInput);
            product product = await SearchProductById(productId);

            product.facts!.Count.Should().Be(4);

            fact finalFact1 = product.facts.Single(f => f!.id == factId1)!;
            fact finalFact2 = product.facts.Single(f => f!.id == factId2)!;

            finalFact1.ShouldHaveSameValuesAsInput(updateFactInput1);
            finalFact2.ShouldHaveSameValuesAsInput(updateFactInput2);
        }

        async Task<(string, addFactInput)> AddFact(productId productId)
        {
            addFactInput addFactInput = new()
            {
                type = "test type",
                value = new scalarValueInput { stringValue = "test value" }
            };

            return (await AddFactToProduct(productId, addFactInput), addFactInput);
        }

        async Task<updateFactInput> UpdateFact(productId productId, string factId)
        {
            updateFactInput updateFactInput = new()
            {
                id = factId,
                type = "updated test type",
                value = new scalarValueInput { stringValue = "test updated value" }
            };

            await UpdateFactOfProduct(productId, updateFactInput);

            return updateFactInput;
        }

        Task UpdateFactOfProduct(productId productId, updateFactInput updateFactInput)
        {
            string mutation = new covergoMutationBuilder()
                .updateFactOfProduct(new covergoMutationBuilder.updateFactOfProductArgs(Products.ProductIdToInput(productId), updateFactInput), new resultBuilder()
                    .WithAllFields())
                .Build();

            return _gatewayGraphQlHttpClient.SendMutationAndEnsureAsync<result>(mutation);
        }

        Task<string> AddFactToProduct(productId productId, addFactInput addFactInput)
        {
            string mutation = new covergoMutationBuilder()
                .addFactToProduct(new covergoMutationBuilder.addFactToProductArgs(Products.ProductIdToInput(productId), addFactInput), new createdStatusResultBuilder()
                    .WithAllFields())
                .Build();

            return _gatewayGraphQlHttpClient.CreateAndReturnId(mutation);
        }

        Task ProductFactBatch(productId productId, factBatchInput factBatchInput)
        {
            string mutation = new covergoMutationBuilder()
                .productFactBatch(new covergoMutationBuilder.productFactBatchArgs(Products.ProductIdToInput(productId), factBatchInput), new resultBuilder()
                    .WithAllFields())
                .Build();

            return _gatewayGraphQlHttpClient.SendMutationAndEnsureAsync<result>(mutation);
        }

        async Task<product> SearchProductById(productId id)
        {
            productBuilder fields = new productBuilder()
                .facts(new factBuilder()
                    .id()
                    .type()
                    .value(new scalarValueBuilder()
                        .stringValue()));

            return (await Products.FindById(id, fields)).list!.First()!;
        }
    }
}
