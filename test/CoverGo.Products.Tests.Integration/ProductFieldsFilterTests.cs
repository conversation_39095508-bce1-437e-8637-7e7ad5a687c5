﻿using CoverGo.Gateway.Client;
using CoverGo.Products.Tests.Integration.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration
{
    [Collection("Sequential")]
    public class ProductFieldsFilterTests : TestBase
    {

        [Fact]
        public async Task GIVEN_product_WHEN_filter_by_fields_contains_THEN_returns_this_product()
        {
            string searchValue = CreateNewGuid();
            createProductInput input1 = BuildCreateProductInput(new { foobar = searchValue + CreateNewGuid() }.ToEscapedJsonString());
            productId productId1 = await Products.Create(input1);

            createProductInput input2 = BuildCreateProductInput(new { foobar = CreateNewGuid() }.ToEscapedJsonString());
            productId productId2 = await Products.Create(input2);

            productWhereInput where = new()
            {
                fields = new fieldsWhereInput
                {
                    path = "fields.foobar",
                    condition = fieldsWhereCondition.STRING_CONTAINS,
                    value = new scalarValueInput { stringValue = searchValue }
                }
            };

            product actual = (await Products.QueryProductsAsync(where))?.FirstOrDefault();

            actual.Should().NotBeNull();
            actual.productId.Should().BeEquivalentTo(productId1);
        }

        [Fact]
        public async Task GIVEN_product_WHEN_filter_by_fields_contains_with_random_value_THEN_returns_nothing()
        {
            createProductInput input = BuildCreateProductInput(new { foobar = CreateNewGuid() }.ToEscapedJsonString());
            await Products.Create(input);

            productWhereInput where = new()
            {
                fields = new fieldsWhereInput
                {
                    path = "fields.foobar",
                    condition = fieldsWhereCondition.STRING_CONTAINS,
                    value = new scalarValueInput { stringValue = CreateNewGuid() }
                },
            };

            product actual = (await Products.QueryProductsAsync(where)).FirstOrDefault();

            actual.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_product_WHEN_filter_by_fields_in_THEN_returns_this_product()
        {
            string searchValue = CreateNewGuid();
            createProductInput input1 = BuildCreateProductInput(new { foobar = searchValue }.ToEscapedJsonString());
            productId productId1 = await Products.Create(input1);

            createProductInput input2 = BuildCreateProductInput(new { foobar = CreateNewGuid() }.ToEscapedJsonString());
            await Products.Create(input2);

            productWhereInput where = new()
            {

                fields = new fieldsWhereInput
                {
                    path = "fields.foobar",
                    condition = fieldsWhereCondition.IN,
                    value = new scalarValueInput
                    {
                        arrayValue = new scalarValueInput[] {
                            new() { stringValue = searchValue },
                            new() { stringValue = CreateNewGuid() }
                        }
                    }
                },
            };

            product actual = (await Products.QueryProductsAsync(where))?.FirstOrDefault();

            actual.Should().NotBeNull();
            actual.productId.Should().BeEquivalentTo(productId1);
        }

        [Fact]
        public async Task GIVEN_product_WHEN_filter_by_fields_in_with_random_values_THEN_returns_nothing()
        {
            createProductInput input1 = BuildCreateProductInput(new { foobar = CreateNewGuid() }.ToEscapedJsonString());
            await Products.Create(input1);

            createProductInput input2 = BuildCreateProductInput(new { foobar = CreateNewGuid() }.ToEscapedJsonString());
            await Products.Create(input2);

            productWhereInput where = new()
            {
                fields = new fieldsWhereInput
                {
                    path = "fields.foobar",
                    condition = fieldsWhereCondition.IN,
                    value = new scalarValueInput
                    {
                        arrayValue = new scalarValueInput[] {
                            new() { stringValue = CreateNewGuid() },
                            new() { stringValue = CreateNewGuid() }
                        }
                    }
                },
            };

            product actual = (await Products.QueryProductsAsync(where))?.FirstOrDefault();

            actual.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_product_WHEN_filter_by_fields_equals_THEN_returns_this_product()
        {
            string searchValue = CreateNewGuid();
            createProductInput input1 = BuildCreateProductInput(new { foobar = searchValue }.ToEscapedJsonString());
            productId productId1 = await Products.Create(input1);

            createProductInput input2 = BuildCreateProductInput(new { foobar = CreateNewGuid() }.ToEscapedJsonString());
            await Products.Create(input2);

            productWhereInput where = new()
            {
                fields = new fieldsWhereInput
                {
                    path = "fields.foobar",
                    condition = fieldsWhereCondition.EQUALS,
                    value = new scalarValueInput { stringValue = searchValue }
                },
            };

            product actual = (await Products.QueryProductsAsync(where))?.FirstOrDefault();

            actual.Should().NotBeNull();
            actual.productId.Should().BeEquivalentTo(productId1);
        }

        [Fact]
        public async Task GIVEN_product_WHEN_filter_by_fields_equals_with_random_value_THEN_returns_nothing()
        {
            createProductInput input = BuildCreateProductInput(new { foobar = CreateNewGuid() }.ToEscapedJsonString());
            productId productId = await Products.Create(input);

            productWhereInput where = new()
            {
                and = new[]
                {
                    new productWhereInput
                    {
                        id_in = new[]
                        {
                            new productIdInput
                            {
                                plan = productId.plan,
                                type = productId.type,
                                version =  productId.version
                            }
                        }
                    },
                    new productWhereInput
                    {
                        fields = new fieldsWhereInput
                        {
                            path = "fields.foobar",
                            condition = fieldsWhereCondition.EQUALS,
                            value = new scalarValueInput { stringValue = CreateNewGuid() }
                        }
                    }
                }
            };

            product actual = (await Products.QueryProductsAsync(where))?.FirstOrDefault();

            actual.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_product_WHEN_filter_by_fields_array_contains_THEN_returns_this_product()
        {
            string searchValue = CreateNewGuid();
            createProductInput input1 = BuildCreateProductInput(new { foobar = new[] { searchValue, CreateNewGuid() } }.ToEscapedJsonString());
            productId productId1 = await Products.Create(input1);

            createProductInput input2 = BuildCreateProductInput(new { foobar = new[] { CreateNewGuid(), CreateNewGuid() } }.ToEscapedJsonString());
            await Products.Create(input2);

            productWhereInput where = new()
            {
                fields = new fieldsWhereInput
                {
                    path = "fields.foobar",
                    condition = fieldsWhereCondition.ARRAY_CONTAINS,
                    value = new scalarValueInput { stringValue = searchValue }
                },
            };

            product actual = (await Products.QueryProductsAsync(where))?.FirstOrDefault();

            actual.Should().NotBeNull();
            actual.productId.Should().BeEquivalentTo(productId1);
        }

        [Fact]
        public async Task GIVEN_product_WHEN_filter_by_fields_array_contains_with_random_value_THEN_returns_nothing()
        {
            createProductInput input = BuildCreateProductInput(new { foobar = new[] { CreateNewGuid(), CreateNewGuid() } }.ToEscapedJsonString());
            await Products.Create(input);

            productWhereInput where = new()
            {
                fields = new fieldsWhereInput
                {
                    path = "fields.foobar",
                    condition = fieldsWhereCondition.ARRAY_CONTAINS,
                    value = new scalarValueInput { stringValue = CreateNewGuid() }
                },
            };

            product actual = (await Products.QueryProductsAsync(where))?.FirstOrDefault();

            actual.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_product_WHEN_filter_by_fields_less_than_with_smaller_value_THEN_returns_nothing()
        {
            createProductInput input = BuildCreateProductInput(new { foobar = 100 }.ToEscapedJsonString());
            productId productId = await Products.Create(input);

            productWhereInput where = new()
            {
                and = new[]
                {
                    new productWhereInput
                    {
                        id_in = new[]
                        {
                            new productIdInput
                            {
                                plan = productId.plan,
                                type = productId.type,
                                version =  productId.version
                            }
                        }
                    },
                    new productWhereInput
                    {
                        fields = new fieldsWhereInput
                        {
                            path = "fields.foobar",
                            condition = fieldsWhereCondition.LESS_THAN,
                            value = new scalarValueInput { numberValue = 10 }
                        }
                    }
                }
            };

            product actual = (await Products.QueryProductsAsync(where))?.FirstOrDefault();

            actual.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_product_WHEN_filter_by_fields_greater_than_with_smaller_value_THEN_returns_nothing()
        {
            createProductInput input = BuildCreateProductInput(new { foobar = 1 }.ToEscapedJsonString());
            productId productId = await Products.Create(input);

            productWhereInput where = new()
            {
                and = new[]
                {
                    new productWhereInput
                    {
                        id_in = new[]
                        {
                            new productIdInput
                            {
                                plan = productId.plan,
                                type = productId.type,
                                version =  productId.version
                            }
                        }
                    },
                    new productWhereInput
                    {
                        fields = new fieldsWhereInput
                        {
                            path = "fields.foobar",
                            condition = fieldsWhereCondition.GREATER_THAN,
                            value = new scalarValueInput { numberValue = 10 }
                        }
                    }
                }
            };

            product actual = (await Products.QueryProductsAsync(where))?.FirstOrDefault();

            actual.Should().BeNull();
        }

        async Task<product> Find(productWhereInput where)
        {
            productBuilder fields = new productBuilder()
                    .productId(new productIdBuilder()
                        .plan()
                        .type()
                        .version());

            string query = new covergoQueryBuilder()
                .products_2(new covergoQueryBuilder.products_2Args(where: where), new productsBuilder()
                    .list(fields))
                .Build();

            return (await _gatewayGraphQlHttpClient.SendQueryAndEnsureAsync<products>(query)).list!.FirstOrDefault()!;
        }


        createProductInput BuildCreateProductInput(string fields) =>
           new()
           {
               productId = new productIdInput
               {
                   type = "0866f50c-0a35-4f04-9f70-88f36c31513a",
                   plan = "0866f50c-0a35-4f04-9f70-88f36c31513b",
                   version = CreateNewGuid()
               },
               fields = fields
           };
    }
}
