﻿using CoverGo.Products.Client;
using CoverGo.ChannelManagement.Client;
using CoverGo.Products.Tests.Integration.Utils;
using GraphQL.Client.Http;
using System;
using System.Threading.Tasks;
using CoverGo.Configuration;
using CoverGo.MongoUtils;
using CoverGo.Products.Infrastructure.Utils.Adapters.Mongo;
using MongoDB.Driver;
using CoverGo.Reference.Client;
using System.IO;
using System.Text.Json;
using System.Linq;
using CoverGo.Gateway.Client;
using GraphQL.Client.Serializer.Newtonsoft;
using System.Net.Http.Headers;
using System.Net.Http;

namespace CoverGo.Products.Tests.Integration;

public abstract class TestBase : IAsyncLifetime
{
    protected Utils.Benefits Benefits { get; set; }
    protected Utils.Products Products { get; private set; }
    protected Utils.PermissionSchema PermissionSchema { get; set; }
    protected Utils.ProductType ProductType { get; set; }
    protected Utils.DataSchema DataSchema { get; set; }
    protected Utils.Login Login { get; set; }
    protected Utils.Scripts Scripts { get; private set; }
    protected Utils.Sbus Sbus { get; private set; }
    protected Utils.ProductBuilder ProductBuilder { get; private set; }
    protected Utils.FileSystem FileSystem { get; private set; }
    protected Utils.L10n L10n { get; private set; }
    protected string TenantId { get; } = UserCredentials.Admin.TenantId;
    protected string ClientId { get; } = UserCredentials.Admin.ClientId;
    protected string LoginId { get; private set; }

    protected GraphQLHttpClient _productsGraphQlClient;

    protected GraphQLHttpClient _gatewayGraphQlHttpClient;
    internal IChannelManagementClient _channelManagementClient { get; private set; }


    public async Task InitializeAsync()
    {
        var accessToken = await Setup.GetAccessToken();
        LoginId = Setup.GetLoginIdFromJwtAccessToken(accessToken);

        var productsClient = Setup.BuildProductsHttpClient(accessToken);
        _productsGraphQlClient = Setup.BuildProductsGraphQlClient(accessToken);
        _gatewayGraphQlHttpClient = Setup.BuildGatewaysGraphQlClient(accessToken);

        Products = new Utils.Products(_productsGraphQlClient, _gatewayGraphQlHttpClient, productsClient, TenantId);
        PermissionSchema = new PermissionSchema(_gatewayGraphQlHttpClient);
        ProductType = new Utils.ProductType(_gatewayGraphQlHttpClient);
        DataSchema = new DataSchema(_gatewayGraphQlHttpClient);
        Login = new Login(_gatewayGraphQlHttpClient);
        Scripts = new Utils.Scripts(productsClient, _gatewayGraphQlHttpClient, TenantId);
        Sbus = new Sbus(_productsGraphQlClient);
        Benefits = new Benefits(Setup.BuildGatewaysGraphQlClient(accessToken));

        ProductBuilder = new Utils.ProductBuilder(Setup.BuildProductBuilderGraphQlClient(accessToken));
        FileSystem = new Utils.FileSystem(Setup.BuildFileSystemHttpClient(accessToken), TenantId);
        L10n = new Utils.L10n(Setup.BuildL10nHttpClient(accessToken), TenantId);

        _channelManagementClient = Setup.GetChannelManagementClient(accessToken);

        var referenceClient = Setup.GetReferenceClient(accessToken);
        await InitializeReferenceData(referenceClient);

        await OnInitializeAsync();
    }
    async Task InitializeReferenceData(ReferenceClient referenceClient)
    {
        using var fileStream = File.OpenRead("referenceInputData.json");
        var registerReferenceTypeInputs = await JsonSerializer.DeserializeAsync<Reference.Client.RegisterReferenceTypeInput[]>(fileStream, new JsonSerializerOptions() { PropertyNamingPolicy = JsonNamingPolicy.CamelCase });
        if (registerReferenceTypeInputs == null) throw new InvalidOperationException("Reference input data could not be deserialized.");
        fileStream.Close();

        foreach (Reference.Client.RegisterReferenceTypeInput referenceTypeInput in registerReferenceTypeInputs)
        {
            var queryResponse = await referenceClient.GetReferenceTypes(new Reference.Client.ReferenceTypeFilterInput() { ReferenceMnemonics = [referenceTypeInput.ReferenceMnemonic] }, take: 1);
            if (queryResponse != null && !queryResponse.Data!.ReferenceTypes!.Items!.Any())
            {
                var response = await referenceClient.RegisterReferenceType(referenceTypeInput);
                if (response.Errors?.Count > 0)
                    throw new InvalidOperationException($"Reference data could not be initialized. {response.Errors}");
            }
        }
    }

    protected virtual async Task OnInitializeAsync() => await Task.CompletedTask;

    public virtual async Task DisposeAsync()
    {
        MongoSetup.RegisterConventions();
        IMongoClient newClient = GetMongoClient();
        await newClient.DropDatabaseAsync("products");
        MongoSetup.UnregisterConventions();
    }

    protected static ProductId GenerateProductId(string version = null) =>
        new()
        {
            Plan = Guid.NewGuid().ToString(),
            Type = "gm",
            Version = version ?? Guid.NewGuid().ToString()
        };

    protected static IMongoClient GetMongoClient()
    {
        var dbConfig = new DbConfig
        {
            ProviderId = "mongoDb",
            ConnectionString = Environment.GetEnvironmentVariable("DATABASE_CONNECT_STRING") ??
                               "****************************************"
        };

        return MongoTools.GetOrAddMongoClient(dbConfig);
    }

    protected static string CreateNewGuid() =>
        Guid.NewGuid().ToString();

    protected static string GetResource(string name)
    {
        string? directory = Path.GetDirectoryName(new Uri(typeof(TestBase).Assembly.Location).LocalPath);
        string path = Path.Combine(directory!, "Resources", name + ".json");

        return File.ReadAllText(path);
    }

    protected static async Task<GraphQLHttpClient> CreateGraphQLHttpClient(string clientId, string username, string password)
    {
        GraphQLClientConfig config = new()
        {
            GatewayGraphQLUrl = "http://localhost:60060/graphql",
            TenantId = "covergo",
            ClientId = clientId!,
            Username = username!,
            Password = password!
        };
        config = config.Load();

        covergoQueryBuilder.token_2Args tokenArgs = new(config.TenantId, config.ClientId, config.Username, config.Password);
        HttpClient httpClient = new()
        {
            Timeout = TimeSpan.FromMinutes(5),
        };
        GraphQLHttpClient client =
            new(new GraphQLHttpClientOptions { EndPoint = new Uri(config.GatewayGraphQLUrl) },
                new NewtonsoftJsonSerializer(), httpClient);
        client.HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", await new Token(client).Fetch(tokenArgs));
        return client;
    }
}
