﻿using CoverGo.Products.Client;
using CoverGo.Products.Tests.Integration.Utils;

using System;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration;

[Collection("Sequential")]
public sealed class SbuCrudTests : TestBase
{
    [Fact]
    public async Task GIVEN_sbu_WHEN_create_THEN_returns_id()
    {
        var sbu = await Sbus.LastSbuInFuture();

        sbu.Should().NotBeNull();
    }

    [Fact]
    public async Task GIVEN_command_to_create_sbu_with_zero_amount_WHEN_execute_THEN_fails()
    {
        var sbu = await Sbus.LastSbuInFuture();

        var prevEndDate = sbu.endDate.Value;

        var createSbuCommand = new products_SbuUpsertInput()
        {
            startDate = prevEndDate.AddDays(1),
            endDate = prevEndDate.AddDays(3),
            amount = 0,
        };

        var result = await Sbus.Update(createSbuCommand);

        result.errors.Should().NotBeEmpty();
    }

    // SBU1 *---*
    // SBU2         *---*   OK
    [Fact]
    public async Task GIVEN_two_not_overlapping_sbus_WHEN_create_THEN_returns_id()
    {
        var sbu = await Sbus.LastSbuInFuture();

        var prevEndDate = sbu.endDate.Value;

        var createSbuCommand = new products_SbuUpsertInput()
        {
            startDate = prevEndDate.AddDays(1),
            endDate = prevEndDate.AddDays(3),
            amount = 0.02M,
        };

        string id = await Sbus.Create(createSbuCommand);

        id.Should().NotBeNullOrWhiteSpace();
    }

    // SBU1 *---*
    // SBU2   *---*   FAIL
    [Fact]
    public async Task GIVEN_sbu_WHEN_create_sbu_starts_before_prev_end_THEN_fails()
    {
        var sbu = await Sbus.LastSbuInFuture();

        var prevEndDate = sbu.endDate.Value;

        var createSbuCommand = new products_SbuUpsertInput()
        {
            startDate = prevEndDate.AddDays(-1),
            endDate = prevEndDate.AddDays(1),
            amount = 0.03M,
        };

        var result = await Sbus.Update(createSbuCommand);

        result.errors.Should().NotBeEmpty();
    }

    // SBU1   *---*
    // SBU2 *---*    FAIL
    [Fact]
    public async Task GIVEN_sbu_WHEN_create_sbu_ends_before_prev_end_THEN_fails()
    {
        var sbu = await Sbus.LastSbuInFuture();

        var prevEndDate = sbu.endDate.Value;

        var createSbuCommand = new products_SbuUpsertInput()
        {
            startDate = prevEndDate.AddDays(-3),
            endDate = prevEndDate.AddDays(-1),
            amount = 0.04M,
        };

        var result = await Sbus.Update(createSbuCommand);

        result.errors.Should().NotBeEmpty();
    }

    // SBU1 *---*
    // SBU2             *---*    OK
    // SBU3       *---*          OK
    [Fact]
    public async Task GIVEN_sbu_WHEN_create_sbu_ends_before_prev_start_THEN_returns_id()
    {
        var sbu = await Sbus.LastSbuInFuture();

        var prevEndDate = sbu.endDate.Value;

        var createSbuCommand2 = new products_SbuUpsertInput()
        {
            startDate = prevEndDate.AddDays(4),
            endDate = prevEndDate.AddDays(6),
            amount = 0.05M,
        };

        string id2 = await Sbus.Create(createSbuCommand2);

        id2.Should().NotBeNullOrWhiteSpace();

        var createSbuCommand3 = new products_SbuUpsertInput()
        {
            startDate = prevEndDate.AddDays(1),
            endDate = prevEndDate.AddDays(3),
            amount = 0.05M,
        };

        string id3 = await Sbus.Create(createSbuCommand3);

        id3.Should().NotBeNullOrWhiteSpace();
    }

    // SBU1   *---*
    // SBU2  *-----*    FAIL
    [Fact]
    public async Task GIVEN_sbu_WHEN_create_sbu_starts_before_prev_start_and_ends_after_prev_end_THEN_fails()
    {
        var sbu = await Sbus.LastSbuInFuture();

        var prevStartDate = sbu.startDate.Value;
        var prevEndDate = sbu.endDate.Value;

        var createSbuCommand = new products_SbuUpsertInput()
        {
            startDate = prevStartDate.AddDays(-1),
            endDate = prevEndDate.AddDays(1),
            amount = 0.06M,
        };

        var result = await Sbus.Update(createSbuCommand);

        result.errors.Should().NotBeEmpty();
    }

    [Fact]
    public async Task GIVEN_command_to_create_sbu_in_past_WHEN_execute_THEN_fails()
    {
        var createSbuCommand = new products_SbuUpsertInput()
        {
            startDate = DateTime.UtcNow.Date.AddDays(-1),
            endDate = DateTime.UtcNow.Date.AddDays(1),
            amount = 0.07M,
        };

        var result = await Sbus.Update(createSbuCommand);

        result.errors.Should().NotBeEmpty();
    }

    // SBU1  *---*
    // SBU1'   *---*   OK
    [Fact]
    public async Task GIVEN_sbu_WHEN_update_THEN_updated()
    {
        var sbu = await Sbus.LastSbuInFuture();

        var prevEndDate = sbu.endDate.Value;

        var updateSbuCommand = new products_SbuUpsertInput()
        {
            id = sbu.id,
            startDate = prevEndDate.AddDays(-1),
            endDate = prevEndDate.AddDays(1),
            amount = 0.08M,
        };

        var result = await Sbus.Update(updateSbuCommand);

        result.errors.Should().BeNullOrEmpty();
    }

    // SBU1  *---*
    // SBU1' *-----*   OK
    [Fact]
    public async Task GIVEN_sbu_WHEN_update_only_end_date_THEN_updated()
    {
        var sbu = await Sbus.LastSbuInFuture();

        var prevEndDate = sbu.endDate.Value;

        var updateSbuCommand = new products_SbuUpsertInput()
        {
            id = sbu.id,
            startDate = null,
            endDate = prevEndDate.AddDays(1),
            amount = 0.09M,
        };

        var result = await Sbus.Update(updateSbuCommand);

        result.errors.Should().BeNullOrEmpty();
    }

    // SBU1  s---e
    // SBU1'     e---s   FAIL
    [Fact]
    public async Task GIVEN_sbu_WHEN_move_startdate_to_after_enddate_THEN_fails()
    {
        var sbu = await Sbus.LastSbuInFuture();

        var prevEndDate = sbu.endDate.Value;

        var updateSbuCommand = new products_SbuUpsertInput()
        {
            id = sbu.id,
            startDate = prevEndDate.AddDays(1),
        };

        var result = await Sbus.Update(updateSbuCommand);

        result.errors.Should().NotBeEmpty();
    }

    [Fact]
    public async Task GIVEN_command_to_update_sbu_to_start_in_past_WHEN_execute_THEN_fails()
    {
        var sbu = await Sbus.LastSbuInFuture();

        var updateSbuCommand = new products_SbuUpsertInput()
        {
            id = sbu.id,
            startDate = DateTime.UtcNow.Date.AddDays(-1),
        };

        var result = await Sbus.Update(updateSbuCommand);

        result.errors.Should().NotBeEmpty();
    }

    [Fact]
    public async Task GIVEN_command_to_delete_sbu_WHEN_execute_THEN_deleted()
    {
        var sbu1 = await Sbus.LastSbuInFuture();

        await Sbus.Delete(sbu1.id);

        var sbu2 = await Sbus.LastSbuInFuture();

        sbu2.id.Should().NotBe(sbu1.id);
    }
}