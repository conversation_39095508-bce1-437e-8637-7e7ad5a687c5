using CoverGo.Gateway.Client;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration
{
    [Collection("Sequential")]
    public class ProductBenefitDefinitionTests : TestBase
    {
        //TODO: find why this test is failing on CI
        [Fact(Skip = "Temporary disabled")]
        public async Task GIVEN_benefit_definitions_WHEN_filter_by_types_THEN_success()
        {
            string typeName = CreateNewGuid();
            string typeId = await Benefits.CreateBenefitDefinitionType(typeName);
            string definitionId = await Benefits.CreateBenefitDefinition(typeId);

            string? query = new covergoQueryBuilder().benefitDefinitions(
                    new covergoQueryBuilder.benefitDefinitionsArgs(where: new benefitDefinitionWhereInput
                    {
                        type = new benefitDefinitionTypeWhereInput { name_contains = typeName }
                    }), new benefitDefinitionsBuilder()
                        .totalCount()
                        .list(new benefitDefinitionBuilder().id()))
                .Build();

            benefitDefinitions definitions = await Benefits.GetBenefitDefinitions(new benefitDefinitionWhereInput
            {
                type = new benefitDefinitionTypeWhereInput { name_contains = typeName }
            });

            definitions.totalCount.Should().Be(1);
            benefitDefinition? definition = definitions!.list!.First();
            definition!.id.Should().Be(definitionId);
        }


        //TODO: find why this test is failing on CI
        [Fact(Skip = "Temporary disabled")]
        public async Task GIVEN_two_benefit_definitions_WHEN_searching_by_type_id_using_or_clause_THEN_success()
        {
            string typeName1 = CreateNewGuid();
            string type1Id = await Benefits.CreateBenefitDefinitionType(typeName1);
            string definition1Id = await Benefits.CreateBenefitDefinition(type1Id);

            string typeName2 = CreateNewGuid();
            string type2Id = await Benefits.CreateBenefitDefinitionType(typeName2);
            string definition2Id = await Benefits.CreateBenefitDefinition(type2Id);

            benefitDefinitions definitions = await Benefits.GetBenefitDefinitions(new benefitDefinitionWhereInput
            {
                or = new List<benefitDefinitionWhereInput?>
                {
                    new() { type = new benefitDefinitionTypeWhereInput { name_contains = typeName1 } },
                    new() { type = new benefitDefinitionTypeWhereInput { name_contains = typeName2 } }
                }
            });

            definitions.totalCount.Should().Be(2);

            string[] definitionsIds = definitions!.list!.Select(x => x!.id).ToArray()!;

            definitionsIds.Should().Contain(definition1Id);
            definitionsIds.Should().Contain(definition2Id);
        }

        [Fact]
        public async Task GIVEN_benefit_definitions_with_fields_WHEN_filter_by_fields_THEN_success()
        {
            string uniqueString = CreateNewGuid();
            string fields = $@"{{""test"":""{uniqueString}""}}";
            string typeName = CreateNewGuid();
            string typeId = await Benefits.CreateBenefitDefinitionType(typeName, "{}");
            string definitionId = await Benefits.CreateBenefitDefinition(typeId, fields.Escape());

            benefitDefinitions definitions = await Benefits.GetBenefitDefinitions(new benefitDefinitionWhereInput
            {
                fields = new fieldsWhereInput()
                {
                    path = "fields.test",
                    value = new scalarValueInput() { stringValue = uniqueString },
                    condition = fieldsWhereCondition.EQUALS
                }
            });

            definitions.totalCount.Should().Be(1);
            benefitDefinition? definition = definitions!.list!.First();
            definition!.id.Should().Be(definitionId);
            definition.fields.Should().Be(fields);

            definition.benefitDefinitionTypes.Should().HaveCount(1);
            benefitDefinitionType? benefitDefinitionType = definition.benefitDefinitionTypes!.First();
            benefitDefinitionType!.fields.Should().Be("{}");
        }


        [Fact]
        public async Task
            GIVEN_two_benefit_definitions_have_type_contains_fields_WHEN_searching_by_type_fields_THEN_success()
        {
            string uniqueString = CreateNewGuid();
            string fields = $@"{{""test"":""{uniqueString}""}}";
            string typeName1 = CreateNewGuid();
            string type1Id = await Benefits.CreateBenefitDefinitionType(typeName1, fields.Escape());
            string definition1Id = await Benefits.CreateBenefitDefinition(type1Id);

            string typeName2 = CreateNewGuid();
            string type2Id = await Benefits.CreateBenefitDefinitionType(typeName2);
            string definition2Id = await Benefits.CreateBenefitDefinition(type2Id);


            benefitDefinitions definitions = await Benefits.GetBenefitDefinitions(new benefitDefinitionWhereInput
            {
                type = new benefitDefinitionTypeWhereInput
                {
                    fields = new fieldsWhereInput()
                    {
                        path = "fields.test",
                        value = new scalarValueInput() { stringValue = uniqueString },
                        condition = fieldsWhereCondition.EQUALS
                    }
                }
            });
            definitions.totalCount.Should().Be(1);
            definitions.list!.First()!.id.Should().Be(definition1Id);
        }


        protected static string CreateNewGuid() =>
            Guid.NewGuid().ToString();
    }
}