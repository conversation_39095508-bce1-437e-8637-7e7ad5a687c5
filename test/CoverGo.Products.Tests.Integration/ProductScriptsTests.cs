﻿using CoverGo.Gateway.Client;
using CoverGo.Products.Client;
using CoverGo.Products.Tests.Integration.Utils;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration
{
    [Collection("Sequential")]
    public class ProductScriptsTests : TestBase
    {

        [Fact]
        public async Task GIVEN_product_added_WHEN_add_script_to_it_THEN_succeed_and_product_contains_script()
        {
            productId productId = await Products.Create();

            (string scriptId, createScriptInput createScriptCommand) = await CreateScript();

            await Products.AddScriptToProduct(Products.ProductIdToInput(productId), scriptId);

            script script = (await SearchProductById(productId)).scripts!.First()!;

            script.id.Should().BeEquivalentTo(scriptId);
            script.ShouldHaveSameValuesAsInput(createScriptCommand);
        }

        [Fact]
        public async Task GIVEN_product_with_script_added_WHEN_remove_script_THEN_succeed_and_product_does_not_contain_script()
        {
            productId productId = await Products.Create();

            (string scriptId, _) = await CreateScript();

            await Products.AddScriptToProduct(Products.ProductIdToInput(productId), scriptId);

            await Products.RemoveScriptFromProduct(Products.ProductIdToInput(productId), scriptId);

            product product = await SearchProductById(productId);

            product.scripts.Should().BeNullOrEmpty();
        }

        [Trait("Ticket", "CH-24754")]
        [Fact]
        public async Task GIVEN_product_with_script_WHEN_querying_product_THEN_product_contains_script()
        {
            productId productId = await Products.Create();

            (string scriptId, createScriptInput createScriptCommand) = await CreateScript();

            await Products.AddScriptToProduct(Products.ProductIdToInput(productId), scriptId);

            products_Product product = (await GetProductByIdFromProductsGraphqlClient(productId));
            var script = product.scripts.FirstOrDefault();

            script.id.Should().BeEquivalentTo(scriptId);
            script.inputSchema.Should().Be(createScriptCommand.inputSchema);
            script.type.Should().Be(products_ScriptTypeEnum.PRICING);
        }

        [Trait("Ticket", "CH-24754")]
        [Fact]
        public async Task GIVEN_product_with_script_WHEN_querying_product_with_existing_scriptType_THEN_product_contains_script()
        {
            productId productId = await Products.Create();

            (string scriptId, createScriptInput createScriptCommand) = await CreateScript();

            await Products.AddScriptToProduct(Products.ProductIdToInput(productId), scriptId);

            products_Product product = await GetProductByIdFromProductsGraphqlClient(productId, products_ScriptTypeEnum.PRICING);
            var script = product.scripts.FirstOrDefault();

            script.id.Should().BeEquivalentTo(scriptId);
            script.inputSchema.Should().Be(createScriptCommand.inputSchema);
            script.type.Should().Be(products_ScriptTypeEnum.PRICING);
        }

        [Trait("Ticket", "CH-24754")]
        [Fact]
        public async Task GIVEN_product_with_script_WHEN_querying_product_with_nonexistent_scriptType_THEN_product_contains_no_scripts()
        {
            productId productId = await Products.Create();

            (string scriptId, createScriptInput createScriptCommand) = await CreateScript();

            await Products.AddScriptToProduct(Products.ProductIdToInput(productId), scriptId);

            products_Product product = await GetProductByIdFromProductsGraphqlClient(productId, products_ScriptTypeEnum.UNDERWRITING);
           product.scripts.Should().BeEmpty();
        }

        async Task<(string, createScriptInput)> CreateScript()
        {
            createScriptInput input = new()
            {
                name = CreateNewGuid(),
                inputSchema = "{}",
                outputSchema = "{}",
                sourceCode = "{}",
                referenceSourceCodeUrl = "",
                externalTableDataUrl = "test/tableUrl",
                type = scriptTypeEnum.PRICING
            };

            return (await Scripts.Create(input), input);
        }

        async Task<product> SearchProductById(productId id)
        {
            productBuilder fields = new productBuilder()
                .scripts(new scriptBuilder()
                    .id()
                    .name()
                    .inputSchema()
                    .outputSchema()
                    .sourceCode()
                    .referenceSourceCodeUrl()
                    .externalTableDataUrl()
                    .type());

            products response = await Products.FindById(id, fields);
            return response.list!.Single()!;
        }

        async Task<products_Product> GetProductByIdFromProductsGraphqlClient(productId id, products_ScriptTypeEnum? scriptType = null)
        {
            string query = scriptType == null
                ? new QueryBuilder().product(
                    new QueryBuilder.productArgs(
                        new Client.products_ProductIdInput { plan = id.plan, version = id.version, type = id.type }),
                    new products_ProductBuilder()
                        .id(new Client.products_ProductIdBuilder().WithAllFields())
                        .scripts(new products_ProductBuilder.scriptsArgs(), new products_ScriptBuilder().WithAllFields())).Build()
                : new QueryBuilder().product(
                    new QueryBuilder.productArgs(
                        new Client.products_ProductIdInput { plan = id.plan, version = id.version, type = id.type }),
                    new products_ProductBuilder()
                        .id(new Client.products_ProductIdBuilder().WithAllFields())
                        .scripts(new products_ProductBuilder.scriptsArgs(new products_ScriptWhereInput
                        {
                            type = scriptType.Value
                        }), new products_ScriptBuilder().WithAllFields())).Build()
                ;

            products_Product product = await _productsGraphQlClient.SendQueryAndEnsureAsync<products_Product>(query);
            return product;
        }
    }
}
