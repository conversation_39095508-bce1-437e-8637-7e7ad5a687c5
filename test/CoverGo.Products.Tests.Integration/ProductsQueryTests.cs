﻿using System;
using CoverGo.Products.Client;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Products.Infrastructure.Products.Adapters.Mongo;
using CoverGo.Products.Infrastructure.Utils.Adapters.Mongo;
using MongoDB.Bson;
using MongoDB.Driver;
using ProductType = CoverGo.Products.Domain.Products.ProductType;

namespace CoverGo.Products.Tests.Integration;

[Collection("Sequential")]
public sealed class ProductsQueryTests : TestBase
{
    private IMongoDatabase _db;
    private readonly List<CoverGo.Products.Domain.Products.ProductId> _tenantProducts = new();
    private readonly List<CoverGo.Products.Domain.Products.ProductId> _defaultProducts = new();
    private readonly List<ProductType> _types = new();
    private string _configId;

    protected override async Task OnInitializeAsync() => await InitializedData();

    public override async Task DisposeAsync()
    {
        foreach (ProductType type in _types)
        {
            await DeleteType(type);
        }

        await DeleteConfig();

        await base.DisposeAsync();
    }

    [Theory]
    [InlineData(0, 0, 9)]
    [InlineData(0, 1, 1)]
    [InlineData(0, 7, 7)]
    [InlineData(1, 1, 1)]
    [InlineData(4, 2, 2)]
    [InlineData(5, 1, 1)]
    [InlineData(7, 1, 1)]
    [InlineData(7, 3, 2)]
    public async Task GIVEN_tenant_and_default_products_WHEN_getting_with_skip_and_limit_THEN_returns_correct_products(int? skip, int? limit, int expectedResultsCount)
    {
        var where = new ProductWhere { ProductId = new ProductIdWhere { Type_in = _types.Select(t => t.TypeId).ToList() } };
        var query = new ProductQuery { Where = where, LoadRepresentation = true};
        var queryWithSkipAndLimit = new ProductQuery { Where = where, LoadRepresentation = true, Skip = skip, Limit = limit};

        List<Product> products = await Products.GetProducts(query, ClientId);
        List<Product> productsWithSkipAndLimit = await Products.GetProducts(queryWithSkipAndLimit, ClientId);

        if (queryWithSkipAndLimit.Skip is > 0)
        {
            products = products.Skip(queryWithSkipAndLimit.Skip.Value).ToList();
        }

        if (queryWithSkipAndLimit.Limit is > 0)
        {
            products = products.Take(queryWithSkipAndLimit.Limit.Value).ToList();
        }

        productsWithSkipAndLimit.Count.Should().Be(expectedResultsCount);
        AssertProducts(products, productsWithSkipAndLimit);
    }

    [Theory]
    [InlineData("Plan 1", 4)]
    [InlineData("Plan 3", 1)]
    [InlineData("Plan 1,Plan 3", 5)]
    [InlineData("", 9)]
    public async Task GIVEN_tenant_and_default_products_WHEN_getting_total_count_THEN_return_correct_count(string plans, long expectedCount)
    {
        var productIdWhere = new ProductIdWhere {Type_in = _types.Select(t => t.TypeId).ToList()};
        var where = new ProductWhere { ProductId = productIdWhere };

        if (!string.IsNullOrEmpty(plans))
        {
            where.ProductId.Plan_in = plans.Split(',').ToList();
        }

        long productsCount = await Products.GetProductsTotalCount(where, ClientId);

        productsCount.Should().Be(expectedCount);
    }

    [Theory]
    [InlineData(0, 0, OrderByType.ASC, 9)]
    [InlineData(0, 0, OrderByType.DSC, 9)]
    [InlineData(2, 5, OrderByType.ASC, 5)]
    [InlineData(2, 5, OrderByType.DSC, 5)]
    public async Task GIVEN_tenant_and_default_products_WHEN_getting_sorted_THEN_returns_sorted_products(int? skip, int? limit, OrderByType orderByType, int expectedResultsCount)
    {
        var where = new ProductWhere { ProductId = new ProductIdWhere { Type_in = _types.Select(t => t.TypeId).ToList() } };

        var query = new ProductQuery { Where = where, LoadRepresentation = true};
        var queryWithOrderBy = new ProductQuery { Where = where, LoadRepresentation = true, Skip = skip, Limit = limit, OrderBy = new OrderBy { Type = orderByType, FieldName = "createdAt"}};

        List<Product> products = await Products.GetProducts(query, ClientId);
        List<Product> sortedProducts = await Products.GetProducts(queryWithOrderBy, ClientId);

        products = orderByType == OrderByType.ASC ? products.OrderBy(p => p.CreatedAt).ToList() : products.OrderByDescending(p => p.CreatedAt).ToList();

        if (queryWithOrderBy.Skip is > 0)
        {
            products = products.Skip(queryWithOrderBy.Skip.Value).ToList();
        }

        if (queryWithOrderBy.Limit is > 0)
        {
            products = products.Take(queryWithOrderBy.Limit.Value).ToList();
        }

        sortedProducts.Count.Should().Be(expectedResultsCount);
        AssertProducts(products, sortedProducts);
    }

    private static void AssertProducts(List<Product> products, List<Product> products2)
    {
        products.Count.Should().Be(products2.Count);

        for (int i = 0; i < products.Count; i++)
        {
            Product product = products[i];
            Product product2 = products2[i];

            product.Id?.Plan.Should().Be(product2.Id?.Plan);
            product.Id?.Type.Should().Be(product2.Id?.Type);
            product.Id?.Version.Should().Be(product2.Id?.Version);
            product.TenantId.Should().Be(product2.TenantId);
            product.LastModifiedAt.Should().Be(product2.LastModifiedAt);
        }
    }

     private async Task InitializedData()
    {
        var tenantType1 = new ProductType { TypeId = $"car-{Guid.NewGuid()}", TenantId = TenantId };
        var tenantType2 = new ProductType { TypeId = $"medical-{Guid.NewGuid()}", TenantId = TenantId };
        var defaultType1 = new ProductType { TypeId = $"life-{Guid.NewGuid()}", TenantId = "default" };
        var defaultType2 = new ProductType { TypeId = $"home-{Guid.NewGuid()}", TenantId = "default" };

        _types.AddRange(new []{tenantType1, tenantType2, defaultType1, defaultType2});

        var tenantProduct11 = new CoverGo.Products.Domain.Products.ProductId { Type = tenantType1.TypeId, Plan = "Plan 1", Version = "1" };
        var tenantProduct12 = new CoverGo.Products.Domain.Products.ProductId { Type = tenantType1.TypeId, Plan = "Plan 2", Version = "1" };
        var tenantProduct13 = new CoverGo.Products.Domain.Products.ProductId { Type = tenantType1.TypeId, Plan = "Plan 3", Version = "1" };

        var tenantProduct21 = new CoverGo.Products.Domain.Products.ProductId { Type = tenantType2.TypeId, Plan = "Plan 1", Version = "1" };
        var tenantProduct22 = new CoverGo.Products.Domain.Products.ProductId { Type = tenantType2.TypeId, Plan = "Plan 2", Version = "1" };

        var defaultProduct11 = new CoverGo.Products.Domain.Products.ProductId { Type = defaultType1.TypeId, Plan = "Plan 1", Version = "1" };
        var defaultProduct12 = new CoverGo.Products.Domain.Products.ProductId { Type = defaultType1.TypeId, Plan = "Plan 2", Version = "1" };

        var defaultProduct21 = new CoverGo.Products.Domain.Products.ProductId { Type = defaultType2.TypeId, Plan = "Plan 1", Version = "1" };
        var defaultProduct22 = new CoverGo.Products.Domain.Products.ProductId { Type = defaultType2.TypeId, Plan = "Plan 2", Version = "1" };

        _tenantProducts.AddRange(new []{tenantProduct11, tenantProduct12, tenantProduct13, tenantProduct21, tenantProduct22});
        _defaultProducts.AddRange(new []{defaultProduct11, defaultProduct12, defaultProduct21, defaultProduct22});

        MongoSetup.RegisterConventions();

        _db = GetMongoDatabase();

        foreach (ProductType type in _types)
        {
            await CreateType(type);
        }

        DateTime lastModifiedAt = DateTime.UtcNow;
        DateTime createdAt = DateTime.UtcNow;
        int counter = 0;
        foreach (Domain.Products.ProductId productId in _tenantProducts)
        {
            await CreateProduct(TenantId, productId, createdAt.AddSeconds(counter), lastModifiedAt.AddSeconds(counter));
            counter++;
        }

        lastModifiedAt = DateTime.UtcNow;
        createdAt = DateTime.UtcNow;
        counter = 0;
        foreach (Domain.Products.ProductId productId in _defaultProducts)
        {
            await CreateProduct("default", productId, createdAt.AddSeconds(counter), lastModifiedAt.AddSeconds(counter));
            counter++;
        }

        _configId = await CreateConfig(_tenantProducts, _defaultProducts);

        MongoSetup.UnregisterConventions();
    }

    private async Task CreateType(ProductType type)
    {
        IMongoCollection<ProductType> collection = _db.GetCollection<ProductType>($"{type.TenantId}-types");

        await collection.InsertOneAsync(
            new ProductType
            {
                TypeId = type.TypeId,
                LogoUrl = string.Empty,
                CreatedAt = DateTime.UtcNow,
                LastModifiedAt = DateTime.UtcNow
            });
    }

    private async Task CreateProduct(string tenantId, CoverGo.Products.Domain.Products.ProductId productId, DateTime? createdAt = null, DateTime? lastModifiedAt = null)
    {
        IMongoCollection<MongoProductDao2> collection = _db.GetCollection<MongoProductDao2>($"{tenantId}-{productId.Type}");

        var product = new MongoProductDao2
        {
            Id = ObjectId.GenerateNewId().ToString(),
            ProductId = productId,
            LastModifiedAt = lastModifiedAt ?? DateTime.UtcNow,
            CreatedAt = createdAt ?? DateTime.UtcNow
        };

        await collection.InsertOneAsync(product);
    }

    private async Task<string> CreateConfig(IEnumerable<CoverGo.Products.Domain.Products.ProductId> tenantProducts,
        IEnumerable<CoverGo.Products.Domain.Products.ProductId> defaultProducts)
    {
        var command = new CreateProductConfigCommand
        {
            ClientId = ClientId,
            DisplayedProducts = new Dictionary<string, List<ProductId>>
            {
                {
                    "default",
                    defaultProducts
                        .Select(p => new ProductId { Type = p.Type, Plan = p.Plan, Version = p.Version }).ToList()
                },
                {
                    TenantId,
                    tenantProducts
                        .Select(p => new ProductId { Type = p.Type, Plan = p.Plan, Version = p.Version }).ToList()
                }
            }
        };

        CreatedStatus status = await Products.CreateConfig(command);
        return status.Id;
    }

    private async Task DeleteConfig()
    {
        if (_configId != null)
        {
            await Products.DeleteConfig(_configId);
        }
    }

    private async Task DeleteType(ProductType type)
    {
        IMongoCollection<ProductType> collection = _db.GetCollection<ProductType>($"{type.TenantId}-types");
        FilterDefinition<ProductType> filter = Builders<ProductType>.Filter.Eq(p => p.TypeId, type.TypeId);
        await collection.DeleteOneAsync(filter);
        await _db.DropCollectionAsync($"{type.TenantId}-{type.TypeId}");
    }

    private static IMongoDatabase GetMongoDatabase() => GetMongoClient().GetDatabase("products");
}