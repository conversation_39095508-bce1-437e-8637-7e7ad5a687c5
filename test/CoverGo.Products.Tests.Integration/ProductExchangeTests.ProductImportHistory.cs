using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using CoverGo.Products.Client;
using CoverGo.Products.Tests.Integration.Utils;

namespace CoverGo.Products.Tests.Integration;

public partial class ProductExchangeTests
{
    [Fact]
    public async Task GIVEN_an_imported_product_WHEN_getting_its_import_history_THEN_expected_history_returned()
    {
        string sourceProductTreeId = await CreateProductTree();
        ProductId productId = await CreateProduct(sourceProductTreeId, lifecycleStage: "Alpha");
        await FileSystem.Initialize();
        string productName = $"Test Product {productId.Plan}";
        await L10n.Upsert($"products-{productId.Plan}|{productId.Version}|{productId.Type}-name", productName);
        string filePath = await ExportProduct(productId);
        products_ProductImportInfo productImportInfo = await GetProductImportInfo(filePath);
        await ImportProduct(filePath);

        ICollection<products_ProductImportHistoryRecord> history = await GetProductImportHistory(productId);

        history.Should().HaveCount(1);

        products_ProductImportHistoryRecord record = history.First();
        products_ProductImportHistoryPackage package = record.package;
        package.Should().NotBeNull();

        package.productLastModifiedAt.Should().NotBeNull().And.BeCloseTo(productImportInfo.productLastModifiedAt.Value, TimeSpan.FromSeconds(1));
        record.importedAt.Should().NotBeNull().And.BeAfter(package.productLastModifiedAt.Value);
        record.importedById.Should().NotBeNullOrEmpty().And.Be(LoginId);
        record.importedBy.Should().Be(UserCredentials.Admin.UserName);

        package.productId.Should().NotBeNull();
        package.productId.plan.Should().Be(productId.Plan);
        package.productId.type.Should().Be(productId.Type);
        package.productId.version.Should().Be(productId.Version);
        package.productName.Should().Be(productName);
        package.productLifecycleStage.Should().Be("Alpha");
        package.productLastModifiedBy.Should().Be(UserCredentials.Admin.UserName);
        package.tenant.Should().Be(TenantId);
        productImportInfo.exportedAt.Should().NotBeNull();
        package.exportedAt.Should().NotBeNull().And.BeCloseTo(productImportInfo.exportedAt.Value, TimeSpan.FromSeconds(1));
        package.exportedBy.Should().Be(UserCredentials.Admin.UserName);
        package.includedEntries.Should().BeEquivalentTo(productImportInfo.includedEntries);
        package.requestId.Should().NotBeNull();
        filePath.Should().StartWith("exchange%2F").And.EndWith(".cgx");
        package.relativeFilePath.Should().Be(filePath);
    }

    [Fact]
    public async Task GIVEN_a_product_imported_two_times_WHEN_getting_its_import_history_THEN_expected_history_returned()
    {
        string sourceProductTreeId = await CreateProductTree();
        ProductId productId = await CreateProduct(sourceProductTreeId);
        await FileSystem.Initialize();
        string filePath = await ExportProduct(productId);
        await ImportProduct(filePath);
        await ImportProduct(filePath);

        ICollection<products_ProductImportHistoryRecord> history = await GetProductImportHistory(productId);

        history.Should().HaveCount(2);
    }

    [Fact]
    public async Task GIVEN_a_product_without_import_history_WHEN_getting_its_import_history_THEN_the_history_is_empty()
    {
        ProductId productId = await CreateProduct(productTreeId: null);

        ICollection<products_ProductImportHistoryRecord> history = await GetProductImportHistory(productId);

        history.Should().BeEmpty();
    }

    [Fact]
    public async Task GIVEN_two_products_imported_two_times_each_WHEN_getting_their_import_history_THEN_expected_history_returned()
    {
        string sourceProductTreeId1 = await CreateProductTree();
        string sourceProductTreeId2 = await CreateProductTree();
        ProductId productId1 = await CreateProduct(sourceProductTreeId1);
        ProductId productId2 = await CreateProduct(sourceProductTreeId1);
        await FileSystem.Initialize();
        string filePath1 = await ExportProduct(productId1);
        string filePath2 = await ExportProduct(productId2);
        await ImportProduct(filePath1);
        await ImportProduct(filePath1);
        await ImportProduct(filePath2);
        await ImportProduct(filePath2);

        ICollection<products_ProductImportHistoryRecord> history = await GetProductImportHistory(productId1, productId2);

        history.Should().HaveCount(4);
        history.Select(r => r.package.productId.type).Should().BeEquivalentTo(new[] { productId1.Type, productId1.Type, productId2.Type, productId2.Type });
    }

    [Fact]
    public async Task GIVEN_product_import_history_WHEN_requested_without_filter_THEN_expected_history_returned()
    {
        string sourceProductTreeId = await CreateProductTree();
        ProductId productId = await CreateProduct(sourceProductTreeId);
        await FileSystem.Initialize();
        string filePath = await ExportProduct(productId);
        await ImportProduct(filePath);

        ICollection<products_ProductImportHistoryRecord> history = await GetProductImportHistory(productId);

        history.Should().HaveCountGreaterThanOrEqualTo(1);
    }

    [Fact]
    public async Task GIVEN_two_products_imported_two_times_each_WHEN_getting_their_import_history_with_disjunction_THEN_expected_history_returned()
    {
        string sourceProductTreeId1 = await CreateProductTree();
        string sourceProductTreeId2 = await CreateProductTree();
        ProductId productId1 = await CreateProduct(sourceProductTreeId1);
        ProductId productId2 = await CreateProduct(sourceProductTreeId1);
        await FileSystem.Initialize();
        string filePath1 = await ExportProduct(productId1);
        string filePath2 = await ExportProduct(productId2);
        await ImportProduct(filePath1);
        await ImportProduct(filePath1);
        await ImportProduct(filePath2);
        await ImportProduct(filePath2);

        ICollection<products_ProductImportHistoryRecord> history = await Products.GetProductImportHistory(new products_GenericProductImportHistoryRecordQueryInput
        {
            where = new products_GenericProductImportHistoryRecordFilterInput
            {
                or = new List<products_GenericProductImportHistoryRecordFilterInput>
                {
                    new products_GenericProductImportHistoryRecordFilterInput
                    {
                        where = new products_ProductImportHistoryRecordFilterInput
                        {
                            productId_in = new List<products_ProductIdInput> { new products_ProductIdInput { plan = productId1.Plan, type = productId1.Type, version = productId1.Version } }
                        }
                    },
                    new products_GenericProductImportHistoryRecordFilterInput
                    {
                        where = new products_ProductImportHistoryRecordFilterInput
                        {
                            productId_in = new List<products_ProductIdInput> { new products_ProductIdInput { plan = productId2.Plan, type = productId2.Type, version = productId2.Version } }
                        }
                    }
                }
            }
        });

        history.Should().HaveCount(4);
        history.Select(r => r.package.productId.type).Should().BeEquivalentTo(new[] { productId1.Type, productId1.Type, productId2.Type, productId2.Type });
    }

    [Fact]
    public async Task GIVEN_two_products_imported_two_times_each_WHEN_getting_their_import_history_with_conjunction_THEN_empty_history_returned()
    {
        string sourceProductTreeId1 = await CreateProductTree();
        string sourceProductTreeId2 = await CreateProductTree();
        ProductId productId1 = await CreateProduct(sourceProductTreeId1);
        ProductId productId2 = await CreateProduct(sourceProductTreeId1);
        await FileSystem.Initialize();
        string filePath1 = await ExportProduct(productId1);
        string filePath2 = await ExportProduct(productId2);
        await ImportProduct(filePath1);
        await ImportProduct(filePath1);
        await ImportProduct(filePath2);
        await ImportProduct(filePath2);

        ICollection<products_ProductImportHistoryRecord> history = await Products.GetProductImportHistory(new products_GenericProductImportHistoryRecordQueryInput
        {
            where = new products_GenericProductImportHistoryRecordFilterInput
            {
                and = new List<products_GenericProductImportHistoryRecordFilterInput>
                {
                    new products_GenericProductImportHistoryRecordFilterInput
                    {
                        where = new products_ProductImportHistoryRecordFilterInput
                        {
                            productId_in = new List<products_ProductIdInput> { new products_ProductIdInput { plan = productId1.Plan, type = productId1.Type, version = productId1.Version } }
                        }
                    },
                    new products_GenericProductImportHistoryRecordFilterInput
                    {
                        where = new products_ProductImportHistoryRecordFilterInput
                        {
                            productId_in = new List<products_ProductIdInput> { new products_ProductIdInput { plan = productId2.Plan, type = productId2.Type, version = productId2.Version } }
                        }
                    }
                }
            }
        });

        history.Should().BeEmpty();
    }

    private Task<ICollection<products_ProductImportHistoryRecord>> GetProductImportHistory(params ProductId[] productIds) => Products.GetProductImportHistory(new products_GenericProductImportHistoryRecordQueryInput
    {
        where = new products_GenericProductImportHistoryRecordFilterInput
        {
            where = new products_ProductImportHistoryRecordFilterInput
            {
                productId_in = productIds.Select(id => new products_ProductIdInput { plan = id.Plan, type = id.Type, version = id.Version }).ToArray()
            }
        }
    });
}