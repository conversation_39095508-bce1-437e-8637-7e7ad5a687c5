using CoverGo.Gateway.Client;
using CoverGo.Products.Client;
using CoverGo.Products.Tests.Integration.Support;
using CoverGo.Products.Tests.Integration.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Products.Tests.Integration.ProductsStrawberryClient;
using StrawberryShake;
namespace CoverGo.Products.Tests.Integration
{
    [Collection("Sequential")]
    [Trait("Ticket", "CH-22266")]
    public class AttachDocumentsTest : ApiTestBase
    {
        [Fact]
        public async Task AddAttachmentsToProduct()
        {
            // Arrange (Given)
            productId productId = await CreateProductWithRepresentation();

            var templateName = "Test Template 1";
            var templateLogicalId = "TEST_TEMPLATE_1";
            string templateId = await CreateTemplate(templateName, templateLogicalId);
            var category = Products_AttachedDocumentCategory.Offer;

            // Act (When)
            await AttachDocumentsToProduct(productId, templateId, category);

            // Assert (Then)
            var planDetails = await this.ProductsClient.GetProductPlanDetails.ExecuteAsync(productId.plan, productId.type, productId.version);
            var attachedDocument = planDetails.Data.ProductProductPlanDetails.AttachedDocuments[0];
            attachedDocument.Should().NotBeNull();
            attachedDocument.Name.Should().Be(templateName);
            attachedDocument.LogicalId.Should().Be(templateLogicalId);
            attachedDocument.Category.Should().Be(category);
        }

        [Fact]
        public async Task AddToExistingAttachmentsToProduct()
        {
            // Arrange (Given)
            productId productId = await CreateProductWithRepresentation();

            var category = Products_AttachedDocumentCategory.Offer;
            var template1Name = "Test Template 1";
            var template1LogicalId = "TEST_TEMPLATE_1";
            string tempalte1Id = await CreateTemplate(template1Name, template1LogicalId);

            var template2Name = "Other Test Template 2";
            var template2LogicalId = "OTHER_TEST_TEMPLATE_2";
            string tempalte2Id = await CreateTemplate(template2Name, template2LogicalId);

            // Act (When)
            await AttachDocumentsToProduct(productId, tempalte1Id, category);
            await AttachDocumentsToProduct(productId, tempalte2Id, category);

            // Assert (Then)
            var planDetails = await this.ProductsClient.GetProductPlanDetails.ExecuteAsync(productId.plan, productId.type, productId.version);
            var attachedDocuments = planDetails.Data.ProductProductPlanDetails.AttachedDocuments;
            attachedDocuments.Should().NotBeNull();
            attachedDocuments.Count.Should().Be(2);
            attachedDocuments[0].Name.Should().Be(template1Name);
            attachedDocuments[0].LogicalId.Should().Be(template1LogicalId);
            attachedDocuments[0].Category.Should().Be(category);
            attachedDocuments[1].Name.Should().Be(template2Name);
            attachedDocuments[1].LogicalId.Should().Be(template2LogicalId);
            attachedDocuments[1].Category.Should().Be(category);
        }

        [Fact]
        public async Task RemoveAttachmentFromProduct()
        {
            // Arrange (Given)
            productId productId = await CreateProductWithRepresentation();

            var templateName = "Test Template 1";
            var templateLogicalId = "TEST_TEMPLATE_1";
            string templateId = await CreateTemplate(templateName, templateLogicalId);
            var category = Products_AttachedDocumentCategory.Offer;

            // Add document first
            await AttachDocumentsToProduct(productId, templateId, category);

            // Act (When)
            var result = await ProductsClient.RemoveAttachedDocumentsFromProduct.ExecuteAsync(
                new()
                {
                    ProductId = new()
                    {
                        Plan = productId.plan,
                        Type = productId.type,
                        Version = productId.version
                    },
                    AttachedDocumentId = templateId
                }
            );

            // Assert (Then)
            result.Errors.Should().BeNullOrEmpty();
            var planDetails = await this.ProductsClient.GetProductPlanDetails.ExecuteAsync(productId.plan, productId.type, productId.version);
            var attachedDocuments = planDetails.Data.ProductProductPlanDetails.AttachedDocuments;
            attachedDocuments.Should().BeEmpty();
        }

        [Fact]
        [Trait("Ticket", "CH-22266")]
        public async Task CloneProductWithAttachments()
        {
            // Arrange (Given)
            productId productId = await CreateProductWithRepresentation();

            var templateName = "Test Template 1";
            var templateLogicalId = "TEST_TEMPLATE_1";
            string templateId = await CreateTemplate(templateName, templateLogicalId);
            var category = Products_AttachedDocumentCategory.Offer;

            // Act (When)
            await AttachDocumentsToProduct(productId, templateId, category);

            // Act (When)
            var newVersion = Guid.NewGuid().ToString();
            await CloneProduct(productId, newVersion);

            // Assert (Then)
            var clonedProduct = await this.ProductsClient.GetProductPlanDetails.ExecuteAsync(productId.plan, productId.type, newVersion);
            clonedProduct.Errors.Should().BeNullOrEmpty();
            clonedProduct.Data.ProductProductPlanDetails.AttachedDocuments.Should().NotBeNull();
            clonedProduct.Data.ProductProductPlanDetails.AttachedDocuments.Count.Should().Be(1);
            clonedProduct.Data.ProductProductPlanDetails.AttachedDocuments[0].Name.Should().Be(templateName);
            clonedProduct.Data.ProductProductPlanDetails.AttachedDocuments[0].LogicalId.Should().Be(templateLogicalId);
            clonedProduct.Data.ProductProductPlanDetails.AttachedDocuments[0].Category.Should().Be(category);
        }

        [Fact]
        [Trait("Ticket", "CH-22793")]
        public async Task GetProductPlanDetailsWithNoRepresentationShouldNotThrowErrors()
        {
            // Arrange (Given)
            productId productId = await Products.Create();

            var templateName = "Test Template 1";
            var templateLogicalId = "TEST_TEMPLATE_1";
            string templateId = await CreateTemplate(templateName, templateLogicalId);
            var category = Products_AttachedDocumentCategory.Offer;

            // Act (When)
            await AttachDocumentsToProduct(productId, templateId, category);

            // Assert (Then)
            var planDetails = await this.ProductsClient.GetProductPlanDetails.ExecuteAsync(productId.plan, productId.type, productId.version);
            planDetails.Errors.Should().BeNullOrEmpty();
            var attachedDocument = planDetails.Data.ProductProductPlanDetails.AttachedDocuments[0];
            attachedDocument.Should().NotBeNull();
            attachedDocument.Name.Should().Be(templateName);
            attachedDocument.LogicalId.Should().Be(templateLogicalId);
            attachedDocument.Category.Should().Be(category);
        }

        async Task CloneProduct(productId productId, string newVersion)
        {
            await ProductsClient.CloneProduct.ExecuteAsync(new() { Plan = productId.plan, Type = productId.type, Version = productId.version }, newVersion, false);
        }

        async Task<productId> CreateProductWithRepresentation()
        {
            var productId = await Products.Create();
            await Products.Update(
                new productIdInput() { plan = productId.plan, type = productId.type, version = productId.version },
                new updateProductInput()
                {
                    representation = "[{'id':'1','label':'Product Template','props':{'theme':'#e4b917'},'productType':'gm','prorateMethod':'prorataMonthly','minified':true,'ch':[{'id':'2','label':'Plan','readonly':false,'ch':[],'n':'plan','p':[{'n':'id','l':'ID','c':'Text','d':'string','v':'plan1'},{'n':'name','l':'Name','c':'Text','d':'string','v':'Plan 1'}]}],'n':'root','pc':'HKD','pbf':'annually'}]"
                });
            return productId;
        }

        async Task<string> CreateTemplate(string name, string logicalId)
        {

            string mutation = new covergoMutationBuilder()
                .createWkhtmltopdfTemplate(new covergoMutationBuilder.createWkhtmltopdfTemplateArgs(new createWkhtmltopdfTemplateInput { name = name, logicalId = logicalId }), new createdStatusResultBuilder()
                    .WithAllFields())
                .Build();

            await _gatewayGraphQlHttpClient.SendMutationAndEnsureAsync<result>(mutation);

            string query = new covergoQueryBuilder()
                .templates(new covergoQueryBuilder.templatesArgs(where: new templateWhereInput { logicalId = logicalId }), new templatesBuilder()
                    .list(new templateInterfaceBuilder()
                        .id()))
                    .Build();

            templates response = await _gatewayGraphQlHttpClient.SendQueryAndEnsureAsync<templates>(query);
            return response.list!.First()!.id!;
        }

        private async Task AttachDocumentsToProduct(productId productId, string templateId, Products_AttachedDocumentCategory category)
        {
            var result = await ProductsClient.AttachDocumentsToProduct.ExecuteAsync(
                new()
                {
                    ProductId =
                    new()
                    {
                        Plan = productId.plan,
                        Type = productId.type,
                        Version = productId.version
                    },
                    AttachedDocumentsIds =
                    [
                        new()
                        {
                            TemplateId = templateId,
                            Category = category
                        }
                    ]
                }
            );
            result.Errors.Should().BeNullOrEmpty();
        }
    }
}
