using CoverGo.Gateway.Client;
using CoverGo.Products.Client;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration;

[Collection("Sequential")]
public class ProductTermsAndConditionsTests : TestBase
{
    [Fact(Skip = "Temporary disable for Kobe to fix")]
    public async Task GIVEN_product_WHEN_setting_terms_and_conditions_THEN_terms_and_conditions_should_be_set()
    {
        string testTemplateId = "testId";

        productId productId = await Products.Create();

        await Products.SetTermsAndConditions(new products_SetTermsAndConditionsInput { productId = new() { plan = productId.plan, type = productId.type, version = productId.version }, templateId = testTemplateId });

        Product product = await Products.GetProductByProductId(new ProductId { Plan = productId.plan, Type = productId.type, Version = productId.version });

        product.TermsAndConditionsTemplateId.Should().Be(testTemplateId);

    }

    [Fact(Skip = "Temporary disable for Kobe to fix")]
    public async Task GIVEN_product_with_terms_and_conditions_WHEN_removing_terms_and_conditions_THEN_terms_and_conditions_should_be_null()
    {
        string testTemplateId = "testId";

        productId productId = await Products.Create();

        await Products.SetTermsAndConditions(new products_SetTermsAndConditionsInput { productId = new() { plan = productId.plan, type = productId.type, version = productId.version }, templateId = testTemplateId });
        await Products.RemoveTermsAndConditions(new products_RemoveTermsAndConditionsInput { productId = new() { plan = productId.plan, type = productId.type, version = productId.version } });

        Product product = await Products.GetProductByProductId(new ProductId { Plan = productId.plan, Type = productId.type, Version = productId.version });

        product.TermsAndConditionsTemplateId.Should().Be(null);
    }
}