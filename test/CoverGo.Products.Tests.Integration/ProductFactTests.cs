﻿using CoverGo.DomainUtils;
using CoverGo.Products.Client;
using CoverGo.Products.Tests.Integration.Utils;

using Newtonsoft.Json.Linq;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration;

[Collection("Sequential")]
public sealed class ProductFactTests : TestBase
{
    [Fact]
    public async Task GIVEN_update_fact_command_WHEN_updating_fact_of_product_THEN_update_fact_content()
    {
        ProductId productId = await CreateProduct();
        string factId = await AddFact(productId);

        var type = Guid.NewGuid().ToString();
        var value = JToken.FromObject(new { });
        var updatedBy = Guid.NewGuid().ToString();
        UpdateFactCommand updateFactCommand = new UpdateFactCommand
        {
            Id = factId,
            Type = type,
            IsTypeChanged = true,
            Value = value,
            IsValueChanged = true,
            ModifiedById = updatedBy
        };
        Result result = await Products.UpdateFact(productId.ToProductIdString(), updateFactCommand);
        result.Status.Should().Be("success");
        result.Errors.Should().BeNullOrEmpty();

        Product product = await Products.GetProductByProductId(productId);
        Fact fact = product.Facts.FirstOrDefault();

        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });

        fact.Type.Should().Be(type);
        fact.Value.Should().BeEquivalentTo(value);
        product.LastModifiedById.Should().Be(updatedBy);

    }

    [Fact]
    public async Task GIVEN_update_fact_command_WHEN_product_not_exist_THEN_return_failure()
    {
        ProductId productId = GenerateProductId();

        var type = Guid.NewGuid().ToString();
        var value = JToken.FromObject(new { });
        var updatedBy = Guid.NewGuid().ToString();
        UpdateFactCommand updateFactCommand = new UpdateFactCommand
        {
            Id = Guid.NewGuid().ToString(),
            Type = type,
            IsTypeChanged = true,
            Value = value,
            IsValueChanged = true,
            ModifiedById = updatedBy
        };
        Result result = await Products.UpdateFact(productId.ToProductIdString(), updateFactCommand);

        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });

        result.Status.Should().Be("failure");
        result.Errors.Should().NotBeNullOrEmpty();
        result.Errors.FirstOrDefault().Should().Be($"The product {productId.ToProductIdString()} was not found.");
    }

    [Fact]
    public async Task GIVEN_update_fact_command_WHEN_fact_not_exist_THEN_return_failure()
    {
        ProductId productId = await CreateProduct();

        var type = Guid.NewGuid().ToString();
        var value = JToken.FromObject(new { });
        var updatedBy = Guid.NewGuid().ToString();
        UpdateFactCommand updateFactCommand = new UpdateFactCommand
        {
            Id = Guid.NewGuid().ToString(),
            Type = type,
            IsTypeChanged = true,
            Value = value,
            IsValueChanged = true,
            ModifiedById = updatedBy
        };
        Result result = await Products.UpdateFact(productId.ToProductIdString(), updateFactCommand);

        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });

        result.Status.Should().Be("failure");
        result.Errors.Should().NotBeNullOrEmpty();
        result.Errors.FirstOrDefault().Should().Be($"The fact with Id '{updateFactCommand.Id}' was not found for this product.");
    }

    [Fact]
    public async Task GIVEN_fact_command_batch_WHEN_adding_and_updating_multiple_facts_of_product_THEN_add_and_update_multiple_facts_content()
    {
        ProductId productId = await CreateProduct();
        string factId1 = await AddFact(productId);
        string factId2 = await AddFact(productId);
        string factId3 = await AddFact(productId);

        var updateType1 = Guid.NewGuid().ToString();
        var updateType2 = Guid.NewGuid().ToString();
        var updateType3 = Guid.NewGuid().ToString();
        var value = JToken.FromObject(new { });
        var updatedBy = Guid.NewGuid().ToString();

        FactCommandBatch factCommandBatch = new FactCommandBatch
        {
            AddFactCommands = new List<AddFactCommand>
            {
                new AddFactCommand
                {
                    Id = Guid.NewGuid().ToString(),
                    Type = Guid.NewGuid().ToString(),
                },
                new AddFactCommand
                {
                    Id = Guid.NewGuid().ToString(),
                    Type = Guid.NewGuid().ToString(),
                }
            },

            UpdateFactCommands = new List<UpdateFactCommand> {
                new UpdateFactCommand
                {
                    Id = factId1,
                    Type = updateType1,
                    IsTypeChanged = true,
                    Value = value,
                    IsValueChanged = true,
                    ModifiedById = updatedBy
                },
                new UpdateFactCommand
                {
                    Id = factId2,
                    Type = updateType2,
                    IsTypeChanged = true,
                    Value = value,
                    IsValueChanged = true,
                    ModifiedById = updatedBy
                },
                new UpdateFactCommand
                {
                    Id = factId3,
                    Type = updateType3,
                    IsTypeChanged = true,
                    Value = value,
                    IsValueChanged = true,
                    ModifiedById = updatedBy
                }
            }
        };
        Result result = await Products.FactBatch(productId.ToProductIdString(), factCommandBatch);

        Product product = await Products.GetProductByProductId(productId);
        Fact fact1 = product.Facts.Where(f => f.Id == factId1).FirstOrDefault();
        Fact fact2 = product.Facts.Where(f => f.Id == factId2).FirstOrDefault();
        Fact fact3 = product.Facts.Where(f => f.Id == factId3).FirstOrDefault();

        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });

        result.Status.Should().Be("success");
        result.Errors.Should().BeNullOrEmpty();

        product.Facts.Count().Should().Be(5);

        fact1.Type.Should().Be(updateType1);
        fact1.Value.Should().BeEquivalentTo(value);
        product.LastModifiedById.Should().Be(updatedBy);

        fact2.Type.Should().Be(updateType2);
        fact2.Value.Should().BeEquivalentTo(value);
        product.LastModifiedById.Should().Be(updatedBy);

        fact3.Type.Should().Be(updateType3);
        fact3.Value.Should().BeEquivalentTo(value);
        product.LastModifiedById.Should().Be(updatedBy);
    }

    [Fact]
    public async Task GIVEN_add_fact_only_WHEN_querying_product_fact_batch_THEN_add_multiple_facts_content()
    {
        ProductId productId = await CreateProduct();

        FactCommandBatch factCommandBatch = new FactCommandBatch
        {
            AddFactCommands = new List<AddFactCommand>
            {
                new AddFactCommand
                {
                    Id = Guid.NewGuid().ToString(),
                    Type = Guid.NewGuid().ToString(),
                },
                new AddFactCommand
                {
                    Id = Guid.NewGuid().ToString(),
                    Type = Guid.NewGuid().ToString(),
                }
            },
        };
        Result result = await Products.FactBatch(productId.ToProductIdString(), factCommandBatch);

        Product product = await Products.GetProductByProductId(productId);

        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });

        result.Status.Should().Be("success");
        result.Errors.Should().BeNullOrEmpty();

        product.Facts.Count().Should().Be(2);
    }

    [Fact]
    public async Task GIVEN_add_fact_only_WHEN_querying_product_fact_batch_THEN_update_multiple_facts_content()
    {
        ProductId productId = await CreateProduct();
        string factId1 = await AddFact(productId);
        string factId2 = await AddFact(productId);
        string factId3 = await AddFact(productId);

        var updateType1 = Guid.NewGuid().ToString();
        var updateType2 = Guid.NewGuid().ToString();
        var updateType3 = Guid.NewGuid().ToString();
        var value = JToken.FromObject(new { });
        var updatedBy = Guid.NewGuid().ToString();

        FactCommandBatch factCommandBatch = new FactCommandBatch
        {
            UpdateFactCommands = new List<UpdateFactCommand> {
                new UpdateFactCommand
                {
                    Id = factId1,
                    Type = updateType1,
                    IsTypeChanged = true,
                    Value = value,
                    IsValueChanged = true,
                    ModifiedById = updatedBy
                },
                new UpdateFactCommand
                {
                    Id = factId2,
                    Type = updateType2,
                    IsTypeChanged = true,
                    Value = value,
                    IsValueChanged = true,
                    ModifiedById = updatedBy
                },
                new UpdateFactCommand
                {
                    Id = factId3,
                    Type = updateType3,
                    IsTypeChanged = true,
                    Value = value,
                    IsValueChanged = true,
                    ModifiedById = updatedBy
                }
            }
        };
        Result result = await Products.FactBatch(productId.ToProductIdString(), factCommandBatch);

        Product product = await Products.GetProductByProductId(productId);
        Fact fact1 = product.Facts.Where(f => f.Id == factId1).FirstOrDefault();
        Fact fact2 = product.Facts.Where(f => f.Id == factId2).FirstOrDefault();
        Fact fact3 = product.Facts.Where(f => f.Id == factId3).FirstOrDefault();

        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });

        result.Status.Should().Be("success");
        result.Errors.Should().BeNullOrEmpty();

        fact1.Type.Should().Be(updateType1);
        fact1.Value.Should().BeEquivalentTo(value);
        product.LastModifiedById.Should().Be(updatedBy);

        fact2.Type.Should().Be(updateType2);
        fact2.Value.Should().BeEquivalentTo(value);
        product.LastModifiedById.Should().Be(updatedBy);

        fact3.Type.Should().Be(updateType3);
        fact3.Value.Should().BeEquivalentTo(value);
        product.LastModifiedById.Should().Be(updatedBy);
    }

    [Fact]
    public async Task GIVEN_fact_command_batch_WHEN_updating_multiple_facts_of_product_THEN_update_multiple_facts_content()
    {
        ProductId productId = await CreateProduct();
        string factId1 = await AddFact(productId);
        string factId2 = await AddFact(productId);
        string factId3 = await AddFact(productId);

        var type1 = Guid.NewGuid().ToString();
        var type2 = Guid.NewGuid().ToString();
        var type3 = Guid.NewGuid().ToString();
        var value = JToken.FromObject(new { });
        var updatedBy = Guid.NewGuid().ToString();

        FactCommandBatch factCommandBatch = new FactCommandBatch
        {
            UpdateFactCommands = new List<UpdateFactCommand> {
                new UpdateFactCommand
                {
                    Id = factId1,
                    Type = type1,
                    IsTypeChanged = true,
                    Value = value,
                    IsValueChanged = true,
                    ModifiedById = updatedBy
                },
                new UpdateFactCommand
                {
                    Id = factId2,
                    Type = type2,
                    IsTypeChanged = true,
                    Value = value,
                    IsValueChanged = true,
                    ModifiedById = updatedBy
                },
                new UpdateFactCommand
                {
                    Id = factId3,
                    Type = type3,
                    IsTypeChanged = true,
                    Value = value,
                    IsValueChanged = true,
                    ModifiedById = updatedBy
                }
            }
        };
        Result result = await Products.FactBatch(productId.ToProductIdString(), factCommandBatch);

        Product product = await Products.GetProductByProductId(productId);
        Fact fact1 = product.Facts.Where(f => f.Id == factId1).FirstOrDefault();
        Fact fact2 = product.Facts.Where(f => f.Id == factId2).FirstOrDefault();
        Fact fact3 = product.Facts.Where(f => f.Id == factId3).FirstOrDefault();

        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });

        result.Status.Should().Be("success");
        result.Errors.Should().BeNullOrEmpty();

        fact1.Type.Should().Be(type1);
        fact1.Value.Should().BeEquivalentTo(value);
        fact2.Type.Should().Be(type2);
        fact2.Value.Should().BeEquivalentTo(value);
        fact3.Type.Should().Be(type3);
        fact3.Value.Should().BeEquivalentTo(value);
        product.LastModifiedById.Should().Be(updatedBy);
    }

    [Fact]
    public async Task GIVEN_fact_command_batch_WHEN_product_not_exist_THEN_return_failure()
    {
        ProductId productId = GenerateProductId();
        var value = JToken.FromObject(new { });
        var updatedBy = Guid.NewGuid().ToString();

        FactCommandBatch factCommandBatch = new FactCommandBatch
        {
            UpdateFactCommands = new List<UpdateFactCommand> {
                new UpdateFactCommand
                {
                    Id = Guid.NewGuid().ToString(),
                    Type = Guid.NewGuid().ToString(),
                    IsTypeChanged = true,
                    Value = value,
                    IsValueChanged = true,
                    ModifiedById = updatedBy
                },
                new UpdateFactCommand
                {
                    Id = Guid.NewGuid().ToString(),
                    Type = Guid.NewGuid().ToString(),
                    IsTypeChanged = true,
                    Value = value,
                    IsValueChanged = true,
                    ModifiedById = updatedBy
                },
                new UpdateFactCommand
                {
                    Id = Guid.NewGuid().ToString(),
                    Type = Guid.NewGuid().ToString(),
                    IsTypeChanged = true,
                    Value = value,
                    IsValueChanged = true,
                    ModifiedById = updatedBy
                }
            }
        };
        Result result = await Products.FactBatch(productId.ToProductIdString(), factCommandBatch);

        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });

        result.Status.Should().Be("failure");
        result.Errors.Should().NotBeNullOrEmpty();
        result.Errors.FirstOrDefault().Should().Be($"The product {productId.ToProductIdString()} was not found.");
    }

    [Fact]
    public async Task GIVEN_fact_command_batch_WHEN_some_facts_not_exist_THEN_return_failure()
    {
        ProductId productId = await CreateProduct();
        string factId1 = await AddFact(productId);
        await AddFact(productId);
        await AddFact(productId);

        var type1 = Guid.NewGuid().ToString();
        var type2 = Guid.NewGuid().ToString();
        var type3 = Guid.NewGuid().ToString();
        var value = JToken.FromObject(new { });
        var updatedBy = Guid.NewGuid().ToString();

        var randomFactId2 = Guid.NewGuid().ToString();
        var randomFactId3 = Guid.NewGuid().ToString();

        FactCommandBatch factCommandBatch = new FactCommandBatch
        {
            UpdateFactCommands = new List<UpdateFactCommand> {
                new UpdateFactCommand
                {
                    Id = factId1,
                    Type = type1,
                    IsTypeChanged = true,
                    Value = value,
                    IsValueChanged = true,
                    ModifiedById = updatedBy
                },
                new UpdateFactCommand
                {
                    Id = randomFactId2,
                    Type = type2,
                    IsTypeChanged = true,
                    Value = value,
                    IsValueChanged = true,
                    ModifiedById = updatedBy
                },
                new UpdateFactCommand
                {
                    Id = randomFactId3,
                    Type = type3,
                    IsTypeChanged = true,
                    Value = value,
                    IsValueChanged = true,
                    ModifiedById = updatedBy
                }
            }
        };
        Result result = await Products.FactBatch(productId.ToProductIdString(), factCommandBatch);

        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });

        result.Status.Should().Be("failure");
        result.Errors.Should().NotBeNullOrEmpty();
        result.Errors.FirstOrDefault().Should().Be($"Facts of Ids '{randomFactId2}, {randomFactId3}' were not found for this product.");
    }

    async Task<string> AddFact(ProductId productId)
    {
        AddFactCommand addFactCommand = new AddFactCommand
        {
            Id = Guid.NewGuid().ToString(),
            Type = Guid.NewGuid().ToString(),
        };
        var result = await Products.AddFact(productId.ToProductIdString(), addFactCommand);
        return result.Value.Id;
    }

    async Task<ProductId> CreateProduct()
    {
        var productId = GenerateProductId();
        await Products.CreateProduct(new CreateProductCommand { ProductId = productId });
        return productId;
    }
}