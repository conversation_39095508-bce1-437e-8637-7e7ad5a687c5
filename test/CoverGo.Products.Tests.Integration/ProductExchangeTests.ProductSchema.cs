using System;
using System.Linq;
using System.Threading.Tasks;

using CoverGo.ProductBuilder.Client;
using CoverGo.Products.Client;

namespace CoverGo.Products.Tests.Integration;

[Collection("Sequential")]
public partial class ProductExchangeTests : TestBase
{
    [Fact]
    public async Task GIVEN_an_exported_product_without_product_schema_WHEN_importing_THEN_the_product_is_imported_without_product_schema()
    {
        string sourceProductTreeId = await CreateProductTree();
        ProductId productId = await CreateProduct(sourceProductTreeId);
        await FileSystem.Initialize();
        string filePath = await ExportProduct(productId);

        products_ProductImportInfo productImportInfo = await GetProductImportInfo(filePath);
        await ImportProduct(filePath);
        string targetProductTreeId = await GetProductTreeId(productId);
        ProductSchemaDescription targetProductSchema = await GetProductSchema(targetProductTreeId);

        productImportInfo.includedEntries.Should().ContainSingle().Which.Should().Be("productTree.json");
        productImportInfo.validationMessages.Should().BeEquivalentTo(new[] {
            new products_ProductImportValidationMessage
            {
                level = products_ProductImportValidationMessageLevel.WARNING,
                entry = "productTree.json",
                code = "product_tree_exists"
            }
        });

        targetProductSchema.Should().BeNull();
    }

    [Fact]
    public async Task GIVEN_an_exported_product_with_data_schema_only_WHEN_importing_THEN_the_product_is_imported_with_data_schema()
    {
        string sourceProductTreeId = await CreateProductTree();
        ResultOfGuid createProductSchemaResult = await CreateProductSchema(sourceProductTreeId, dataSchema: "{ \\\"properties\\\": {} }");
        createProductSchemaResult.status.Should().Be("success");
        Guid productSchemaId = createProductSchemaResult.value.Value;
        ProductId productId = await CreateProduct(sourceProductTreeId);
        await FileSystem.Initialize();
        string filePath = await ExportProduct(productId);

        products_ProductImportInfo productImportInfo = await GetProductImportInfo(filePath);
        await ImportProduct(filePath);
        string targetProductTreeId = await GetProductTreeId(productId);
        ProductSchemaDescription targetProductSchema = await GetProductSchema(targetProductTreeId);

        productImportInfo.includedEntries.Should().BeEquivalentTo(new[] { "productTree.json", "dataSchema.json" });
        productImportInfo.validationMessages.Should().BeEquivalentTo(new[] {
            new products_ProductImportValidationMessage
            {
                level = products_ProductImportValidationMessageLevel.WARNING,
                entry = "productTree.json",
                code = "product_tree_exists"
            },
            new products_ProductImportValidationMessage
            {
                level = products_ProductImportValidationMessageLevel.WARNING,
                entry = "dataSchema.json",
                code = "data_schema_exists"
            }
        });

        targetProductSchema.Should().NotBeNull();
        targetProductSchema.dataSchema.Should().Be("{ \"properties\": {} }");
        targetProductSchema.uiSchemas.Should().NotBeNull().And.BeEmpty();
    }

    [Fact]
    public async Task GIVEN_an_exported_product_with_non_default_ui_schema_WHEN_exporting_THEN_the_ui_schema_should_be_excluded()
    {
        string sourceProductTreeId = await CreateProductTree();
        ResultOfGuid createProductSchemaResult = await CreateProductSchema(sourceProductTreeId, dataSchema: "{ \\\"properties\\\": {} }");
        createProductSchemaResult.status.Should().Be("success");
        Guid productSchemaId = createProductSchemaResult.value.Value;
        Result addUISchemaToProductSchemaResult = await AddUISchemaToProductSchema(productSchemaId, "non-default", "{\\\"type\\\":\\\"VerticalLayout\\\"}");
        addUISchemaToProductSchemaResult.status.Should().Be("success");
        ProductId productId = await CreateProduct(sourceProductTreeId);
        await FileSystem.Initialize();
        string filePath = await ExportProduct(productId);

        products_ProductImportInfo productImportInfo = await GetProductImportInfo(filePath);
        await ImportProduct(filePath);
        string targetProductTreeId = await GetProductTreeId(productId);
        ProductSchemaDescription targetProductSchema = await GetProductSchema(targetProductTreeId);

        productImportInfo.includedEntries.Should().BeEquivalentTo(new[] { "productTree.json", "dataSchema.json" });
        productImportInfo.validationMessages.Should().BeEquivalentTo(new[] {
            new products_ProductImportValidationMessage
            {
                level = products_ProductImportValidationMessageLevel.WARNING,
                entry = "productTree.json",
                code = "product_tree_exists"
            },
            new products_ProductImportValidationMessage
            {
                level = products_ProductImportValidationMessageLevel.WARNING,
                entry = "dataSchema.json",
                code = "data_schema_exists"
            }
        });

        targetProductSchema.Should().NotBeNull();
        targetProductSchema.dataSchema.Should().Be("{ \"properties\": {} }");
        targetProductSchema.uiSchemas.Should().NotBeNull().And.BeEmpty();
    }

    [Fact]
    public async Task GIVEN_an_exported_product_with_default_ui_schema_WHEN_importing_THEN_the_ui_schema_should_be_imported()
    {
        string sourceProductTreeId = await CreateProductTree();
        ResultOfGuid createProductSchemaResult = await CreateProductSchema(sourceProductTreeId, dataSchema: "{ \\\"properties\\\": {} }");
        createProductSchemaResult.status.Should().Be("success");
        Guid productSchemaId = createProductSchemaResult.value.Value;
        Result addUISchemaToProductSchemaResult = await AddUISchemaToProductSchema(productSchemaId, sourceProductTreeId, "{\\\"type\\\":\\\"VerticalLayout\\\"}");
        addUISchemaToProductSchemaResult.status.Should().Be("success");
        ProductId productId = await CreateProduct(sourceProductTreeId);
        await FileSystem.Initialize();
        string filePath = await ExportProduct(productId);

        products_ProductImportInfo productImportInfo = await GetProductImportInfo(filePath);
        await ImportProduct(filePath);
        string targetProductTreeId = await GetProductTreeId(productId);
        ProductSchemaDescription targetProductSchema = await GetProductSchema(targetProductTreeId);

        productImportInfo.includedEntries.Should().BeEquivalentTo(new[] { "productTree.json", "dataSchema.json", "uiSchema.json" });
        productImportInfo.validationMessages.Should().BeEquivalentTo(new[] {
            new products_ProductImportValidationMessage
            {
                level = products_ProductImportValidationMessageLevel.WARNING,
                entry = "productTree.json",
                code = "product_tree_exists"
            },
            new products_ProductImportValidationMessage
            {
                level = products_ProductImportValidationMessageLevel.WARNING,
                entry = "dataSchema.json",
                code = "data_schema_exists"
            },
            new products_ProductImportValidationMessage
            {
                level = products_ProductImportValidationMessageLevel.WARNING,
                entry = "uiSchema.json",
                code = "ui_schema_exists"
            }
        });

        targetProductSchema.Should().NotBeNull();
        targetProductSchema.dataSchema.Should().Be("{ \"properties\": {} }");
        targetProductSchema.uiSchemas.Should().ContainSingle();
        targetProductSchema.uiSchemas.First().name.Should().Be(targetProductTreeId);
        targetProductSchema.uiSchemas.First().schema.Should().Be("{\"type\":\"VerticalLayout\"}");
    }

    [Fact]
    public async Task GIVEN_an_exported_product_with_default_ui_schema_and_different_lifecycle_stage_WHEN_importing_THEN_expected_validation_messages_returned()
    {
        string sourceProductTreeId = await CreateProductTree();
        ResultOfGuid createProductSchemaResult = await CreateProductSchema(sourceProductTreeId, dataSchema: "{ \\\"properties\\\": {} }");
        createProductSchemaResult.status.Should().Be("success");
        Guid productSchemaId = createProductSchemaResult.value.Value;
        Result addUISchemaToProductSchemaResult = await AddUISchemaToProductSchema(productSchemaId, sourceProductTreeId, "{\\\"type\\\":\\\"VerticalLayout\\\"}");
        addUISchemaToProductSchemaResult.status.Should().Be("success");
        ProductId productId = await CreateProduct(sourceProductTreeId, lifecycleStage: "Alpha");
        await FileSystem.Initialize();
        string filePath = await ExportProduct(productId);
        await Products.DeleteProduct(new DeleteProductCommand { ProductId = productId });
        await Products.CreateProduct(new CreateProductCommand { ProductId = productId, ProductTreeId = sourceProductTreeId, LifecycleStage = "Pre-production" });

        products_ProductImportInfo productImportInfo = await GetProductImportInfo(filePath);

        productImportInfo.validationMessages.Should().BeEquivalentTo(new[] {
            new products_ProductImportValidationMessage
            {
                level = products_ProductImportValidationMessageLevel.WARNING,
                entry = null,
                code = "different_lifecycle_stages"
            },
            new products_ProductImportValidationMessage
            {
                level = products_ProductImportValidationMessageLevel.WARNING,
                entry = "productTree.json",
                code = "product_tree_exists"
            },
            new products_ProductImportValidationMessage
            {
                level = products_ProductImportValidationMessageLevel.WARNING,
                entry = "dataSchema.json",
                code = "data_schema_exists"
            },
            new products_ProductImportValidationMessage
            {
                level = products_ProductImportValidationMessageLevel.WARNING,
                entry = "uiSchema.json",
                code = "ui_schema_exists"
            }
        });
    }

    private Task<ProductSchemaDescription> GetProductSchema(string productTreeId) => ProductBuilder.GetProductSchema(Guid.Parse(productTreeId));

    private Task<ResultOfGuid> CreateProductSchema(string productTreeId, string dataSchema) => ProductBuilder.CreateProductSchema(new ProductSchemaInput { nodeId = Guid.Parse(productTreeId), dataSchema = dataSchema });

    private Task<Result> AddUISchemaToProductSchema(Guid productSchemaId, string name, string schema) => ProductBuilder.AddUISchemaToProductSchema(productSchemaId, new UiSchemaInput { name = name, schema = schema });
}