using CoverGo.Gateway.Client;

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration
{
    [Collection("Sequential")]
    public class ProductBenefitsTests : TestBase
    {

        [Fact]
        public async Task GIVEN_product_added_WHEN_add_and_update_multiple_benefits_of_it_THEN_product_contains_new_and_updated_benefits_content()
        {
            productId productId = await Products.Create();

            benefitInput benefit1 = await Products.AddBenefit(productId);
            benefitInput benefit2 = await Products.AddBenefit(productId);

            benefitInput addBenefitInput1 = new()
            {
                typeId = CreateNewGuid(),
                optionKey = CreateNewGuid(),
                value = new scalarValueInput { stringValue = "test value" }
            };
            benefitInput addBenefitInput2 = new()
            {
                typeId = CreateNewGuid(),
                optionKey = CreateNewGuid(),
                value = new scalarValueInput { stringValue = "test value" }
            };
            benefitInput updateBenefitInput1 = new()
            {
                typeId = benefit1.typeId!,
                optionKey = benefit1.optionKey!,
                value = new scalarValueInput { stringValue = "test update batch 1" }
            };
            benefitInput updateBenefitInput2 = new()
            {
                typeId = benefit2.typeId!,
                optionKey = benefit2.optionKey!,
                value = new scalarValueInput { stringValue = "test update batch 2" }
            };
            benefitBatchInput benefitBatchInput = new()
            {
                addBenefitInputs = new List<benefitInput?> { addBenefitInput1, addBenefitInput2 },
                updateBenefitInputs = new List<benefitInput?> { updateBenefitInput1, updateBenefitInput2 }
            };

            await Products.ProductBenefitBatch(productId, benefitBatchInput);
            product product = await Products.SearchProductById(productId);

            product.benefitGraph!.Count.Should().Be(4);

            benefitGraph finalBenefit1 = product.benefitGraph.First(b => b!.typeId == benefit1.typeId && b.optionKey == benefit1.optionKey)!;
            benefitGraph finalBenefit2 = product.benefitGraph.First(b => b!.typeId == benefit2.typeId && b.optionKey == benefit2.optionKey)!;

            finalBenefit1.value2!.stringValue.Should().Be(updateBenefitInput1.value.stringValue);
            finalBenefit2.value2!.stringValue.Should().Be(updateBenefitInput2.value.stringValue);
        }


        [Fact]
        public async Task GIVEN_product_with_benefits_WHEN_remove_benefit_batch_THEN_benefits_are_removed()
        {
            productId productId = await Products.Create();

            benefitInput benefit1 = await Products.AddBenefit(productId);
            benefitInput benefit2 = await Products.AddBenefit(productId);

            benefitBatchInput benefitBatchInput = new()
            {
                removeBenefitInputs = new List<removeBenefitInput?> {
                    new ()
                    {
                        typeId = benefit1.typeId,
                        optionKey = benefit1.optionKey
                    },
                    new (){
                        typeId = benefit2.typeId,
                        optionKey = benefit2.optionKey
                    }
                }
            };

            await Products.ProductBenefitBatch(productId, benefitBatchInput);
            product product = await Products.SearchProductById(productId);

            product.benefitGraph.Should().BeNullOrEmpty();
        }

    }
}
