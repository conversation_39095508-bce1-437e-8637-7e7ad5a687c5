using System.Threading.Tasks;

using CoverGo.Products.Application;

using HotChocolate.Execution;

using Microsoft.Extensions.DependencyInjection;

using Snapshooter.Xunit;

namespace CoverGo.Products.Tests.Integration;

public class GraphQlSchemaTests
{
    /// <summary>
    /// This test fails when you update the schema, but don't update snapshot in source control.
    /// Update the snapshot and check for any breaking changes!
    /// </summary>
    /// <remarks>
    /// Steps to update GraphQL schema:
    /// 1. Run backend services in local
    /// 2. Run this test
    /// 3. Compare changes between files in `__snapshots__/__mismatch__GraphQlSchemaTests.SchemaChangeTest.snap` and `__snapshots__/GraphQlSchemaTests.SchemaChangeTest.snap`, update them if need.
    /// </remarks>
    [Fact]
    public async Task SchemaChangeTest()
    {
        // Arrange (Given)
        var serviceCollection = new ServiceCollection()
            .AddLogging()
            .AddHttpContextAccessor();

        // Act (When)
        var schema = await serviceCollection
            .AddProductsGraphQLSchema()
            .BuildSchemaAsync();

        // Assert (Then)
        schema.ToString().MatchSnapshot();
    }
}
