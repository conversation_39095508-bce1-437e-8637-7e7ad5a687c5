﻿using CoverGo.Products.Client;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration;

[Collection("Sequential")]
public class ProductQueryActiveNetworksTests : TestBase
{
    [Fact]
    public async Task GIVEN_WHEN_querying_active_networks_for_not_enabled_tenant_THEN_empty_list_is_returned()
    {
        List<products_Network> activeNetworks = await Products.GetActiveNetworks();
        activeNetworks.Should().HaveCount(0);
    }
}
