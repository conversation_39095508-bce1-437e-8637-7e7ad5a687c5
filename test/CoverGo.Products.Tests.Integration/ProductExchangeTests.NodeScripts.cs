using System;
using System.Threading.Tasks;

using CoverGo.ProductBuilder.Client;
using CoverGo.Products.Client;

namespace CoverGo.Products.Tests.Integration;

public partial class ProductExchangeTests
{
    [Fact]
    public async Task GIVEN_an_exported_product_with_a_validation_script_WHEN_importing_THEN_the_product_is_imported_with_the_validation_script()
    {
        string sourceProductTreeId = await CreateProductTreeForResolution();
        ResultOfGuid createProductSchemaResult = await CreateProductSchema(sourceProductTreeId, dataSchema: "{ \\\"properties\\\": {} }");
        createProductSchemaResult.status.Should().Be("success");
        Result attachNodeScriptResult = await AttachNodeScript(sourceProductTreeId, "async ({ a, b }) => { if (a === b) return [{ message: 'Sample', fieldValue: '', field: 'a must not be equal to b', }]; return []; }");
        attachNodeScriptResult.status.Should().Be("success");
        ProductId productId = await CreateProduct(sourceProductTreeId);
        await FileSystem.Initialize();
        string filePath = await ExportProduct(productId);

        products_ProductImportInfo productImportInfo = await GetProductImportInfo(filePath);
        await ImportProduct(filePath);
        string targetProductTreeId = await GetProductTreeId(productId);
        ResolutionResult resolutionResult = await Resolve(targetProductTreeId, "pricing", new ExpressionInput
        {
            language = Language.CLEAR_SCRIPT,
            text = "{ a: 1, b: 2 }"
        });

        productImportInfo.includedEntries.Should().BeEquivalentTo(new[] { "productTree.json", "dataSchema.json", "validationScript.js" });
        productImportInfo.validationMessages.Should().BeEquivalentTo(new[] {
            new products_ProductImportValidationMessage
            {
                level = products_ProductImportValidationMessageLevel.WARNING,
                entry = "productTree.json",
                code = "product_tree_exists"
            },
            new products_ProductImportValidationMessage
            {
                level = products_ProductImportValidationMessageLevel.WARNING,
                entry = "dataSchema.json",
                code = "data_schema_exists"
            },
            new products_ProductImportValidationMessage
            {
                level = products_ProductImportValidationMessageLevel.WARNING,
                entry = "validationScript.js",
                code = "validation_script_exists"
            }
        });

        resolutionResult.value.Should().Be("{}");
    }

    private Task<string> CreateProductTreeForResolution() => ProductBuilder.CreateNode(new CreateNodeInput
    {
        @ref = "product1",
        fields = new NodeFieldInput[]
        {
            new NodeFieldInput
            {
                @ref = "pricing",
                type = "String",
                resolver = new ExpressionInput
                {
                    text = "PRICING_SUMMARY()",
                    language = Language.MATHJS
                }
            }
        }
    });

    private Task<Result> AttachNodeScript(string productTreeId, string text) => ProductBuilder.AttachNodeScript(Guid.Parse(productTreeId), new NodeScriptInput { text = text });

    private Task<ResolutionResult> Resolve(string productTreeId, string fieldName, ExpressionInput input) => ProductBuilder.Resolve(Guid.Parse(productTreeId), fieldName, input);
}