﻿using CoverGo.Gateway.Client;
using CoverGo.Products.Tests.Integration.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests.Integration
{
    [Collection("Sequential")]
    public class ProductTypeWithDataSchemasTests : TestBase
    {
        [Fact]
        public async Task GIVEN_productType_WHEN_add_schema_THEN_success()
        {
            string typeId = await ProductType.Create();
            (string id, _) = await DataSchema.Create();

            await AddDataSchemaToProductType(typeId, id);
        }

        [Fact]
        public async Task GIVEN_productType_without_schemas_WHEN_request_this_productType_THEN_returns_productType()
        {
            string typeId = await ProductType.Create();

            productType productType = await FindProductTypeById(typeId);

            productType.typeId.Should().Be(typeId);
        }

        [Fact]
        public async Task GIVEN_productType_WHEN_add_schema_THEN_succeed()
        {
            string typeId = await ProductType.Create();
            (string schemaId, _) = await DataSchema.Create();

            await AddDataSchemaToProductType(typeId, schemaId);

            productType productType = await FindProductTypeById(typeId);

            productType.dataSchemas!.Single()!.id.Should().Be(schemaId);
        }

        [Fact]
        public async Task GIVEN_productType_with_schema_WHEN_remove_schema_THEN_succeed()
        {
            string typeId = await ProductType.Create();
            (string schemaId, _) = await DataSchema.Create();

            await AddDataSchemaToProductType(typeId, schemaId);

            await RemoveDataSchemaFromProductType(typeId, schemaId);

            productType productType = await FindProductTypeById(typeId);

            productType.dataSchemas.Should().BeNullOrEmpty();
        }

        [Fact]
        public async Task GIVEN_productType_with_schemas_WHEN_filter_schemas_THEN_returns_one_schema()
        {
            string typeId = await ProductType.Create();
            (string schemaId1, createDataSchemaInput createDataSchemaInput) = await DataSchema.Create();
            (string schemaId2, _) = await DataSchema.Create();
            await AddDataSchemaToProductType(typeId, schemaId1);
            await AddDataSchemaToProductType(typeId, schemaId2);

            await RemoveDataSchemaFromProductType(typeId, schemaId1);

            productType productType
                = await SearchProductTypeByIdAndFilterSchemas(typeId, new dataSchemaWhereInput { type = createDataSchemaInput.type });

            productType.dataSchemas.Should().BeNullOrEmpty();
        }

        Task AddDataSchemaToProductType(string typeId, string schemaId)
        {
            string mutation = new covergoMutationBuilder()
                .addDataSchemaToProductType(new covergoMutationBuilder.addDataSchemaToProductTypeArgs(new addDataSchemaToProductTypeInput { productTypeId = typeId, dataSchemaId = schemaId }), new resultBuilder()
                    .WithAllFields())
            .Build();

            return _gatewayGraphQlHttpClient.SendMutationAndEnsureAsync<Result>(mutation);
        }

        Task RemoveDataSchemaFromProductType(string typeId, string schemaId)
        {
            string mutation = new covergoMutationBuilder()
                .removeDataSchemaFromProductType(new covergoMutationBuilder.removeDataSchemaFromProductTypeArgs(new removeDataSchemaFromProductTypeInput { productTypeId = typeId, dataSchemaId = schemaId }), new resultBuilder()
                    .WithAllFields())
            .Build();

            return _gatewayGraphQlHttpClient.SendMutationAndEnsureAsync<Result>(mutation);
        }

        Task<productType> FindProductTypeById(string id)
        {
            productTypeBuilder fields = new productTypeBuilder()
                .typeId()
                .dataSchemas(new productTypeBuilder.dataSchemasArgs(), new dataSchemaBuilder()
                    .WithAllScalarFields()
                    .tags()
                    .standard(new dataSchemaStandardBuilder()
                        .WithAllFields()));

            return ProductType.FindById(id, fields);
        }

        async Task<productType> SearchProductTypeByIdAndFilterSchemas(string id, dataSchemaWhereInput where)
        {
            string query = new covergoQueryBuilder()
                .productTypes(new covergoQueryBuilder.productTypesArgs(new productTypeWhereInput { typeId = id }), new productTypeBuilder()
                    .typeId()
                    .dataSchemas(new productTypeBuilder.dataSchemasArgs(where), new dataSchemaBuilder()
                        .id()))
                .Build();

            return (await _gatewayGraphQlHttpClient.SendQueryAndEnsureAsync<ICollection<productType>>(query)).Single();
        }

    }
}
