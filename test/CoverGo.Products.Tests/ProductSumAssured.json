﻿{
  "saLimit": [
    {
      "limitUnit": "1",
      "maxAmount": 10000000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 216,
      "highAgeMonth": 551,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "01"
    },
    {
      "limitUnit": "1",
      "maxAmount": 5000000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 552,
      "highAgeMonth": 611,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "01"
    },
    {
      "limitUnit": "1",
      "maxAmount": 4000000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 612,
      "highAgeMonth": 671,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "01"
    },
    {
      "limitUnit": "1",
      "maxAmount": 2500000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 672,
      "highAgeMonth": 731,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "01"
    },
    {
      "limitUnit": "1",
      "maxAmount": 1000000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 732,
      "highAgeMonth": 791,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "01"
    },
    {
      "limitUnit": "1",
      "maxAmount": 10000000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 216,
      "highAgeMonth": 551,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "09"
    },
    {
      "limitUnit": "1",
      "maxAmount": 5000000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 552,
      "highAgeMonth": 611,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "09"
    },
    {
      "limitUnit": "1",
      "maxAmount": 4000000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 612,
      "highAgeMonth": 671,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "09"
    },
    {
      "limitUnit": "1",
      "maxAmount": 2500000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 672,
      "highAgeMonth": 731,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "09"
    },
    {
      "limitUnit": "1",
      "maxAmount": 1000000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 732,
      "highAgeMonth": 791,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "09"
    },
    {
      "limitUnit": "1",
      "maxAmount": 3200000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 216,
      "highAgeMonth": 551,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "05"
    },
    {
      "limitUnit": "1",
      "maxAmount": 3200000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 552,
      "highAgeMonth": 611,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "05"
    },
    {
      "limitUnit": "1",
      "maxAmount": 3200000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 612,
      "highAgeMonth": 671,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "05"
    },
    {
      "limitUnit": "1",
      "maxAmount": 2500000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 672,
      "highAgeMonth": 731,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "05"
    },
    {
      "limitUnit": "1",
      "maxAmount": 1000000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 732,
      "highAgeMonth": 791,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "05"
    },
    {
      "limitUnit": "1",
      "maxAmount": 3200000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 216,
      "highAgeMonth": 551,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "06"
    },
    {
      "limitUnit": "1",
      "maxAmount": 3200000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 552,
      "highAgeMonth": 611,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "06"
    },
    {
      "limitUnit": "1",
      "maxAmount": 3200000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 612,
      "highAgeMonth": 671,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "06"
    },
    {
      "limitUnit": "1",
      "maxAmount": 2500000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 672,
      "highAgeMonth": 731,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "06"
    },
    {
      "limitUnit": "1",
      "maxAmount": 1000000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 732,
      "highAgeMonth": 791,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "06"
    },
    {
      "limitUnit": "1",
      "maxAmount": 3200000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 216,
      "highAgeMonth": 551,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "07"
    },
    {
      "limitUnit": "1",
      "maxAmount": 3200000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 552,
      "highAgeMonth": 611,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "07"
    },
    {
      "limitUnit": "1",
      "maxAmount": 3200000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 612,
      "highAgeMonth": 671,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "07"
    },
    {
      "limitUnit": "1",
      "maxAmount": 2500000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 672,
      "highAgeMonth": 731,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "07"
    },
    {
      "limitUnit": "1",
      "maxAmount": 1000000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 732,
      "highAgeMonth": 791,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "07"
    },
    {
      "limitUnit": "1",
      "maxAmount": 3200000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 216,
      "highAgeMonth": 551,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "12"
    },
    {
      "limitUnit": "1",
      "maxAmount": 3200000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 552,
      "highAgeMonth": 611,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "12"
    },
    {
      "limitUnit": "1",
      "maxAmount": 3200000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 612,
      "highAgeMonth": 671,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "12"
    },
    {
      "limitUnit": "1",
      "maxAmount": 2500000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 672,
      "highAgeMonth": 731,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "12"
    },
    {
      "limitUnit": "1",
      "maxAmount": 1000000,
      "minAmount": 250000,
      "currency": 3,
      "lowAgeMonth": 732,
      "highAgeMonth": 791,
      "occupationCode": 0,
      "paymentMethod": 0,
      "employedIndi": "12"
    }
  ]
}
