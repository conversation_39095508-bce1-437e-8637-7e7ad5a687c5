{"properties": {}, "type": "object", "policyFields": {"description": "Policy Fields", "properties": {"startDate": {"type": "string", "meta": {"label": "Start Date", "component": "CDatePicker", "required": true, "fixed": true, "order": 1}}, "endDate": {"type": "string", "meta": {"label": "End Date", "component": "CDatePicker", "required": false, "fixed": true, "order": 2}}}}, "insuredsFields": {"properties": {"numberOfInsureds": {"type": "number", "meta": {"label": "Number of Insureds", "component": "JInputNumber", "required": true, "order": 1, "fixed": true}}, "planSelected": {"type": "string", "meta": {"label": "Plan Selected", "component": "JSelect", "required": true, "options": [{"key": "033006475240315414", "name": "MG12", "value": "plan01"}, {"key": "245592056899536", "name": "MG15", "value": "plan04"}, {"key": "8651811936707077", "name": "MG19", "value": "plan08"}, {"key": "16901875035098124", "name": "MG23", "value": "plan12"}], "order": 2, "fixed": true}}, "officialID": {"type": "string", "meta": {"label": "Official ID", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": ""}}, "sponsorID": {"type": "string", "meta": {"label": "Sponsor ID", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": ""}}, "mobileNo": {"type": "string", "meta": {"label": "Mobile No", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "number", "numeric": true}}, "firstNameEng": {"type": "string", "meta": {"label": "First Name (ENG)", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": ""}}, "lastNameEng": {"type": "string", "meta": {"label": "Last Name (ENG)", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": ""}}, "secondNameEng": {"type": "string", "meta": {"label": "Second Name (ENG)", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": ""}}, "thirdNameEng": {"type": "string", "meta": {"label": "Third Name (ENG)", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": ""}}, "iqamaExpiryDate": {"type": "string", "meta": {"label": "Iqama Expiry Date", "component": "CDatePicker", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "date", "validations": ""}}, "nationalityCode": {"type": "number", "meta": {"label": "Nationality Code", "component": "JInputNumber", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "number", "validations": ""}}, "occupationCode": {"type": "number", "meta": {"label": "Occupation Code", "component": "JInputNumber", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "number", "validations": ""}}, "maritalStatus": {"type": "string", "meta": {"label": "Marital Status", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": ""}}}}, "planFields_plan01": {"description": "MG12 Fields", "properties": {"benefitLimit": {"type": "string", "meta": {"label": "Global Benefit Limit", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "placeholder": "Global Benefit Limit", "options": [{"key": "170900542710394946", "name": "500000", "value": "500000"}, {"key": "170900543340712139", "name": "1000000", "value": "1000000"}, {"key": "170900543445656053", "name": "1500000", "value": "1500000"}], "isCustom": true}}, "roomType": {"type": "string", "meta": {"label": "Room Type", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "placeholder": "Room Type", "options": [{"key": "170851036312513653", "name": "Suite", "value": "suite"}, {"key": "17085103989266751", "name": "Private", "value": "private"}, {"key": "170851042041948846", "name": "Semi-Private", "value": "semiPrivate"}], "isCustom": true}}, "coInsurance": {"type": "string", "meta": {"label": "Co-Insurance (Non Medicine)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "placeholder": "Co-Insurance", "options": [{"key": "170851153982857801", "name": "0%", "value": "0"}, {"key": "170851161602941617", "name": "5%", "value": "5"}, {"key": "170851170703838472", "name": "10%", "value": "10"}, {"key": "170852336663853029", "name": "15%", "value": "15"}, {"key": "170852331501964023", "name": "20%", "value": "20"}], "isCustom": true}}, "maxCoInsurance": {"type": "string", "meta": {"label": "Max of Co-Insurance (Non Medicine)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "placeholder": "Max of Co-Insurance ", "options": [{"name": "0", "value": "0"}, {"name": "5", "value": "5"}, {"name": "10", "value": "10"}, {"name": "15", "value": "15"}, {"name": "20", "value": "20"}, {"name": "25", "value": "25"}, {"name": "30", "value": "30"}, {"name": "35", "value": "35"}, {"name": "40", "value": "40"}, {"name": "45", "value": "45"}, {"name": "50", "value": "50"}, {"name": "55", "value": "55"}, {"name": "60", "value": "60"}, {"name": "65", "value": "65"}, {"name": "70", "value": "70"}, {"name": "75", "value": "75"}, {"name": "80", "value": "80"}, {"name": "85", "value": "85"}, {"name": "90", "value": "90"}, {"name": "95", "value": "95"}, {"name": "100", "value": "100"}, {"name": "110", "value": "110"}, {"name": "120", "value": "120"}, {"name": "130", "value": "130"}, {"name": "140", "value": "140"}, {"name": "150", "value": "150"}, {"name": "160", "value": "160"}, {"name": "170", "value": "170"}, {"name": "280", "value": "280"}, {"name": "290", "value": "290"}, {"name": "300", "value": "300"}], "isCustom": true}}, "maternity": {"type": "string", "meta": {"label": "Maternity", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "placeholder": "Maternity", "options": [{"key": "170852406274494844", "name": "10000", "value": "10000"}, {"key": "170852407959569568", "name": "15000", "value": "15000"}, {"key": "170852408911642653", "name": "20000", "value": "20000"}, {"key": "170852410149949648", "name": "25000", "value": "25000"}, {"key": "170852411213176694", "name": "30000", "value": "30000"}]}}, "dentalEssential": {"type": "string", "meta": {"label": "Dental (Essential and preventive dentistry)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "placeholder": "Dental (Essential and preventive dentistry)", "options": [{"key": "170852416125858213", "name": "1200", "value": "1200"}, {"key": "170852418039730344", "name": "1500", "value": "1500"}, {"key": "170852418257599015", "name": "1800", "value": "1800"}, {"key": "170852418399089806", "name": "2400", "value": "2400"}, {"key": "170852418562886388", "name": "3000", "value": "3000"}, {"key": "170852422179467639", "name": "4500", "value": "4500"}, {"key": "170852423507856224", "name": "6000", "value": "6000"}]}}, "dentalTreatment": {"type": "string", "meta": {"label": "Dental (Treatment of Root Canals and Emergencies)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "placeholder": "Dental (Treatment of Root Canals and Emergencies)", "options": [{"key": "170852429300573262", "name": "800", "value": "800"}, {"key": "170852429517073004", "name": "1000", "value": "1000"}, {"key": "170852429745334050", "name": "1200", "value": "1200"}, {"key": "170852429852019014", "name": "1600", "value": "1600"}, {"key": "170852434936416484", "name": "2000", "value": "2000"}, {"key": "170852435923985831", "name": "3000", "value": "3000"}, {"key": "170852436850638600", "name": "4000", "value": "4000"}]}}, "optical": {"type": "string", "meta": {"label": "Optical", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "placeholder": "Optical", "options": [{"key": "170852441425835740", "name": "400", "value": "400"}, {"key": "170852442440234170", "name": "500", "value": "500"}, {"key": "170852442631618617", "name": "600", "value": "600"}, {"key": "170852442818388929", "name": "750", "value": "750"}, {"key": "170852443002316127", "name": "800", "value": "800"}, {"key": "170852483916780841", "name": "1000", "value": "1000"}, {"key": "170852484147020417", "name": "1250", "value": "1250"}, {"key": "17085248436554694", "name": "1500", "value": "1500"}, {"key": "170852484593357541", "name": "1750", "value": "1750"}, {"key": "170852488465195277", "name": "2000", "value": "2000"}, {"key": "170852489788026739", "name": "2250", "value": "2250"}, {"key": "170852491392878415", "name": "2500", "value": "2500"}], "isCustom": true}}, "coInsuranceGenericMedicine": {"type": "string", "meta": {"label": "Co-Insurance (Generic Medicine)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "0%", "value": "0"}, {"name": "5%", "value": "5"}, {"name": "10%", "value": "10"}, {"name": "15%", "value": "15"}, {"name": "20%", "value": "20"}]}}, "maxCoInsuranceGenericMedicine": {"type": "string", "meta": {"label": "Max of Co-Insurance (Generic Medicine)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "0", "value": "0"}, {"name": "5", "value": "5"}, {"name": "10", "value": "10"}, {"name": "15", "value": "15"}, {"name": "20", "value": "20"}, {"name": "25", "value": "25"}, {"name": "30", "value": "30"}, {"name": "35", "value": "35"}, {"name": "40", "value": "40"}, {"name": "45", "value": "45"}, {"name": "50", "value": "50"}, {"name": "55", "value": "55"}, {"name": "60", "value": "60"}, {"name": "65", "value": "65"}, {"name": "70", "value": "70"}, {"name": "75", "value": "75"}, {"name": "80", "value": "80"}, {"name": "85", "value": "85"}, {"name": "90", "value": "90"}, {"name": "95", "value": "95"}, {"name": "100", "value": "100"}, {"name": "110", "value": "110"}, {"name": "120", "value": "120"}, {"name": "130", "value": "130"}, {"name": "140", "value": "140"}, {"name": "150", "value": "150"}, {"name": "160", "value": "160"}, {"name": "170", "value": "170"}, {"name": "280", "value": "280"}, {"name": "290", "value": "290"}, {"name": "300", "value": "300"}]}}, "coInsuranceBrandedMedicine": {"type": "string", "meta": {"label": "Co-Insurance (Branded Medicine)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "0%", "value": "0"}, {"name": "5%", "value": "5"}, {"name": "10%", "value": "10"}, {"name": "15%", "value": "15"}, {"name": "20%", "value": "20"}]}}, "maxCoInsuranceBrandedMedicine": {"type": "string", "meta": {"label": "Max of Co-Insurance (Branded Medicine)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "0", "value": "0"}, {"name": "5", "value": "5"}, {"name": "10", "value": "10"}, {"name": "15", "value": "15"}, {"name": "20", "value": "20"}, {"name": "25", "value": "25"}, {"name": "30", "value": "30"}, {"name": "35", "value": "35"}, {"name": "40", "value": "40"}, {"name": "45", "value": "45"}, {"name": "50", "value": "50"}, {"name": "55", "value": "55"}, {"name": "60", "value": "60"}, {"name": "65", "value": "65"}, {"name": "70", "value": "70"}, {"name": "75", "value": "75"}, {"name": "80", "value": "80"}, {"name": "85", "value": "85"}, {"name": "90", "value": "90"}, {"name": "95", "value": "95"}, {"name": "100", "value": "100"}, {"name": "110", "value": "110"}, {"name": "120", "value": "120"}, {"name": "130", "value": "130"}, {"name": "140", "value": "140"}, {"name": "150", "value": "150"}, {"name": "160", "value": "160"}, {"name": "170", "value": "170"}, {"name": "280", "value": "280"}, {"name": "290", "value": "290"}, {"name": "300", "value": "300"}]}}, "coInsuranceDentalEmergency": {"type": "string", "meta": {"label": "Co-Insurance (Dental Emergency)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "0%", "value": "0"}, {"name": "5%", "value": "5"}, {"name": "10%", "value": "10"}, {"name": "15%", "value": "15"}, {"name": "20%", "value": "20"}]}}, "maxCoInsuranceDentalEmergency": {"type": "string", "meta": {"label": "Max of Co-Insurance (Dental Emergency)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "0", "value": "0"}, {"name": "5", "value": "5"}, {"name": "10", "value": "10"}, {"name": "15", "value": "15"}, {"name": "20", "value": "20"}, {"name": "25", "value": "25"}, {"name": "30", "value": "30"}, {"name": "35", "value": "35"}, {"name": "40", "value": "40"}, {"name": "45", "value": "45"}, {"name": "50", "value": "50"}, {"name": "55", "value": "55"}, {"name": "60", "value": "60"}, {"name": "65", "value": "65"}, {"name": "70", "value": "70"}, {"name": "75", "value": "75"}, {"name": "80", "value": "80"}, {"name": "85", "value": "85"}, {"name": "90", "value": "90"}, {"name": "95", "value": "95"}, {"name": "100", "value": "100"}, {"name": "110", "value": "110"}, {"name": "120", "value": "120"}, {"name": "130", "value": "130"}, {"name": "140", "value": "140"}, {"name": "150", "value": "150"}, {"name": "160", "value": "160"}, {"name": "170", "value": "170"}, {"name": "280", "value": "280"}, {"name": "290", "value": "290"}, {"name": "300", "value": "300"}]}}, "coInsuranceOptical": {"type": "string", "meta": {"label": "Co-Insurance (Optical)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "0%", "value": "0"}, {"name": "5%", "value": "5"}, {"name": "10%", "value": "10"}, {"name": "15%", "value": "15"}, {"name": "20%", "value": "20"}]}}, "maxCoInsuranceOptical": {"type": "string", "meta": {"label": "Max of Co-Insurance (Optical)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "options": [{"name": "0", "value": "0"}, {"name": "5", "value": "5"}, {"name": "10", "value": "10"}, {"name": "15", "value": "15"}, {"name": "20", "value": "20"}, {"name": "25", "value": "25"}, {"name": "30", "value": "30"}, {"name": "35", "value": "35"}, {"name": "40", "value": "40"}, {"name": "45", "value": "45"}, {"name": "50", "value": "50"}, {"name": "55", "value": "55"}, {"name": "60", "value": "60"}, {"name": "65", "value": "65"}, {"name": "70", "value": "70"}, {"name": "75", "value": "75"}, {"name": "80", "value": "80"}, {"name": "85", "value": "85"}, {"name": "90", "value": "90"}, {"name": "95", "value": "95"}, {"name": "100", "value": "100"}, {"name": "110", "value": "110"}, {"name": "120", "value": "120"}, {"name": "130", "value": "130"}, {"name": "140", "value": "140"}, {"name": "150", "value": "150"}, {"name": "160", "value": "160"}, {"name": "170", "value": "170"}, {"name": "280", "value": "280"}, {"name": "290", "value": "290"}, {"name": "300", "value": "300"}], "isCustom": true}}}}, "planFields_plan04": {"description": "MG15 Fields", "properties": {"benefitLimit": {"type": "string", "meta": {"label": "Global Benefit Limit", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "placeholder": "Global Benefit Limit", "options": [{"key": "170900542710394946", "name": "500000", "value": "500000"}, {"key": "170900543340712139", "name": "1000000", "value": "1000000"}, {"key": "170900543445656053", "name": "1500000", "value": "1500000"}], "isCustom": true, "order": 1}}, "roomType": {"type": "string", "meta": {"label": "Room Type", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "placeholder": "Room Type", "options": [{"key": "17085103989266751", "name": "Private", "value": "private"}, {"key": "170851042041948846", "name": "Semi-Private", "value": "semiPrivate"}], "isCustom": true, "order": 2}}, "coInsurance": {"type": "string", "meta": {"label": "Co-Insurance (Non Medicine)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "placeholder": "Co-Insurance", "options": [{"key": "170851153982857801", "name": "0%", "value": "0"}, {"key": "170851161602941617", "name": "5%", "value": "5"}, {"key": "170851170703838472", "name": "10%", "value": "10"}, {"key": "170852336663853029", "name": "15%", "value": "15"}, {"key": "170852331501964023", "name": "20%", "value": "20"}], "isCustom": true, "order": 3}}, "maxCoInsurance": {"type": "string", "meta": {"label": "Max of Co-Insurance (Non Medicine)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "placeholder": "Max of Co-Insurance", "options": [{"name": "0", "value": "0"}, {"name": "5", "value": "5"}, {"name": "10", "value": "10"}, {"name": "15", "value": "15"}, {"name": "20", "value": "20"}, {"name": "25", "value": "25"}, {"name": "30", "value": "30"}, {"name": "35", "value": "35"}, {"name": "40", "value": "40"}, {"name": "45", "value": "45"}, {"name": "50", "value": "50"}, {"name": "55", "value": "55"}, {"name": "60", "value": "60"}, {"name": "65", "value": "65"}, {"name": "70", "value": "70"}, {"name": "75", "value": "75"}, {"name": "80", "value": "80"}, {"name": "85", "value": "85"}, {"name": "90", "value": "90"}, {"name": "95", "value": "95"}, {"name": "100", "value": "100"}, {"name": "110", "value": "110"}, {"name": "120", "value": "120"}, {"name": "130", "value": "130"}, {"name": "140", "value": "140"}, {"name": "150", "value": "150"}, {"name": "160", "value": "160"}, {"name": "170", "value": "170"}, {"name": "280", "value": "280"}, {"name": "290", "value": "290"}, {"name": "300", "value": "300"}], "isCustom": true, "order": 4}}, "maternity": {"type": "string", "meta": {"label": "Maternity", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "placeholder": "Maternity", "options": [{"key": "170852406274494844", "name": "10000", "value": "10000"}, {"key": "170852407959569568", "name": "15000", "value": "15000"}, {"key": "170852408911642653", "name": "20000", "value": "20000"}, {"key": "170852410149949648", "name": "25000", "value": "25000"}, {"key": "170852411213176694", "name": "30000", "value": "30000"}], "order": 5}}, "dentalEssential": {"type": "string", "meta": {"label": "Dental (Essential and preventive dentistry)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "placeholder": "Dental (Essential and preventive dentistry)", "options": [{"key": "170852416125858213", "name": "1200", "value": "1200"}, {"key": "170852418039730344", "name": "1500", "value": "1500"}, {"key": "170852418257599015", "name": "1800", "value": "1800"}, {"key": "170852418399089806", "name": "2400", "value": "2400"}, {"key": "170852418562886388", "name": "3000", "value": "3000"}, {"key": "170852422179467639", "name": "4500", "value": "4500"}, {"key": "170852423507856224", "name": "6000", "value": "6000"}], "order": 6}}, "dentalTreatment": {"type": "string", "meta": {"label": "Dental (Treatment of Root Canals and Emergencies)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "placeholder": "Dental (Treatment of Root Canals and Emergencies)", "options": [{"key": "170852429300573262", "name": "800", "value": "800"}, {"key": "170852429517073004", "name": "1000", "value": "1000"}, {"key": "170852429745334050", "name": "1200", "value": "1200"}, {"key": "170852429852019014", "name": "1600", "value": "1600"}, {"key": "170852434936416484", "name": "2000", "value": "2000"}, {"key": "170852435923985831", "name": "3000", "value": "3000"}, {"key": "170852436850638600", "name": "4000", "value": "4000"}], "order": 7}}, "optical": {"type": "string", "meta": {"label": "Optical", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "placeholder": "Optical", "options": [{"key": "170852441425835740", "name": "400", "value": "400"}, {"key": "170852442440234170", "name": "500", "value": "500"}, {"key": "170852442631618617", "name": "600", "value": "600"}, {"key": "170852442818388929", "name": "750", "value": "750"}, {"key": "170852443002316127", "name": "800", "value": "800"}, {"key": "170852483916780841", "name": "1000", "value": "1000"}, {"key": "170852484147020417", "name": "1250", "value": "1250"}, {"key": "17085248436554694", "name": "1500", "value": "1500"}, {"key": "170852484593357541", "name": "1750", "value": "1750"}, {"key": "170852488465195277", "name": "2000", "value": "2000"}, {"key": "170852489788026739", "name": "2250", "value": "2250"}, {"key": "170852491392878415", "name": "2500", "value": "2500"}], "isCustom": true, "order": 8}}, "coInsuranceGenericMedicine": {"type": "string", "meta": {"label": "Co-Insurance (Generic Medicine)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "0%", "value": "0"}, {"name": "5%", "value": "5"}, {"name": "10%", "value": "10"}, {"name": "15%", "value": "15"}, {"name": "20%", "value": "20"}], "order": 9}}, "maxCoInsuranceGenericMedicine": {"type": "string", "meta": {"label": "Max of Co-Insurance (Generic Medicine)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "0", "value": "0"}, {"name": "5", "value": "5"}, {"name": "10", "value": "10"}, {"name": "15", "value": "15"}, {"name": "20", "value": "20"}, {"name": "25", "value": "25"}, {"name": "30", "value": "30"}, {"name": "35", "value": "35"}, {"name": "40", "value": "40"}, {"name": "45", "value": "45"}, {"name": "50", "value": "50"}, {"name": "55", "value": "55"}, {"name": "60", "value": "60"}, {"name": "65", "value": "65"}, {"name": "70", "value": "70"}, {"name": "75", "value": "75"}, {"name": "80", "value": "80"}, {"name": "85", "value": "85"}, {"name": "90", "value": "90"}, {"name": "95", "value": "95"}, {"name": "100", "value": "100"}, {"name": "110", "value": "110"}, {"name": "120", "value": "120"}, {"name": "130", "value": "130"}, {"name": "140", "value": "140"}, {"name": "150", "value": "150"}, {"name": "160", "value": "160"}, {"name": "170", "value": "170"}, {"name": "280", "value": "280"}, {"name": "290", "value": "290"}, {"name": "300", "value": "300"}], "order": 10}}, "coInsuranceBrandedMedicine": {"type": "string", "meta": {"label": "Co-Insurance (Branded Medicine)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "0%", "value": "0"}, {"name": "5%", "value": "5"}, {"name": "10%", "value": "10"}, {"name": "15%", "value": "15"}, {"name": "20%", "value": "20"}], "order": 11}}, "maxCoInsuranceBrandedMedicine": {"type": "string", "meta": {"label": "Max of Co-Insurance (Branded Medicine)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "0", "value": "0"}, {"name": "5", "value": "5"}, {"name": "10", "value": "10"}, {"name": "15", "value": "15"}, {"name": "20", "value": "20"}, {"name": "25", "value": "25"}, {"name": "30", "value": "30"}, {"name": "35", "value": "35"}, {"name": "40", "value": "40"}, {"name": "45", "value": "45"}, {"name": "50", "value": "50"}, {"name": "55", "value": "55"}, {"name": "60", "value": "60"}, {"name": "65", "value": "65"}, {"name": "70", "value": "70"}, {"name": "75", "value": "75"}, {"name": "80", "value": "80"}, {"name": "85", "value": "85"}, {"name": "90", "value": "90"}, {"name": "95", "value": "95"}, {"name": "100", "value": "100"}, {"name": "110", "value": "110"}, {"name": "120", "value": "120"}, {"name": "130", "value": "130"}, {"name": "140", "value": "140"}, {"name": "150", "value": "150"}, {"name": "160", "value": "160"}, {"name": "170", "value": "170"}, {"name": "280", "value": "280"}, {"name": "290", "value": "290"}, {"name": "300", "value": "300"}], "order": 12}}, "coInsuranceDentalEmergency": {"type": "string", "meta": {"label": "Co-Insurance (Dental Emergency)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "0%", "value": "0"}, {"name": "5%", "value": "5"}, {"name": "10%", "value": "10"}, {"name": "15%", "value": "15"}, {"name": "20%", "value": "20"}], "order": 13}}, "maxCoInsuranceDentalEmergency": {"type": "string", "meta": {"label": "Max of Co-Insurance (Dental Emergency)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "0", "value": "0"}, {"name": "5", "value": "5"}, {"name": "10", "value": "10"}, {"name": "15", "value": "15"}, {"name": "20", "value": "20"}, {"name": "25", "value": "25"}, {"name": "30", "value": "30"}, {"name": "35", "value": "35"}, {"name": "40", "value": "40"}, {"name": "45", "value": "45"}, {"name": "50", "value": "50"}, {"name": "55", "value": "55"}, {"name": "60", "value": "60"}, {"name": "65", "value": "65"}, {"name": "70", "value": "70"}, {"name": "75", "value": "75"}, {"name": "80", "value": "80"}, {"name": "85", "value": "85"}, {"name": "90", "value": "90"}, {"name": "95", "value": "95"}, {"name": "100", "value": "100"}, {"name": "110", "value": "110"}, {"name": "120", "value": "120"}, {"name": "130", "value": "130"}, {"name": "140", "value": "140"}, {"name": "150", "value": "150"}, {"name": "160", "value": "160"}, {"name": "170", "value": "170"}, {"name": "280", "value": "280"}, {"name": "290", "value": "290"}, {"name": "300", "value": "300"}], "order": 14}}, "coInsuranceOptical": {"type": "string", "meta": {"label": "Co-Insurance (Optical)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "0%", "value": "0"}, {"name": "5%", "value": "5"}, {"name": "10%", "value": "10"}, {"name": "15%", "value": "15"}, {"name": "20%", "value": "20"}], "order": 15}}, "maxCoInsuranceOptical": {"type": "string", "meta": {"label": "Max of Co-Insurance (Optical)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "0", "value": "0"}, {"name": "5", "value": "5"}, {"name": "10", "value": "10"}, {"name": "15", "value": "15"}, {"name": "20", "value": "20"}, {"name": "25", "value": "25"}, {"name": "30", "value": "30"}, {"name": "35", "value": "35"}, {"name": "40", "value": "40"}, {"name": "45", "value": "45"}, {"name": "50", "value": "50"}, {"name": "55", "value": "55"}, {"name": "60", "value": "60"}, {"name": "65", "value": "65"}, {"name": "70", "value": "70"}, {"name": "75", "value": "75"}, {"name": "80", "value": "80"}, {"name": "85", "value": "85"}, {"name": "90", "value": "90"}, {"name": "95", "value": "95"}, {"name": "100", "value": "100"}, {"name": "110", "value": "110"}, {"name": "120", "value": "120"}, {"name": "130", "value": "130"}, {"name": "140", "value": "140"}, {"name": "150", "value": "150"}, {"name": "160", "value": "160"}, {"name": "170", "value": "170"}, {"name": "280", "value": "280"}, {"name": "290", "value": "290"}, {"name": "300", "value": "300"}], "order": 16}}}}, "planFields_plan08": {"description": "MG19 Fields", "properties": {"benefitLimit": {"type": "string", "meta": {"label": "Global Benefit Limit", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "placeholder": "Global Benefit Limit", "options": [{"key": "170900542710394946", "name": "500000", "value": "500000"}, {"key": "170900543340712139", "name": "1000000", "value": "1000000"}, {"key": "170900543445656053", "name": "1500000", "value": "1500000"}], "isCustom": true, "order": 1}}, "roomType": {"type": "string", "meta": {"label": "Room Type", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "placeholder": "Room Type", "options": [{"key": "17085103989266751", "name": "Private", "value": "private"}, {"key": "170851042041948846", "name": "Semi-Private", "value": "semiPrivate"}], "isCustom": true, "order": 2}}, "coInsurance": {"type": "string", "meta": {"label": "Co-Insurance (Non Medicine)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "placeholder": "Co-Insurance", "options": [{"key": "170851153982857801", "name": "0%", "value": "0"}, {"key": "170851161602941617", "name": "5%", "value": "5"}, {"key": "170851170703838472", "name": "10%", "value": "10"}, {"key": "170852336663853029", "name": "15%", "value": "15"}, {"key": "170852331501964023", "name": "20%", "value": "20"}], "isCustom": true, "order": 3}}, "maxCoInsurance": {"type": "string", "meta": {"label": "Max of Co-Insurance (Non Medicine)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "placeholder": "Max of Co-Insurance", "options": [{"name": "0", "value": "0"}, {"name": "5", "value": "5"}, {"name": "10", "value": "10"}, {"name": "15", "value": "15"}, {"name": "20", "value": "20"}, {"name": "25", "value": "25"}, {"name": "30", "value": "30"}, {"name": "35", "value": "35"}, {"name": "40", "value": "40"}, {"name": "45", "value": "45"}, {"name": "50", "value": "50"}, {"name": "55", "value": "55"}, {"name": "60", "value": "60"}, {"name": "65", "value": "65"}, {"name": "70", "value": "70"}, {"name": "75", "value": "75"}, {"name": "80", "value": "80"}, {"name": "85", "value": "85"}, {"name": "90", "value": "90"}, {"name": "95", "value": "95"}, {"name": "100", "value": "100"}, {"name": "110", "value": "110"}, {"name": "120", "value": "120"}, {"name": "130", "value": "130"}, {"name": "140", "value": "140"}, {"name": "150", "value": "150"}, {"name": "160", "value": "160"}, {"name": "170", "value": "170"}, {"name": "280", "value": "280"}, {"name": "290", "value": "290"}, {"name": "300", "value": "300"}], "isCustom": true, "order": 4}}, "maternity": {"type": "string", "meta": {"label": "Maternity", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "placeholder": "Maternity", "options": [{"key": "170852406274494844", "name": "10000", "value": "10000"}, {"key": "170852407959569568", "name": "15000", "value": "15000"}, {"key": "170852408911642653", "name": "20000", "value": "20000"}, {"key": "170852410149949648", "name": "25000", "value": "25000"}, {"key": "170852411213176694", "name": "30000", "value": "30000"}], "order": 5}}, "dentalEssential": {"type": "string", "meta": {"label": "Dental (Essential and preventive dentistry)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "placeholder": "Dental (Essential and preventive dentistry)", "options": [{"key": "170852416125858213", "name": "1200", "value": "1200"}, {"key": "170852418039730344", "name": "1500", "value": "1500"}, {"key": "170852418257599015", "name": "1800", "value": "1800"}, {"key": "170852418399089806", "name": "2400", "value": "2400"}, {"key": "170852418562886388", "name": "3000", "value": "3000"}, {"key": "170852422179467639", "name": "4500", "value": "4500"}, {"key": "170852423507856224", "name": "6000", "value": "6000"}], "order": 6}}, "dentalTreatment": {"type": "string", "meta": {"label": "Dental (Treatment of Root Canals and Emergencies)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "placeholder": "Dental (Treatment of Root Canals and Emergencies)", "options": [{"key": "170852429300573262", "name": "800", "value": "800"}, {"key": "170852429517073004", "name": "1000", "value": "1000"}, {"key": "170852429745334050", "name": "1200", "value": "1200"}, {"key": "170852429852019014", "name": "1600", "value": "1600"}, {"key": "170852434936416484", "name": "2000", "value": "2000"}, {"key": "170852435923985831", "name": "3000", "value": "3000"}, {"key": "170852436850638600", "name": "4000", "value": "4000"}], "order": 7}}, "optical": {"type": "string", "meta": {"label": "Optical", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "placeholder": "Optical", "options": [{"key": "170852441425835740", "name": "400", "value": "400"}, {"key": "170852442440234170", "name": "500", "value": "500"}, {"key": "170852442631618617", "name": "600", "value": "600"}, {"key": "170852442818388929", "name": "750", "value": "750"}, {"key": "170852443002316127", "name": "800", "value": "800"}, {"key": "170852483916780841", "name": "1000", "value": "1000"}, {"key": "170852484147020417", "name": "1250", "value": "1250"}, {"key": "17085248436554694", "name": "1500", "value": "1500"}, {"key": "170852484593357541", "name": "1750", "value": "1750"}, {"key": "170852488465195277", "name": "2000", "value": "2000"}, {"key": "170852489788026739", "name": "2250", "value": "2250"}, {"key": "170852491392878415", "name": "2500", "value": "2500"}], "isCustom": true, "order": 8}}, "coInsuranceGenericMedicine": {"type": "string", "meta": {"label": "Co-Insurance (Generic Medicine)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "0%", "value": "0"}, {"name": "5%", "value": "5"}, {"name": "10%", "value": "10"}, {"name": "15%", "value": "15"}, {"name": "20%", "value": "20"}], "order": 9}}, "maxCoInsuranceGenericMedicine": {"type": "string", "meta": {"label": "Max of Co-Insurance (Generic Medicine)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "0", "value": "0"}, {"name": "5", "value": "5"}, {"name": "10", "value": "10"}, {"name": "15", "value": "15"}, {"name": "20", "value": "20"}, {"name": "25", "value": "25"}, {"name": "30", "value": "30"}, {"name": "35", "value": "35"}, {"name": "40", "value": "40"}, {"name": "45", "value": "45"}, {"name": "50", "value": "50"}, {"name": "55", "value": "55"}, {"name": "60", "value": "60"}, {"name": "65", "value": "65"}, {"name": "70", "value": "70"}, {"name": "75", "value": "75"}, {"name": "80", "value": "80"}, {"name": "85", "value": "85"}, {"name": "90", "value": "90"}, {"name": "95", "value": "95"}, {"name": "100", "value": "100"}, {"name": "110", "value": "110"}, {"name": "120", "value": "120"}, {"name": "130", "value": "130"}, {"name": "140", "value": "140"}, {"name": "150", "value": "150"}, {"name": "160", "value": "160"}, {"name": "170", "value": "170"}, {"name": "280", "value": "280"}, {"name": "290", "value": "290"}, {"name": "300", "value": "300"}], "order": 10}}, "coInsuranceBrandedMedicine": {"type": "string", "meta": {"label": "Co-Insurance (Branded Medicine)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "0%", "value": "0"}, {"name": "5%", "value": "5"}, {"name": "10%", "value": "10"}, {"name": "15%", "value": "15"}, {"name": "20%", "value": "20"}], "order": 11}}, "maxCoInsuranceBrandedMedicine": {"type": "string", "meta": {"label": "Max of Co-Insurance (Branded Medicine)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "0", "value": "0"}, {"name": "5", "value": "5"}, {"name": "10", "value": "10"}, {"name": "15", "value": "15"}, {"name": "20", "value": "20"}, {"name": "25", "value": "25"}, {"name": "30", "value": "30"}, {"name": "35", "value": "35"}, {"name": "40", "value": "40"}, {"name": "45", "value": "45"}, {"name": "50", "value": "50"}, {"name": "55", "value": "55"}, {"name": "60", "value": "60"}, {"name": "65", "value": "65"}, {"name": "70", "value": "70"}, {"name": "75", "value": "75"}, {"name": "80", "value": "80"}, {"name": "85", "value": "85"}, {"name": "90", "value": "90"}, {"name": "95", "value": "95"}, {"name": "100", "value": "100"}, {"name": "110", "value": "110"}, {"name": "120", "value": "120"}, {"name": "130", "value": "130"}, {"name": "140", "value": "140"}, {"name": "150", "value": "150"}, {"name": "160", "value": "160"}, {"name": "170", "value": "170"}, {"name": "280", "value": "280"}, {"name": "290", "value": "290"}, {"name": "300", "value": "300"}], "order": 12}}, "coInsuranceOptical": {"type": "string", "meta": {"label": "Co-Insurance (Optical)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "0%", "value": "0"}, {"name": "5%", "value": "5"}, {"name": "10%", "value": "10"}, {"name": "15%", "value": "15"}, {"name": "20%", "value": "20"}], "order": 13}}, "maxCoInsuranceOptical": {"type": "string", "meta": {"label": "Max of Co-Insurance (Optical)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "0", "value": "0"}, {"name": "5", "value": "5"}, {"name": "10", "value": "10"}, {"name": "15", "value": "15"}, {"name": "20", "value": "20"}, {"name": "25", "value": "25"}, {"name": "30", "value": "30"}, {"name": "35", "value": "35"}, {"name": "40", "value": "40"}, {"name": "45", "value": "45"}, {"name": "50", "value": "50"}, {"name": "55", "value": "55"}, {"name": "60", "value": "60"}, {"name": "65", "value": "65"}, {"name": "70", "value": "70"}, {"name": "75", "value": "75"}, {"name": "80", "value": "80"}, {"name": "85", "value": "85"}, {"name": "90", "value": "90"}, {"name": "95", "value": "95"}, {"name": "100", "value": "100"}, {"name": "110", "value": "110"}, {"name": "120", "value": "120"}, {"name": "130", "value": "130"}, {"name": "140", "value": "140"}, {"name": "150", "value": "150"}, {"name": "160", "value": "160"}, {"name": "170", "value": "170"}, {"name": "280", "value": "280"}, {"name": "290", "value": "290"}, {"name": "300", "value": "300"}], "order": 14}}, "coInsuranceDentalEmergency": {"type": "string", "meta": {"label": "Co-Insurance (Dental Emergency)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "0%", "value": "0"}, {"name": "5%", "value": "5"}, {"name": "10%", "value": "10"}, {"name": "15%", "value": "15"}, {"name": "20%", "value": "20"}], "order": 15}}, "maxCoInsuranceDentalEmergency": {"type": "string", "meta": {"label": "Max of Co-Insurance (Dental Emergency)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "0", "value": "0"}, {"name": "5", "value": "5"}, {"name": "10", "value": "10"}, {"name": "15", "value": "15"}, {"name": "20", "value": "20"}, {"name": "25", "value": "25"}, {"name": "30", "value": "30"}, {"name": "35", "value": "35"}, {"name": "40", "value": "40"}, {"name": "45", "value": "45"}, {"name": "50", "value": "50"}, {"name": "55", "value": "55"}, {"name": "60", "value": "60"}, {"name": "65", "value": "65"}, {"name": "70", "value": "70"}, {"name": "75", "value": "75"}, {"name": "80", "value": "80"}, {"name": "85", "value": "85"}, {"name": "90", "value": "90"}, {"name": "95", "value": "95"}, {"name": "100", "value": "100"}, {"name": "110", "value": "110"}, {"name": "120", "value": "120"}, {"name": "130", "value": "130"}, {"name": "140", "value": "140"}, {"name": "150", "value": "150"}, {"name": "160", "value": "160"}, {"name": "170", "value": "170"}, {"name": "280", "value": "280"}, {"name": "290", "value": "290"}, {"name": "300", "value": "300"}], "order": 16}}}}, "planFields_plan12": {"description": "MG23 Fields", "properties": {"benefitLimit": {"type": "string", "meta": {"label": "Global Benefit Limit", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "placeholder": "Global Benefit Limit", "options": [{"key": "170900542710394946", "name": "500000", "value": "500000"}, {"key": "170900543340712139", "name": "1000000", "value": "1000000"}, {"key": "170900543445656053", "name": "1500000", "value": "1500000"}], "isCustom": true, "order": 1}}, "roomType": {"type": "string", "meta": {"label": "Room Type", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "placeholder": "Room Type", "options": [{"key": "170851042041948846", "name": "Semi-Private", "value": "semiPrivate"}], "isCustom": true, "order": 2}}, "coInsurance": {"type": "string", "meta": {"label": "Co-Insurance (Non Medicine)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "placeholder": "Co-Insurance", "options": [{"key": "170852331501964023", "name": "20%", "value": "20"}], "isCustom": true, "order": 3}}, "maxCoInsurance": {"type": "string", "meta": {"label": "Max of Co-Insurance (Non Medicine)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "placeholder": "Max of Co-Insurance", "options": [{"name": "100", "value": "100"}, {"name": "200", "value": "200"}, {"name": "300", "value": "300"}], "isCustom": true, "order": 4}}, "maternity": {"type": "string", "meta": {"label": "Maternity", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "placeholder": "Maternity", "options": [{"key": "170852407959569568", "name": "15000", "value": "15000"}], "isCustom": true, "order": 5}}, "dentalEssential": {"type": "string", "meta": {"label": "Dental (Essential and preventive dentistry)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "placeholder": "Dental (Essential and preventive dentistry)", "options": [{"key": "170852416125858213", "name": "1200", "value": "1200"}, {"key": "170852418039730344", "name": "1500", "value": "1500"}, {"key": "170852418257599015", "name": "1800", "value": "1800"}], "isCustom": true, "order": 6}}, "dentalTreatment": {"type": "string", "meta": {"label": "Dental (Treatment of Root Canals and Emergencies)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "placeholder": "Dental (Treatment of Root Canals and Emergencies)", "options": [{"key": "170852429300573262", "name": "800", "value": "800"}, {"key": "170852429517073004", "name": "1000", "value": "1000"}, {"key": "170852429745334050", "name": "1200", "value": "1200"}], "isCustom": true, "order": 7}}, "optical": {"type": "string", "meta": {"label": "Optical", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "placeholder": "Optical", "options": [{"key": "170852441425835740", "name": "400", "value": "400"}, {"key": "170852442440234170", "name": "500", "value": "500"}, {"key": "170852442631618617", "name": "600", "value": "600"}, {"key": "170852442818388929", "name": "750", "value": "750"}, {"key": "170852443002316127", "name": "800", "value": "800"}], "isCustom": true, "order": 8}}, "coInsuranceGenericMedicine": {"type": "string", "meta": {"label": "Co-Insurance (Generic Medicine)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "20%", "value": "20"}], "order": 9}}, "maxCoInsuranceGenericMedicine": {"type": "string", "meta": {"label": "Max of Co-Insurance (Generic Medicine)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "100", "value": "100"}, {"name": "200", "value": "200"}, {"name": "300", "value": "300"}], "order": 10}}, "coInsuranceBrandedMedicine": {"type": "string", "meta": {"label": "Co-Insurance (Branded Medicine)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "20%", "value": "20"}], "order": 11}}, "maxCoInsuranceBrandedMedicine": {"type": "string", "meta": {"label": "Max of Co-Insurance (Branded Medicine)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "100", "value": "100"}, {"name": "200", "value": "200"}, {"name": "300", "value": "300"}], "order": 12}}, "coInsuranceDentalEmergency": {"type": "string", "meta": {"label": "Co-Insurance (Dental Emergency)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "20%", "value": "20"}], "order": 13}}, "maxCoInsuranceDentalEmergency": {"type": "string", "meta": {"label": "Max of Co-Insurance (Dental Emergency)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": true, "fieldType": "text", "validations": "", "options": [{"name": "100", "value": "100"}, {"name": "200", "value": "200"}, {"name": "300", "value": "300"}], "isCustom": true, "order": 14}}, "coInsuranceOptical": {"type": "string", "meta": {"label": "Co-Insurance (Optical)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "20%", "value": "20"}], "order": 15}}, "maxCoInsuranceOptical": {"type": "string", "meta": {"label": "Max of Co-Insurance (Optical)", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"name": "100", "value": "100"}, {"name": "200", "value": "200"}, {"name": "300", "value": "300"}], "order": 16}}}}, "memberCustomSchema": {"description": "Insureds Fields", "properties": {"officialID": {"type": "string", "meta": {"label": "Official ID", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": ""}}, "sponsorID": {"type": "string", "meta": {"label": "Sponsor ID", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": ""}}, "mobileNo": {"type": "string", "meta": {"label": "Mobile No", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "number", "numeric": true}}, "firstNameEng": {"type": "string", "meta": {"label": "First Name (ENG)", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": ""}}, "lastNameEng": {"type": "string", "meta": {"label": "Last Name (ENG)", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": ""}}, "secondNameEng": {"type": "string", "meta": {"label": "Second Name (ENG)", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": ""}}, "thirdNameEng": {"type": "string", "meta": {"label": "Third Name (ENG)", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": ""}}, "iqamaExpiryDate": {"type": "string", "meta": {"label": "Iqama Expiry Date", "component": "CDatePicker", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "date", "validations": ""}}, "nationalityCode": {"type": "number", "meta": {"label": "Nationality Code", "component": "JInputNumber", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "number", "validations": ""}}, "occupationCode": {"type": "number", "meta": {"label": "Occupation Code", "component": "JInputNumber", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "number", "validations": ""}}, "maritalStatus": {"type": "string", "meta": {"label": "Marital Status", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": ""}}}}}