﻿using AutoFixture;
using CoverGo.Auth.Client;
using CoverGo.Products.Domain;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.Ports;
using CoverGo.Products.Infrastructure.Products.Adapters.EventStore;
using EventStore.Client;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests
{
    public class ProductBuilderEventStoreTests
    {
        private readonly Fixture _fixture = new();

        [Fact]
        public async Task GIVEN_ProductBuilder_WHEN_valid_ProductId_THEN_Product_Nodes_can_be_pulled()
        {
            string tenantId = _fixture.Create<string>();
            string productTreeId = _fixture.Create<Guid>().ToString();
            string nodeId = _fixture.Create<Guid>().ToString();
            string nodeStreamId = $"pd-node-{nodeId}";
            EventStoreSettings eventStoreSettings = _fixture.Create<EventStoreSettings>();
            CancellationToken cancellationToken = _fixture.Create<CancellationToken>();

            Mock<IEventStoreClientAdapter> mockEventStoreClientAdapter = new Mock<IEventStoreClientAdapter>();


            mockEventStoreClientAdapter.Setup(x => x.ReadStreamAsync(
                It.IsAny<Direction>(),
                It.IsAny<string>(),
                It.IsAny<StreamPosition>(),
                It.IsAny<long>(),
                It.IsAny<bool>(),
                It.IsAny<TimeSpan?>(),
                It.IsAny<UserCredentials?>(),
                It.IsAny<CancellationToken>())
            ).ReturnsAsync(await Task.FromResult(
                new List<ResolvedEvent>()));


            Mock<IEventStoreClientFactory> eventStoreClientFactoryMock = new();

            eventStoreClientFactoryMock.Setup(x => x.GetEventStoreClient(It.IsAny<EventStoreSettings>()))
                .Returns(mockEventStoreClientAdapter.Object);

            var productBuilderEventStore = new ProductBuilderEventStore(eventStoreClientFactoryMock.Object, eventStoreSettings, NullLogger<ProductBuilderEventStore>.Instance);

            var nodes = await productBuilderEventStore.GetProductNodes(tenantId, productTreeId, cancellationToken);
            nodes.Should().NotBeNull();
            nodes.Count.Should().Be(0);
        }

        [Fact]
        public async Task GIVEN_ProductBuilder_WHEN_valid_ProductId_FOR_Node_Events_THEN_Product_Node_Events_can_be_pulled()
        {
            string tenantId = _fixture.Create<string>();
            string nodeId = _fixture.Create<Guid>().ToString();
            ProductInfo productInfo = _fixture.Create<ProductInfo>();

            EventStoreSettings eventStoreSettings = _fixture.Create<EventStoreSettings>();
            CancellationToken cancellationToken = _fixture.Create<CancellationToken>();

            Mock<IEventStoreClientAdapter> mockEventStoreClientAdapter = new Mock<IEventStoreClientAdapter>();


            mockEventStoreClientAdapter.Setup(x => x.ReadStreamAsync(
                It.IsAny<Direction>(),
                It.IsAny<string>(),
                It.IsAny<StreamPosition>(),
                It.IsAny<long>(),
                It.IsAny<bool>(),
                It.IsAny<TimeSpan?>(),
                It.IsAny<UserCredentials?>(),
                It.IsAny<CancellationToken>())
            ).ReturnsAsync(await Task.FromResult(
                new List<ResolvedEvent>()));


            Mock<IEventStoreClientFactory> eventStoreClientFactoryMock = new();

            eventStoreClientFactoryMock.Setup(x => x.GetEventStoreClient(It.IsAny<EventStoreSettings>()))
                .Returns(mockEventStoreClientAdapter.Object);

            var productBuilderEventStore = new ProductBuilderEventStore(eventStoreClientFactoryMock.Object, eventStoreSettings, NullLogger<ProductBuilderEventStore>.Instance);

            var nodeEvents = await productBuilderEventStore.GetNodeEvents(tenantId, nodeId, productInfo, cancellationToken);

            nodeEvents.Should().NotBeNull();
        }

        [Fact]
        public async Task GIVEN_EventStoreClientFactory_WHEN_valid_EventStoreSettings_THEN_EventStoreClient_can_be_created()
        {
            EventStoreSettings eventStoreSettings = _fixture.Create<EventStoreSettings>();
            var eventStoreClientFactory = new EventStoreClientFactory();
            var eventStoreClient = eventStoreClientFactory.GetEventStoreClient(eventStoreSettings);
            eventStoreClient.Should().NotBeNull();
        }
    }
}
