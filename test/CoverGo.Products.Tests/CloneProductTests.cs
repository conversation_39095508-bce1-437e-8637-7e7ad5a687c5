#nullable enable

using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using AutoFixture.Xunit2;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.Commands;
using CoverGo.Products.Domain.Products.Events;
using CoverGo.Products.Domain.Products.Ports;
using CoverGo.Products.Infrastructure.Products;

using Moq;

namespace CoverGo.Products.Tests;

[Trait("Ticket", "CH-7546")]
public class CloneProductTests
{
    [Fact]
    public void CloneProductSetsNewId()
    {
        // Arrange (Given)
        var product = new Product()
        {
            Id = new()
            {
                Plan = "123",
                Type = "gm",
                Version = "1.0"
            },
        };

        // Act (When)
        var clonedProduct = product.CloneProduct(
            "1.0_custom",
            false,
            ["123"]);

        // Assert (Then)
        clonedProduct.Id.Should().Be(new ProductId()
        {
            Plan = "123",
            Type = "gm",
            Version = "1.0_custom",
        });
    }

    [Theory]
    [InlineData(
        null,
        $$"""{"readonly":false}""")]
    [InlineData(
        "{}",
        $$"""{"readonly":false}""")]
    [InlineData(
        $$"""{"readonly": true}""",
        $$"""{"readonly":false}""")]
    [InlineData(
        """
        {
            "testField": true
        }
        """,
        $$"""{"testField":true,"readonly":false}""")]
    public void CloneProductSetsFieldReadonlyToFalse(string? originalFields, string expectedFields)
    {
        // Arrange (Given)
        var product = new Product()
        {
            Id = new()
            {
                Plan = "123",
                Type = "gm",
                Version = "1.0"
            },
            Fields = originalFields,
        };

        // Act (When)
        var clonedProduct = product.CloneProduct(
            "1.0_custom",
            false,
            []);

        // Assert (Then)
        clonedProduct.Fields.Should().Be(expectedFields);
    }

    [Theory]
    [InlineData(
        $$"""[{"ch":[{}]}]""",
        $$"""[{"ch":[{}]}]""")]
    [InlineData(
        $$"""[{"ch":[{"n":"plan"}]}]""",
        $$"""[{"ch":[{"n":"plan","readonly":false}]}]""")]
    [InlineData(
        $$"""[{"ch":[{"n":"plan","readonly":true}]}]""",
        $$"""[{"ch":[{"n":"plan","readonly":false}]}]""")]
    public void CloneProductUpdatesPlanNodesReadonlyToFalse(
        string originalRepresentation,
        string expectedRepresentation)
    {
        // Arrange (Given)
        var product = new Product()
        {
            Id = new()
            {
                Plan = "123",
                Type = "gm",
                Version = "1.0"
            },
            Representation = originalRepresentation,
        };

        // Act (When)
        var clonedProduct = product.CloneProduct(
            "1.0_custom",
            true,
            []);

        // Assert (Then)
        clonedProduct.Representation.Should().Be(expectedRepresentation);
    }

    [Theory]
    [InlineData(
        $$"""[{"ch":[{"n":"plan","readonly":false}]}]""",
        $$"""[{"ch":[{"n":"plan","readonly":false}]}]""")]
    [InlineData(
        $$"""[{"ch":[{"n":"plan"}]}]""",
        $$"""[{"ch":[{"n":"plan"}]}]""")]
    [InlineData(
        $$"""[{"ch":[{"n":"plan","readonly":true}]}]""",
        $$"""[{"ch":[{"n":"plan","readonly":true}]}]""")]
    public void CloneProductDoesNotUpdatePlanNodesReadonlyToFalse(
        string originalRepresentation,
        string expectedRepresentation)
    {
        // Arrange (Given)
        var product = new Product()
        {
            Id = new()
            {
                Plan = "123",
                Type = "gm",
                Version = "1.0"
            },
            Representation = originalRepresentation,
        };

        // Act (When)
        var clonedProduct = product.CloneProduct(
            "1.0_custom",
            false,
            []);

        // Assert (Then)
        clonedProduct.Representation.Should().Be(expectedRepresentation);
    }

    [Fact]
    public void CloneProductSetsNewScriptIds()
    {
        // Arrange (Given)
        var product = new Product()
        {
            Id = new()
            {
                Plan = "123",
                Type = "gm",
                Version = "1.0"
            },
            ScriptIds = [],
        };

        // Act (When)
        var clonedProduct = product.CloneProduct(
            "1.0_custom",
            false,
            ["123"]);

        // Assert (Then)
        clonedProduct.ScriptIds.Should().BeEquivalentTo(["123"]);
    }

    [Theory, CustomAutoData]
    public async Task CloneProductCopiesScripts(
        [Frozen] Mock<IProductVersionRepository> productVersionRepositoryMock,
        [Frozen] Mock<IProductScriptsRepository> productScriptsRepositoryMock,
        CloneProductVersionCommandHandler sut
    )
    {
        // Arrange (Given)
        var productId = new ProductId
        {
            Plan = "123",
            Type = "gm",
            Version = "1.0"
        };
        var product = new Product
        {
            Id = productId,
            ScriptIds = ["222", "333"],
        };
        productVersionRepositoryMock
            .Setup(it => it.Get(productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(product);
        productScriptsRepositoryMock
            .Setup(it => it.CloneProductScripts(product.ScriptIds, It.IsAny<CancellationToken>()))
            .ReturnsAsync(["123", "345"]);

        // Act (When)
        var result = await sut.Handle(
            new CloneProductVersionCommand()
            {
                OriginalProductId = productId,
                CloneProductVersion = "1.0_custom",
                IsReadonly = true,
            },
            default);

        // Assert (Then)
        result.ClonedProduct.ScriptIds.Should().BeEquivalentTo(["123", "345"]);
    }

    [Theory]
    [CustomInlineAutoData("123", "345", "345")]
    [CustomInlineAutoData("123", null, "123")]
    public async Task CloneProductCopiesName(
        string productPlan,
        string? productName,
        string expectedName,
        [Frozen] Mock<IProductVersionRepository> productVersionRepositoryMock,
        [Frozen] Mock<IProductNameRepository> productNameRepositoryMock,
        [Frozen] Mock<IProductScriptsRepository> productScriptsRepositoryMock,
        CloneProductVersionCommandHandler sut
    )
    {
        // Arrange (Given)
        var productId = new ProductId
        {
            Plan = productPlan,
            Type = "gm",
            Version = "1.0"
        };
        var product = new Product
        {
            Id = productId,
            ScriptIds = ["222", "333"],
        };
        productVersionRepositoryMock
            .Setup(it => it.Get(productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(product);
        productNameRepositoryMock
            .Setup(it => it.Get(productId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(productName);
        productNameRepositoryMock
            .Setup(it => it.Create(It.IsAny<ProductId>(), productName ?? productPlan, It.IsAny<CancellationToken>()))
            .ReturnsAsync((ProductId p, string n, CancellationToken ct) => n);
        productScriptsRepositoryMock
            .Setup(it => it.CloneProductScripts(product.ScriptIds, It.IsAny<CancellationToken>()))
            .ReturnsAsync(["123", "345"]);

        // Act (When)
        var result = await sut.Handle(
            new CloneProductVersionCommand()
            {
                OriginalProductId = productId,
                CloneProductVersion = "1.0_custom",
                IsReadonly = true,
            },
            default);

        // Assert (Then)
        result.ClonedProductName.Should().Be(expectedName);
        productNameRepositoryMock
            .Verify(
                it => it.Create(new ProductId()
                {
                    Plan = productPlan,
                    Type = productId.Type,
                    Version = "1.0_custom",
                }, productName ?? productPlan, It.IsAny<CancellationToken>()),
                Times.Once());
    }

    [Trait("Ticket", "CH-15008")]
    [Theory, CustomAutoData]
    public async Task CloneProductCopiesPolicyIssuanceMethod(
        [Frozen] Mock<IProductRepository> productRepositoryMock,
        [Frozen] Mock<IProductEventStore> productEventStoreMock,
        ProductClonedHandler sut,
        ProductCloned notification,
        CancellationToken cancellationToken
    )
    {
        // Arrange (Given)
        productRepositoryMock
            .Setup(x => x.CreateAsync(
                It.IsAny<string>(),
                It.IsAny<CreateProductCommand>(),
                cancellationToken))
            .ReturnsAsync(Result.Success());
        productEventStoreMock
            .Setup(x => x.AddEventAsync(
                It.IsAny<string>(),
                It.IsAny<ProductEvent>(),
                cancellationToken))
            .ReturnsAsync(Result.Success());

        // Act (When)
        await sut.Handle(notification, cancellationToken);

        // Assert (Then)
        productRepositoryMock.Verify(x => x.CreateAsync(
            It.IsAny<string>(),
            It.Is<CreateProductCommand>(x => x.PolicyIssuanceMethod == notification.ClonedProduct.PolicyIssuanceMethod),
            cancellationToken
        ), Times.Once);
    }

    [Trait("Ticket", "CH-16524")]
    [Theory, CustomAutoData]
    public async Task CloneProductCopiesAllowCustomProduct(
        [Frozen] Mock<IProductRepository> productRepositoryMock,
        [Frozen] Mock<IProductEventStore> productEventStoreMock,
        ProductClonedHandler sut,
        ProductCloned notification)
    {
        // Arrange (Given)
        productRepositoryMock
            .Setup(x => x.CreateAsync(
                It.IsAny<string>(),
                It.IsAny<CreateProductCommand>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());
        productEventStoreMock
            .Setup(x => x.AddEventAsync(
                It.IsAny<string>(),
                It.IsAny<ProductEvent>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());

        // Act (When)
        await sut.Handle(notification, default);

        // Assert (Then)
        productRepositoryMock.Verify(x => x.CreateAsync(
            It.IsAny<string>(),
            It.Is<CreateProductCommand>(x => x.AllowCustomProduct == notification.ClonedProduct.AllowCustomProduct),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Trait("Ticket", "CH-16524")]
    [Theory, CustomAutoData]
    public async Task CloneProductThrowsException(
        [Frozen] Mock<IProductRepository> productRepositoryMock,
        [Frozen] Mock<IProductEventStore> productEventStoreMock,
        ProductClonedHandler sut,
        ProductCloned notification,
        string errorMessage)
    {
        // Arrange (Given)
        productRepositoryMock
            .Setup(x => x.CreateAsync(
                It.IsAny<string>(),
                It.IsAny<CreateProductCommand>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());
        productEventStoreMock
            .Setup(x => x.AddEventAsync(
                It.IsAny<string>(),
                It.IsAny<ProductEvent>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Failure(errorMessage));

        // Act (When)
        var func = async() => await sut.Handle(notification, default);

        // Assert (Then)
        await Assert.ThrowsAsync<Exception>(func);
    }

    [Trait("Ticket", "CH-2216")]
    [Trait("Ticket", "CH-17039")]
    [Theory, CustomAutoData]
    public async Task ClonesProductOfferValidity(
        [Frozen] Mock<IProductRepository> productRepositoryMock,
        [Frozen] Mock<IProductEventStore> productEventStoreMock,
        ProductClonedHandler sut,
        ProductCloned notification)
    {
        // Arrange (Given)
        productRepositoryMock
            .Setup(x => x.CreateAsync(
                It.IsAny<string>(),
                It.IsAny<CreateProductCommand>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());
        productEventStoreMock
            .Setup(x => x.AddEventAsync(
                It.IsAny<string>(),
                It.IsAny<ProductEvent>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());

        // Act (When)
        await sut.Handle(notification, default);

        // Assert (Then)
        productRepositoryMock.Verify(x => x.CreateAsync(
            It.IsAny<string>(),
            It.Is<CreateProductCommand>(x => x.OfferValidityPeriod == notification.ClonedProduct.OfferValidityPeriod),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Trait("Ticket", "CH-16930")]
    [Trait("Ticket", "CH-22791")]
    [Theory, CustomAutoData]
    public async Task ClonesProductAttachedDocuments(
        [Frozen] Mock<IProductRepository> productRepositoryMock,
        [Frozen] Mock<IProductEventStore> productEventStoreMock,
        ProductClonedHandler sut,
        ProductCloned notification)
    {
        // Arrange (Given)
        productRepositoryMock
            .Setup(x => x.CreateAsync(
                It.IsAny<string>(),
                It.IsAny<CreateProductCommand>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());
        productEventStoreMock
            .Setup(x => x.AddEventAsync(
                It.IsAny<string>(),
                It.IsAny<ProductEvent>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result.Success());

        // Act (When)
        await sut.Handle(notification, default);

        // Assert (Then)
        productRepositoryMock.Verify(x => x.CreateAsync(
            It.IsAny<string>(),
            It.Is<CreateProductCommand>(x => x.AttachedDocumentsIds == notification.ClonedProduct.AttachedDocumentsIds),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Trait("Ticket", "CH-24253")]
    [Trait("Ticket", "CH-23867")]
    [Theory, CustomAutoData]
    public async Task Handle_WhenCreatingNewVersion_ShouldIncreaseVersion(
        [Frozen] Mock<IProductVersionRepository> productVersionRepositoryMock,
        [Frozen] Mock<IProductNameRepository> productNameRepositoryMock,
        [Frozen] Mock<IProductScriptsRepository> productScriptsRepositoryMock,
        [Frozen] Mock<IProductRepository> productRepository,
        CreateProductVersionCommandHandler sut
    )
    {
        // Arrange (Given)
        var originalProductId = new ProductId { Plan = "plan1", Type = "type1", Version = "1.0" };
        var originalProduct = new Product
        {
            Id = originalProductId,
            LifecycleStage = "beta"
        };
        var existingProduct = new Product
        {
            Id = new ProductId { Plan = "plan1", Type = "type1", Version = "2.0" }
        };

        productVersionRepositoryMock
            .SetupSequence(x => x.Get(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(originalProduct)
            .ReturnsAsync(default(Product)!);

        productNameRepositoryMock
            .Setup(x => x.Get(originalProductId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(default(string));

        productScriptsRepositoryMock
            .Setup(x => x.CloneProductScripts(It.IsAny<IReadOnlyCollection<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        productNameRepositoryMock
            .Setup(x => x.Create(It.IsAny<ProductId>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("plan1");

        productRepository
            .Setup(x => x.GetAsync(
                It.IsAny<string>(),
                It.IsAny<ProductWhere>(),
                It.IsAny<ProductConfig>(),
                It.IsAny<QueryParameters>(),
                It.IsAny<bool>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([originalProduct, existingProduct]);

        // Act (When)
        var result = await sut.Handle(new CreateProductVersionCommand
        {
            OriginalProductId = originalProductId,
            IsReadonly = false
        }, CancellationToken.None);

        // Assert (Then)
        result.ClonedProductId.Version.Should().Be("3.0");
    }

    [Trait("Ticket", "CH-23867")]
    [Theory, CustomAutoData]
    public async Task Handle_WhenProductInAlphaStage_ShouldThrowException(
        [Frozen] Mock<IProductVersionRepository> productVersionRepositoryMock,
        CreateProductVersionCommandHandler sut
    )
    {
        // Arrange (Given)
        var originalProductId = new ProductId { Plan = "plan1", Type = "type1", Version = "1.0" };
        var originalProduct = new Product
        {
            Id = originalProductId,
            LifecycleStage = "alpha"
        };

        productVersionRepositoryMock
            .Setup(x => x.Get(originalProductId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(originalProduct);

        // Act (When)
        Func<Task> func = async () => await sut.Handle(new CreateProductVersionCommand
        {
            OriginalProductId = originalProductId,
            IsReadonly = false
        }, CancellationToken.None);

        // Assert (Then)
        var ex = await func.Should().ThrowAsync<ProductVersioningException>();
        ex.And.Code.Should().Be("PRODUCT_VERSIONING_ERROR");
    }

    [Trait("Ticket", "CH-23867")]
    [Theory, CustomAutoData]
    public async Task Handle_WhenInvalidVersion_ShouldThrowException(
        [Frozen] Mock<IProductVersionRepository> productVersionRepositoryMock,
        CreateProductVersionCommandHandler sut
    )
    {
        // Arrange (Given)
        var originalProductId = new ProductId { Plan = "plan1", Type = "type1", Version = "invalid" };
        var originalProduct = new Product
        {
            Id = originalProductId,
            LifecycleStage = "beta"
        };

        productVersionRepositoryMock
            .Setup(x => x.Get(originalProductId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(originalProduct);

        // Act (When) & Assert (Then)
        await Assert.ThrowsAsync<ProductVersioningException>(() => sut.Handle(new CreateProductVersionCommand
        {
            OriginalProductId = originalProductId,
            IsReadonly = false
        }, CancellationToken.None));
    }

    [Trait("Ticket", "CH-24253")]
    [Trait("Ticket", "CH-23867")]
    [Theory, CustomAutoData]
    public async Task Handle_WhenNoProductName_ShouldUsePlanAsName(
        [Frozen] Mock<IProductVersionRepository> productVersionRepositoryMock,
        [Frozen] Mock<IProductNameRepository> productNameRepositoryMock,
        [Frozen] Mock<IProductScriptsRepository> productScriptsRepositoryMock,
        [Frozen] Mock<IProductRepository> productRepository,
        CreateProductVersionCommandHandler sut
    )
    {
        // Arrange (Given)
        var originalProductId = new ProductId { Plan = "plan1", Type = "type1", Version = "1.0" };
        var originalProduct = new Product
        {
            Id = originalProductId,
            ScriptIds = Array.Empty<string>()
        };

        productVersionRepositoryMock
            .Setup(x => x.Get(originalProductId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(originalProduct);

        productNameRepositoryMock
            .SetupSequence(x => x.Get(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("plan1");
        productNameRepositoryMock
            .SetupSequence(x => x.Create(It.IsAny<ProductId>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("plan1");

        productScriptsRepositoryMock
            .Setup(x => x.CloneProductScripts(It.IsAny<string[]>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        productRepository
            .Setup(x => x.GetAsync(
                It.IsAny<string>(),
                It.IsAny<ProductWhere>(),
                It.IsAny<ProductConfig>(),
                It.IsAny<QueryParameters>(),
                It.IsAny<bool>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([originalProduct]);

        // Act (When)
        var result = await sut.Handle(new CreateProductVersionCommand
        {
            OriginalProductId = originalProductId,
            IsReadonly = false
        }, CancellationToken.None);

        // Assert (Then)
        result.ClonedProductName.Should().Be("plan1");
    }
}
