﻿{
  "$schema": "http://json-schema.org/draft-07/schema",
  "$id": "http://example.com/example.json",
  "type": "object",
  "required": [
    "insureds"
  ],
  "properties": {
    "insureds": {
      "minItems": 1,
      "maxItems": 1,
      "items": {
        "allOf": [
          {
            "required": [
              "age",
              "sumAssured"
            ],
            "properties": {
              "age": {
                "type": "integer",
                "minimum": 18,
                "maximum": 75
              },
              "sumAssured": {
                "type": "integer",
                "minimum": 250000,
                "maximum": 3200000
              }
            }
          },
          {
            "if": {
              "properties": {
                "age": {
                  "minimum": 18,
                  "maximum": 45
                }
              }
            },
            "then": {
              "properties": {
                "sumAssured": {
                  "minimum": 250000,
                  "maximum": 3200000
                }
              }
            }
          },
          {
            "if": {
              "properties": {
                "age": {
                  "minimum": 46,
                  "maximum": 50
                }
              }
            },
            "then": {
              "properties": {
                "sumAssured": {
                  "minimum": 250000,
                  "maximum": 3200000
                }
              }
            }
          },
          {
            "if": {
              "properties": {
                "age": {
                  "minimum": 51,
                  "maximum": 55
                }
              }
            },
            "then": {
              "properties": {
                "sumAssured": {
                  "minimum": 250000,
                  "maximum": 3200000
                }
              }
            }
          },
          {
            "if": {
              "properties": {
                "age": {
                  "minimum": 56,
                  "maximum": 60
                }
              }
            },
            "then": {
              "properties": {
                "sumAssured": {
                  "minimum": 250000,
                  "maximum": 2500000
                }
              }
            }
          },
          {
            "if": {
              "properties": {
                "age": {
                  "minimum": 61,
                  "maximum": 65
                }
              }
            },
            "then": {
              "properties": {
                "sumAssured": {
                  "minimum": 250000,
                  "maximum": 1000000
                }
              }
            }
          }
        ]
      }
    }
  }
}