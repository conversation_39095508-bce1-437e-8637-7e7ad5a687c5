﻿using AutoFixture;
using AutoFixture.Xunit2;
using CoverGo.FeatureManagement;
using CoverGo.Products.Application.Controllers;
using CoverGo.Products.Domain.Auth;
using CoverGo.Products.Domain.Illustrations;
using CoverGo.Products.Domain.ProductImportHistory;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.Commands;
using CoverGo.Products.Domain.Products.Ports;
using CoverGo.Products.Domain.Underwriting;
using CoverGo.Proxies.Auth;
using CoverGo.Users.Client;
using JsonLogic.Net;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests
{
    public class ProductExchangeControllerTests
    {
        Fixture _fixture = new Fixture();

        [Theory, CustomAutoData]
        public async Task GIVEN_Product_WITH_FilePath_WHEN_Imported_THEN_Product_Is_Imported_Successful(
            [Frozen] Mock<ProductService> mockProductService
        )
        {
            // Arrange (Given)
            Mock<ProductEventService> mockProductEventService = new Mock<ProductEventService>(
                new Mock<IProductEventStore>().Object
                );

            Mock<ProductExchangeService> mockProductExchangeService = new Mock<ProductExchangeService>(
            mockProductService.Object,
            new Mock<IProductL10NAdapter>().Object,
            new Mock<IAuthAdapter>().Object,
            new Mock<IProductBuilderAdapter>().Object,
            new Mock<IProductExchangeAdapter>().Object,
            new Mock<IExternalTablesAdapter>().Object,
            new Mock<IProductImportHistoryService>().Object,
            NullLogger<ProductExchangeService>.Instance
                );

            mockProductExchangeService.Setup(x => x.ImportAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(),It.IsAny<bool?>(), It.IsAny<CancellationToken>(), It.IsAny<bool>()))
                .ReturnsAsync(new DomainUtils.Result() { Status = "success" });

            Mock<ProductEventLogsService> mockProductEventLogsService = new Mock<ProductEventLogsService>(
                new Mock<IProductEventStore>().Object,
                new Mock<IProductBuilderEventStore>().Object,
                mockProductService.Object,
                new Mock<IAuthService>().Object,
                new Mock<IUsersClient>().Object,
                NullLogger<ProductEventLogsService>.Instance
            );


            ProductExchangeController sut = new(
                mockProductService.Object,
                mockProductExchangeService.Object,
                mockProductEventLogsService.Object,
                NullLogger<ProductExchangeController>.Instance
                );

            // Act (When)
            var result = await sut.Import("test", "test", "test", CancellationToken.None);

            // Assert (Then)
            result.Should().NotBeNull();
            result.Status.Should().Be("success");
        }

        [Theory, CustomAutoData]
        public async Task GIVEN_Product_WITH_FilePath_WHEN_Import_Failed_THEN_Error_is_Returned(
            [Frozen] Mock<ProductService> mockProductService
        )
        {
            // Arrange (Given)
            Mock<ProductEventService> mockProductEventService = new Mock<ProductEventService>(
                new Mock<IProductEventStore>().Object
                );

            Mock<ProductExchangeService> mockProductExchangeService = new Mock<ProductExchangeService>(
            mockProductService.Object,
            new Mock<IProductL10NAdapter>().Object,
            new Mock<IAuthAdapter>().Object,
            new Mock<IProductBuilderAdapter>().Object,
            new Mock<IProductExchangeAdapter>().Object,
            new Mock<IExternalTablesAdapter>().Object,
            new Mock<IProductImportHistoryService>().Object,
            NullLogger<ProductExchangeService>.Instance
                );

            mockProductExchangeService.Setup(x => x.ImportAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<bool?>(), It.IsAny<CancellationToken>(), It.IsAny<bool>()))
                .ReturnsAsync(new DomainUtils.Result() { Status = "failure" });

            Mock<ProductEventLogsService> mockProductEventLogsService = new Mock<ProductEventLogsService>(
                new Mock<IProductEventStore>().Object,
                new Mock<IProductBuilderEventStore>().Object,
                mockProductService.Object,
                new Mock<IAuthService>().Object,
                new Mock<IUsersClient>().Object,
                NullLogger<ProductEventLogsService>.Instance
            );

            ProductExchangeController sut = new(
                mockProductService.Object,
                mockProductExchangeService.Object,
                mockProductEventLogsService.Object,
                NullLogger<ProductExchangeController>.Instance
                );

            // Act (When)
            var result = await sut.Import("test", "test", "test", CancellationToken.None);

            // Assert (Then)
            result.Should().NotBeNull();
            result.Status.Should().Be("failure");
        }

        [Theory, CustomAutoData]
        public async Task GIVEN_Product_WITH_FilePath_WHEN_ImportInfo_Requested_THEN_Product_ImportInfor_Is_returned_Successful(
            [Frozen] Mock<ProductService> mockProductService
        )
        {
            // Arrange (Given)
            Mock<ProductEventService> mockProductEventService = new Mock<ProductEventService>(
                new Mock<IProductEventStore>().Object
                );

            Mock<ProductExchangeService> mockProductExchangeService = new Mock<ProductExchangeService>(
            mockProductService.Object,
            new Mock<IProductL10NAdapter>().Object,
            new Mock<IAuthAdapter>().Object,
            new Mock<IProductBuilderAdapter>().Object,
            new Mock<IProductExchangeAdapter>().Object,
            new Mock<IExternalTablesAdapter>().Object,
            new Mock<IProductImportHistoryService>().Object,
            NullLogger<ProductExchangeService>.Instance
                );

            Mock<ProductEventLogsService> mockProductEventLogsService = new Mock<ProductEventLogsService>(
                new Mock<IProductEventStore>().Object,
                new Mock<IProductBuilderEventStore>().Object,
                mockProductService.Object,
                new Mock<IAuthService>().Object,
                new Mock<IUsersClient>().Object,
                NullLogger<ProductEventLogsService>.Instance
            );

            mockProductExchangeService.Setup(x => x.GetImportInfoAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>(), It.IsAny<bool>()))
                .ReturnsAsync(new DomainUtils.Result<ProductImportInfo>() { Status = "success" });

            ProductExchangeController sut = new(
                mockProductService.Object,
                mockProductExchangeService.Object,
                mockProductEventLogsService.Object,
                NullLogger<ProductExchangeController>.Instance
                );

            // Act (When)
            var result = await sut.ImportInfo("test", "test", CancellationToken.None);

            // Assert (Then)
            result.Should().NotBeNull();
            result.Status.Should().Be("success");
        }

        [Theory, CustomAutoData]
        public async Task GIVEN_Product_WITH_ProductId_WHEN_Cloned_THEN_Product_Clone_Success(
            [Frozen] Mock<ProductService> mockProductService
        )
        {
            // Arrange (Given)
            Fixture fixture = new Fixture();

            Mock<ProductEventService> mockProductEventService = new Mock<ProductEventService>(
                new Mock<IProductEventStore>().Object
                );

            Mock<ProductExchangeService> mockProductExchangeService = new Mock<ProductExchangeService>(
            mockProductService.Object,
            new Mock<IProductL10NAdapter>().Object,
            new Mock<IAuthAdapter>().Object,
            new Mock<IProductBuilderAdapter>().Object,
            new Mock<IProductExchangeAdapter>().Object,
            new Mock<IExternalTablesAdapter>().Object,
            new Mock<IProductImportHistoryService>().Object,
            NullLogger<ProductExchangeService>.Instance
                );

            mockProductExchangeService.Setup(x => x.CloneProductTreeAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<ProductId>(), It.IsAny<ProductName>(), It.IsAny<ProductId>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new DomainUtils.Result() { Status = "success" });

            Mock<ProductEventLogsService> mockProductEventLogsService = new Mock<ProductEventLogsService>(
               new Mock<IProductEventStore>().Object,
               new Mock<IProductBuilderEventStore>().Object,
               mockProductService.Object,
               new Mock<IAuthService>().Object,
               new Mock<IUsersClient>().Object,
               NullLogger<ProductEventLogsService>.Instance
           );

            ProductExchangeController sut = new(
                mockProductService.Object,
                mockProductExchangeService.Object,
                mockProductEventLogsService.Object,
                NullLogger<ProductExchangeController>.Instance
                );

            // Act (When)
            var result = await sut.Clone(
                fixture.Create<string>(),
                fixture.Create<string>(),
                fixture.Create<string>(),
                fixture.Create<CloneProductTreeCommand>(),
                fixture.Create<string>(),
                CancellationToken.None);

            // Assert (Then)
            result.Should().NotBeNull();
            result.Status.Should().Be("success");
        }

        [Theory, CustomAutoData]
        public async Task GIVEN_ProductId_WHEN_Queries_ProductEventLogs_THEN_Logs_are_Returned_Successfully(
            [Frozen] Mock<ProductService> mockProductService
        )
        {
            // Arrange (Given)
            Fixture fixture = new Fixture();

            Mock<ProductEventService> mockProductEventService = new Mock<ProductEventService>(
                new Mock<IProductEventStore>().Object
                );

            Mock<ProductExchangeService> mockProductExchangeService = new Mock<ProductExchangeService>(
            mockProductService.Object,
            new Mock<IProductL10NAdapter>().Object,
            new Mock<IAuthAdapter>().Object,
            new Mock<IProductBuilderAdapter>().Object,
            new Mock<IProductExchangeAdapter>().Object,
            new Mock<IExternalTablesAdapter>().Object,
            new Mock<IProductImportHistoryService>().Object,
            NullLogger<ProductExchangeService>.Instance
                );


            Mock<ProductEventLogsService> mockProductEventLogsService = new Mock<ProductEventLogsService>(
               new Mock<IProductEventStore>().Object,
               new Mock<IProductBuilderEventStore>().Object,
               mockProductService.Object,
               new Mock<IAuthService>().Object,
               new Mock<IUsersClient>().Object,
               NullLogger<ProductEventLogsService>.Instance
           );

            ProductExchangeController sut = new(
                mockProductService.Object,
                mockProductExchangeService.Object,
                mockProductEventLogsService.Object,
                NullLogger<ProductExchangeController>.Instance
                );

            var tenantId = "test-tenant";
            var productType = "test-product";
            var where = new ProductEventWhere();
            var cancellationToken = CancellationToken.None;

            var expectedEvents = new List<ProductEvent>
    {
        new ProductEvent { Id = "1", Type = ProductEventType.creation },
        new ProductEvent { Id = "2", Type = ProductEventType.update }
    };

            mockProductEventLogsService.Setup(x => x.GetProductEventStoreLogsAsync(
                    tenantId,
                    productType,
                    where,
                    cancellationToken))
                .ReturnsAsync(expectedEvents);

            // Act (When)
            var result = await sut.ProductEventLogs(
                tenantId,
                productType,
                where,
                cancellationToken);

            // Assert (Then)
            result.IsSuccess.Should().BeTrue();
            result.Value.Should().BeEquivalentTo(expectedEvents);
            result.Value.Count.Should().Be(expectedEvents.Count);

        }

        [Theory, CustomAutoData]
        public async Task GIVEN_ProductId_WHEN_Queries_ProductEventLogs_Failure_THEN_Logs_Return_Failure(
            [Frozen] Mock<ProductService> mockProductService
        )
        {
            // Arrange (Given)
            Fixture fixture = new Fixture();

            Mock<ProductEventService> mockProductEventService = new Mock<ProductEventService>(
                new Mock<IProductEventStore>().Object
                );

            Mock<ProductExchangeService> mockProductExchangeService = new Mock<ProductExchangeService>(
            mockProductService.Object,
            new Mock<IProductL10NAdapter>().Object,
            new Mock<IAuthAdapter>().Object,
            new Mock<IProductBuilderAdapter>().Object,
            new Mock<IProductExchangeAdapter>().Object,
            new Mock<IExternalTablesAdapter>().Object,
            new Mock<IProductImportHistoryService>().Object,
            NullLogger<ProductExchangeService>.Instance
                );

            Mock<ProductEventLogsService> mockProductEventLogsService = new Mock<ProductEventLogsService>(
               new Mock<IProductEventStore>().Object,
               new Mock<IProductBuilderEventStore>().Object,
               mockProductService.Object,
               new Mock<IAuthService>().Object,
               new Mock<IUsersClient>().Object,
               NullLogger<ProductEventLogsService>.Instance
           );

            ProductExchangeController sut = new(
                mockProductService.Object,
                mockProductExchangeService.Object,
                mockProductEventLogsService.Object,
                NullLogger<ProductExchangeController>.Instance
                );

            var tenantId = "test-tenant";
            var productType = "test-product";
            var where = new ProductEventWhere();
            var cancellationToken = CancellationToken.None;

            var expectedEvents = new List<ProductEvent>
    {
        new ProductEvent { Id = "1", Type = ProductEventType.creation },
        new ProductEvent { Id = "2", Type = ProductEventType.update }
    };

            var exception = new Exception("Test exception");

            mockProductEventLogsService.Setup(x => x.GetProductEventStoreLogsAsync(
                    tenantId,
                    productType,
                    where,
                    cancellationToken))
                .ThrowsAsync(exception);

            // Act (When)
            var result = await sut.ProductEventLogs(
                tenantId,
                productType,
                where,
                cancellationToken);

            // Assert (Then)
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("Unable to get the product nodes event store logs");

        }

        [Theory, CustomAutoData]
        public async Task GIVEN_ProductId_WHEN_Query_ProductEvent_THEN_Events_are_Returned_Successfully(
            [Frozen] Mock<ProductService> mockProductService
        )
        {
            // Arrange (Given)
            Fixture fixture = new Fixture();

            Mock<ProductEventService> mockProductEventService = new Mock<ProductEventService>(
                new Mock<IProductEventStore>().Object
                );

            Mock<ProductExchangeService> mockProductExchangeService = new Mock<ProductExchangeService>(
            mockProductService.Object,
            new Mock<IProductL10NAdapter>().Object,
            new Mock<IAuthAdapter>().Object,
            new Mock<IProductBuilderAdapter>().Object,
            new Mock<IProductExchangeAdapter>().Object,
            new Mock<IExternalTablesAdapter>().Object,
            new Mock<IProductImportHistoryService>().Object,
            NullLogger<ProductExchangeService>.Instance
                );


            Mock<ProductEventLogsService> mockProductEventLogsService = new Mock<ProductEventLogsService>(
               new Mock<IProductEventStore>().Object,
               new Mock<IProductBuilderEventStore>().Object,
               mockProductService.Object,
               new Mock<IAuthService>().Object,
               new Mock<IUsersClient>().Object,
               NullLogger<ProductEventLogsService>.Instance
           );

            ProductExchangeController sut = new(
                mockProductService.Object,
                mockProductExchangeService.Object,
                mockProductEventLogsService.Object,
                NullLogger<ProductExchangeController>.Instance
                );

            var tenantId = "tenant-1";
            var productType = "type-a";
            var query = new ProductEventQuery
            {
                Where = new ProductEventWhere(),
                OrderBy = new DomainUtils.OrderBy() { FieldName = "TimeStamp", Type = DomainUtils.OrderByType.DSC},
                Skip = 0,
                Limit = 10
            };

            var expectedEvents = new List<ProductEvent>
            {
                new ProductEvent { Id = "event-1", Type = ProductEventType.creation },
                new ProductEvent { Id = "event-2", Type = ProductEventType.update }
            };

            mockProductEventLogsService.Setup(x => x.GetProductEventsAsync(
                tenantId,
                productType,
                query.Where,
                query.OrderBy,
                query.Skip,
                query.Limit,
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedEvents);

            // Act (When)
            var result = await sut.ProductEvents(
                tenantId,
                productType,
                query,
                CancellationToken.None);

            // Assert (Then)
            result.IsSuccess.Should().BeTrue();
            result.Value.Should().BeEquivalentTo(expectedEvents);
            result.Value.Count.Should().Be(expectedEvents.Count);

        }

        [Theory, CustomAutoData]
        public async Task GIVEN_ProductId_WHEN_Queries_ProductEvents_Failure_THEN_Logs_Return_Failure(
            [Frozen] Mock<ProductService> mockProductService
        )
        {
            // Arrange (Given)
            Fixture fixture = new Fixture();

            Mock<ProductEventService> mockProductEventService = new Mock<ProductEventService>(
                new Mock<IProductEventStore>().Object
                );

            Mock<ProductExchangeService> mockProductExchangeService = new Mock<ProductExchangeService>(
            mockProductService.Object,
            new Mock<IProductL10NAdapter>().Object,
            new Mock<IAuthAdapter>().Object,
            new Mock<IProductBuilderAdapter>().Object,
            new Mock<IProductExchangeAdapter>().Object,
            new Mock<IExternalTablesAdapter>().Object,
            new Mock<IProductImportHistoryService>().Object,
            NullLogger<ProductExchangeService>.Instance
                );

            Mock<ProductEventLogsService> mockProductEventLogsService = new Mock<ProductEventLogsService>(
               new Mock<IProductEventStore>().Object,
               new Mock<IProductBuilderEventStore>().Object,
               mockProductService.Object,
               new Mock<IAuthService>().Object,
               new Mock<IUsersClient>().Object,
               NullLogger<ProductEventLogsService>.Instance
           );

            ProductExchangeController sut = new(
                mockProductService.Object,
                mockProductExchangeService.Object,
                mockProductEventLogsService.Object,
                NullLogger<ProductExchangeController>.Instance
                );

            var tenantId = "tenant-1";
            var productType = "type-a";
            var query = new ProductEventQuery
            {
                Where = new ProductEventWhere(),
                OrderBy = new DomainUtils.OrderBy() { FieldName = "TimeStamp", Type = DomainUtils.OrderByType.DSC },
                Skip = 0,
                Limit = 10
            };

            var exception = new Exception("Test exception");

            mockProductEventLogsService.Setup(x => x.GetProductEventsAsync(
                 tenantId,
                 productType,
                 query.Where,
                 query.OrderBy,
                 query.Skip,
                 query.Limit,
                 It.IsAny<CancellationToken>()))
                 .ThrowsAsync(exception);

            // Act (When)
            var result = await sut.ProductEvents(
                tenantId,
                productType,
                query,
                CancellationToken.None);

            // Assert (Then)
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("Unable to get the product event logs");

        }

        [Theory, CustomAutoData]
        public async Task GIVEN_Empty_Event_WHEN_AddProductEvents_THEN_Returns_Failure(
            [Frozen] Mock<ProductService> mockProductService
        )
        {
            // Arrange (Given)
            var tenantId = "tenant-1";
            var emptyEvents = new List<ProductEvent>();

            Mock<ProductEventService> mockProductEventService = new Mock<ProductEventService>(
                new Mock<IProductEventStore>().Object
                );

            Mock<ProductExchangeService> mockProductExchangeService = new Mock<ProductExchangeService>(
            mockProductService.Object,
            new Mock<IProductL10NAdapter>().Object,
            new Mock<IAuthAdapter>().Object,
            new Mock<IProductBuilderAdapter>().Object,
            new Mock<IProductExchangeAdapter>().Object,
            new Mock<IExternalTablesAdapter>().Object,
            new Mock<IProductImportHistoryService>().Object,
            NullLogger<ProductExchangeService>.Instance
                );

            Mock<ProductEventLogsService> mockProductEventLogsService = new Mock<ProductEventLogsService>(
               new Mock<IProductEventStore>().Object,
               new Mock<IProductBuilderEventStore>().Object,
               mockProductService.Object,
               new Mock<IAuthService>().Object,
               new Mock<IUsersClient>().Object,
               NullLogger<ProductEventLogsService>.Instance
           );

            ProductExchangeController sut = new(
                mockProductService.Object,
                mockProductExchangeService.Object,
                mockProductEventLogsService.Object,
                NullLogger<ProductExchangeController>.Instance
                );

            // Act (When)
            var result = await sut.AddProductEvents(tenantId, emptyEvents, CancellationToken.None);

            // Assert (Then)
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("Event is null");
        }

        [Theory, CustomAutoData]
        public async Task GIVEN_Null_WHEN_AddProductEvents_THEN_Returns_Failure(
            [Frozen] Mock<ProductService> mockProductService
        )
        {
            // Arrange (Given)
            var tenantId = "tenant-1";
            var emptyEvents = new List<ProductEvent>();

            Mock<ProductEventService> mockProductEventService = new Mock<ProductEventService>(
                new Mock<IProductEventStore>().Object
                );

            Mock<ProductExchangeService> mockProductExchangeService = new Mock<ProductExchangeService>(
            mockProductService.Object,
            new Mock<IProductL10NAdapter>().Object,
            new Mock<IAuthAdapter>().Object,
            new Mock<IProductBuilderAdapter>().Object,
            new Mock<IProductExchangeAdapter>().Object,
            new Mock<IExternalTablesAdapter>().Object,
            new Mock<IProductImportHistoryService>().Object,
            NullLogger<ProductExchangeService>.Instance
                );

            Mock<ProductEventLogsService> mockProductEventLogsService = new Mock<ProductEventLogsService>(
               new Mock<IProductEventStore>().Object,
               new Mock<IProductBuilderEventStore>().Object,
               mockProductService.Object,
               new Mock<IAuthService>().Object,
               new Mock<IUsersClient>().Object,
               NullLogger<ProductEventLogsService>.Instance
           );

            ProductExchangeController sut = new(
                mockProductService.Object,
                mockProductExchangeService.Object,
                mockProductEventLogsService.Object,
                NullLogger<ProductExchangeController>.Instance
                );

            // Act (When)
            var result = await sut.AddProductEvents(tenantId, null, CancellationToken.None);

            // Assert (Then)
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("Event is null");
        }
        private List<ProductEvent> CreateValidEvents(int count = 1)
        {
            return Enumerable.Range(1, count)
                .Select(i => _fixture.Create<ProductEvent>())
                .ToList();
        }
    }
}