using System;
using System.Linq;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Infrastructure.Products.Adapters.Mongo;
using CoverGo.Products.Infrastructure.Utils.Adapters.Mongo;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Conventions;
using MongoDB.Driver;

namespace CoverGo.Products.Tests.Infrastructure.Utils.Adapters.Mongo;

[Trait("Ticket", "CH-24253")]
public class MongoSearchExtensionsTests
{
    private static readonly IBsonSerializer<MongoProductDao2> _serializer;
    private static readonly IBsonSerializerRegistry _serializerRegistry;

    static MongoSearchExtensionsTests()
    {
        var conventionPack = new ConventionPack
        {
            new CamelCaseElementNameConvention()
        };
        ConventionRegistry.Register("CamelCase", conventionPack, t => true);

        _serializer = BsonSerializer.SerializerRegistry.GetSerializer<MongoProductDao2>();
        _serializerRegistry = BsonSerializer.SerializerRegistry;
    }

    [Fact]
    public void AddProductSearchFilters2_WithProductId_ReturnsCorrectFilter()
    {
        // Arrange (Given)
        var filter = new ProductWhere
        {
            ProductId = new ProductIdWhere
            {
                Plan = "test-plan",
                Type = "test-type",
                Version = "1.0"
            }
        };

        // Act (When)
        var result = MongoSearchExtensions.AddProductSearchFilters2(filter);

        // Assert (Then)
        var expectedFilter = Builders<MongoProductDao2>.Filter.In(
            c => c.ProductId,
            new[] { new ProductId { Plan = "test-plan", Type = "test-type", Version = "1.0" } }
        );
        Assert.Equal(expectedFilter.Render(_serializer, _serializerRegistry).ToString(),
                   result.Render(_serializer, _serializerRegistry).ToString());
    }

    [Fact]
    public void AddProductSearchFilters2_WithProductIdWithoutVersion_ReturnsCorrectFilter()
    {
        // Arrange (Given)
        var filter = new ProductWhere
        {
            ProductId = new ProductIdWhere
            {
                Plan = "test-plan",
                Type = "test-type"
            }
        };

        // Act (When)
        var result = MongoSearchExtensions.AddProductSearchFilters2(filter);

        // Assert (Then)
        var expectedFilter = Builders<MongoProductDao2>.Filter.Where(c =>
            c.ProductId.Plan == "test-plan" && c.ProductId.Type == "test-type");
        Assert.Equal(expectedFilter.Render(_serializer, _serializerRegistry).ToString(),
                   result.Render(_serializer, _serializerRegistry).ToString());
    }

    [Fact]
    public void AddProductSearchFilters2_WithInsurer_ReturnsCorrectFilter()
    {
        // Arrange (Given)
        var filter = new ProductWhere
        {
            Insurer = new InsurerWhere
            {
                Id = "insurer-1"
            }
        };

        // Act (When)
        var result = MongoSearchExtensions.AddProductSearchFilters2(filter);

        // Assert (Then)
        var expectedFilter = Builders<MongoProductDao2>.Filter.Eq(c => c.InsurerId, "insurer-1");
        Assert.Equal(expectedFilter.Render(_serializer, _serializerRegistry).ToString(),
                   result.Render(_serializer, _serializerRegistry).ToString());
    }

    [Fact]
    public void AddProductSearchFilters2_WithStatus_ReturnsCorrectFilter()
    {
        // Arrange (Given)
        var filter = new ProductWhere
        {
            Status = "Active"
        };

        // Act (When)
        var result = MongoSearchExtensions.AddProductSearchFilters2(filter);

        // Assert (Then)
        var expectedFilter = Builders<MongoProductDao2>.Filter.Eq(c => c.Status, "Active");
        Assert.Equal(expectedFilter.Render(_serializer, _serializerRegistry).ToString(),
                   result.Render(_serializer, _serializerRegistry).ToString());
    }

    [Fact]
    public void AddProductSearchFilters2_WithDateRange_ReturnsCorrectFilter()
    {
        // Arrange (Given)
        var startDate = DateTime.UtcNow.AddDays(-1);
        var endDate = DateTime.UtcNow;
        var filter = new ProductWhere
        {
            CreatedAt_gt = startDate,
            CreatedAt_lt = endDate
        };

        // Act (When)
        var result = MongoSearchExtensions.AddProductSearchFilters2(filter);

        // Assert (Then)
        var expectedFilter = Builders<MongoProductDao2>.Filter.Gt(c => c.CreatedAt, startDate);
        Assert.Equal(expectedFilter.Render(_serializer, _serializerRegistry).ToString(),
                   result.Render(_serializer, _serializerRegistry).ToString());
    }

    [Fact]
    public void AddProductSearchFilters2_WithOrCondition_ReturnsCorrectFilter()
    {
        // Arrange (Given)
        var filter = new ProductWhere
        {
            Or = new[]
            {
                new ProductWhere { Status = "Active" },
                new ProductWhere { Status = "Draft" }
            }
        };

        // Act (When)
        var result = MongoSearchExtensions.AddProductSearchFilters2(filter);

        // Assert (Then)
        var expectedFilter = Builders<MongoProductDao2>.Filter.Eq(c => c.Status, "Active") |
                           Builders<MongoProductDao2>.Filter.Eq(c => c.Status, "Draft");
        Assert.Equal(expectedFilter.Render(_serializer, _serializerRegistry).ToString(),
                   result.Render(_serializer, _serializerRegistry).ToString());
    }

    [Fact]
    public void AddProductSearchFilters2_WithAndCondition_ReturnsCorrectFilter()
    {
        // Arrange (Given)
        var filter = new ProductWhere
        {
            And = new[]
            {
                new ProductWhere { Status = "Active" },
                new ProductWhere { Insurer = new InsurerWhere { Id = "insurer-1" } }
            }
        };

        // Act (When)
        var result = MongoSearchExtensions.AddProductSearchFilters2(filter);

        // Assert (Then)
        var expectedFilter = Builders<MongoProductDao2>.Filter.Eq(c => c.Status, "Active") &
                           Builders<MongoProductDao2>.Filter.Eq(c => c.InsurerId, "insurer-1");
        Assert.Equal(expectedFilter.Render(_serializer, _serializerRegistry).ToString(),
                   result.Render(_serializer, _serializerRegistry).ToString());
    }

    [Fact]
    public void AddProductSearchFilters2_WithTags_ReturnsCorrectFilter()
    {
        // Arrange (Given)
        var filter = new ProductWhere
        {
            Tags_some = new TagWhere
            {
                Id = "tag-1",
                Type = "test-type"
            }
        };

        // Act (When)
        var result = MongoSearchExtensions.AddProductSearchFilters2(filter);

        // Assert (Then)
        var expectedFilter = Builders<MongoProductDao2>.Filter.Where(c =>
            c.Tags.Any(a => a.Id == "tag-1"));
        Assert.Equal(expectedFilter.Render(_serializer, _serializerRegistry).ToString(),
                   result.Render(_serializer, _serializerRegistry).ToString());
    }

    [Fact]
    public void AddProductSearchFilters2_WithBenefits_ReturnsCorrectFilter()
    {
        // Arrange (Given)
        var filter = new ProductWhere
        {
            Benefits_some = new BenefitWhere
            {
                TypeId = "benefit-1"
            }
        };

        // Act (When)
        var result = MongoSearchExtensions.AddProductSearchFilters2(filter);

        // Assert (Then)
        var expectedFilter = Builders<MongoProductDao2>.Filter.Where(c =>
            c.Benefits.Any(a => a.TypeId == "benefit-1"));
        Assert.Equal(expectedFilter.Render(_serializer, _serializerRegistry).ToString(),
                   result.Render(_serializer, _serializerRegistry).ToString());
    }

    [Fact]
    public void AddProductSearchFilters2_WithEmptyFilter_ReturnsEmptyFilter()
    {
        // Arrange (Given)
        var filter = new ProductWhere();

        // Act (When)
        var result = MongoSearchExtensions.AddProductSearchFilters2(filter);

        // Assert (Then)
        Assert.Equal(FilterDefinition<MongoProductDao2>.Empty.Render(_serializer, _serializerRegistry).ToString(),
                   result.Render(_serializer, _serializerRegistry).ToString());
    }

    [Fact]
    public void AddProductSearchFilters2_WithNullFilter_ReturnsEmptyFilter()
    {
        // Act (When)
        var result = MongoSearchExtensions.AddProductSearchFilters2(null);

        // Assert (Then)
        Assert.Equal(FilterDefinition<MongoProductDao2>.Empty.Render(_serializer, _serializerRegistry).ToString(),
                   result.Render(_serializer, _serializerRegistry).ToString());
    }
}