using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using AutoFixture.Xunit2;
using CoverGo.DomainUtils;
using CoverGo.FileSystem.Client;
using CoverGo.Multitenancy;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.Events;
using CoverGo.Products.Infrastructure.Products;
using Moq;

namespace CoverGo.Products.Tests.Infrastructure.Products;

[Trait("Ticket", "CH-22514")]
public class CloneTobDocumentOnProductClonedHandlerTests
{
    [Theory]
    [CustomAutoData]
    public async Task GIVEN_ValidNotificationWithSourceFiles_WHEN_HandleIsCalled_THEN_ShouldCloneFiles(
        [Frozen] Mock<IFileSystemClient> mockFileSystemClient,
        [Frozen] TenantId tenantId,
        CloneTobDocumentOnProductClonedHandler handler,
        ProductCloned notification)
    {
        // Arrange
        var sourceFiles = new List<ObjectSummary>
        {
            new() { Key = "products/plan1/type1/1.0/tob/document1.pdf" },
            new() { Key = "products/plan1/type1/1.0/tob/document2.pdf" }
        };

        var listFilesResult = new ResultOfObjectListing
        {
            IsSuccess = true,
            Value = new ObjectListing
            {
                ObjectSummaries = sourceFiles
            }
        };

        mockFileSystemClient
            .Setup(x => x.FileSystem_ListAsync(
                tenantId.Value,
                null,
                It.IsAny<ListFilesCommand>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(listFilesResult);

        Result copyFileResult = new Result();
        mockFileSystemClient
            .Setup(x => x.FileSystem_CopyAsync(
                tenantId.Value,
                null,
                It.IsAny<CopyFileCommand>(),
                It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult<Result?>(copyFileResult));

        // Act
        await handler.Handle(notification, CancellationToken.None);

        // Assert
        mockFileSystemClient.Verify(
            x => x.FileSystem_ListAsync(
                tenantId.Value,
                null,
                It.Is<ListFilesCommand>(cmd =>
                    cmd.Path == $"products/{notification.OriginalProduct.Id.Plan}/{notification.OriginalProduct.Id.Type}/{notification.OriginalProduct.Id.Version}/tob/" &&
                    cmd.AllowedPrefixes == null),
                It.IsAny<CancellationToken>()),
            Times.Once);

        mockFileSystemClient.Verify(
            x => x.FileSystem_CopyAsync(
                tenantId.Value,
                null,
                It.Is<CopyFileCommand>(cmd =>
                    cmd.Key == "products/plan1/type1/1.0/tob/document1.pdf" &&
                    cmd.NewKey == $"products/{notification.ClonedProduct.Id.Plan}/{notification.ClonedProduct.Id.Type}/{notification.ClonedProduct.Id.Version}/tob/document1.pdf"),
                It.IsAny<CancellationToken>()),
            Times.Once);

        mockFileSystemClient.Verify(
            x => x.FileSystem_CopyAsync(
                tenantId.Value,
                null,
                It.Is<CopyFileCommand>(cmd =>
                    cmd.Key == "products/plan1/type1/1.0/tob/document2.pdf" &&
                    cmd.NewKey == $"products/{notification.ClonedProduct.Id.Plan}/{notification.ClonedProduct.Id.Type}/{notification.ClonedProduct.Id.Version}/tob/document2.pdf"),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Theory]
    [CustomAutoData]
    public async Task GIVEN_ListFilesFailure_WHEN_HandleIsCalled_THEN_ShouldNotCopyFiles(
        [Frozen] Mock<IFileSystemClient> mockFileSystemClient,
        [Frozen] TenantId tenantId,
        CloneTobDocumentOnProductClonedHandler handler,
        ProductCloned notification)
    {
        // Arrange
        var listFilesResult = new ResultOfObjectListing
        {
            IsSuccess = false,
            Value = null
        };

        mockFileSystemClient
            .Setup(x => x.FileSystem_ListAsync(
                tenantId.Value,
                null,
                It.IsAny<ListFilesCommand>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(listFilesResult);

        // Act
        await handler.Handle(notification, CancellationToken.None);

        // Assert
        mockFileSystemClient.Verify(
            x => x.FileSystem_ListAsync(
                tenantId.Value,
                null,
                It.IsAny<ListFilesCommand>(),
                It.IsAny<CancellationToken>()),
            Times.Once);

        mockFileSystemClient.Verify(
            x => x.FileSystem_CopyAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<CopyFileCommand>(),
                It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Theory]
    [CustomAutoData]
    public async Task GIVEN_NullObjectListingResult_WHEN_HandleIsCalled_THEN_ShouldNotCopyFiles(
        [Frozen] Mock<IFileSystemClient> mockFileSystemClient,
        [Frozen] TenantId tenantId,
        CloneTobDocumentOnProductClonedHandler handler,
        ProductCloned notification)
    {
        // Arrange
        mockFileSystemClient
            .Setup(x => x.FileSystem_ListAsync(
                tenantId.Value,
                null,
                It.IsAny<ListFilesCommand>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync((ResultOfObjectListing?)null);

        // Act
        await handler.Handle(notification, CancellationToken.None);

        // Assert
        mockFileSystemClient.Verify(
            x => x.FileSystem_CopyAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<CopyFileCommand>(),
                It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Theory]
    [CustomAutoData]
    public async Task GIVEN_EmptySourceFilesList_WHEN_HandleIsCalled_THEN_ShouldNotCopyFiles(
        [Frozen] Mock<IFileSystemClient> mockFileSystemClient,
        [Frozen] TenantId tenantId,
        CloneTobDocumentOnProductClonedHandler handler,
        ProductCloned notification)
    {
        // Arrange
        var listFilesResult = new ResultOfObjectListing
        {
            IsSuccess = true,
            Value = new ObjectListing
            {
                ObjectSummaries = new List<ObjectSummary>()
            }
        };

        mockFileSystemClient
            .Setup(x => x.FileSystem_ListAsync(
                tenantId.Value,
                null,
                It.IsAny<ListFilesCommand>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(listFilesResult);

        // Act
        await handler.Handle(notification, CancellationToken.None);

        // Assert
        mockFileSystemClient.Verify(
            x => x.FileSystem_CopyAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<CopyFileCommand>(),
                It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Theory]
    [CustomAutoData]
    public async Task GIVEN_NullOrEmptyFileKeys_WHEN_HandleIsCalled_THEN_ShouldSkipThoseFiles(
        [Frozen] Mock<IFileSystemClient> mockFileSystemClient,
        [Frozen] TenantId tenantId,
        CloneTobDocumentOnProductClonedHandler handler,
        ProductCloned notification)
    {
        // Arrange
        var sourceFiles = new List<ObjectSummary>
        {
            new() { Key = "products/plan1/type1/1.0/tob/document1.pdf" },
            new() { Key = null }, // Null key should be skipped
            new() { Key = "products/plan1/type1/1.0/tob/document2.pdf" },
            null! // Null object should be skipped
        };

        var listFilesResult = new ResultOfObjectListing
        {
            IsSuccess = true,
            Value = new ObjectListing
            {
                ObjectSummaries = sourceFiles
            }
        };

        mockFileSystemClient
            .Setup(x => x.FileSystem_ListAsync(
                tenantId.Value,
                null,
                It.IsAny<ListFilesCommand>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(listFilesResult);

        Result copyFileResult = new Result();
        mockFileSystemClient
            .Setup(x => x.FileSystem_CopyAsync(
                tenantId.Value,
                null,
                It.IsAny<CopyFileCommand>(),
                It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult<Result?>(copyFileResult));

        // Act
        await handler.Handle(notification, CancellationToken.None);

        // Assert
        // Should only copy 2 valid files, not the null ones
        mockFileSystemClient.Verify(
            x => x.FileSystem_CopyAsync(
                tenantId.Value,
                null,
                It.IsAny<CopyFileCommand>(),
                It.IsAny<CancellationToken>()),
            Times.Exactly(2));
    }

    [Theory]
    [CustomAutoData]
    public async Task GIVEN_FileNamesFromDifferentPaths_WHEN_HandleIsCalled_THEN_ShouldExtractCorrectFileNames(
        [Frozen] Mock<IFileSystemClient> mockFileSystemClient,
        [Frozen] TenantId tenantId,
        CloneTobDocumentOnProductClonedHandler handler,
        ProductCloned notification)
    {
        // Arrange
        var sourceFiles = new List<ObjectSummary>
        {
            new() { Key = "products/plan1/type1/1.0/tob/subfolder/nested/document.pdf" },
            new() { Key = "products/plan1/type1/1.0/tob/simple.txt" }
        };

        var listFilesResult = new ResultOfObjectListing
        {
            IsSuccess = true,
            Value = new ObjectListing
            {
                ObjectSummaries = sourceFiles
            }
        };

        mockFileSystemClient
            .Setup(x => x.FileSystem_ListAsync(
                tenantId.Value,
                null,
                It.IsAny<ListFilesCommand>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(listFilesResult);

        Result copyFileResult = new Result();
        mockFileSystemClient
            .Setup(x => x.FileSystem_CopyAsync(
                tenantId.Value,
                null,
                It.IsAny<CopyFileCommand>(),
                It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult<Result?>(copyFileResult));

        // Act
        await handler.Handle(notification, CancellationToken.None);

        // Assert
        mockFileSystemClient.Verify(
            x => x.FileSystem_CopyAsync(
                tenantId.Value,
                null,
                It.Is<CopyFileCommand>(cmd =>
                    cmd.Key == "products/plan1/type1/1.0/tob/subfolder/nested/document.pdf" &&
                    cmd.NewKey == $"products/{notification.ClonedProduct.Id.Plan}/{notification.ClonedProduct.Id.Type}/{notification.ClonedProduct.Id.Version}/tob/document.pdf"),
                It.IsAny<CancellationToken>()),
            Times.Once);

        mockFileSystemClient.Verify(
            x => x.FileSystem_CopyAsync(
                tenantId.Value,
                null,
                It.Is<CopyFileCommand>(cmd =>
                    cmd.Key == "products/plan1/type1/1.0/tob/simple.txt" &&
                    cmd.NewKey == $"products/{notification.ClonedProduct.Id.Plan}/{notification.ClonedProduct.Id.Type}/{notification.ClonedProduct.Id.Version}/tob/simple.txt"),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Theory]
    [CustomAutoData]
    public async Task GIVEN_OriginalAndClonedProducts_WHEN_HandleIsCalled_THEN_ShouldConstructCorrectSourceAndTargetPaths(
        [Frozen] Mock<IFileSystemClient> mockFileSystemClient,
        [Frozen] TenantId tenantId,
        CloneTobDocumentOnProductClonedHandler handler)
    {
        // Arrange
        var originalProduct = new Product
        {
            Id = new ProductId
            {
                Plan = "original-plan",
                Type = "original-type",
                Version = "1.0"
            }
        };

        var clonedProduct = new Product
        {
            Id = new ProductId
            {
                Plan = "cloned-plan",
                Type = "cloned-type",
                Version = "2.0"
            }
        };

        var notification = new ProductCloned
        {
            OriginalProduct = originalProduct,
            ClonedProduct = clonedProduct,
            ClonedProductName = "Cloned Product"
        };

        var sourceFiles = new List<ObjectSummary>
        {
            new() { Key = "products/original-plan/original-type/1.0/tob/test.pdf" }
        };

        var listFilesResult = new ResultOfObjectListing
        {
            IsSuccess = true,
            Value = new ObjectListing
            {
                ObjectSummaries = sourceFiles
            }
        };

        mockFileSystemClient
            .Setup(x => x.FileSystem_ListAsync(
                tenantId.Value,
                null,
                It.IsAny<ListFilesCommand>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(listFilesResult);

        Result copyFileResult = new Result();
        mockFileSystemClient
            .Setup(x => x.FileSystem_CopyAsync(
                tenantId.Value,
                null,
                It.IsAny<CopyFileCommand>(),
                It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult<Result?>(copyFileResult));

        // Act
        await handler.Handle(notification, CancellationToken.None);

        // Assert
        mockFileSystemClient.Verify(
            x => x.FileSystem_ListAsync(
                tenantId.Value,
                null,
                It.Is<ListFilesCommand>(cmd =>
                    cmd.Path == "products/original-plan/original-type/1.0/tob/"),
                It.IsAny<CancellationToken>()),
            Times.Once);

        mockFileSystemClient.Verify(
            x => x.FileSystem_CopyAsync(
                tenantId.Value,
                null,
                It.Is<CopyFileCommand>(cmd =>
                    cmd.Key == "products/original-plan/original-type/1.0/tob/test.pdf" &&
                    cmd.NewKey == "products/cloned-plan/cloned-type/2.0/tob/test.pdf"),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }
}