using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoFixture;
using AutoFixture.Xunit2;
using CoverGo.DomainUtils;
using CoverGo.FeatureManagement;
using CoverGo.Products.Domain.Facts;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.Commands;
using CoverGo.Products.Infrastructure.Products.Adapters.Mongo;
using MongoDB.Driver;
using Moq;

namespace CoverGo.Products.Tests.Infrastructure.Products.Adapters.Mongo;

[Trait("Ticket", "CH-23867")]
public class MongoProductRepositoryTests
{
    [Theory, CustomAutoData]
    public async Task UpdateAsyncTest(
        IFixture fixture,
        [Frozen] Mock<IMongoDbContext> mockContext,
        MongoProductRepository sut,
        string tenantId,
        MongoProductDao2 product,
        CancellationToken cancellationToken
    )
    {
        // Arrange (Given)
        var command = fixture
            .Build<UpdateProductCommand>()
            .With(x => x.IsRepresentationChanged, false)
            .With(x => x.IsFieldsChanged, false)
            .With(x => x.RepresentationPatch, string.Empty)
            .With(x => x.FieldsPatch, string.Empty)
            .With(x => x.IsUpdateTypesChanged, true)
            .Create();
        mockContext
            .Setup(x => x.FindAsync(It.IsAny<IMongoCollection<MongoProductDao2>>(), It.IsAny<FilterDefinition<MongoProductDao2>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(product);

        // Act (When)
        var result = await sut.UpdateAsync(tenantId, command, cancellationToken);

        // Assert (Then)
        result.Should().NotBeNull();
    }

    [Theory]
    [CustomInlineAutoData(true)]
    public async Task GetAsyncTest(
        bool optimizedProductsQueryEnabled,
        [Frozen] Mock<IMultiTenantFeatureManager> mockFeatureManager,
        [Frozen] Mock<IAggregateFluent<MongoProductDao2>> mockCollectionAggregation,
        [Frozen] Mock<IMongoDatabase> mockDatabase,
        [Frozen] Mock<IMongoDbContext> mockContext,
        MongoProductRepository sut,
        string tenantId,
        ProductId id,
        ProductConfig config,
        MongoProductDao2 product,
        string collectionName)
    {
        // Arrange (Given)
        ProductWhere where = new(){ Id_in = [id] };
        mockFeatureManager.Setup(x => x.IsEnabled("OptimizedProductsQueryEnabled")).ReturnsAsync(optimizedProductsQueryEnabled);
        CancellationToken cancellationToken = new();
        mockCollectionAggregation
            .Setup(x => x.ToCursorAsync(cancellationToken))
            .ReturnsAsync(new DummyAsyncCursor<MongoProductDao2>(product));
        mockDatabase
            .Setup(x => x.ListCollectionNamesAsync(It.IsAny<ListCollectionNamesOptions>(), default))
            .ReturnsAsync(new DummyAsyncCursor<string>(collectionName));
        mockContext
            .Setup(x => x.FindListAsync(
                It.IsAny<IMongoDatabase>(),
                It.IsAny<string>(),
                It.IsAny<FilterDefinition<MongoProductDao2>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([product]);

        // Act (When)
        var products = await sut.GetAsync(tenantId, where, config, null, true, cancellationToken);

        // Assert (Then)
        products.Should().HaveCount(1);
    }

    [Theory(Skip = "future use"), CustomAutoData]
    public async Task GetTotalCountAsync_ShouldReturnCorrectCount(
        IFixture fixture,
        [Frozen] Mock<IMultiTenantFeatureManager> mockFeatureManager,
        [Frozen] Mock<IAggregateFluent<AggregateCountResult>> mockAggregateCount,
        MongoProductRepository sut,
        string tenantId,
        ProductConfig config,
        long expectedCount,
        CancellationToken cancellationToken)
    {
        // Arrange (Given)
        fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
            .ForEach(b => fixture.Behaviors.Remove(b));
        fixture.Behaviors.Add(new OmitOnRecursionBehavior());
        var where = fixture.Create<ProductWhere>();
        mockFeatureManager.Setup(x => x.IsEnabled("OptimizedProductsQueryEnabled")).ReturnsAsync(true);
        var countResult = new AggregateCountResult(expectedCount);
        mockAggregateCount
            .Setup(x => x.ToCursorAsync(cancellationToken))
            .ReturnsAsync(new DummyAsyncCursor<AggregateCountResult>(new AggregateCountResult(expectedCount)));

        // Act (When)
        var result = await sut.GetTotalCountAsync(tenantId, where, config, cancellationToken);

        // Assert (Then)
        result.Should().Be(expectedCount);
    }

    [Theory(Skip = "future use"), CustomAutoData]
    public async Task AddBenefitAsync_ShouldAddBenefitSuccessfully(
        [Frozen] Mock<IMongoCollection<MongoProductDao2>> mockCollection,
        [Frozen] Mock<IMongoDbContext> mockContext,
        MongoProductRepository sut,
        string tenantId,
        MongoProductDao2 product,
        ProductId productId,
        AddBenefitCommand command
    )
    {
        // Arrange (Given)
        SetupMocks(mockCollection, mockContext, tenantId, productId, () => product);

        // Act (When)
        var result = await sut.AddBenefitAsync(tenantId, productId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
    }

    [Theory(Skip = "future use"), CustomAutoData]
    public async Task UpdateBenefitAsync_ShouldUpdateBenefitSuccessfully(
        IFixture fixture,
        [Frozen] Mock<IMongoCollection<MongoProductDao2>> mockCollection,
        [Frozen] Mock<IMongoDbContext> mockContext,
        MongoProductRepository sut,
        string tenantId,
        ProductId productId,
        UpdateBenefitCommand command
    )
    {
        // Arrange (Given)
        Func<MongoProductDao2> buildProduct = () => fixture
            .Build<MongoProductDao2>()
            .With(x => x.Benefits, [fixture
                .Build<Benefit>()
                .With(x => x.TypeId, command.TypeId)
                .With(x => x.OptionKey, command.OptionKey)
                .Create()])
            .Create();
        SetupMocks(mockCollection, mockContext, tenantId, productId, buildProduct);

        // Act (When)
        var result = await sut.UpdateBenefitAsync(tenantId, productId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
    }

    [Theory(Skip = "future use"), CustomAutoData]
    public async Task RemoveBenefitAsync_ShouldRemoveBenefitSuccessfully(
        IFixture fixture,
        [Frozen] Mock<IMongoCollection<MongoProductDao2>> mockCollection,
        [Frozen] Mock<IMongoDbContext> mockContext,
        MongoProductRepository sut,
        string tenantId,
        ProductId productId,
        RemoveBenefitCommand command
    )
    {
        // Arrange (Given)
        Func<MongoProductDao2> buildProduct = () => fixture
            .Build<MongoProductDao2>()
            .With(x => x.Benefits, [fixture
                .Build<Benefit>()
                .With(x => x.TypeId, command.TypeId)
                .With(x => x.OptionKey, command.OptionKey)
                .Create()])
            .Create();
        SetupMocks(mockCollection, mockContext, tenantId, productId, buildProduct);

        // Act (When)
        var result = await sut.RemoveBenefitAsync(tenantId, productId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
    }

    [Theory(Skip = "future use"), CustomAutoData]
    public async Task AddFactAsync_ShouldAddFactSuccessfully(
        [Frozen] Mock<IMongoCollection<MongoProductDao2>> mockCollection,
        [Frozen] Mock<IMongoDbContext> mockContext,
        MongoProductRepository sut,
        string tenantId,
        MongoProductDao2 product,
        ProductId productId,
        AddFactCommand command
    )
    {
        // Arrange (Given)
        var updateResult = new UpdateResult.Acknowledged(1, 1, null);
        mockCollection
            .Setup(x => x.UpdateOneAsync(
                It.IsAny<FilterDefinition<MongoProductDao2>>(),
                It.IsAny<UpdateDefinition<MongoProductDao2>>(),
                null,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(updateResult);
        mockContext
            .Setup(x => x.FindAsync(It.IsAny<IMongoCollection<MongoProductDao2>>(), It.IsAny<FilterDefinition<MongoProductDao2>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(product);
        mockContext
            .Setup(x => x.GetCollection<MongoProductDao2>(tenantId, It.IsAny<string>(), productId.Type))
            .Returns(mockCollection.Object);

        // Act (When)
        var result = await sut.AddFactAsync(tenantId, productId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
    }

    [Theory(Skip = "future use"), CustomAutoData]
    public async Task UpdateFactAsync_ShouldUpdateFactSuccessfully(
        IFixture fixture,
        [Frozen] Mock<IMongoCollection<MongoProductDao2>> mockCollection,
        [Frozen] Mock<IMongoDbContext> mockContext,
        MongoProductRepository sut,
        string tenantId,
        ProductId productId,
        UpdateFactCommand command
    )
    {
        // Arrange (Given)
        var updateResult = new UpdateResult.Acknowledged(1, 1, null);
        mockCollection
            .Setup(x => x.UpdateOneAsync(
                It.IsAny<FilterDefinition<MongoProductDao2>>(),
                It.IsAny<UpdateDefinition<MongoProductDao2>>(),
                null,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(updateResult);
        MongoProductDao2 product = fixture
            .Build<MongoProductDao2>()
            .With(x => x.Facts, [fixture
                .Build<CoverGo.Products.Domain.Facts.Fact>()
                .With(x => x.Id, command.Id)
                .Create()])
            .Create();
        mockContext
            .Setup(x => x.FindAsync(It.IsAny<IMongoCollection<MongoProductDao2>>(), It.IsAny<FilterDefinition<MongoProductDao2>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(product);
        mockContext
            .Setup(x => x.GetCollection<MongoProductDao2>(tenantId, It.IsAny<string>(), productId.Type))
            .Returns(mockCollection.Object);

        // Act (When)
        var result = await sut.UpdateFactAsync(tenantId, productId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
    }

    [Theory(Skip = "future use"), CustomAutoData]
    public async Task RemoveFactAsync_ShouldRemoveFactSuccessfully(
        [Frozen] Mock<IMongoCollection<MongoProductDao2>> mockCollection,
        [Frozen] Mock<IMongoDbContext> mockContext,
        MongoProductRepository sut,
        string tenantId,
        ProductId productId,
        RemoveCommand command
    )
    {
        // Arrange (Given)
        var updateResult = new UpdateResult.Acknowledged(1, 1, null);
        mockCollection
            .Setup(x => x.UpdateOneAsync(
                It.IsAny<FilterDefinition<MongoProductDao2>>(),
                It.IsAny<UpdateDefinition<MongoProductDao2>>(),
                It.IsAny<UpdateOptions>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(updateResult);
        mockContext
            .Setup(x => x.GetCollection<MongoProductDao2>(tenantId, It.IsAny<string>(), productId.Type))
            .Returns(mockCollection.Object);

        // Act (When)
        var result = await sut.RemoveFactAsync(tenantId, productId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
    }

    [Theory(Skip = "future use"), CustomAutoData]
    public async Task AddInternalReviewAsync_ShouldAddReviewSuccessfully(
        [Frozen] Mock<IMongoCollection<MongoProductDao2>> mockCollection,
        [Frozen] Mock<IMongoDbContext> mockContext,
        MongoProductRepository sut,
        string tenantId,
        MongoProductDao2 product,
        ProductId productId,
        AddInternalReviewCommand command
    )
    {
        // Arrange (Given)
        var updateResult = new UpdateResult.Acknowledged(1, 1, null);
        mockCollection
            .Setup(x => x.UpdateOneAsync(
                It.IsAny<FilterDefinition<MongoProductDao2>>(),
                It.IsAny<UpdateDefinition<MongoProductDao2>>(),
                null,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(updateResult);
        mockContext
            .Setup(x => x.FindAsync(It.IsAny<IMongoCollection<MongoProductDao2>>(), It.IsAny<FilterDefinition<MongoProductDao2>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(product);
        mockContext
            .Setup(x => x.GetCollection<MongoProductDao2>(tenantId, It.IsAny<string>(), productId.Type))
            .Returns(mockCollection.Object);

        // Act (When)
        var result = await sut.AddInternalReviewAsync(tenantId, productId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
    }

    [Theory(Skip = "future use"), CustomAutoData]
    public async Task UpdateInternalReviewAsync_ShouldUpdateReviewSuccessfully(
        IFixture fixture,
        [Frozen] Mock<IMongoCollection<MongoProductDao2>> mockCollection,
        [Frozen] Mock<IMongoDbContext> mockContext,
        MongoProductRepository sut,
        string tenantId,
        ProductId productId,
        UpdateInternalReviewCommand command
    )
    {
        // Arrange (Given)
        var updateResult = new UpdateResult.Acknowledged(1, 1, null);
        mockCollection
            .Setup(x => x.UpdateOneAsync(
                It.IsAny<FilterDefinition<MongoProductDao2>>(),
                It.IsAny<UpdateDefinition<MongoProductDao2>>(),
                null,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(updateResult);
        MongoProductDao2 product = fixture
            .Build<MongoProductDao2>()
            .With(x => x.InternalReviews, [fixture
                .Build<InternalReview>()
                .With(x => x.Id, command.Id)
                .Create()])
            .Create();
        mockContext
            .Setup(x => x.FindAsync(It.IsAny<IMongoCollection<MongoProductDao2>>(), It.IsAny<FilterDefinition<MongoProductDao2>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(product);
        mockContext
            .Setup(x => x.GetCollection<MongoProductDao2>(tenantId, It.IsAny<string>(), productId.Type))
            .Returns(mockCollection.Object);

        // Act (When)
        var result = await sut.UpdateInternalReviewAsync(tenantId, productId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
    }

    [Theory(Skip = "future use"), CustomAutoData]
    public async Task RemoveInternalReviewAsync_ShouldRemoveReviewSuccessfully(
        [Frozen] Mock<IMongoCollection<MongoProductDao2>> mockCollection,
        [Frozen] Mock<IMongoDbContext> mockContext,
        MongoProductRepository sut,
        string tenantId,
        ProductId productId,
        RemoveCommand command
    )
    {
        // Arrange (Given)
        var updateResult = new UpdateResult.Acknowledged(1, 1, null);
        mockCollection
            .Setup(x => x.UpdateOneAsync(
                It.IsAny<FilterDefinition<MongoProductDao2>>(),
                It.IsAny<UpdateDefinition<MongoProductDao2>>(),
                null,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(updateResult);
        mockContext
            .Setup(x => x.GetCollection<MongoProductDao2>(tenantId, It.IsAny<string>(), productId.Type))
            .Returns(mockCollection.Object);

        // Act (When)
        var result = await sut.RemoveInternalReviewAsync(tenantId, productId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
    }

    [Theory(Skip = "future use"), CustomAutoData]
    public async Task AddScriptToProduct_ShouldAddScriptSuccessfully(
        [Frozen] Mock<IMongoCollection<MongoProductDao2>> mockCollection,
        [Frozen] Mock<IMongoDbContext> mockContext,
        MongoProductRepository sut,
        string tenantId,
        MongoProductDao2 product,
        AddScriptToProductCommand command
    )
    {
        // Arrange (Given)
        var updateResult = new UpdateResult.Acknowledged(1, 1, null);
        mockCollection
            .Setup(x => x.UpdateOneAsync(
                It.IsAny<FilterDefinition<MongoProductDao2>>(),
                It.IsAny<UpdateDefinition<MongoProductDao2>>(),
                null,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(updateResult);
        mockContext
            .Setup(x => x.FindAsync(It.IsAny<IMongoCollection<MongoProductDao2>>(), It.IsAny<FilterDefinition<MongoProductDao2>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(product);
        mockContext
            .Setup(x => x.GetCollection<MongoProductDao2>(tenantId, It.IsAny<string>(), command.ProductId.Type))
            .Returns(mockCollection.Object);

        // Act (When)
        var result = await sut.AddScriptToProduct(tenantId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
    }

    [Theory(Skip = "future use"), CustomAutoData]
    public async Task AddTemplateRelationshipToProduct_ShouldAddRelationshipSuccessfully(
        [Frozen] Mock<IMongoCollection<MongoProductDao2>> mockCollection,
        [Frozen] Mock<IMongoDbContext> mockContext,
        MongoProductRepository sut,
        string tenantId,
        MongoProductDao2 product,
        AddTemplateRelationshipToProductCommand command
    )
    {
        // Arrange (Given)
        var updateResult = new UpdateResult.Acknowledged(1, 1, null);
        mockCollection
            .Setup(x => x.UpdateOneAsync(
                It.IsAny<FilterDefinition<MongoProductDao2>>(),
                It.IsAny<UpdateDefinition<MongoProductDao2>>(),
                null,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(updateResult);
        mockContext
            .Setup(x => x.FindAsync(It.IsAny<IMongoCollection<MongoProductDao2>>(), It.IsAny<FilterDefinition<MongoProductDao2>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(product);
        mockContext
            .Setup(x => x.GetCollection<MongoProductDao2>(tenantId, It.IsAny<string>(), command.ProductId.Type))
            .Returns(mockCollection.Object);

        // Act (When)
        var result = await sut.AddTemplateRelationshipToProduct(tenantId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
    }

    [Theory(Skip = "future use"), CustomAutoData]
    public async Task RemoveTemplateRelationshipFromProduct_ShouldRemoveRelationshipSuccessfully(
        [Frozen] Mock<IMongoCollection<MongoProductDao2>> mockCollection,
        [Frozen] Mock<IMongoDbContext> mockContext,
        MongoProductRepository sut,
        string tenantId,
        RemoveTemplateRelationshipFromProductCommand command
    )
    {
        // Arrange (Given)
        var updateResult = new UpdateResult.Acknowledged(1, 1, null);
        mockCollection
            .Setup(x => x.UpdateOneAsync(
                It.IsAny<FilterDefinition<MongoProductDao2>>(),
                It.IsAny<UpdateDefinition<MongoProductDao2>>(),
                null,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(updateResult);
        mockContext
            .Setup(x => x.GetCollection<MongoProductDao2>(tenantId, It.IsAny<string>(), command.ProductId.Type))
            .Returns(mockCollection.Object);

        // Act (When)
        var result = await sut.RemoveTemplateRelationshipFromProduct(tenantId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
    }

    [Theory(Skip = "future use"), CustomAutoData]
    public async Task SetTermsAndConditionsTemplateToProduct_ShouldSetTemplateSuccessfully(
        [Frozen] Mock<IMongoCollection<MongoProductDao2>> mockCollection,
        [Frozen] Mock<IMongoDbContext> mockContext,
        MongoProductRepository sut,
        string tenantId,
        MongoProductDao2 product,
        SetTermsAndConditionsCommand command
    )
    {
        // Arrange (Given)
        var updateResult = new UpdateResult.Acknowledged(1, 1, null);
        mockCollection
            .Setup(x => x.UpdateOneAsync(
                It.IsAny<FilterDefinition<MongoProductDao2>>(),
                It.IsAny<UpdateDefinition<MongoProductDao2>>(),
                null,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(updateResult);
        mockContext
            .Setup(x => x.FindAsync(It.IsAny<IMongoCollection<MongoProductDao2>>(), It.IsAny<FilterDefinition<MongoProductDao2>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(product);
        mockContext
            .Setup(x => x.GetCollection<MongoProductDao2>(tenantId, It.IsAny<string>(), command.ProductId.Type))
            .Returns(mockCollection.Object);

        // Act (When)
        var result = await sut.SetTermsAndConditionsTemplateToProduct(tenantId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
    }

    [Theory(Skip = "future use"), CustomAutoData]
    public async Task SetRatingFactorsTableToProduct_ShouldSetTableSuccessfully(
        [Frozen] Mock<IMongoCollection<MongoProductDao2>> mockCollection,
        [Frozen] Mock<IMongoDbContext> mockContext,
        MongoProductRepository sut,
        string tenantId,
        MongoProductDao2 product,
        SetRatingFactorsTableCommand command
    )
    {
        // Arrange (Given)
        var updateResult = new UpdateResult.Acknowledged(1, 1, null);
        mockCollection
            .Setup(x => x.UpdateOneAsync(
                It.IsAny<FilterDefinition<MongoProductDao2>>(),
                It.IsAny<UpdateDefinition<MongoProductDao2>>(),
                null,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(updateResult);
        mockContext
            .Setup(x => x.FindAsync(It.IsAny<IMongoCollection<MongoProductDao2>>(), It.IsAny<FilterDefinition<MongoProductDao2>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(product);
        mockContext
            .Setup(x => x.GetCollection<MongoProductDao2>(tenantId, It.IsAny<string>(), command.ProductId.Type))
            .Returns(mockCollection.Object);

        // Act (When)
        var result = await sut.SetRatingFactorsTableToProduct(tenantId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
    }

    [Theory(Skip = "future use"), CustomAutoData]
    public async Task SetSegmentsToProduct_ShouldSetSegmentsSuccessfully(
        [Frozen] Mock<IMongoCollection<MongoProductDao2>> mockCollection,
        [Frozen] Mock<IMongoDbContext> mockContext,
        MongoProductRepository sut,
        string tenantId,
        MongoProductDao2 product,
        SetSegmentsCommand command
    )
    {
        // Arrange (Given)
        var updateResult = new UpdateResult.Acknowledged(1, 1, null);
        mockCollection
            .Setup(x => x.UpdateOneAsync(
                It.IsAny<FilterDefinition<MongoProductDao2>>(),
                It.IsAny<UpdateDefinition<MongoProductDao2>>(),
                null,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(updateResult);
        mockContext
            .Setup(x => x.FindAsync(It.IsAny<IMongoCollection<MongoProductDao2>>(), It.IsAny<FilterDefinition<MongoProductDao2>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(product);
        mockContext
            .Setup(x => x.GetCollection<MongoProductDao2>(tenantId, It.IsAny<string>(), command.ProductId.Type))
            .Returns(mockCollection.Object);

        // Act (When)
        var result = await sut.SetSegmentsToProduct(tenantId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
    }

    private static void SetupMocks(
        Mock<IMongoCollection<MongoProductDao2>> mockCollection,
        Mock<IMongoDbContext> mockContext,
        string tenantId,
        ProductId productId,
        Func<MongoProductDao2> buildProduct)
    {
        var updateResult = new UpdateResult.Acknowledged(1, 1, null);
        mockCollection
            .Setup(x => x.UpdateOneAsync(
                It.IsAny<FilterDefinition<MongoProductDao2>>(),
                It.IsAny<UpdateDefinition<MongoProductDao2>>(),
                null,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(updateResult);

        MongoProductDao2 product = buildProduct();

        mockContext
            .Setup(x => x.FindAsync(It.IsAny<IMongoCollection<MongoProductDao2>>(), It.IsAny<FilterDefinition<MongoProductDao2>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(product);

        mockContext
            .Setup(x => x.GetCollection<MongoProductDao2>(tenantId, It.IsAny<string>(), productId.Type))
            .Returns(mockCollection.Object);
    }
}

class DummyAsyncCursor<T>(params T[] elements) : IAsyncCursor<T>
{
    private IEnumerator _enumerator = elements.GetEnumerator();

    public IEnumerable<T> Current => elements;

    public void Dispose() => elements = null;
    public bool MoveNext(CancellationToken cancellationToken = default) => _enumerator.MoveNext();
    public Task<bool> MoveNextAsync(CancellationToken cancellationToken = default)
    {
        return Task.FromResult(_enumerator.MoveNext());
    }
}