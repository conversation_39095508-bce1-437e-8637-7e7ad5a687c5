﻿{
  "$schema": "http://json-schema.org/draft-07/schema",
  "$id": "http://example.com/example.json",
  "type": "object",
  "required": [
    "insureds"
  ],
  "properties": {
    "insureds": {
      "minItems": 1,
      "maxItems": 1,
      "items": {
        "allOf": [
          {
            "required": [
              "age",
              "employmentStatus",
              "sumAssured"
            ],
            "properties": {
              "age": {
                "type": "integer",
                "minimum": 18,
                "maximum": 75
              },
              "employmentStatus": {
                "type": "string",
                "enum": [
                  "salariedEmployee",
                  "retired",
                  "housewife",
                  "student",
                  "employed",
                  "unEmployed"
                ]
              },
              "sumAssured": {
                "type": "integer",
                "minimum": 250000,
                "maximum": 10000000
              }
            }
          },
          {
            "if": {
              "properties": {
                "employmentStatus": {
                  "const": "salariedEmployee"
                }
              }
            },
            "then": {
              "allOf": [
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 18,
                        "maximum": 45
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 10000000
                      }
                    }
                  }
                },
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 46,
                        "maximum": 50
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 5000000
                      }
                    }
                  }
                },
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 51,
                        "maximum": 55
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 4000000
                      }
                    }
                  }
                },
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 56,
                        "maximum": 60
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 2500000
                      }
                    }
                  }
                },
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 61,
                        "maximum": 65
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 1000000
                      }
                    }
                  }
                }
              ]
            }
          },
          {
            "if": {
              "properties": {
                "employmentStatus": {
                  "const": "employed"
                }
              }
            },
            "then": {
              "allOf": [
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 18,
                        "maximum": 45
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 10000000
                      }
                    }
                  }
                },
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 46,
                        "maximum": 50
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 5000000
                      }
                    }
                  }
                },
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 51,
                        "maximum": 55
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 4000000
                      }
                    }
                  }
                },
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 56,
                        "maximum": 60
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 2500000
                      }
                    }
                  }
                },
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 61,
                        "maximum": 65
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 1000000
                      }
                    }
                  }
                }
              ]
            }
          },
          {
            "if": {
              "properties": {
                "employmentStatus": {
                  "const": "retired"
                }
              }
            },
            "then": {
              "allOf": [
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 18,
                        "maximum": 45
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 3200000
                      }
                    }
                  }
                },
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 46,
                        "maximum": 50
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 3200000
                      }
                    }
                  }
                },
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 51,
                        "maximum": 55
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 3200000
                      }
                    }
                  }
                },
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 56,
                        "maximum": 60
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 2500000
                      }
                    }
                  }
                },
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 61,
                        "maximum": 65
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 1000000
                      }
                    }
                  }
                }
              ]
            }
          },
          {
            "if": {
              "properties": {
                "employmentStatus": {
                  "const": "housewife"
                }
              }
            },
            "then": {
              "allOf": [
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 18,
                        "maximum": 45
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 3200000
                      }
                    }
                  }
                },
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 46,
                        "maximum": 50
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 3200000
                      }
                    }
                  }
                },
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 51,
                        "maximum": 55
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 3200000
                      }
                    }
                  }
                },
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 56,
                        "maximum": 60
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 2500000
                      }
                    }
                  }
                },
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 61,
                        "maximum": 65
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 1000000
                      }
                    }
                  }
                }
              ]
            }
          },
          {
            "if": {
              "properties": {
                "employmentStatus": {
                  "const": "student"
                }
              }
            },
            "then": {
              "allOf": [
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 18,
                        "maximum": 45
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 3200000
                      }
                    }
                  }
                },
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 46,
                        "maximum": 50
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 3200000
                      }
                    }
                  }
                },
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 51,
                        "maximum": 55
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 3200000
                      }
                    }
                  }
                },
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 56,
                        "maximum": 60
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 2500000
                      }
                    }
                  }
                },
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 61,
                        "maximum": 65
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 1000000
                      }
                    }
                  }
                }
              ]
            }
          },
          {
            "if": {
              "properties": {
                "employmentStatus": {
                  "const": "unEmployed"
                }
              }
            },
            "then": {
              "allOf": [
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 18,
                        "maximum": 45
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 3200000
                      }
                    }
                  }
                },
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 46,
                        "maximum": 50
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 3200000
                      }
                    }
                  }
                },
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 51,
                        "maximum": 55
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 3200000
                      }
                    }
                  }
                },
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 56,
                        "maximum": 60
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 2500000
                      }
                    }
                  }
                },
                {
                  "if": {
                    "properties": {
                      "age": {
                        "minimum": 61,
                        "maximum": 65
                      }
                    }
                  },
                  "then": {
                    "properties": {
                      "sumAssured": {
                        "minimum": 250000,
                        "maximum": 1000000
                      }
                    }
                  }
                }
              ]
            }
          }
        ]
      }
    }
  }
}