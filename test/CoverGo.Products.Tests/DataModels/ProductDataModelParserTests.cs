using CoverGo.Products.Domain.Products.DataModels;

using Newtonsoft.Json.Linq;

namespace CoverGo.Products.Tests.DataModels;

public class ProductDataModelParserTests
{
    [Theory]
    [InlineData("JSelect", "text", "Start Date")]
    [InlineData("CDatePicker", "date", "Start Date")]
    public void FieldTypeIsFilledIfMissing(
        string component,
        string expectedFieldType,
        string expectedFieldLabel)
    {
        // Arrange (Given)
        var schema = $$"""
        {
            "testFields": {
                "properties": {
                    "testField": {
                        "type": "string",
                        "meta": {
                            "label": "Start Date",
                            "component": "{{component}}",
                            "required": true,
                            "fixed": true,
                            "order": 1
                        }
                    }
                }
            }
        }
        """;

        // Act (When)
        var fields = ProductDataModelParser.Parse(JObject.Parse(schema), "testFields");

        // Assert (Then)
        fields["testField"].Meta.FieldType.Should().Be(expectedFieldType);
        fields["testField"].Meta.Label.Should().Be(expectedFieldLabel);
    }
}
