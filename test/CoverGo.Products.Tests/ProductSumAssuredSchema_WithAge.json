﻿{
  "$schema": "http://json-schema.org/draft-07/schema",
  "$id": "http://example.com/example.json",
  "type": "object",
  "required": [
    "insureds"
  ],
  "properties": {
    "insureds": {
      "minItems": 1,
      "maxItems": 1,
      "items": {
        "allOf": [
          {
            "required": [
              "employmentStatus",
              "sumAssured"
            ],
            "properties": {
              "employmentStatus": {
                "type": "string",
                "enum": [
                  "salariedEmployee",
                  "retired",
                  "housewife",
                  "student",
                  "employed",
                  "unEmployed"
                ]
              },
              "sumAssured": {
                "type": "integer",
                "minimum": 250000,
                "maximum": 10000000
              }
            }
          },
          {
            "if": {
              "properties": {
                "employmentStatus": {
                  "const": "salariedEmployee"
                }
              }
            },
            "then": {
              "properties": {
                "sumAssured": {
                  "minimum": 250000,
                  "maximum": 10000000
                }
              }
            }
          },
          {
            "if": {
              "properties": {
                "employmentStatus": {
                  "const": "employed"
                }
              }
            },
            "then": {
              "properties": {
                "sumAssured": {
                  "minimum": 250000,
                  "maximum": 10000000
                }
              }
            }
          },
          {
            "if": {
              "properties": {
                "employmentStatus": {
                  "const": "retired"
                }
              }
            },
            "then": {
              "properties": {
                "sumAssured": {
                  "minimum": 250000,
                  "maximum": 3200000
                }
              }
            }
          },
          {
            "if": {
              "properties": {
                "employmentStatus": {
                  "const": "housewife"
                }
              }
            },
            "then": {
              "properties": {
                "sumAssured": {
                  "minimum": 250000,
                  "maximum": 3200000
                }
              }
            }
          },
          {
            "if": {
              "properties": {
                "employmentStatus": {
                  "const": "student"
                }
              }
            },
            "then": {
              "properties": {
                "sumAssured": {
                  "minimum": 250000,
                  "maximum": 3200000
                }
              }
            }
          },
          {
            "if": {
              "properties": {
                "employmentStatus": {
                  "const": "unEmployed"
                }
              }
            },
            "then": {
              "properties": {
                "sumAssured": {
                  "minimum": 250000,
                  "maximum": 3200000
                }
              }
            }
          }
        ]
      }
    }
  }
}