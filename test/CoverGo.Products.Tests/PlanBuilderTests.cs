using System.Collections.Generic;
using System.IO;
using System.Linq;
using CoverGo.Products.Domain.BenefitDefinitions;
using CoverGo.Products.Domain.BenefitDefinitionTypes;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.Limits;
using CoverGo.Products.Domain.Products.ProductTreeParsing;

using FluentAssertions.Execution;

namespace CoverGo.Products.Tests;

public class PlanBuilderTests
{
    public PlanBuilderTests()
    {
        AssertionOptions.AssertEquivalencyUsing(options => options.ComparingRecordsByValue());
    }

    [Fact]
    public void GIVEN_representation1_WHEN_building_planDetail_THEN_planDetail_is_built()
    {
        string representation = PlanBuilderTestsData.ProductRepresentation1;
        ProductPlanDetails details = PlanDetailBuilder.GetProductPlanDetails(
            new ProductId(),
            representation,
            [],
            [],
            [],
            []);
        details.Should().NotBeNull();
        details.PlanDetails.Should().HaveCount(3);
        IEnumerable<List<PlanBenefitType>> benefitTypesDetails = details.PlanDetails.Select(d =>
            d.BenefitTypes
        );
        foreach (List<PlanBenefitType> types in benefitTypesDetails)
        {
            types.Should().NotBeNullOrEmpty();
        }
        IEnumerable<List<PlanBenefit>> benefitDetails = details.PlanDetails.SelectMany(d =>
            d.BenefitTypes.Select(b => b.Benefits)
        );
        foreach (List<PlanBenefit> benefits in benefitDetails)
        {
            benefits.Should().NotBeNullOrEmpty();
        }
        IEnumerable<PlanAddOn> addOns = details.PlanDetails.SelectMany(d => d.AddOns);
        addOns.Should().NotBeNullOrEmpty();
        addOns.Select(d => d.BenefitTypes).Should().NotBeNullOrEmpty();
    }

    [Fact]
    public void GIVEN_empty_array_representation1_WHEN_building_planDetail_THEN_planDetail_is_not_null()
    {
        string representation = "[]";
        ProductPlanDetails details = PlanDetailBuilder.GetProductPlanDetails(
            new ProductId(),
            representation,
            [],
            [],
            [],
            []);
        details.Should().NotBeNull();
    }

    [Fact]
    public void GIVEN_representation2_WHEN_building_planDetail_THEN_planDetail_is_built()
    {
        string representation = PlanBuilderTestsData.ProductRepresentation2;
        ProductPlanDetails details = PlanDetailBuilder.GetProductPlanDetails(
            new ProductId(),
            representation,
            [],
            [],
            [],
            []);
        details.Should().NotBeNull();
        details.PlanDetails.Should().HaveCount(3);
        IEnumerable<List<PlanBenefitType>> benefitTypesDetails = details.PlanDetails.Select(d =>
            d.BenefitTypes
        );
        IEnumerable<List<PlanBenefit>> benefitDetails = details.PlanDetails.SelectMany(d =>
            d.BenefitTypes.Select(b => b.Benefits)
        );
        foreach (List<PlanBenefit> benefits in benefitDetails)
        {
            benefits.Should().NotBeNullOrEmpty();
        }
    }

    [Fact]
    public void GIVEN_representation3_WHEN_building_planDetail_THEN_planDetail_is_built_AND_addOnNodes_are_built_from_top_level()
    {
        string representation = PlanBuilderTestsData.ProductRepresentation3;
        ProductPlanDetails details = PlanDetailBuilder.GetProductPlanDetails(
            new ProductId(),
            representation,
            [],
            [],
            [],
            []);
        details.Should().NotBeNull();
        details.AddOns.Should().HaveCount(1);
        details.AddOns.First().Name.Should().Be("Dental Tier");
    }

    [Fact]
    public void GIVEN_representation4_WHEN_building_planDetail_THEN_planDetail_is_built()
    {
        string representation = PlanBuilderTestsData.ProductRepresentation4;
        var details = PlanDetailBuilder.GetProductPlanDetails(
            new ProductId(),
            representation,
            [],
            [],
            [],
            []);

        details.Should().NotBeNull();

        List<ProductRepresentationNode> masterPlan = details.MasterPlan;
        List<ProductRepresentationNode> allPlans = details.AllPlans;

        allPlans[0].Children.Should().HaveCount(607);
        masterPlan.Should().HaveCount(608);
    }

    [Fact]
    public void PlanBuilderReturnsPlanIdAndName()
    {
        // Arrange (Given)
        var representation = """
            [{"id":"9893716540652182","label":"Product Template","props":{"theme":"#e4b917"},"productType":"gm","prorateMethod":"prorataMonthly","minified":true,"ch":[{"id":"25968678279036994","label":"Plan","readonly":false,"ch":[{"id":"199780794073386","label":"Benefit Type","ch":[{"id":"7479329343773278","label":"Benefit","ch":[],"n":"benefit","p":[{"n":"type","c":"Radios","d":"string","v":"free"},{"n":"name","l":"Name","c":"Select","dv":"China Health Link Network","d":"string","v":"Cn-health-link"}]},{"id":"06416997934226831","label":"Co-insurance","n":"reimbursement","p":[{"display":"radio","n":"type","l":"","c":"Radio","d":"string","v":"coInsurance"},{"n":"inputType","l":"Input Type","c":"Select","d":"string","v":"reImbursementAmount"},{"display":"none","n":"percentage","l":"Percentage","c":"Formula","d":"formula","v":[]},{"display":"reImbursementAmount","n":"reImbursementAmount","l":"Percentage","c":"Number","d":"number","v":"10%"},{"display":"reImbursementFormula","n":"reImbursementFormula","c":"Formula","d":"formula","v":[]},{"display":"reImbursementFormula","n":"description","l":"Description","c":"LongText","de":"What is the formula about","d":"string","v":""},{"n":"maxLimitType","l":"Max Limit","c":"Select","d":"string","v":"amount"},{"display":"none","n":"maxLimit","l":"Amount","c":"Number","d":"number","v":1000},{"n":"maxLimitCurrency","l":"Currency","c":"Select","d":"string","v":""}]}],"n":"benefitType","p":[{"n":"name","l":"Name","c":"Select","dv":"Cancer Treatment","d":"string","v":"cancer"}]}],"n":"plan","p":[{"n":"id","l":"ID","c":"Text","d":"string","v":"planId1"},{"n":"name","l":"Name","c":"Text","d":"string","v":"Plan 1"}]}],"n":"root","pc":"HKD","pbf":"annually"}]
            """;

        // Act (When)
        var details = PlanDetailBuilder.GetProductPlanDetails(
            new ProductId(),
            representation,
            [],
            [],
            [],
            []);

        // Assert (Then)
        details.Should().NotBeNull();
        details.PlanDetails.Should().HaveCount(1);
        details.PlanDetails[0].Id.Should().Be("planId1");
        details.PlanDetails[0].Name.Should().Be("Plan 1");
    }

    [Fact]
    public void PlanBuilderReturnsBenefitTypeCode()
    {
        // Arrange (Given)
        var representation = """
            [{"id":"9893716540652182","label":"Product Template","props":{"theme":"#e4b917"},"productType":"gm","prorateMethod":"prorataMonthly","minified":true,"ch":[{"id":"25968678279036994","label":"Plan","readonly":false,"ch":[{"id":"199780794073386","label":"Benefit Type","ch":[{"id":"7479329343773278","label":"Benefit","ch":[],"n":"benefit","p":[{"n":"type","c":"Radios","d":"string","v":"free"},{"n":"name","l":"Name","c":"Select","dv":"China Health Link Network","d":"string","v":"Cn-health-link"}]},{"id":"06416997934226831","label":"Co-insurance","n":"reimbursement","p":[{"display":"radio","n":"type","l":"","c":"Radio","d":"string","v":"coInsurance"},{"n":"inputType","l":"Input Type","c":"Select","d":"string","v":"reImbursementAmount"},{"display":"none","n":"percentage","l":"Percentage","c":"Formula","d":"formula","v":[]},{"display":"reImbursementAmount","n":"reImbursementAmount","l":"Percentage","c":"Number","d":"number","v":"10%"},{"display":"reImbursementFormula","n":"reImbursementFormula","c":"Formula","d":"formula","v":[]},{"display":"reImbursementFormula","n":"description","l":"Description","c":"LongText","de":"What is the formula about","d":"string","v":""},{"n":"maxLimitType","l":"Max Limit","c":"Select","d":"string","v":"amount"},{"display":"none","n":"maxLimit","l":"Amount","c":"Number","d":"number","v":1000},{"n":"maxLimitCurrency","l":"Currency","c":"Select","d":"string","v":""}]}],"n":"benefitType","p":[{"n":"name","l":"Name","c":"Select","dv":"Cancer Treatment","d":"string","v":"cancer"}]}],"n":"plan","p":[{"n":"id","l":"ID","c":"Text","d":"string","v":"planId1"},{"n":"name","l":"Name","c":"Text","d":"string","v":"Plan 1"}]}],"n":"root","pc":"HKD","pbf":"annually"}]
            """;

        // Act (When)
        var details = PlanDetailBuilder.GetProductPlanDetails(
            new ProductId(),
            representation,
            [],
            [],
            [],
            []);

        // Assert (Then)
        details.Should().NotBeNull();
        details.PlanDetails.Should().HaveCount(1);
        details.PlanDetails[0].BenefitTypes.Should().NotBeNull();
        details.PlanDetails[0].BenefitTypes[0].Code.Should().Be("cancer");
    }

    [Fact]
    public void PlanBuilderReturnsProductId()
    {
        // Arrange (Given)
        var representation = """
            [{"id":"9893716540652182","label":"Product Template","props":{"theme":"#e4b917"},"productType":"gm","prorateMethod":"prorataMonthly","minified":true,"ch":[{"id":"25968678279036994","label":"Plan","readonly":false,"ch":[{"id":"199780794073386","label":"Benefit Type","ch":[{"id":"7479329343773278","label":"Benefit","ch":[],"n":"benefit","p":[{"n":"type","c":"Radios","d":"string","v":"free"},{"n":"name","l":"Name","c":"Select","dv":"China Health Link Network","d":"string","v":"Cn-health-link"}]},{"id":"06416997934226831","label":"Co-insurance","n":"reimbursement","p":[{"display":"radio","n":"type","l":"","c":"Radio","d":"string","v":"coInsurance"},{"n":"inputType","l":"Input Type","c":"Select","d":"string","v":"reImbursementAmount"},{"display":"none","n":"percentage","l":"Percentage","c":"Formula","d":"formula","v":[]},{"display":"reImbursementAmount","n":"reImbursementAmount","l":"Percentage","c":"Number","d":"number","v":"10%"},{"display":"reImbursementFormula","n":"reImbursementFormula","c":"Formula","d":"formula","v":[]},{"display":"reImbursementFormula","n":"description","l":"Description","c":"LongText","de":"What is the formula about","d":"string","v":""},{"n":"maxLimitType","l":"Max Limit","c":"Select","d":"string","v":"amount"},{"display":"none","n":"maxLimit","l":"Amount","c":"Number","d":"number","v":1000},{"n":"maxLimitCurrency","l":"Currency","c":"Select","d":"string","v":""}]}],"n":"benefitType","p":[{"n":"name","l":"Name","c":"Select","dv":"Cancer Treatment","d":"string","v":"cancer"}]}],"n":"plan","p":[{"n":"id","l":"ID","c":"Text","d":"string","v":"planId1"},{"n":"name","l":"Name","c":"Text","d":"string","v":"Plan 1"}]}],"n":"root","pc":"HKD","pbf":"annually"}]
            """;

        // Act (When)
        var details = PlanDetailBuilder.GetProductPlanDetails(
            new ProductId()
            {
                Plan = "plan",
                Type = "gm",
                Version = "1.0",
            },
            representation,
            [],
            [],
            [],
            []);

        // Assert (Then)
        using var scope = new AssertionScope();
        details.PlanDetails[0].ProductId.Plan.Should().Be("plan");
        details.PlanDetails[0].ProductId.Type.Should().Be("gm");
        details.PlanDetails[0].ProductId.Version.Should().Be("1.0");
    }

    [Fact]
    public void PlanBuilderParsesAllBenefitsAndBenefitTypes()
    {
        // Arrange (Given)
        var representation = """
            [{"id":"930677219734275","label":"Product Template","props":{"theme":"#e4b917"},"productType":"im","prorateMethod":"prorataMonthly","ch":[{"id":"3577982857476185","label":"Plan","readonly":false,"ch":[{"id":"7591109056595822","label":"Benefit","ch":[{"id":"7443470882383836","label":"Benefit Type","n":"benefitType","p":[{"n":"name","l":"Name","c":"Select","dv":"Annual Travel","d":"string","v":"annualtravel"}]},{"id":"9631772187671792","label":"Benefit","n":"benefit","p":[{"n":"type","l":"","c":"Radios","d":"string","v":"Basic Oral Surgery/Extractions"},{"n":"name","l":"Name","c":"Select","d":"string","v":null}]}],"n":"benefit","p":[{"n":"type","l":"","c":"Radios","d":"string","v":null},{"n":"name","l":"Name","c":"Select","dv":"Cervical Collars","d":"string","v":"cervical"}]},{"id":"6376143754985786","label":"Benefit Type","ch":[{"id":"3102299545284841","label":"Benefit","n":"benefit","p":[{"n":"type","l":"","c":"Radios","d":"string","v":null},{"n":"name","l":"Name","c":"Select","dv":"Breast Prosthesis","d":"string","v":"breastprosthesis"}]},{"id":"43758462754663285","label":"Benefit Type","n":"benefitType","p":[{"n":"name","l":"Name","c":"Select","dv":"Standard Benefits","d":"string","v":"stdben"}]}],"n":"benefitType","p":[{"n":"name","l":"Name","c":"Select","dv":"Orthodontics","d":"string","v":"orth"}]}],"n":"plan","p":[{"n":"id","l":"ID","c":"Text","d":"string","v":"plan"},{"n":"name","l":"Name","c":"Text","d":"string","v":"Plan"}]},{"id":"35993268167246373","label":"Add On","readonly":false,"ch":[{"id":"07904143777558192","label":"Benefit","n":"benefit","p":[{"n":"type","l":"","c":"Radios","d":"string","v":null},{"n":"name","l":"Name","c":"Select","dv":"Travel Days Add-On","d":"string","v":"traveldaysaddon"}]},{"id":"1925138920672358","label":"Benefit Type","ch":[],"n":"benefitType","p":[{"n":"name","l":"Name","c":"Select","dv":"Drug","d":"string","v":"Drug"}]}],"n":"addOn","p":[{"n":"id","l":"ID","c":"Text","d":"string","v":"addon"},{"n":"name","l":"Name","c":"Text","d":"string","v":"Addon"},{"n":"expression","l":"Expression","c":"Formula","de":"expression to evaluate the if condition","d":"formula","v":null}]}],"n":"root","pc":"CAD","pbf":"annually","minified":true}]
            """;

        // Act (When)
        var details = PlanDetailBuilder.GetProductPlanDetails(
            new ProductId()
            {
                Plan = "plan",
                Type = "gm",
                Version = "1.0",
            },
            representation,
            [],
            [],
            [],
            []);

        // Assert (Then)
        using var scope = new AssertionScope();
        details.PlanDetails[0].Id.Should().Be("plan");
        details.PlanDetails[0].Name.Should().Be("Plan");
        details.PlanDetails[0].Benefits.Should().HaveCount(1);
        details.PlanDetails[0].Benefits[0].Name.Should().Be("Cervical Collars");
        details.PlanDetails[0].Benefits[0].Benefits.Should().HaveCount(1);
        details.PlanDetails[0].Benefits[0].Benefits[0].Name.Should().Be("Basic Oral Surgery/Extractions");
        details.PlanDetails[0].Benefits[0].BenefitTypes.Should().HaveCount(1);
        details.PlanDetails[0].Benefits[0].BenefitTypes[0].Name.Should().Be("Annual Travel");

        details.PlanDetails[0].BenefitTypes.Should().HaveCount(1);
        details.PlanDetails[0].BenefitTypes[0].Name.Should().Be("Orthodontics");
        details.PlanDetails[0].BenefitTypes[0].Benefits.Should().HaveCount(1);
        details.PlanDetails[0].BenefitTypes[0].Benefits[0].Name.Should().Be("Breast Prosthesis");
        details.PlanDetails[0].BenefitTypes[0].BenefitTypes.Should().HaveCount(1);
        details.PlanDetails[0].BenefitTypes[0].BenefitTypes[0].Name.Should().Be("Standard Benefits");

        details.AddOns[0].Id.Should().Be("addon");
        details.AddOns[0].Name.Should().Be("Addon");
        details.AddOns[0].Benefits.Should().HaveCount(1);
        details.AddOns[0].Benefits[0].Name.Should().Be("Travel Days Add-On");
        details.AddOns[0].BenefitTypes.Should().HaveCount(1);
        details.AddOns[0].BenefitTypes[0].Name.Should().Be("Drug");
    }

    [Trait("Ticket", "CH-14545")]
    [Fact]
    public void PlanBuilderParsesLimitNodes()
    {
        // Arrange (Given)
        var representation = File.OpenText("PlanBuilderTests_Limits.json").ReadToEnd();

        // Act (When)
        var details = PlanDetailBuilder.GetProductPlanDetails(
            new ProductId()
            {
                Plan = "plan",
                Type = "gm",
                Version = "1.0",
            },
            representation,
            [],
            [],
            [],
            []);

        // Assert (Then)
        using var scope = new AssertionScope();
        details.PlanDetails[0].Benefits[0].Limits[0].Should().Be(new PlanBenefitLimit()
        {
            LimitValue = new AmountLimitValue() { Amount = 45L, PerTimeUnit = "perDay", PerVariableUnit = "perDelivery", Currency = "HKD" },
            Network = new InNetwork()
            {
                Organizations = [
                    new() { Id = "2a9f6692-2a4c-4daa-9015-7c2392898425", Name = "MediNet" },
                    new() { Id = "d8362d9c-56bf-4ac9-8330-5f76fff6b3cc", Name = "U Care" }
                ]
            },
        });
        details.PlanDetails[0].Benefits[0].Limits[1].Should().Be(new PlanBenefitLimit()
        {
            LimitValue = new NumberLimitValue() { Amount = 11L, PerTimeUnit = "per5Years", PerVariableUnit = "perAccident", Unit = "perItem" },
            Network = new OutOfNetwork(),
        });
        details.PlanDetails[0].Benefits[0].Limits[2].Should().Be(new PlanBenefitLimit()
        {
            LimitValue = new AmountFormulaLimitValue() { Currency = "HKD", PerTimeUnit = "perYear", PerVariableUnit = "perTrip" },
            Network = new InNetworkAndOutOfNetwork(),
            Description = "Trips",
        });
        details.PlanDetails[0].Benefits[0].Limits[3].Should().Be(new PlanBenefitLimit()
        {
            LimitValue = new AmountFormulaLimitValue() { Currency = "HKD", PerTimeUnit = "12rollingMonths", PerVariableUnit = "perLife" },
            Network = new InNetworkAndOutOfNetwork(),
        });
    }

    [Trait("Ticket", "CH-14545")]
    [Fact]
    public void PlanBuilderParsesOverseasAccidentalLimitNodes()
    {
        // Arrange (Given)
        var representation = File.OpenText("PlanBuilderTests_Limits.json").ReadToEnd();

        // Act (When)
        var details = PlanDetailBuilder.GetProductPlanDetails(
            new ProductId()
            {
                Plan = "plan",
                Type = "gm",
                Version = "1.0",
            },
            representation,
            [],
            [],
            [],
            []);

        // Assert (Then)
        using var scope = new AssertionScope();
        details.PlanDetails[0].Benefits[0].OverseasAccidentalLimits[0].Should().Be(new PlanBenefitOverseasAccidentalLimit()
        {
            Percentage = 444
        });
    }

    [Trait("Ticket", "CH-14545")]
    [Fact]
    public void PlanBuilderParsesGroupedBenefitLimitNodes()
    {
        // Arrange (Given)
        var representation = File.OpenText("PlanBuilderTests_Limits.json").ReadToEnd();
        var benefitDefinitions = new[]
        {
            new BenefitDefinition
            {
                BusinessId = "AD01",
                Name = "Accident Damage"
            },
            new BenefitDefinition
            {
                BusinessId = "aidsPreg",
                Name = "Aids test for pregnant women"
            }
        };
        var benefitDefinitionTypes = new[]
        {
            new BenefitDefinitionType
            {
                BusinessId = "free",
                Name = "Additional Free Services"
            }
        };

        // Act (When)
        var details = PlanDetailBuilder.GetProductPlanDetails(
            new ProductId()
            {
                Plan = "plan",
                Type = "gm",
                Version = "1.0",
            },
            representation,
            benefitDefinitions,
            benefitDefinitionTypes,
            [],
            []);
        // Assert (Then)
        using var scope = new AssertionScope();
        details.PlanDetails[0].Benefits[0].GroupedBenefitLimits[0].Should().Be(new PlanBenefitGroupedBenefitLimit()
        {
            GroupedBenefits = [
                new GroupedBenefit { Value = benefitDefinitions[0] },
                new GroupedBenefit { Value = benefitDefinitions[1] },
                new GroupedBenefitType { Value = benefitDefinitionTypes[0] }
            ],
            LimitValue = new AmountLimitValue() { Amount = 44L, PerTimeUnit = null, PerVariableUnit = "perDelivery", Currency = "HKD" },
        });
    }

    [Trait("Ticket", "CH-14545")]
    [Fact]
    public void PlanBuilderParsesDeductibleNodes()
    {
        // Arrange (Given)
        var representation = File.OpenText("PlanBuilderTests_Limits.json").ReadToEnd();

        // Act (When)
        var details = PlanDetailBuilder.GetProductPlanDetails(
            new ProductId()
            {
                Plan = "plan",
                Type = "gm",
                Version = "1.0",
            },
            representation,
            [],
            [],
            [],
            []);

        // Assert (Then)
        using var scope = new AssertionScope();
        details.PlanDetails[0].Benefits[0].Deductibles[0].Should().Be(new PlanBenefitDeductible()
        {
            DeductibleValue = new AmountDeductibleValue() { Amount = 33, PerTimeUnit = "perLifetime", PerVariableUnit = "perDelivery", Currency = "HKD" },
        });
        details.PlanDetails[0].Benefits[0].Deductibles[1].Should().Be(new PlanBenefitDeductible()
        {
            DeductibleValue = new AmountFormulaDeductibleValue() { PerTimeUnit = "perYear", PerVariableUnit = "perDisability", Currency = "HKD" },
            Description = "Ded",
        });
    }

    [Trait("Ticket", "CH-14545")]
    [Fact]
    public void PlanBuilderParsesCopaymentNodes()
    {
        // Arrange (Given)
        var representation = File.OpenText("PlanBuilderTests_Limits.json").ReadToEnd();

        // Act (When)
        var details = PlanDetailBuilder.GetProductPlanDetails(
            new ProductId()
            {
                Plan = "plan",
                Type = "gm",
                Version = "1.0",
            },
            representation,
            [],
            [],
            [],
            []);

        // Assert (Then)
        using var scope = new AssertionScope();
        details.PlanDetails[0].Benefits[0].Copayments[0].Should().Be(new PlanBenefitCopayment()
        {
            CopaymentValue = new AmountCopaymentValue() { Amount = 333, Currency = "HKD" },
        });
        details.PlanDetails[0].Benefits[0].Copayments[1].Should().Be(new PlanBenefitCopayment()
        {
            CopaymentValue = new AmountFormulaCopaymentValue() { Currency = "HKD" },
            Description = "Hello There",
        });
    }

    [Trait("Ticket", "CH-14545")]
    [Fact]
    public void PlanBuilderParsesPanelCopaymentNodes()
    {
        // Arrange (Given)
        var representation = File.OpenText("PlanBuilderTests_Limits.json").ReadToEnd();

        // Act (When)
        var details = PlanDetailBuilder.GetProductPlanDetails(
            new ProductId()
            {
                Plan = "plan",
                Type = "gm",
                Version = "1.0",
            },
            representation,
            [],
            [],
            [],
            []);

        // Assert (Then)
        using var scope = new AssertionScope();
        details.PlanDetails[0].Benefits[0].PanelCopayments[0].Should().Be(new PlanBenefitPanelCopayment()
        {
            PanelCopaymentValue = new AmountPanelCopaymentValue
            {
                Amount = 44,
                Currency = "HKD",
                PerTimeUnit = "perDay",
                PerVariableUnit = "perPregnancy"
            }
        });
    }


    [Trait("Ticket", "CH-14545")]
    [Fact]
    public void PlanBuilderParsesCoinsuranceLimitNodes()
    {
        // Arrange (Given)
        var representation = File.OpenText("PlanBuilderTests_Limits.json").ReadToEnd();

        // Act (When)
        var details = PlanDetailBuilder.GetProductPlanDetails(
            new ProductId()
            {
                Plan = "plan",
                Type = "gm",
                Version = "1.0",
            },
            representation,
            [],
            [],
            [],
            []);

        // Assert (Then)
        using var scope = new AssertionScope();
        details.PlanDetails[0].Benefits[0].Coinsurances[0].Should().Be(new PlanBenefitCoinsurance()
        {
            CoinsuranceValue = new PercentageCoinsuranceValue { Amount = 30 },
            MaxLimit = new AmountCoinsuranceMaxLimitValue { Amount = 88, Currency = "HKD" },
        });
        details.PlanDetails[0].Benefits[0].Coinsurances[1].Should().Be(new PlanBenefitCoinsurance()
        {
            CoinsuranceValue = new PercentageFormulaCoinsuranceValue { },
            MaxLimit = new AmountFormulaCoinsuranceMaxLimitValue { Currency = "HKD" },
            Description = "Max Limit Descr"
        });
        details.PlanDetails[0].Benefits[0].Coinsurances[2].Should().Be(new PlanBenefitCoinsurance()
        {
            CoinsuranceValue = new PercentageCoinsuranceValue { Amount = 77 },
            MaxLimit = null,
            Description = null,
        });
    }


    [Trait("Ticket", "CH-14545")]
    [Fact]
    public void PlanBuilderParsesReimbursementNodes()
    {
        // Arrange (Given)
        var representation = File.OpenText("PlanBuilderTests_Limits.json").ReadToEnd();

        // Act (When)
        var details = PlanDetailBuilder.GetProductPlanDetails(
            new ProductId()
            {
                Plan = "plan",
                Type = "gm",
                Version = "1.0",
            },
            representation,
            [],
            [],
            [],
            []);

        // Assert (Then)
        using var scope = new AssertionScope();
        details.PlanDetails[0].Benefits[0].Reimbursements[0].Should().Be(new PlanBenefitReimbursement()
        {
            ReimbursementValue = new PercentageFormulaReimbursementValue { },
            Description = "ReDescr"
        });
        details.PlanDetails[0].Benefits[0].Reimbursements[1].Should().Be(new PlanBenefitReimbursement()
        {
            ReimbursementValue = new PercentageReimbursementValue { Amount = 23.6M },
        });
    }

    [Trait("Ticket", "CH-14545")]
    [Fact]
    public void PlanBuilderParsesGeographicalLimitNodes()
    {
        // Arrange (Given)
        var representation = File.OpenText("PlanBuilderTests_Limits.json").ReadToEnd();

        // Act (When)
        var details = PlanDetailBuilder.GetProductPlanDetails(
            new ProductId()
            {
                Plan = "plan",
                Type = "gm",
                Version = "1.0",
            },
            representation,
            [],
            [],
            [],
            []);

        // Assert (Then)
        using var scope = new AssertionScope();
        details.PlanDetails[0].Benefits[0].GeographicalLimits[0].Should().Be(new PlanBenefitGeographicalLimit()
        {
            Region = "europe",
        });
    }

    [Trait("Ticket", "CH-14545")]
    [Fact]
    public void PlanBuilderParsesWaitingPeriodNodes()
    {
        // Arrange (Given)
        var representation = File.OpenText("PlanBuilderTests_Limits.json").ReadToEnd();

        // Act (When)
        var details = PlanDetailBuilder.GetProductPlanDetails(
            new ProductId()
            {
                Plan = "plan",
                Type = "gm",
                Version = "1.0",
            },
             representation,
            [],
            [],
            [],
            []);

        // Assert (Then)
        using var scope = new AssertionScope();
        details.PlanDetails[0].Benefits[0].WaitingPeriods[0].Should().Be(new PlanBenefitWaitingPeriod()
        {
            WaitingPeriodValue = new AmountWaitingPeriodValue { Amount = 77, Unit = "hour(s)" },
            Description = "W8",
        });
        details.PlanDetails[0].Benefits[0].WaitingPeriods[1].Should().Be(new PlanBenefitWaitingPeriod()
        {
            WaitingPeriodValue = new AmountFormulaWaitingPeriodValue { Unit = "hour(s)" },
            Description = "Wait!",
        });
    }

    [Trait("Ticket", "CH-14545")]
    [Fact]
    public void PlanBuilderParsesDeferredPeriodNodes()
    {
        // Arrange (Given)
        var representation = File.OpenText("PlanBuilderTests_Limits.json").ReadToEnd();

        // Act (When)
        var details = PlanDetailBuilder.GetProductPlanDetails(
            new ProductId()
            {
                Plan = "plan",
                Type = "gm",
                Version = "1.0",
            },
             representation,
            [],
            [],
            [],
            []);

        // Assert (Then)
        using var scope = new AssertionScope();
        details.PlanDetails[0].Benefits[0].DeferredPeriods[0].Should().Be(new PlanBenefitDeferredPeriod()
        {
            DeferredPeriodValue = new NumberDeferredPeriodValue
            {
                Length = 787,
                Period = "day"
            },
        });
        details.PlanDetails[0].Benefits[0].DeferredPeriods[1].Should().Be(new PlanBenefitDeferredPeriod()
        {
            DeferredPeriodValue = new NumberFormulaDeferredPeriodValue
            {
                Period = "day"
            },
        });
    }

    [Trait("Ticket", "CH-14545")]
    [Fact]
    public void PlanBuilderParsesQualificationPeriodNodes()
    {
        // Arrange (Given)
        var representation = File.OpenText("PlanBuilderTests_Limits.json").ReadToEnd();

        // Act (When)
        var details = PlanDetailBuilder.GetProductPlanDetails(
            new ProductId()
            {
                Plan = "plan",
                Type = "gm",
                Version = "1.0",
            },
             representation,
            [],
            [],
            [],
            []);

        // Assert (Then)
        using var scope = new AssertionScope();
        details.PlanDetails[0].Benefits[0].QualificationPeriods[0].Should().Be(new PlanBenefitQualificationPeriod()
        {
            QualificationPeriodValue = new NumberQualificationPeriodValue
            {
                Length = 667,
                Period = "day"
            },
        });
        details.PlanDetails[0].Benefits[0].QualificationPeriods[1].Should().Be(new PlanBenefitQualificationPeriod()
        {
            QualificationPeriodValue = new NumberFormulaQualificationPeriodValue
            {
                Period = "day"
            },
        });
    }
}