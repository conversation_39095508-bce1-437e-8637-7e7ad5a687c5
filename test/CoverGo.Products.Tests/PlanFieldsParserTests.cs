using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.DataModels;

using System.IO;
using System.Linq;

namespace CoverGo.Products.Tests;

[Trait("Ticket", "CH-1644")]
[<PERSON>rait("Ticket", "CH-1645")]
[<PERSON><PERSON><PERSON>("Ticket", "CH-1646")]
public class PlanFieldsParserTests
{
    [Fact]
    public void PlanFieldsParserReturnsFields()
    {
        // Arrange (Given)
        var scriptInput = """
            {
                "properties": {},
                "type": "object",
                "policyFields": {
                    "properties": {
                        "startDate": {
                            "type": "string",
                            "meta": {
                                "label": "Start Date",
                                "component": "CDatePicker",
                                "required": true,
                                "fixed": true,
                                "order": 1
                            }
                        },
                        "endDate": {
                            "type": "string",
                            "meta": {
                                "label": "End Date",
                                "component": "CDatePicker",
                                "required": false,
                                "fixed": true,
                                "order": 2
                            }
                        }
                    }
                },
                "insuredsFields": {
                    "properties": {
                        "numberOfInsureds": {
                            "type": "number",
                            "meta": {
                                "label": "Number of Insureds",
                                "component": "JInputNumber",
                                "required": true,
                                "order": 1,
                                "fixed": true
                            }
                        },
                        "planSelected": {
                            "type": "string",
                            "meta": {
                                "label": "Plan Selected",
                                "component": "JSelect",
                                "required": true,
                                "options": [
                                    {
                                        "key": "25968678279036994",
                                        "name": "1",
                                        "value": "1"
                                    }
                                ],
                                "order": 2,
                                "fixed": true
                            }
                        }
                    }
                },
                "planFields_1": {
                    "description": "1 Fields",
                    "properties": {
                        "roomType": {
                            "type": "string",
                            "meta": {
                                "label": "Room Type",
                                "component": "JSelect",
                                "required": false,
                                "hideFromMemberAdditionForm": true,
                                "fieldType": "text",
                                "validations": "",
                                "options": [
                                    {
                                        "key": "171430437575484864",
                                        "name": "Bad",
                                        "value": "bad"
                                    },
                                    {
                                        "key": "171430438812077680",
                                        "name": "Good",
                                        "value": "good"
                                    }
                                ]
                            }
                        },
                        "Is Smokers": {
                            "type": "boolean",
                            "meta": {
                                "label": "isSmoker",
                                "component": "JToggle",
                                "required": true,
                                "hideFromMemberAdditionForm": true,
                                "fieldType": "boolean",
                                "validations": "required"
                            }
                        },
                        "reason": {
                            "type": "string",
                            "meta": {
                                "label": "Reason",
                                "component": "JText",
                                "required": false,
                                "hideFromMemberAdditionForm": false,
                                "fieldType": "description",
                                "validations": "length:10,200",
                                "minLength": 10,
                                "maxLength": 200
                            }
                        },
                        "money": {
                            "type": "number",
                            "meta": {
                                "label": "Money",
                                "component": "JSelect",
                                "required": false,
                                "hideFromMemberAdditionForm": false,
                                "fieldType": "number",
                                "validations": "",
                                "options": [
                                    {
                                        "key": "171430446667730882",
                                        "name": "10",
                                        "value": "10"
                                    },
                                    {
                                        "key": "171430447458656227",
                                        "name": "100",
                                        "value": "100"
                                    }
                                ]
                            }
                        },
                        "maxDob": {
                            "type": "string",
                            "meta": {
                                "label": "MaxDob",
                                "component": "CDatePicker",
                                "required": false,
                                "hideFromMemberAdditionForm": false,
                                "fieldType": "date",
                                "validations": ""
                            }
                        },
                        "email": {
                            "type": "string",
                            "meta": {
                                "label": "Email",
                                "component": "JInputText",
                                "required": true,
                                "hideFromMemberAdditionForm": false,
                                "fieldType": "text",
                                "validations": "email|required"
                            }
                        },
                        "needles": {
                            "type": "number",
                            "meta": {
                                "label": "Needles",
                                "component": "JInputNumber",
                                "required": false,
                                "hideFromMemberAdditionForm": false,
                                "fieldType": "number",
                                "validations": "number",
                                "numeric": true
                            }
                        }
                    }
                }
            }
            """;

        // Act (When)
        var results = PlanFieldsParser.Parse(scriptInput).ToList();

        // Assert (Then)
        var singlePlan = results.Single();
        singlePlan.PlanId.Should().Be("1");

        singlePlan.Fields.Should().HaveCount(7);

        singlePlan.Fields["roomType"].Type.Should().Be("string");
        singlePlan.Fields["roomType"].Meta.Required.Should().BeFalse();
        singlePlan.Fields["roomType"].Meta.FieldType.Should().Be("text");
        singlePlan.Fields["roomType"].Meta.Validations.Should().BeEmpty();
        singlePlan.Fields["roomType"].Meta.Options.Should().HaveCount(2);
        singlePlan.Fields["roomType"].Meta.Options.Should().ContainEquivalentOf(new ScriptSchemaPlanFieldOption { Key = "171430437575484864", Name = "Bad", Value = "bad" });

        singlePlan.Fields["Is Smokers"].Type.Should().Be("boolean");
        singlePlan.Fields["Is Smokers"].Meta.Required.Should().BeTrue();
        singlePlan.Fields["Is Smokers"].Meta.FieldType.Should().Be("boolean");
        singlePlan.Fields["Is Smokers"].Meta.Validations.Should().Be("required");
        singlePlan.Fields["Is Smokers"].Meta.Options.Should().BeNull();

        singlePlan.Fields["reason"].Type.Should().Be("string");
        singlePlan.Fields["reason"].Meta.Required.Should().BeFalse();
        singlePlan.Fields["reason"].Meta.FieldType.Should().Be("description");
        singlePlan.Fields["reason"].Meta.Validations.Should().Be("length:10,200");
        singlePlan.Fields["reason"].Meta.MinLength.Should().Be(10);
        singlePlan.Fields["reason"].Meta.MaxLength.Should().Be(200);
        singlePlan.Fields["reason"].Meta.Options.Should().BeNull();

        singlePlan.Fields["money"].Type.Should().Be("number");
        singlePlan.Fields["money"].Meta.Required.Should().BeFalse();
        singlePlan.Fields["money"].Meta.FieldType.Should().Be("number");
        singlePlan.Fields["money"].Meta.Validations.Should().BeEmpty();
        singlePlan.Fields["money"].Meta.Options.Should().HaveCount(2);
        singlePlan.Fields["money"].Meta.Options.Should().ContainEquivalentOf(new ScriptSchemaPlanFieldOption { Key = "171430446667730882", Name = "10", Value = "10" });

        singlePlan.Fields["maxDob"].Type.Should().Be("string");
        singlePlan.Fields["maxDob"].Meta.Required.Should().BeFalse();
        singlePlan.Fields["maxDob"].Meta.FieldType.Should().Be("date");
        singlePlan.Fields["maxDob"].Meta.Validations.Should().BeEmpty();
        singlePlan.Fields["maxDob"].Meta.Options.Should().BeNull();

        singlePlan.Fields["email"].Type.Should().Be("string");
        singlePlan.Fields["email"].Meta.Required.Should().BeTrue();
        singlePlan.Fields["email"].Meta.FieldType.Should().Be("text");
        singlePlan.Fields["email"].Meta.Validations.Should().Be("email|required");
        singlePlan.Fields["email"].Meta.Options.Should().BeNull();

        singlePlan.Fields["needles"].Type.Should().Be("number");
        singlePlan.Fields["needles"].Meta.Required.Should().BeFalse();
        singlePlan.Fields["needles"].Meta.FieldType.Should().Be("number");
        singlePlan.Fields["needles"].Meta.Validations.Should().Be("number");
        singlePlan.Fields["needles"].Meta.Numeric.Should().BeTrue();
        singlePlan.Fields["needles"].Meta.Options.Should().BeNull();
    }

    [Fact]
    public void PlanFieldsParsedWithMultiplePlans()
    {
        // Arrange (Given)
        var scriptInput = File.OpenText("PlanFieldsParserTests_MultiplePlansSchema.json").ReadToEnd();

        // Act (When)
        var results = PlanFieldsParser.Parse(scriptInput).ToList();

        // Assert (Then)
        results.Should().HaveCount(4);
    }

    [Fact]
    public void PlanFieldsParsedWithEmptyPlans()
    {
        // Arrange (Given)
        var scriptInput = File.OpenText("PlanFieldsParserTests_EmptyPlansSchema.json").ReadToEnd();

        // Act (When)
        var results = PlanFieldsParser.Parse(scriptInput).ToList();

        // Assert (Then)
        results.Should().HaveCount(3);
    }

    [Fact]
    public void Plan_WithoutFields_ReturnsNoFields()
    {
        // Arrange (Given)
        var scriptInput = File.OpenText("PlanFieldsParserTests_DefaultPlanWithoutFields.json").ReadToEnd();

        // Act (When)
        var results = PlanFieldsParser.Parse(scriptInput).ToList();

        // Assert (Then)
        results.Should().HaveCount(1);
        results.Single().Fields.Should().HaveCount(0);
    }
}
