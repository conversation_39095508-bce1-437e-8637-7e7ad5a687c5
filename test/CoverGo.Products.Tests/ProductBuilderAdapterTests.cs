﻿using System.Net;
using System.Net.Http;
using System.Text.Json;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Infrastructure.Products.Adapters;
using System.Threading.Tasks;
using System.Threading;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using System;
using System.Linq;

namespace CoverGo.Products.Tests
{
    public class ProductBuilderAdapterTests
    {
        private readonly Mock<IHttpClientFactory> _httpClientFactoryMock;
        private readonly Mock<ILogger<ProductBuilderAdapter>> _loggerMock;
        private readonly ProductBuilderAdapter _adapter;

        public ProductBuilderAdapterTests()
        {
            _httpClientFactoryMock = new Mock<IHttpClientFactory>();
            _loggerMock = new Mock<ILogger<ProductBuilderAdapter>>();
            _adapter = new ProductBuilderAdapter(_httpClientFactoryMock.Object, _loggerMock.Object);
        }

        [Fact]
        public async Task GIVEN_CreateProductSchema_WHEN_SuccessfulResponse_THEN_Returns_SuccessResult()
        {
            // Arrange (Given)
            var nodeId = "test-node-id";
            var productSchema = new ProductSchema { DataSchema = "{}" };

            var expectedResponse = new
            {
                data = new
                {
                    createProductSchema = new
                    {
                        status = "success",
                        errors = (string)null
                    }
                }
            };

            var httpClient = SetupHttpClient(HttpStatusCode.OK, expectedResponse);
            _httpClientFactoryMock.Setup(f => f.CreateClient(It.IsAny<string>())).Returns(httpClient);

            // Act (When)
            var result = await _adapter.CreateProductSchema(nodeId, productSchema, CancellationToken.None);

            // Assert (Then)
            result.IsSuccess.Should().BeTrue();
            result.Value.Status.Should().Be("success");
        }

        [Fact]
        public async Task GIVEN_CreateProductSchema_WHEN_NonSuccessStatusCode_THEN_Returns_FailureResult()
        {
            // Arrange (Given)
            var nodeId = "test-node-id";
            var productSchema = new ProductSchema { DataSchema = "{}" };

            var httpClient = SetupHttpClient(HttpStatusCode.BadRequest, "null");
            _httpClientFactoryMock.Setup(f => f.CreateClient(It.IsAny<string>())).Returns(httpClient);

            // Act (When)
            var result = await _adapter.CreateProductSchema(nodeId, productSchema, CancellationToken.None);

            // Assert (Then)
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("Unable to create the product schema");
        }

        [Fact]
        public async Task GIVEN_CreateProductSchema_WHEN_NullResponse_THEN_Returns_FailureResult()
        {
            // Arrange (Given)
            var nodeId = "test-node-id";
            var productSchema = new ProductSchema { DataSchema = "{}" };

            var httpClient = SetupHttpClient(HttpStatusCode.OK, null);
            _httpClientFactoryMock.Setup(f => f.CreateClient(It.IsAny<string>())).Returns(httpClient);

            // Act (When)
            var result = await _adapter.CreateProductSchema(nodeId, productSchema, CancellationToken.None);

            // Assert (Then)
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("createProductSchema Failed");
        }

        [Fact]
        public async Task GIVEN_CreateProductSchema_WHEN_InvalidStatus_THEN_Returns_FailureResult()
        {
            // Arrange (Given)
            var nodeId = "test-node-id";
            var productSchema = new ProductSchema { DataSchema = "{}" };

            var expectedResponse = new
            {
                data = new
                {
                    createProductSchema = new
                    {
                        status = "failed",
                        errors = "Invalid input"
                    }
                }
            };

            var httpClient = SetupHttpClient(HttpStatusCode.OK, expectedResponse);
            _httpClientFactoryMock.Setup(f => f.CreateClient(It.IsAny<string>())).Returns(httpClient);

            // Act (When)
            var result = await _adapter.CreateProductSchema(nodeId, productSchema, CancellationToken.None);

            // Assert (Then)
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("Unable to create the product schema");
        }

        [Fact]
        public async Task GIVEN_CloneProductTree_WHEN_Valid_ClonedTreeId_THEN_Returns_SuccessfulResponse()
        {
            // Arrange (Given)
            var mockHttpClientFactory = new Mock<IHttpClientFactory>();
            var mockLogger = new Mock<ILogger<ProductBuilderAdapter>>();

            var handlerMock = new Mock<HttpMessageHandler>();
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(@"{""data"": {""cloneTree"": ""clonedTreeId123""}}"),
            };

            handlerMock
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>()
                )
                .ReturnsAsync(response);

            var httpClient = new HttpClient(handlerMock.Object)
            {
                BaseAddress = new Uri("http://example.com/"),
            };

            mockHttpClientFactory
                .Setup(x => x.CreateClient(It.IsAny<string>()))
                .Returns(httpClient);

            var adapter = new ProductBuilderAdapter(mockHttpClientFactory.Object, mockLogger.Object);

            // Act (When)
            var result = await adapter.CloneProductTree("productTreeId123", CancellationToken.None);

            // Assert (Then)
            result.IsSuccess.Should().BeTrue();
            result.Value.Should().Be("clonedTreeId123");
        }

        [Fact]
        public async Task GIVEN_CloneProductTree_WHEN_UnsuccessfulResponseTHEN_Returns_Failure()
        {
            // Arrange (Given)
            var mockHttpClientFactory = new Mock<IHttpClientFactory>();
            var mockLogger = new Mock<ILogger<ProductBuilderAdapter>>();

            var handlerMock = new Mock<HttpMessageHandler>();
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent(@"{""errors"": [{""message"": ""Invalid request""}]}"),
            };

            handlerMock
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>()
                )
                .ReturnsAsync(response);

            var httpClient = new HttpClient(handlerMock.Object)
            {
                BaseAddress = new Uri("http://example.com/"),
            };

            mockHttpClientFactory
                .Setup(x => x.CreateClient(It.IsAny<string>()))
                .Returns(httpClient);

            var adapter = new ProductBuilderAdapter(mockHttpClientFactory.Object, mockLogger.Object);

            // Act (When)
            var result = await adapter.CloneProductTree("productTreeId123", CancellationToken.None);

            // Assert (Then)
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("Unable to clone the product tree");
        }

        [Fact]
        public async Task GIVEN_CloneProductTree_WHEN_EmptyResponse_THEN_Returns_Failure()
        {
            // Arrange (Given)
            var mockHttpClientFactory = new Mock<IHttpClientFactory>();
            var mockLogger = new Mock<ILogger<ProductBuilderAdapter>>();

            var handlerMock = new Mock<HttpMessageHandler>();
            var response = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(@"{""data"": {""cloneTree"": """"}}"),
            };

            handlerMock
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>()
                )
                .ReturnsAsync(response);

            var httpClient = new HttpClient(handlerMock.Object)
            {
                BaseAddress = new Uri("http://example.com/"),
            };

            mockHttpClientFactory
                .Setup(x => x.CreateClient(It.IsAny<string>()))
                .Returns(httpClient);

            var adapter = new ProductBuilderAdapter(mockHttpClientFactory.Object, mockLogger.Object);

            // Act (When)
            var result = await adapter.CloneProductTree("productTreeId123", CancellationToken.None);

            // Assert (Then)
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("Unable to clone the product tree");
        }


        [Fact]
        public async Task GIVEN_CloneProduct_WHEN_Api_Returns_Success_THEN_Should_Return_Success()
        {
            // Arrange (Given)
            var productId = new ProductId { Plan = "plan1", Type = "type1", Version = "v1" };
            var productName = new ProductName { Locale = "en-US", Value = "Product Name" };
            var cloneProductId = new ProductId { Plan = "plan2", Type = "type2", Version = "v2" };
            var cancellationToken = CancellationToken.None;

            var handlerMock = new Mock<HttpMessageHandler>();
            handlerMock
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>()
                )
                .ReturnsAsync(new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(@"{""data"": {""cloneProduct"": {""status"": ""success""}}}")
                });

            var httpClient = new HttpClient(handlerMock.Object)
            {
                BaseAddress = new Uri("http://example.com/"),
            };
            _httpClientFactoryMock.Setup(x => x.CreateClient(nameof(ProductBuilderAdapter))).Returns(httpClient);

            // Act (When)
            var result = await _adapter.CloneProduct(productId, productName, cloneProductId, cancellationToken);

            // Assert (Then)
            result.IsSuccess.Should().BeTrue();
            handlerMock.Protected().Verify(
                "SendAsync",
                Times.Once(),
                ItExpr.Is<HttpRequestMessage>(req =>
                    req.Method == HttpMethod.Post &&
                    req.RequestUri.ToString().EndsWith("graphql")),
                ItExpr.IsAny<CancellationToken>()
            );
        }

        [Fact]
        public async Task GIVEN_CloneProduct_WHEN_Api_Returns_Error_THEN_Should_Return_Failure()
        {
            // Arrange (Given)
            var productId = new ProductId { Plan = "plan1", Type = "type1", Version = "v1" };
            var productName = new ProductName { Locale = "en-US", Value = "Product Name" };
            var cloneProductId = new ProductId { Plan = "plan2", Type = "type2", Version = "v2" };
            var cancellationToken = CancellationToken.None;

            var handlerMock = new Mock<HttpMessageHandler>();
            handlerMock
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>()
                )
                .ReturnsAsync(new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(@"{""data"": {""cloneProduct"": {""status"": ""error"", ""errors"": [""Something went wrong""]}}}")
                });

            var httpClient = new HttpClient(handlerMock.Object)
            {
                BaseAddress = new Uri("http://example.com/"),
            };
            _httpClientFactoryMock.Setup(x => x.CreateClient(nameof(ProductBuilderAdapter))).Returns(httpClient);

            // Act (When)
            var result = await _adapter.CloneProduct(productId, productName, cloneProductId, cancellationToken);

            // Assert (Then)
            result.IsSuccess.Should().BeFalse();
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("cloneProduct mutation failed")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()
                ),
                Times.Once
            );
        }


        private static HttpClient SetupHttpClient(HttpStatusCode statusCode, object responseContent)
        {
            var responseMessage = new HttpResponseMessage(statusCode)
            {
                Content = new StringContent(JsonSerializer.Serialize(responseContent))
            };

            var handler = new Mock<HttpMessageHandler>();
            handler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>()
                )
                .ReturnsAsync(responseMessage);

            var client = new HttpClient(handler.Object);
            client.BaseAddress = new Uri("https://localhost:60060/graphql");
            return client;
        }
    }
}