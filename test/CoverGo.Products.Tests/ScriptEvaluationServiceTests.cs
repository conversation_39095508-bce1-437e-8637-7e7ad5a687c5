﻿#nullable enable
using AutoFixture;
using CoverGo.ChannelManagement.Client;
using CoverGo.FeatureManagement;
using CoverGo.FileSystem.Client;
using CoverGo.Products.Domain.DiscountCodes;
using CoverGo.Products.Domain.Illustrations;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.Ports;
using CoverGo.Products.Domain.ScriptEvaluation;
using CoverGo.Products.Domain.Scripts;
using CoverGo.Products.Domain.Underwriting;
using CoverGo.Reference.Client;
using CoverGo.Scripts.Client;
using JsonLogic.Net;
using MediatR;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using StrawberryShake;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests;

public class ScriptEvaluationServiceTests
{
    private readonly Mock<IScriptRepository> _mockScriptRepository = new();
    private readonly Mock<IFileSystemClient> _mockFileSystemClient = new();
    private readonly Mock<ProductService> _mockProductService = new(
        new Mock<ProductEventService>(new Mock<IProductEventStore>().Object).Object,
        new Mock<IEnumerable<IIllustrationRepository>>().Object,
        new Mock<IEnumerable<IUnderwritingEngine>>().Object,
        new Mock<IMultiTenantFeatureManager>().Object,
        new Mock<IProductScriptsRepository>().Object,
        new Mock<IProductVersionRepository>().Object,
        new Mock<IProductRepository>().Object,
        new Mock<IReferenceGenerator>().Object,
        new Mock<JsonLogicEvaluator>(new Mock<IManageOperators>().Object).Object,
        Mock.Of<IProductNameRepository>(),
        Mock.Of<IMediator>()
    );
    private readonly Mock<IScriptAdapter> _mockScriptAdapter = new();
    private readonly Mock<IDiscountCodeService> _mockDiscountCodeService = new();
    private readonly Mock<IReferenceGraphQlClient> _mockReferenceGraphQlClient = new();
    private readonly Mock<IChannelManagementAdapter> _mockChannelManagementAdapter = new();
    private readonly ScriptEvaluationService _scriptEvaluationService;

    public ScriptEvaluationServiceTests()
    {
        _scriptEvaluationService = new ScriptEvaluationService(
            new ScriptService(_mockScriptRepository.Object, _mockFileSystemClient.Object),
            _mockProductService.Object,
            _mockScriptAdapter.Object,
            _mockDiscountCodeService.Object,
            _mockReferenceGraphQlClient.Object,
            _mockChannelManagementAdapter.Object,
            new NullLogger<ScriptEvaluationService>());
    }

    [Fact]
    [Trait("Ticket", "CH-20390")]
    public async Task GIVEN_ProductWithoutUwScript_WHEN_EvaluateStandardUnderwriting_THEN_ReturnDefaultUwResult()
    {
        // Arrange (Given)
        var fixture = new Fixture();
        var tenantId = "tenant1";
        var clientId = "client1";
        Product product = fixture.Create<Product>();
        product.ScriptIds = ["pricingScript"];
        EvaluateStandardUnderwritingCommand command = new()
        {
            DataInput = "{}",
            ProductId = product.Id,
            ScriptType = ScriptTypeEnum.Underwriting_standard,
            InsuredUnderwritingInputs = [new(){
                    Id="m1", DateOfBirth=DateTime.Parse("2000-01-01"), Fields=new(){}, InsuredSimplifiedMedicalUnderwritingResponse=new(){
                        Template=new(){Id="t1",Name="t1"},Answers=new(){}
                }}],
            Modes = new() { BenefitLevel = true }
        };
        var cancellationToken = new CancellationToken(false);

        MockProduct(tenantId, clientId, product, cancellationToken);
        _mockScriptRepository
            .Setup(x => x.GetAsync(tenantId, It.IsAny<ScriptWhere>(), cancellationToken))
            .ReturnsAsync([]);

        // Act (When)
        var result = await _scriptEvaluationService.EvaluateStandardUnderwriting(tenantId, clientId, command, cancellationToken);

        // Assert (Then)
        result.Data.Policy.Decision.Should().Be(UnderwritingResultDecision.Pending);
        result.Data.Insureds[0].Decision.Should().Be(UnderwritingResultDecision.Pending);
    }

    [Fact]
    [Trait("Ticket", "CH-20390")]
    public async Task GIVEN_ProductWithoutUwScript_WHEN_EvaluateStandardUnderwriting_THEN_ReturnDefaultUwResult_BasedOnDataInputInsureds()
    {
        // Arrange (Given)
        var fixture = new Fixture();
        var tenantId = "tenant1";
        var clientId = "client1";
        Product product = fixture.Create<Product>();
        product.ScriptIds = ["pricingScript"];
        EvaluateStandardUnderwritingCommand command = new()
        {
            DataInput = """{"insureds":[{"id":"m1"},{"id":"m2"}]}""",
            ProductId = product.Id,
            ScriptType = ScriptTypeEnum.Underwriting_standard,
            InsuredUnderwritingInputs = null,
            Modes = new() { BenefitLevel = true }
        };
        var cancellationToken = new CancellationToken(false);

        MockProduct(tenantId, clientId, product, cancellationToken);
        MockScript(tenantId, [], cancellationToken);

        // Act (When)
        var result = await _scriptEvaluationService.EvaluateStandardUnderwriting(
            tenantId, clientId, command, cancellationToken);

        // Assert (Then)
        result.Data.Policy.Decision.Should().Be(UnderwritingResultDecision.Pending);
        result.Data.Insureds.Should().HaveCount(2);
        result.Data.Insureds[0].Decision.Should().Be(UnderwritingResultDecision.Pending);
    }

    [Fact]
    [Trait("Ticket", "CH-19046")]
    public async Task GIVEN_ProductWithTaxConfig_WHEN_EvaluateStandardPricing_THEN_ShouldQueryTaxBasedOnConfig()
    {
        // Arrange (Given)
        var fixture = new Fixture();
        var tenantId = "tenant1";
        var clientId = "client1";
        Product product = fixture.Create<Product>();
        product.TaxConfiguration = new() { ReferenceMnemonic = "X-FIN-CUSTOMTAX", MnemonicCode = "VAT-UAE-10", AllowOverrideAtOfferStage = true, AllowOverrideAtProposalStage = true };
        product.ScriptIds = ["pricingScript"];
        Script script = fixture.Create<Script>();
        script.Id = "pricingScript";
        script.Type = ScriptTypeEnum.Pricing_standard;

        EvaluateStandardPricingCommand command = new()
        {
            ProductId = product.Id,
            ScriptType = ScriptTypeEnum.Pricing_standard,
            Taxes = null,
            UwLoadings = new() { Loadings = [], Members = [] },
            DataInput = """{"policy":{"startDate":"2023-01-01", "endDate":"2023-12-31"}}""",
            Modes = [new() { Count = 0, Meta = new() { }, Billing = new() { BillingFrequency = BillingFrequency.Annually, BillingMode = BillingMode.PolicyYear } }],
        };
        var cancellationToken = new CancellationToken(false);

        MockProduct(tenantId, clientId, product, cancellationToken);
        MockScript(tenantId, script, cancellationToken);
        var captureRef = MockReference(rtCustomTax, cancellationToken);
        var captureEval = MockEvaluateStandardPricing(tenantId, cancellationToken);

        // Act (When)
        await _scriptEvaluationService.EvaluateStandardPricing(tenantId, clientId, command, cancellationToken);

        // Assert (Then)
        captureRef.ReferenceMnemonic.Should().Be("X-FIN-CUSTOMTAX");
        captureEval.StandardPricingCommand!.Vat.Should().Be(vatUAE);
    }

    [Fact]
    [Trait("Ticket", "CH-19046")]
    public async Task GIVEN_ProductWithTaxConfig_WHEN_EvaluateLegacyPricing_THEN_ShouldQueryTaxBasedOnConfig()
    {
        // Arrange (Given)
        var fixture = new Fixture();
        var tenantId = "tenant1";
        var clientId = "client1";
        Product product = fixture.Create<Product>();
        product.TaxConfiguration = new() { ReferenceMnemonic = "X-FIN-CUSTOMTAX", MnemonicCode = "VAT-UAE-10", AllowOverrideAtOfferStage = true, AllowOverrideAtProposalStage = true };
        product.ScriptIds = ["pricingScript"];
        Script script = fixture.Create<Script>();
        script.Id = "pricingScript";
        script.Type = ScriptTypeEnum.Pricing;

        EvaluateProductScriptCommand command = new()
        {
            ProductId = product.Id,
            ScriptType = ScriptTypeEnum.Pricing,
            DataInput = """{"policy":{"startDate":"2023-01-01", "endDate":"2023-12-31"}}""",
        };
        var cancellationToken = new CancellationToken(false);

        MockProduct(tenantId, clientId, product, cancellationToken);
        MockScript(tenantId, script, cancellationToken);
        MockDiscountCodes(tenantId, [], cancellationToken);
        var captureRef = MockReference(rtCustomTax, cancellationToken);
        var captureEval = MockEvaluate(tenantId, cancellationToken);

        // Act (When)
        var result = await _scriptEvaluationService.Evaluate(tenantId, clientId, command, cancellationToken);

        // Assert (Then)
        captureRef.ReferenceMnemonic.Should().Be("X-FIN-CUSTOMTAX");
        captureEval.PricingCommand!.Vat.Should().Be(vatUAE);
    }

    [Fact]
    [Trait("Ticket", "CH-19046")]
    public async Task GIVEN_Product_WHEN_EvaluateLegacyPricing_THEN_ShouldQueryDiscountCodes()
    {
        // Arrange (Given)
        var fixture = new Fixture();
        var tenantId = "tenant1";
        var clientId = "client1";
        Product product = fixture.Create<Product>();
        product.ScriptIds = ["pricingScript"];
        Script script = fixture.Create<Script>();
        script.Id = "pricingScript";
        script.Type = ScriptTypeEnum.Pricing;
        IReadOnlyCollection<Products.Domain.DiscountCodes.DiscountCode> discountCodes = [new Products.Domain.DiscountCodes.DiscountCode { Id = "discountCode1" }];

        EvaluateProductScriptCommand command = new()
        {
            ProductId = product.Id,
            ScriptType = ScriptTypeEnum.Pricing,
            DataInput = """{"policy":{"startDate":"2023-01-01", "endDate":"2023-12-31"}}""",
        };
        var cancellationToken = new CancellationToken(false);

        MockProduct(tenantId, clientId, product, cancellationToken);
        MockScript(tenantId, script, cancellationToken);
        MockDiscountCodes(tenantId, discountCodes, cancellationToken);
        MockReference(rtDefaultTax, cancellationToken);
        MockCommissions([], cancellationToken);
        var captureEval = MockEvaluate(tenantId, cancellationToken);

        // Act (When)
        await _scriptEvaluationService.Evaluate(tenantId, clientId, command, cancellationToken);

        // Assert (Then)
        captureEval.PricingCommand!.DiscountCodes[0].Id.Should().Be("discountCode1");
    }

    [Fact]
    [Trait("Ticket", "CH-19046")]
    public async Task GIVEN_Product_WHEN_EvaluateStandardPricing_With_DistributorID_THEN_ShouldQueryCommissions()
    {
        // Arrange (Given)
        var fixture = new Fixture();
        var tenantId = "tenant1";
        var clientId = "client1";
        Product product = fixture.Create<Product>();
        product.ScriptIds = ["pricingScript"];
        Script script = fixture.Create<Script>();
        script.Id = "pricingScript";
        script.Type = ScriptTypeEnum.Pricing_standard;
        IReadOnlyList<ICommissionCandidates_CommissionCandidates> commissions = [new CommissionCandidates_CommissionCandidates_cmCommission("commissionId", "", "", [], [])];

        EvaluateStandardPricingCommand command = new()
        {
            ProductId = product.Id,
            ScriptType = ScriptTypeEnum.Pricing_standard,
            DistributorID = "distributorId",
            DataInput = """{"policy":{"startDate":"2023-01-01", "endDate":"2023-12-31"}}""",
            Modes = [new() { Count = 0, Meta = new() { }, Billing = new() { BillingFrequency = BillingFrequency.Annually, BillingMode = BillingMode.PolicyYear } }],
        };
        var cancellationToken = new CancellationToken(false);

        MockProduct(tenantId, clientId, product, cancellationToken);
        MockScript(tenantId, script, cancellationToken);
        MockReference(rtDefaultTax, cancellationToken);
        MockCommissions(commissions, cancellationToken);
        var captured = MockEvaluateStandardPricing(tenantId, cancellationToken);

        // Act (When)
        await _scriptEvaluationService.EvaluateStandardPricing(tenantId, clientId, command, cancellationToken);

        // Assert (Then)
        captured.StandardPricingCommand!.Commissions[0].CommissionID.Should().Be("commissionId");
    }

    [Fact]
    [Trait("Ticket", "CH-19046")]
    public async Task GIVEN_Product_WHEN_EvaluateLegacyPricing_With_DistributorID_THEN_ShouldQueryCommissions()
    {
        // Arrange (Given)
        var fixture = new Fixture();
        var tenantId = "tenant1";
        var clientId = "client1";
        Product product = fixture.Create<Product>();
        product.ScriptIds = ["pricingScript"];
        Script script = fixture.Create<Script>();
        script.Id = "pricingScript";
        script.Type = ScriptTypeEnum.Pricing;
        IReadOnlyList<ICommissionCandidates_CommissionCandidates> commissions = [new CommissionCandidates_CommissionCandidates_cmCommission("commissionId", "", "", [], [])];

        EvaluateProductScriptCommand command = new()
        {
            ProductId = product.Id,
            ScriptType = ScriptTypeEnum.Pricing,
            DistributorID = "distributorId",
            DataInput = """{"policy":{"startDate":"2023-01-01", "endDate":"2023-12-31"}}""",
        };
        var cancellationToken = new CancellationToken(false);

        MockProduct(tenantId, clientId, product, cancellationToken);
        MockScript(tenantId, script, cancellationToken);
        MockDiscountCodes(tenantId, [], cancellationToken);
        MockReference(rtDefaultTax, cancellationToken);
        MockCommissions(commissions, cancellationToken);
        var captured = MockEvaluate(tenantId, cancellationToken);

        // Act (When)
        await _scriptEvaluationService.Evaluate(tenantId, clientId, command, cancellationToken);

        // Assert (Then)
        captured.PricingCommand!.Commissions[0].CommissionID.Should().Be(commissions[0].CommissionID);
    }

    [Fact]
    [Trait("Ticket", "CH-19046")]
    public async Task GIVEN_Product_WHEN_EvaluateStandardPricing_With_CampaignCodes_THEN_ShouldQueryCampaignCodes()
    {
        // Arrange (Given)
        var fixture = new Fixture();
        var tenantId = "tenant1";
        var clientId = "client1";
        Product product = fixture.Create<Product>();
        product.ScriptIds = ["pricingScript"];
        Script script = fixture.Create<Script>();
        script.Id = "pricingScript";
        script.Type = ScriptTypeEnum.Pricing_standard;
        IReadOnlyList<ICampaigns_Campaigns_Items> campaigns = [new Campaigns_Campaigns_Items_Campaign("campaignId1", new Campaigns_Campaigns_Items_ProductID_ProductId("plan", "version", "type"), "productName", "saleChannelID", false, [], DateTime.Parse("2023-01-01"), DateTime.Parse("2023-12-31"), "campaignCode1", 0, PremiumCalculationType.Gross)];

        EvaluateStandardPricingCommand command = new()
        {
            ProductId = product.Id,
            ScriptType = ScriptTypeEnum.Pricing_standard,
            DistributorID = "distributorId",
            DataInput = """{"policy":{"startDate":"2023-01-01", "endDate":"2023-12-31"}}""",
            CampaignCodes = ["campaignCode1"],
            Modes = [new() { Count = 0, Meta = new() { }, Billing = new() { BillingFrequency = BillingFrequency.Annually, BillingMode = BillingMode.PolicyYear } }],
        };
        var cancellationToken = new CancellationToken(false);

        MockProduct(tenantId, clientId, product, cancellationToken);
        MockScript(tenantId, script, cancellationToken);
        MockReference(rtDefaultTax, cancellationToken);
        MockCommissions([], cancellationToken);
        MockCampaigns(campaigns, cancellationToken);
        var captured = MockEvaluateStandardPricing(tenantId, cancellationToken);

        // Act (When)
        await _scriptEvaluationService.EvaluateStandardPricing(tenantId, clientId, command, cancellationToken);

        // Assert (Then)
        captured.StandardPricingCommand!.Campaigns[0].CampaignID.Should().Be("campaignId1");
    }

    [Fact]
    [Trait("Ticket", "CH-19046")]
    public async Task GIVEN_Product_WHEN_EvaluateLegacyPricing_With_CampaignCodes_THEN_ShouldQueryCampaignCodes()
    {
        // Arrange (Given)
        var fixture = new Fixture();
        var tenantId = "tenant1";
        var clientId = "client1";
        Product product = fixture.Create<Product>();
        product.ScriptIds = ["pricingScript"];
        Script script = fixture.Create<Script>();
        script.Id = "pricingScript";
        script.Type = ScriptTypeEnum.Pricing;
        IReadOnlyList<ICampaigns_Campaigns_Items> campaigns = [new Campaigns_Campaigns_Items_Campaign("campaignId1", new Campaigns_Campaigns_Items_ProductID_ProductId("plan", "version", "type"), "productName", "saleChannelID", false, [], DateTime.Parse("2023-01-01"), DateTime.Parse("2023-12-31"), "campaignCode1", 0, PremiumCalculationType.Gross)];

        EvaluateProductScriptCommand command = new()
        {
            ProductId = product.Id,
            ScriptType = ScriptTypeEnum.Pricing,
            DistributorID = "distributorId",
            CampaignCodes = ["campaignCode1"],
            DataInput = """{"policy":{"startDate":"2023-01-01", "endDate":"2023-12-31"}}""",
        };
        var cancellationToken = new CancellationToken(false);

        MockProduct(tenantId, clientId, product, cancellationToken);
        MockScript(tenantId, script, cancellationToken);
        MockDiscountCodes(tenantId, [], cancellationToken);
        MockReference(rtDefaultTax, cancellationToken);
        MockCommissions([], cancellationToken);
        MockCampaigns(campaigns, cancellationToken);
        var captured = MockEvaluate(tenantId, cancellationToken);

        // Act (When)
        await _scriptEvaluationService.Evaluate(tenantId, clientId, command, cancellationToken);

        // Assert (Then)
        captured.PricingCommand.Campaigns[0].CampaignID.Should().Be("campaignId1");
    }


    const string vatUAE = """[{"id":"VAT-UAE-10","name":"Value Added Tax UAE @ 10%","type":"factor","value": 0.1}]""";
    const string gstIndia = """[{"id":"GST-INDIA-18","name":"Goods and Services Tax @ 18%","type":"factor","value": 0.18}]""";
    readonly ReferenceTypes_ReferenceTypes_Items_ReferenceTypeState rtCustomTax = new ReferenceTypes_ReferenceTypes_Items_ReferenceTypeState(
        "X-FIN-CUSTOMTAX", DateTime.Parse("9999-12-31"), [], string.Empty, """{}""", DateTime.Parse("2000-01-01"), DateTime.Parse("2000-01-01"), [
            new ReferenceTypes_ReferenceTypes_Items_ReferenceData_ReferenceData("GST-INDIA-18", string.Empty, [], [], gstIndia,DateTime.Parse("2000-01-01"),DateTime.Parse("9999-12-31")),
                new ReferenceTypes_ReferenceTypes_Items_ReferenceData_ReferenceData ("VAT-UAE-10", string.Empty, [], [], vatUAE, DateTime.Parse("2000-01-01"), DateTime.Parse("9999-12-31")),
        ]);
    readonly ReferenceTypes_ReferenceTypes_Items_ReferenceTypeState rtDefaultTax = new ReferenceTypes_ReferenceTypes_Items_ReferenceTypeState(
        "X-FIN-TAXRATE", DateTime.Parse("9999-12-31"), [], string.Empty, """{}""", DateTime.Parse("2000-01-01"), DateTime.Parse("2000-01-01"), [
            new ReferenceTypes_ReferenceTypes_Items_ReferenceData_ReferenceData("IND-HEALTH", string.Empty, [], [], """[{"id":"individual_tax","name":"Individual tax","type":"factor","value": 0}]""",DateTime.Parse("2000-01-01"),DateTime.Parse("9999-12-31")),
                new ReferenceTypes_ReferenceTypes_Items_ReferenceData_ReferenceData ("GRP-HEALTH", string.Empty, [], [], """[{"id":"group_tax","name":"Group tax","type":"factor","value": 0}]""", DateTime.Parse("2000-01-01"), DateTime.Parse("9999-12-31")),
        ]);

    void MockProduct(string tenantId, string clientId, Product product, CancellationToken cancellationToken)
    {
        _mockProductService
            .Setup(x => x.GetAsync(tenantId, clientId, It.IsAny<ProductWhere>(), It.IsAny<QueryParameters>(), true, cancellationToken))
            .ReturnsAsync([product]);
    }
    void MockScript(string tenantId, Script script, CancellationToken cancellationToken) => MockScript(tenantId, [script], cancellationToken);
    void MockScript(string tenantId, IReadOnlyCollection<Script> scripts, CancellationToken cancellationToken) => _mockScriptRepository
            .Setup(x => x.GetAsync(tenantId, It.IsAny<ScriptWhere>(), cancellationToken))
            .ReturnsAsync(scripts);
    void MockCommissions(IReadOnlyList<ICommissionCandidates_CommissionCandidates> commissions, CancellationToken cancellationToken) => _mockChannelManagementAdapter
            .Setup(x => x.GetCommissionCandidates(It.IsAny<ChannelManagement.Client.CommissionCandidatesInput>(), cancellationToken))
            .ReturnsAsync(commissions);
    void MockCampaigns(IReadOnlyList<ICampaigns_Campaigns_Items> campaigns, CancellationToken cancellationToken) => _mockChannelManagementAdapter
            .Setup(x => x.GetCampaigns(It.IsAny<ChannelManagement.Client.CampaignFilterInput>(), It.IsAny<int>(), cancellationToken))
            .ReturnsAsync(campaigns);
    void MockDiscountCodes(string tenantId, IReadOnlyCollection<Products.Domain.DiscountCodes.DiscountCode> discountCodes, CancellationToken cancellationToken) => _mockDiscountCodeService
            .Setup(x => x.QueryAsync(tenantId, It.IsAny<DomainUtils.QueryArguments<DomainUtils.Filter<Products.Domain.DiscountCodes.DiscountCodeFilter>>>(), cancellationToken))
            .ReturnsAsync(discountCodes);
    CapturedVariables MockReference(ReferenceTypes_ReferenceTypes_Items_ReferenceTypeState rt, CancellationToken cancellationToken)
    {
        var captured = new CapturedVariables();
        _mockReferenceGraphQlClient
            .Setup(x => x.ReferenceTypes.ExecuteAsync(It.IsAny<ReferenceTypeFilterInput>(), 1, 0, null, cancellationToken))
            .Callback((ReferenceTypeFilterInput? filter, int? take, int? skip, IReadOnlyList<ReferenceTypeSortInput>? order, CancellationToken ct) => captured.ReferenceMnemonic = filter!.ReferenceMnemonics[0]!)
            .ReturnsAsync(new OperationResult<IReferenceTypesResult>(
                new ReferenceTypesResult(new ReferenceTypes_ReferenceTypes_ReferenceTypeStateCollectionSegment([rt], new ReferenceTypes_ReferenceTypes_PageInfo_CollectionSegmentInfo(false, false), 1)),
                null, null, null, null));
        return captured;
    }
    CapturedVariables MockEvaluate(string tenantId, CancellationToken cancellationToken)
    {
        var captured = new CapturedVariables();
        _mockScriptAdapter.Setup(x => x.Evaluate(tenantId, It.IsAny<EvaluateScriptCommand>(), cancellationToken))
            .Callback((string tenantId, EvaluateScriptCommand pCommand, CancellationToken ct) => captured.PricingCommand = pCommand)
            .ReturnsAsync(new ScriptResult());
        return captured;
    }
    CapturedVariables MockEvaluateStandardPricing(string tenantId, CancellationToken cancellationToken)
    {
        var captured = new CapturedVariables();
        _mockScriptAdapter.Setup(x => x.EvaluateStandardPricing(tenantId, It.IsAny<StandardPricingCommand>(), cancellationToken))
            .Callback((string tenantId, StandardPricingCommand spCommand, CancellationToken ct) => captured.StandardPricingCommand = spCommand)
            .ReturnsAsync(new PricingResult());
        return captured;
    }

    class CapturedVariables
    {
        public string ReferenceMnemonic { get; set; } = string.Empty;
        public EvaluateScriptCommand? PricingCommand { get; set; }
        public StandardPricingCommand? StandardPricingCommand { get; set; }
    }
}
