using System;
using System.Collections.Generic;

using CoverGo.Products.Domain.Products;
using CoverGo.Products.Infrastructure.Products.Adapters;

namespace CoverGo.Products.Tests
{
    public class ProductPackageExtensionsTests
    {
        public static IEnumerable<object[]> GetProductPackages()
        {
            yield return new object[] {
                new ProductPackage { Metadata = new ProductMetadata { ProductId = new ProductId { Plan = Guid.NewGuid().ToString() } } },
                new ProductPackage { Metadata = new ProductMetadata { ProductId = new ProductId { Plan = Guid.NewGuid().ToString() } } }
            };

            yield return new object[] {
                new ProductPackage { Metadata = new ProductMetadata { ProductId = new ProductId { Type = Guid.NewGuid().ToString() } } },
                new ProductPackage { Metadata = new ProductMetadata { ProductId = new ProductId { Type = Guid.NewGuid().ToString() } } }
            };

            yield return new object[] {
                new ProductPackage { Metadata = new ProductMetadata { ProductId = new ProductId { Version = Guid.NewGuid().ToString() } } },
                new ProductPackage { Metadata = new ProductMetadata { ProductId = new ProductId { Version = Guid.NewGuid().ToString() } } }
            };

            yield return new object[] {
                new ProductPackage { Metadata = new ProductMetadata { ProductLifecycleStage = Guid.NewGuid().ToString() } },
                new ProductPackage { Metadata = new ProductMetadata { ProductLifecycleStage = Guid.NewGuid().ToString() } }
            };

            yield return new object[] {
                new ProductPackage { Metadata = new ProductMetadata { ProductLastModifiedAt = DateTime.MinValue } },
                new ProductPackage { Metadata = new ProductMetadata { ProductLastModifiedAt = DateTime.MaxValue } }
            };

            yield return new object[] {
                new ProductPackage { Metadata = new ProductMetadata { ProductLastModifiedBy = Guid.NewGuid().ToString() } },
                new ProductPackage { Metadata = new ProductMetadata { ProductLastModifiedBy = Guid.NewGuid().ToString() } }
            };

            yield return new object[] {
                new ProductPackage { Metadata = new ProductMetadata { Tenant = Guid.NewGuid().ToString() } },
                new ProductPackage { Metadata = new ProductMetadata { Tenant = Guid.NewGuid().ToString() } }
            };

            yield return new object[] {
                new ProductPackage { Metadata = new ProductMetadata { ExportedAt = DateTime.MinValue } },
                new ProductPackage { Metadata = new ProductMetadata { ExportedAt = DateTime.MaxValue } }
            };

            yield return new object[] {
                new ProductPackage { Metadata = new ProductMetadata { ExportedBy = Guid.NewGuid().ToString() } },
                new ProductPackage { Metadata = new ProductMetadata { ExportedBy = Guid.NewGuid().ToString() } }
            };

            yield return new object[] {
                new ProductPackage { Metadata = new ProductMetadata { IncludedEntries = new[] { Guid.NewGuid().ToString() } } },
                new ProductPackage { Metadata = new ProductMetadata { IncludedEntries = new[] { Guid.NewGuid().ToString() } } }
            };

            yield return new object[] {
                new ProductPackage { ProductTreeContent = Guid.NewGuid().ToString() },
                new ProductPackage { ProductTreeContent = Guid.NewGuid().ToString() }
            };

            yield return new object[] {
                new ProductPackage { DataSchemaContent = Guid.NewGuid().ToString() },
                new ProductPackage { DataSchemaContent = Guid.NewGuid().ToString() }
            };

            yield return new object[] {
                new ProductPackage { UISchemaContent = Guid.NewGuid().ToString() },
                new ProductPackage { UISchemaContent = Guid.NewGuid().ToString() }
            };

            yield return new object[] {
                new ProductPackage { ValidationScriptContent = Guid.NewGuid().ToString() },
                new ProductPackage { ValidationScriptContent = Guid.NewGuid().ToString() }
            };
        }

        [Theory]
        [MemberData(nameof(GetProductPackages))]
        public void GIVEN_two_product_packages_with_different_properties_WHEN_getting_their_checksums_THEN_different_checksums_returned(ProductPackage productPackage1, ProductPackage productPackage2)
        {
            string checksum1 = productPackage1.GetChecksum();
            string checksum2 = productPackage2.GetChecksum();

            checksum1.Should().NotBe(checksum2);
        }
    }
}