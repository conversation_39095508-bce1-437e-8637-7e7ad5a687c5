﻿using AutoFixture;
using CoverGo.DomainUtils;
using CoverGo.FeatureManagement;
using CoverGo.Products.Domain.Auth;
using CoverGo.Products.Domain.Illustrations;
using CoverGo.Products.Domain.ProductImportHistory;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.Ports;
using CoverGo.Products.Domain.Underwriting;
using JsonLogic.Net;
using MediatR;
using Microsoft.Extensions.Logging;
using Moq;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Tests
{
    public class ProductExchangeServiceTests
    {
        private readonly Mock<ProductService> _mockProductService;
        private readonly Mock<IProductL10NAdapter> _mockProductL10NAdapter;
        private readonly Mock<IAuthAdapter> _mockAuthAdapter;
        private readonly Mock<IProductBuilderAdapter> _mockProductBuilderAdapter;
        private readonly Mock<IProductExchangeAdapter> _mockProductExchangeAdapter;
        private readonly Mock<IExternalTablesAdapter> _mockExternalTablesAdapter;
        private readonly Mock<IProductImportHistoryService> _mockProductImportHistoryService;
        private readonly Mock<ILogger<ProductExchangeService>> _mockLogger;
        private readonly ProductExchangeService _productExchangeService;

        public ProductExchangeServiceTests()
        {
            _mockProductService = new Mock<ProductService>(
            new Mock<ProductEventService>(
                new Mock<IProductEventStore>().Object
                ).Object,
            new Mock<IEnumerable<IIllustrationRepository>>().Object,
            new Mock<IEnumerable<IUnderwritingEngine>>().Object,
            new Mock<IMultiTenantFeatureManager>().Object,
            new Mock<IProductScriptsRepository>().Object,
            new Mock<IProductVersionRepository>().Object,
            new Mock<IProductRepository>().Object,
            new Mock<IReferenceGenerator>().Object,
            new Mock<JsonLogicEvaluator>(
                new Mock<IManageOperators>().Object
                ).Object,
            Mock.Of<IProductNameRepository>(),
            Mock.Of<IMediator>()
            );
            _mockProductL10NAdapter = new Mock<IProductL10NAdapter>();
            _mockAuthAdapter = new Mock<IAuthAdapter>();
            _mockProductBuilderAdapter = new Mock<IProductBuilderAdapter>();
            _mockProductExchangeAdapter = new Mock<IProductExchangeAdapter>();
            _mockExternalTablesAdapter = new Mock<IExternalTablesAdapter>();
            _mockProductImportHistoryService = new Mock<IProductImportHistoryService>();
            _mockLogger = new Mock<ILogger<ProductExchangeService>>();

            _productExchangeService = new ProductExchangeService(
                _mockProductService.Object,
                _mockProductL10NAdapter.Object,
                _mockAuthAdapter.Object,
                _mockProductBuilderAdapter.Object,
                _mockProductExchangeAdapter.Object,
                _mockExternalTablesAdapter.Object,
                _mockProductImportHistoryService.Object,
                _mockLogger.Object
            );
        }

        [Fact]
        public async Task GIVEN_ProductId_WHEN_CloneProductProductTree_THEN_Product_Cloned_Successfully()
        {
            // Arrange (Given)
            var fixture = new Fixture();
            var tenantId = "tenant1";
            var clientId = "client1";
            var loginId = "user1";
            var productTreeId = "tree1";
            var productId = fixture.Create<ProductId>();
            var cloneProductId = fixture.Create<ProductId>();
            var name = fixture.Create<ProductName>();
            var lifeCycleStage = "Draft";
            var cancellationToken = CancellationToken.None;

            var productSchema = new ProductSchema { UISchemas = new UISchema[] { new UISchema() } };
            var cloneProductTreeIdResult = Result<string>.Success("clonedTree1");

            _mockProductBuilderAdapter
                .Setup(x => x.GetProductSchema(productTreeId, cancellationToken))
                .ReturnsAsync(Result<ProductSchema>.Success(productSchema));

            _mockProductBuilderAdapter
                .Setup(x => x.CloneProductTree(productTreeId, cancellationToken))
                .ReturnsAsync(cloneProductTreeIdResult);

            _mockProductService
                .Setup(x => x.CloneAsync(tenantId, It.IsAny<CloneProductCommand>(), cancellationToken))
                .ReturnsAsync(Result.Success());

            _mockProductService.Setup(x => x.GetAsync(
            tenantId,
            null,
            It.Is<ProductWhere>(f => f.Id_in.Contains(productId)),
            null,
            It.Is<QueryParameters>(q => q.Limit == 1),
            true,
            cancellationToken))
        .ReturnsAsync(new List<Product> { new Product() { ProductTreeId = productTreeId } });

            _mockProductL10NAdapter
                .Setup(x => x.UpsertAsync(tenantId, name.Locale, It.IsAny<string>(), name.Value, cancellationToken))
                .ReturnsAsync(Result.Success());

            _mockProductBuilderAdapter
                .Setup(x => x.CreateProductSchema(cloneProductTreeIdResult.Value, productSchema, cancellationToken))
                .ReturnsAsync(Result<CreateProductSchema>.Success(new CreateProductSchema()));

            _mockProductBuilderAdapter
                .Setup(x => x.AddUiToProductSchema(cloneProductTreeIdResult.Value, productSchema.UISchemas.FirstOrDefault(), cancellationToken))
                .ReturnsAsync(Result.Success());

            _mockProductService
                .Setup(x => x.UpdateAsync(tenantId, It.IsAny<UpdateProductCommand>(), cancellationToken))
                .ReturnsAsync(Result.Success());

            // Act (When)
            var result = await _productExchangeService.CloneProductTreeAsync(
                tenantId, clientId, loginId, productId, name, cloneProductId, lifeCycleStage, cancellationToken);

            // Assert (Then)
            result.IsSuccess.Should().BeTrue();
        }

        [Fact]
        public async Task GIVEN_ProductId_WHEN_CloneProductTreeAsync_Failure_THEN_CloneProductTree_Failed()
        {
            // Arrange (Given)
            var fixture = new Fixture();
            var tenantId = "tenant1";
            var clientId = "client1";
            var loginId = "user1";
            var productTreeId = "tree1";
            var productId = fixture.Create<ProductId>();
            var cloneProductId = fixture.Create<ProductId>();
            var name = fixture.Create<ProductName>();
            var lifeCycleStage = "Draft";
            var cancellationToken = CancellationToken.None;

            var cloneProductTreeIdResult = Result<string>.Failure("Failed to clone product tree");

            _mockProductBuilderAdapter
                .Setup(x => x.CloneProductTree(productTreeId, cancellationToken))
                .ReturnsAsync(cloneProductTreeIdResult);

            _mockProductService.Setup(x => x.GetAsync(
            tenantId,
            null,
            It.Is<ProductWhere>(f => f.Id_in.Contains(productId)),
            null,
            It.Is<QueryParameters>(q => q.Limit == 1),
            true,
            cancellationToken))
        .ReturnsAsync(new List<Product> { new Product() { ProductTreeId = productTreeId } });

            // Act (When)
            var result = await _productExchangeService.CloneProductTreeAsync(
                tenantId, clientId, loginId, productId, name, cloneProductId, lifeCycleStage, cancellationToken);

            // Assert (Then)
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("Failed to clone product tree");
        }

        [Fact]
        public async Task GIVEN_ProductName_WHEN_Update_ProductName_Failure_THEN_UpdateProductName_Failed()
        {
            // Arrange (Given)
            var fixture = new Fixture();
            var tenantId = "tenant1";
            var clientId = "client1";
            var loginId = "user1";
            var productTreeId = "tree1";
            var productId = fixture.Create<ProductId>();
            var cloneProductId = fixture.Create<ProductId>();
            var name = fixture.Create<ProductName>();
            var lifeCycleStage = "Draft";
            var cancellationToken = CancellationToken.None;

            var productSchema = new ProductSchema { UISchemas = new UISchema[] { new UISchema() } };
            var cloneProductTreeIdResult = Result<string>.Success("clonedTree1");

            _mockProductBuilderAdapter
                .Setup(x => x.GetProductSchema(productTreeId, cancellationToken))
                .ReturnsAsync(Result<ProductSchema>.Success(productSchema));

            _mockProductBuilderAdapter
                .Setup(x => x.CloneProductTree(productTreeId, cancellationToken))
                .ReturnsAsync(cloneProductTreeIdResult);

            _mockProductService.Setup(x => x.GetAsync(
            tenantId,
            null,
            It.Is<ProductWhere>(f => f.Id_in.Contains(productId)),
            null,
            It.Is<QueryParameters>(q => q.Limit == 1),
            true,
            cancellationToken))
        .ReturnsAsync(new List<Product> { new Product() { ProductTreeId = productTreeId } });

            _mockProductService
                .Setup(x => x.CloneAsync(tenantId, It.IsAny<CloneProductCommand>(), cancellationToken))
                .ReturnsAsync(Result.Success());

            _mockProductL10NAdapter
                .Setup(x => x.UpsertAsync(tenantId, name.Locale, It.IsAny<string>(), name.Value, cancellationToken))
                .ReturnsAsync(Result.Failure("Failed to update product name"));

            // Act (When)
            var result = await _productExchangeService.CloneProductTreeAsync(
                tenantId, clientId, loginId, productId, name, cloneProductId, lifeCycleStage, cancellationToken);

            // Assert (Then)
            Assert.False(result.IsSuccess);
            Assert.Equal("Failed to update product name", result.Errors.FirstOrDefault());
        }

        [Fact]
        [Trait("Ticket", "PRODSUPP-1929")]
        public async Task GIVEN_ProductWithUISchema_WHEN_CloneProductTreeAsync_THEN_UISchemaIsClonedWithCorrectProperties()
        {
            // Arrange (Given)
            var fixture = new Fixture();
            var tenantId = "tenant1";
            var clientId = "client1";
            var loginId = "user1";
            var productTreeId = "tree1";
            var cloneProductTreeId = "clonedTree1";
            var productId = fixture.Create<ProductId>();
            var cloneProductId = fixture.Create<ProductId>();
            var name = fixture.Create<ProductName>();
            var lifeCycleStage = "Draft";
            var cancellationToken = CancellationToken.None;
            var originalSchemaContent = "{\"type\": \"object\", \"properties\": {\"name\": {\"type\": \"string\"}}}";
            var productSchemaId = "schema123";

            var originalUISchema = new UISchema
            {
                Name = "originalSchemaName",
                Schema = originalSchemaContent
            };

            var productSchema = new ProductSchema
            {
                UISchemas = new UISchema[] { originalUISchema }
            };

            var cloneProductTreeIdResult = Result<string>.Success(cloneProductTreeId);
            var createProductSchemaResult = Result<CreateProductSchema>.Success(new CreateProductSchema { Value = productSchemaId });

            SetupSuccessfulClonePrerequisites(tenantId, clientId, loginId, productTreeId, productId, cloneProductId, name, lifeCycleStage, cancellationToken, productSchema, cloneProductTreeIdResult, createProductSchemaResult);

            UISchema capturedUISchema = null;
            _mockProductBuilderAdapter
                .Setup(x => x.AddUiToProductSchema(productSchemaId, It.IsAny<UISchema>(), cancellationToken))
                .Callback<string, UISchema, CancellationToken>((_, uiSchema, _) => capturedUISchema = uiSchema)
                .ReturnsAsync(Result.Success());

            // Act (When)
            var result = await _productExchangeService.CloneProductTreeAsync(
                tenantId, clientId, loginId, productId, name, cloneProductId, lifeCycleStage, cancellationToken);

            // Assert (Then)
            result.IsSuccess.Should().BeTrue();
            capturedUISchema.Should().NotBeNull();
            capturedUISchema.Name.Should().Be(cloneProductTreeId);
            capturedUISchema.Schema.Should().Be(originalSchemaContent);
            
            // Verify the adapter was called with the correct parameters
            _mockProductBuilderAdapter.Verify(x => x.AddUiToProductSchema(
                productSchemaId,
                It.Is<UISchema>(ui => ui.Name == cloneProductTreeId && ui.Schema == originalSchemaContent),
                cancellationToken), Times.Once);
        }

        [Fact]
        [Trait("Ticket", "PRODSUPP-1929")]
        public async Task GIVEN_ProductWithoutUISchema_WHEN_CloneProductTreeAsync_THEN_AddUiToProductSchemaNotCalled()
        {
            // Arrange (Given)
            var fixture = new Fixture();
            var tenantId = "tenant1";
            var clientId = "client1";
            var loginId = "user1";
            var productTreeId = "tree1";
            var cloneProductTreeId = "clonedTree1";
            var productId = fixture.Create<ProductId>();
            var cloneProductId = fixture.Create<ProductId>();
            var name = fixture.Create<ProductName>();
            var lifeCycleStage = "Draft";
            var cancellationToken = CancellationToken.None;
            var productSchemaId = "schema123";

            var productSchema = new ProductSchema
            {
                UISchemas = new UISchema[] { } // Empty array
            };

            var cloneProductTreeIdResult = Result<string>.Success(cloneProductTreeId);
            var createProductSchemaResult = Result<CreateProductSchema>.Success(new CreateProductSchema { Value = productSchemaId });

            SetupSuccessfulClonePrerequisites(tenantId, clientId, loginId, productTreeId, productId, cloneProductId, name, lifeCycleStage, cancellationToken, productSchema, cloneProductTreeIdResult, createProductSchemaResult);

            // Act (When)
            var result = await _productExchangeService.CloneProductTreeAsync(
                tenantId, clientId, loginId, productId, name, cloneProductId, lifeCycleStage, cancellationToken);

            // Assert (Then)
            result.IsSuccess.Should().BeTrue();
            
            // Verify AddUiToProductSchema was never called since there's no UI schema
            _mockProductBuilderAdapter.Verify(x => x.AddUiToProductSchema(
                It.IsAny<string>(),
                It.IsAny<UISchema>(),
                It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        [Trait("Ticket", "PRODSUPP-1929")]
        public async Task GIVEN_ProductWithNullUISchemas_WHEN_CloneProductTreeAsync_THEN_AddUiToProductSchemaNotCalled()
        {
            // Arrange (Given)
            var fixture = new Fixture();
            var tenantId = "tenant1";
            var clientId = "client1";
            var loginId = "user1";
            var productTreeId = "tree1";
            var cloneProductTreeId = "clonedTree1";
            var productId = fixture.Create<ProductId>();
            var cloneProductId = fixture.Create<ProductId>();
            var name = fixture.Create<ProductName>();
            var lifeCycleStage = "Draft";
            var cancellationToken = CancellationToken.None;
            var productSchemaId = "schema123";

            var productSchema = new ProductSchema
            {
                UISchemas = null // Null array
            };

            var cloneProductTreeIdResult = Result<string>.Success(cloneProductTreeId);
            var createProductSchemaResult = Result<CreateProductSchema>.Success(new CreateProductSchema { Value = productSchemaId });

            SetupSuccessfulClonePrerequisites(tenantId, clientId, loginId, productTreeId, productId, cloneProductId, name, lifeCycleStage, cancellationToken, productSchema, cloneProductTreeIdResult, createProductSchemaResult);

            // Act (When)
            var result = await _productExchangeService.CloneProductTreeAsync(
                tenantId, clientId, loginId, productId, name, cloneProductId, lifeCycleStage, cancellationToken);

            // Assert (Then)
            result.IsSuccess.Should().BeTrue();
            
            // Verify AddUiToProductSchema was never called since UISchemas is null
            _mockProductBuilderAdapter.Verify(x => x.AddUiToProductSchema(
                It.IsAny<string>(),
                It.IsAny<UISchema>(),
                It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        [Trait("Ticket", "PRODSUPP-1929")]
        public async Task GIVEN_ProductWithUISchema_WHEN_AddUiToProductSchemaFails_THEN_CloneProductTreeAsyncFails()
        {
            // Arrange (Given)
            var fixture = new Fixture();
            var tenantId = "tenant1";
            var clientId = "client1";
            var loginId = "user1";
            var productTreeId = "tree1";
            var cloneProductTreeId = "clonedTree1";
            var productId = fixture.Create<ProductId>();
            var cloneProductId = fixture.Create<ProductId>();
            var name = fixture.Create<ProductName>();
            var lifeCycleStage = "Draft";
            var cancellationToken = CancellationToken.None;
            var originalSchemaContent = "{\"type\": \"object\", \"properties\": {\"name\": {\"type\": \"string\"}}}";
            var productSchemaId = "schema123";
            var expectedErrorMessage = "Failed to add UI schema to product schema";

            var originalUISchema = new UISchema
            {
                Name = "originalSchemaName",
                Schema = originalSchemaContent
            };

            var productSchema = new ProductSchema
            {
                UISchemas = new UISchema[] { originalUISchema }
            };

            var cloneProductTreeIdResult = Result<string>.Success(cloneProductTreeId);
            var createProductSchemaResult = Result<CreateProductSchema>.Success(new CreateProductSchema { Value = productSchemaId });

            SetupSuccessfulClonePrerequisites(tenantId, clientId, loginId, productTreeId, productId, cloneProductId, name, lifeCycleStage, cancellationToken, productSchema, cloneProductTreeIdResult, createProductSchemaResult);

            _mockProductBuilderAdapter
                .Setup(x => x.AddUiToProductSchema(productSchemaId, It.IsAny<UISchema>(), cancellationToken))
                .ReturnsAsync(Result.Failure(expectedErrorMessage));

            // Act (When)
            var result = await _productExchangeService.CloneProductTreeAsync(
                tenantId, clientId, loginId, productId, name, cloneProductId, lifeCycleStage, cancellationToken);

            // Assert (Then)
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain(expectedErrorMessage);
            
            // Verify the operation was attempted
            _mockProductBuilderAdapter.Verify(x => x.AddUiToProductSchema(
                productSchemaId,
                It.Is<UISchema>(ui => ui.Name == cloneProductTreeId && ui.Schema == originalSchemaContent),
                cancellationToken), Times.Once);
        }

        [Fact]
        [Trait("Ticket", "PRODSUPP-1929")]
        public async Task GIVEN_ProductWithMultipleUISchemas_WHEN_CloneProductTreeAsync_THEN_OnlyFirstUISchemaIsCloned()
        {
            // Arrange (Given)
            var fixture = new Fixture();
            var tenantId = "tenant1";
            var clientId = "client1";
            var loginId = "user1";
            var productTreeId = "tree1";
            var cloneProductTreeId = "clonedTree1";
            var productId = fixture.Create<ProductId>();
            var cloneProductId = fixture.Create<ProductId>();
            var name = fixture.Create<ProductName>();
            var lifeCycleStage = "Draft";
            var cancellationToken = CancellationToken.None;
            var firstSchemaContent = "{\"type\": \"object\", \"properties\": {\"firstName\": {\"type\": \"string\"}}}";
            var secondSchemaContent = "{\"type\": \"object\", \"properties\": {\"lastName\": {\"type\": \"string\"}}}";
            var productSchemaId = "schema123";

            var firstUISchema = new UISchema
            {
                Name = "firstSchemaName",
                Schema = firstSchemaContent
            };

            var secondUISchema = new UISchema
            {
                Name = "secondSchemaName",
                Schema = secondSchemaContent
            };

            var productSchema = new ProductSchema
            {
                UISchemas = new UISchema[] { firstUISchema, secondUISchema }
            };

            var cloneProductTreeIdResult = Result<string>.Success(cloneProductTreeId);
            var createProductSchemaResult = Result<CreateProductSchema>.Success(new CreateProductSchema { Value = productSchemaId });

            SetupSuccessfulClonePrerequisites(tenantId, clientId, loginId, productTreeId, productId, cloneProductId, name, lifeCycleStage, cancellationToken, productSchema, cloneProductTreeIdResult, createProductSchemaResult);

            UISchema capturedUISchema = null;
            _mockProductBuilderAdapter
                .Setup(x => x.AddUiToProductSchema(productSchemaId, It.IsAny<UISchema>(), cancellationToken))
                .Callback<string, UISchema, CancellationToken>((_, uiSchema, _) => capturedUISchema = uiSchema)
                .ReturnsAsync(Result.Success());

            // Act (When)
            var result = await _productExchangeService.CloneProductTreeAsync(
                tenantId, clientId, loginId, productId, name, cloneProductId, lifeCycleStage, cancellationToken);

            // Assert (Then)
            result.IsSuccess.Should().BeTrue();
            capturedUISchema.Should().NotBeNull();
            capturedUISchema.Name.Should().Be(cloneProductTreeId);
            capturedUISchema.Schema.Should().Be(firstSchemaContent); // Should be the first schema, not the second
            
            // Verify AddUiToProductSchema was called only once with the first schema
            _mockProductBuilderAdapter.Verify(x => x.AddUiToProductSchema(
                productSchemaId,
                It.Is<UISchema>(ui => ui.Name == cloneProductTreeId && ui.Schema == firstSchemaContent),
                cancellationToken), Times.Once);
        }

        [Fact]
        [Trait("Ticket", "PRODSUPP-1929")]
        public async Task GIVEN_ProductWithEmptyStringSchemaValue_WHEN_CreateProductSchemaReturnsEmptyValue_THEN_AddUiToProductSchemaNotCalled()
        {
            // Arrange (Given)
            var fixture = new Fixture();
            var tenantId = "tenant1";
            var clientId = "client1";
            var loginId = "user1";
            var productTreeId = "tree1";
            var cloneProductTreeId = "clonedTree1";
            var productId = fixture.Create<ProductId>();
            var cloneProductId = fixture.Create<ProductId>();
            var name = fixture.Create<ProductName>();
            var lifeCycleStage = "Draft";
            var cancellationToken = CancellationToken.None;
            var originalSchemaContent = "{\"type\": \"object\", \"properties\": {\"name\": {\"type\": \"string\"}}}";

            var originalUISchema = new UISchema
            {
                Name = "originalSchemaName",
                Schema = originalSchemaContent
            };

            var productSchema = new ProductSchema
            {
                UISchemas = new UISchema[] { originalUISchema }
            };

            var cloneProductTreeIdResult = Result<string>.Success(cloneProductTreeId);
            var createProductSchemaResult = Result<CreateProductSchema>.Success(new CreateProductSchema { Value = "" }); // Empty string

            SetupSuccessfulClonePrerequisites(tenantId, clientId, loginId, productTreeId, productId, cloneProductId, name, lifeCycleStage, cancellationToken, productSchema, cloneProductTreeIdResult, createProductSchemaResult);

            // Act (When)
            var result = await _productExchangeService.CloneProductTreeAsync(
                tenantId, clientId, loginId, productId, name, cloneProductId, lifeCycleStage, cancellationToken);

            // Assert (Then)
            result.IsSuccess.Should().BeTrue();
            
            // Verify AddUiToProductSchema was never called since createProductSchemaResult.Value is empty
            _mockProductBuilderAdapter.Verify(x => x.AddUiToProductSchema(
                It.IsAny<string>(),
                It.IsAny<UISchema>(),
                It.IsAny<CancellationToken>()), Times.Never);
        }

        private void SetupSuccessfulClonePrerequisites(
            string tenantId, 
            string clientId, 
            string loginId, 
            string productTreeId, 
            ProductId productId, 
            ProductId cloneProductId, 
            ProductName name, 
            string lifeCycleStage, 
            CancellationToken cancellationToken, 
            ProductSchema productSchema, 
            Result<string> cloneProductTreeIdResult, 
            Result<CreateProductSchema> createProductSchemaResult)
        {
            _mockProductBuilderAdapter
                .Setup(x => x.GetProductSchema(productTreeId, cancellationToken))
                .ReturnsAsync(Result<ProductSchema>.Success(productSchema));

            _mockProductBuilderAdapter
                .Setup(x => x.CloneProductTree(productTreeId, cancellationToken))
                .ReturnsAsync(cloneProductTreeIdResult);

            _mockProductService
                .Setup(x => x.CloneAsync(tenantId, It.IsAny<CloneProductCommand>(), cancellationToken))
                .ReturnsAsync(Result.Success());

            _mockProductService.Setup(x => x.GetAsync(
                tenantId,
                null,
                It.Is<ProductWhere>(f => f.Id_in.Contains(productId)),
                null,
                It.Is<QueryParameters>(q => q.Limit == 1),
                true,
                cancellationToken))
                .ReturnsAsync(new List<Product> { new Product() { ProductTreeId = productTreeId } });

            _mockProductService.Setup(x => x.GetAsync(
                tenantId,
                null,
                It.Is<ProductWhere>(f => f.Id_in.Contains(cloneProductId)),
                null,
                It.Is<QueryParameters>(q => q.Limit == 1),
                true,
                cancellationToken))
                .ReturnsAsync(new List<Product> { new Product() { ProductTreeId = cloneProductTreeIdResult.Value } });

            _mockProductL10NAdapter
                .Setup(x => x.UpsertAsync(tenantId, name.Locale, It.IsAny<string>(), name.Value, cancellationToken))
                .ReturnsAsync(Result.Success());

            _mockProductBuilderAdapter
                .Setup(x => x.CreateProductSchema(cloneProductTreeIdResult.Value, productSchema, cancellationToken))
                .ReturnsAsync(createProductSchemaResult);

            _mockProductService
                .Setup(x => x.UpdateAsync(tenantId, It.IsAny<UpdateProductCommand>(), cancellationToken))
                .ReturnsAsync(Result.Success());
        }
    }
}
