{"properties": {}, "type": "object", "policyFields": {"description": "Policy Fields", "properties": {"startDate": {"type": "string", "meta": {"label": "Start Date", "component": "CDatePicker", "required": true, "fixed": true, "order": 1}}, "endDate": {"type": "string", "meta": {"label": "End Date", "component": "CDatePicker", "required": false, "fixed": true, "order": 2}}}}, "insuredsFields": {"properties": {"province": {"type": "string", "meta": {"label": "Where do you live?", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"key": "17180838465433759", "name": "Alberta", "value": "alberta"}, {"key": "171808379515725601", "name": "British Columbia", "value": "britishColumbia"}, {"key": "171808378786379907", "name": "Northwest Territories", "value": "northwestTerritories"}, {"key": "171808377619347216", "name": "Nova Scotia", "value": "novaScotia"}, {"key": "171808375898142266", "name": "Ontario", "value": "ontario"}, {"key": "171808374033820970", "name": "Prince Edward Island", "value": "princeEdwardIsland"}, {"key": "171808383369593892", "name": "Saskatchewan", "value": "saskatchewan"}, {"key": "17180249073351787", "name": "Yukon", "value": "yukon"}, {"key": "171808386451483204", "name": "Newfoundland and Labrador", "value": "newfoundlandAndLabrador"}, {"key": "171808391867675256", "name": "Manitoba", "value": "manitoba"}, {"key": "171808395728010013", "name": "Nunavut", "value": "nunavut"}], "isCustom": true}}, "numberOfInsureds": {"type": "number", "meta": {"label": "Number of Insureds", "component": "JInputNumber", "required": true, "order": 1, "fixed": true}}, "allIndividualsHaveProvincialHealthNumber": {"type": "boolean", "meta": {"label": "Do all individuals included under policy have provincial health number?", "component": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "boolean", "validations": ""}}, "coveredUnderTheCanadianDentalCarePlan": {"type": "boolean", "meta": {"label": "Are you covered under the Canadian Dental Care Plan?", "component": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "boolean", "validations": ""}}, "planSelected": {"type": "string", "meta": {"label": "Plan Selected", "component": "JSelect", "required": true, "options": [{"key": "5905638097146451", "name": "Default Plan", "value": "defaultPlan"}], "order": 1.5, "fixed": true}}, "healthCoverage": {"type": "string", "meta": {"label": "Health Coverage", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"key": "171810082638312747", "name": "Tier 1", "value": "healthTier1"}, {"key": "171810083429316807", "name": "Tier 2", "value": "healthTier2"}, {"key": "171810084109617833", "name": "Tier 3", "value": "healthTier3"}], "order": 22}}, "dentalCoverage": {"type": "string", "meta": {"label": "Dental Coverage", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"key": "171810095888287032", "name": "Tier 1", "value": "dentalTier1"}, {"key": "171810096979421298", "name": "Tier 2", "value": "dentalTier2"}, {"key": "171810097497815117", "name": "Tier 3", "value": "dentalTier3"}, {"key": "171810098011063661", "name": "Tier 4", "value": "dentalTier4"}]}}, "drugCoverage": {"type": "string", "meta": {"label": "Drug Coverage", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"key": "171810090521893454", "name": "Tier 1", "value": "drugTier1"}, {"key": "171810091415840115", "name": "Tier 2", "value": "drugTier2"}, {"key": "171810091952852717", "name": "Tier 3", "value": "drugTier3"}, {"key": "171810092559859688", "name": "Tier 4", "value": "drugTier4"}]}}, "hospitalCash": {"type": "string", "meta": {"label": "Hospital Cash", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"key": "171810051096646338", "name": "Yes", "value": "yes"}, {"key": "171810051540482396", "name": "No", "value": "no"}]}}, "additionalTravelCoverage": {"type": "string", "meta": {"label": "Additional Travel Coverage", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"key": "171809913513938376", "name": "15 Days", "value": "travelAddOn15Days"}, {"key": "171809916336155312", "name": "30 Days", "value": "travelAddOn30Days"}, {"key": "171809917530644306", "name": "48 Days", "value": "travelAddOn48Days"}]}}, "dateOfBirth": {"type": "string", "meta": {"label": "What is your date of birth?", "component": "CDatePicker", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "date", "validations": ""}}, "relationship": {"type": "string", "meta": {"label": "What is your relationship?", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"key": "172008168426569985", "name": "Spouse", "value": "spouse"}, {"key": "172008168977289793", "name": "Child", "value": "child"}]}}, "memberType": {"type": "string", "meta": {"label": "Member Type", "component": "JSelect", "required": true, "fieldType": "text", "options": [{"key": "167840377167826686", "name": "Employee", "value": "employee"}, {"key": "167840377878295915", "name": "Dependent", "value": "dependent"}], "order": 8, "validations": "", "propertyName": "memberType", "fixed": false}}}}, "memberCustomSchema": {"description": "Insureds Fields", "properties": {"firstApplicantAge": {"type": "number", "meta": {"label": "First Applicant Age", "component": "JInputNumber", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "number", "validations": "number", "condition": "$get(numberOfInsureds).value >= '1'", "numeric": true, "isCustom": true}}, "secondApplicantAge": {"type": "number", "meta": {"label": "Second Applicant Age", "component": "JInputNumber", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "number", "validations": "number", "condition": "$get(numberOfInsureds).value >= '2'", "numeric": true, "order": 6, "isCustom": true}}, "thirdApplicantAge": {"type": "number", "meta": {"label": "Third Applicant Age", "component": "JInputNumber", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "number", "validations": "number", "condition": "$get(numberOfInsureds).value >= '3'", "numeric": true, "isCustom": true}}, "fourthApplicantAge": {"type": "number", "meta": {"label": "Fourth Applicant Age", "component": "JInputNumber", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "number", "validations": "number", "condition": "$get(numberOfInsureds).value >= '4'", "numeric": true, "isCustom": true}}, "fifthApplicantAge": {"type": "number", "meta": {"label": "Fifth Applicant Age", "component": "JInputNumber", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "number", "validations": "number", "condition": "$get(numberOfInsureds).value >= '5'", "numeric": true, "isCustom": true}}, "province": {"type": "string", "meta": {"label": "Where do you live?", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"key": "17180838465433759", "name": "Alberta", "value": "alberta"}, {"key": "171808379515725601", "name": "British Columbia", "value": "britishColumbia"}, {"key": "171808378786379907", "name": "Northwest Territories", "value": "northwestTerritories"}, {"key": "171808377619347216", "name": "Nova Scotia", "value": "novaScotia"}, {"key": "171808375898142266", "name": "Ontario", "value": "ontario"}, {"key": "171808374033820970", "name": "Prince Edward Island", "value": "princeEdwardIsland"}, {"key": "171808383369593892", "name": "Saskatchewan", "value": "saskatchewan"}, {"key": "17180249073351787", "name": "Yukon", "value": "yukon"}, {"key": "171808386451483204", "name": "Newfoundland and Labrador", "value": "newfoundlandAndLabrador"}, {"key": "171808391867675256", "name": "Manitoba", "value": "manitoba"}, {"key": "171808395728010013", "name": "Nunavut", "value": "nunavut"}], "isCustom": true}}, "additionalTravelCoverage": {"type": "string", "meta": {"label": "Additional Travel Coverage", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"key": "171809913513938376", "name": "15 Days", "value": "travelAddOn15Days"}, {"key": "171809916336155312", "name": "30 Days", "value": "travelAddOn30Days"}, {"key": "171809917530644306", "name": "48 Days", "value": "travelAddOn48Days"}]}}, "hospitalCash": {"type": "string", "meta": {"label": "Hospital Cash", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"key": "171810051096646338", "name": "Yes", "value": "yes"}, {"key": "171810051540482396", "name": "No", "value": "no"}]}}, "healthCoverage": {"type": "string", "meta": {"label": "Health Coverage", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"key": "171810082638312747", "name": "Tier 1", "value": "healthTier1"}, {"key": "171810083429316807", "name": "Tier 2", "value": "healthTier2"}, {"key": "171810084109617833", "name": "Tier 3", "value": "healthTier3"}]}}, "drugCoverage": {"type": "string", "meta": {"label": "Drug Coverage", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"key": "171810090521893454", "name": "Tier 1", "value": "drugTier1"}, {"key": "171810091415840115", "name": "Tier 2", "value": "drugTier2"}, {"key": "171810091952852717", "name": "Tier 3", "value": "drugTier3"}, {"key": "171810092559859688", "name": "Tier 4", "value": "drugTier4"}]}}, "dentalCoverage": {"type": "string", "meta": {"label": "Dental Coverage", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"key": "171810095888287032", "name": "Tier 1", "value": "dentalTier1"}, {"key": "171810096979421298", "name": "Tier 2", "value": "dentalTier2"}, {"key": "171810097497815117", "name": "Tier 3", "value": "dentalTier3"}, {"key": "171810098011063661", "name": "Tier 4", "value": "dentalTier4"}]}}, "allIndividualsHaveProvincialHealthNumber": {"type": "boolean", "meta": {"label": "Do all individuals included under policy have provincial health number?", "component": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "boolean", "validations": ""}}, "coveredUnderTheCanadianDentalCarePlan": {"type": "boolean", "meta": {"label": "Are you covered under the Canadian Dental Care Plan?", "component": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "boolean", "validations": ""}}, "travelCoverage": {"type": "string", "meta": {"label": "Travel Coverage", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": ""}}, "travel": {"type": "string", "meta": {"label": "Travel", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": ""}}, "ageTest": {"type": "number", "meta": {"label": "AgeTest", "component": "JInputNumber", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "number", "validations": ""}}, "dateOfBirth": {"type": "string", "meta": {"label": "What is your date of birth?", "component": "CDatePicker", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "date", "validations": ""}}, "relationship": {"type": "string", "meta": {"label": "What is your relationship?", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"key": "172008168426569985", "name": "Spouse", "value": "spouse"}, {"key": "172008168977289793", "name": "Child", "value": "child"}]}}}}, "planFields_defaultPlan": {}}