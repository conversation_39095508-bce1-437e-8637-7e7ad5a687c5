<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="PlanBuilderTests_Limits.json" />
    <None Remove="PlanFieldsParserTests_DefaultPlanWithoutFields.json" />
    <None Remove="PlanFieldsParserTests_EmptyPlansSchema.json" />
    <None Remove="PlanFieldsParserTests_MultiplePlansSchema.json" />
    <None Remove="ProductSumAssured.json" />
    <None Remove="ProductSumAssuredSchema.json" />
    <None Remove="ProductSumAssuredSchema_WithAge.json" />
    <None Remove="ProductSumAssuredSchema_WithAgeAndEmpoyedStatus.json" />
    <None Remove="ProductSumAssuredSchema_WithEmployedStatus.json" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="PlanBuilderTests_Limits.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="PlanFieldsParserTests_DefaultPlanWithoutFields.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="PlanFieldsParserTests_EmptyPlansSchema.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="PlanFieldsParserTests_MultiplePlansSchema.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="ProductSumAssured.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="ProductSumAssuredSchema_WithAgeAndEmpoyedStatus.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="ProductSumAssuredSchema_WithEmployedStatus.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="ProductSumAssuredSchema_WithAge.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="ProductSumAssuredSchema.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoFixture" />
    <PackageReference Include="AutoFixture.AutoMoq" />
    <PackageReference Include="AutoFixture.Xunit2" />
    <PackageReference Include="Moq" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../../src/CoverGo.Products.Domain/CoverGo.Products.Domain.csproj" />
    <ProjectReference Include="..\..\src\CoverGo.Products.Application\CoverGo.Products.Application.csproj" />
    <ProjectReference Include="..\..\src\CoverGo.Products.Infrastructure\CoverGo.Products.Infrastructure.csproj" />
    <ProjectReference Include="..\..\src\CoverGo.Products.Tests.GatewayClient\CoverGo.Products.Tests.GatewayClient.csproj" />
  </ItemGroup>

</Project>
