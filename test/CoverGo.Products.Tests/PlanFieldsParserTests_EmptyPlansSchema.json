{"properties": {}, "type": "object", "policyFields": {"description": "Policy Fields", "properties": {"startDate": {"type": "string", "meta": {"label": "Start Date", "component": "CDatePicker", "required": true, "fixed": true, "order": 1}}, "endDate": {"type": "string", "meta": {"label": "End Date", "component": "CDatePicker", "required": false, "fixed": true, "order": 2}}}}, "insuredsFields": {"properties": {"numberOfInsureds": {"type": "number", "meta": {"label": "Number of Insureds", "component": "JInputNumber", "required": true, "order": 1, "fixed": true}}, "planSelected": {"type": "string", "meta": {"label": "Plan Selected", "component": "JSelect", "required": true, "options": [{"key": "033006475240315414", "name": "MG12", "value": "plan01"}, {"key": "245592056899536", "name": "MG15", "value": "plan04"}, {"key": "8651811936707077", "name": "MG19", "value": "plan08"}, {"key": "16901875035098124", "name": "MG23", "value": "plan12"}], "order": 2, "fixed": true}}, "officialID": {"type": "string", "meta": {"label": "Official ID", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": ""}}, "sponsorID": {"type": "string", "meta": {"label": "Sponsor ID", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": ""}}, "mobileNo": {"type": "string", "meta": {"label": "Mobile No", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "number", "numeric": true}}, "firstNameEng": {"type": "string", "meta": {"label": "First Name (ENG)", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": ""}}, "lastNameEng": {"type": "string", "meta": {"label": "Last Name (ENG)", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": ""}}, "secondNameEng": {"type": "string", "meta": {"label": "Second Name (ENG)", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": ""}}, "thirdNameEng": {"type": "string", "meta": {"label": "Third Name (ENG)", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": ""}}, "iqamaExpiryDate": {"type": "string", "meta": {"label": "Iqama Expiry Date", "component": "CDatePicker", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "date", "validations": ""}}, "nationalityCode": {"type": "number", "meta": {"label": "Nationality Code", "component": "JInputNumber", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "number", "validations": ""}}, "occupationCode": {"type": "number", "meta": {"label": "Occupation Code", "component": "JInputNumber", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "number", "validations": ""}}, "maritalStatus": {"type": "string", "meta": {"label": "Marital Status", "component": "JInputText", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": ""}}}}, "planFields_T1": {"description": "Tier 1 Fields", "properties": {"dentalTier": {"type": "string", "meta": {"label": "Dental Tier", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"key": "171567571116016363", "name": "Tier 1", "value": "tier1"}, {"key": "17156757218599189", "name": "Tier 2", "value": "tier2"}, {"key": "171567572257355586", "name": "Tier 3", "value": "tier3"}, {"key": "171567572338635321", "name": "Tier 4", "value": "tier1"}]}}, "drugTier": {"type": "string", "meta": {"label": "Drug Tier", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"key": "171567578392911153", "name": "Tier 1", "value": "tier1"}, {"key": "17156758094458657", "name": "Tier 2", "value": "tier2"}]}}, "hospitalcashbenefit": {"type": "string", "meta": {"label": "Hospital Cash Benefit", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"key": "171567659701677404", "name": "Yes", "value": "yes"}, {"key": "171567660154413682", "name": "No", "value": "no"}]}}, "additionaltravelcoverage": {"type": "string", "meta": {"label": "Additional Travel Coverage", "component": "JSelect", "required": false, "hideFromMemberAdditionForm": false, "fieldType": "text", "validations": "", "options": [{"key": "171567705133597441", "name": "15 days", "value": "15days"}, {"key": "171567706070756655", "name": "30 days", "value": "30days"}, {"key": "171567706897093727", "name": "48 days", "value": "48days"}]}}}}, "planFields_T2": {}, "planFields_T3": {}}