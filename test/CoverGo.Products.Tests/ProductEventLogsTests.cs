﻿using AutoFixture.Xunit2;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.Ports;
using CoverGo.Proxies.Auth;
using CoverGo.Users.Client;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using EntityId = CoverGo.Users.Client.EntityId;

namespace CoverGo.Products.Tests
{
    public class ProductEventLogsTests
    {
        private readonly Mock<IProductEventStore> _productEventStoreMock = new();
        private readonly Mock<IProductBuilderEventStore> _productBuilderEventStore = new();
        private readonly Mock<ProductService> _productService = new();
        private readonly Mock<IAuthService> _authServiceMock = new();
        private readonly Mock<IUsersClient> _usersClientMock = new();



        [Theory, CustomAutoData]
        public async Task GIVEN_ProductEventLogs_WITH_creation_WHEN_Query_Logs_THEN_Logs_are_returned(
            [Frozen] Mock<ProductService> productServiceMock
        )
        {
            Mock<ProductEventService> productEventServiceMock = new Mock<ProductEventService>(_productEventStoreMock.Object);

            const string userId = "62d7adc1d030c9fe79f7938d";
            const string entityId = "62d7adc1d030c9fe79f7938d-62d7adc1d030c9fe79f7938d";
            const string name = "Logs Test";
            const string expectedProductType = "Test Product Type";
            const string expectedProductVersion = "Test Product Version";
            const string expectedProductPlan = "Test Product Plan";

            productServiceMock.Setup(x => x.GetAsync(It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<ProductWhere>(), It.IsAny<JToken>(), It.IsAny<QueryParameters>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<Product>()
                {
                    new Product()
                    {
                        Id = new ProductId()
                        {
                            Type = expectedProductType,
                            Plan = expectedProductPlan,
                            Version = expectedProductVersion
                        }
                    }
                });

            _productBuilderEventStore.Setup(x => x.GetProductNodes(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<string>());

            _productBuilderEventStore.Setup(x => x.GetNodeEvents(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<ProductInfo>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<NodeEvent>());

            _productEventStoreMock.Setup(x => x.GetEventsAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<ProductEventWhere>(), It.IsAny<OrderBy?>(), It.IsAny<int?>(), It.IsAny<int?>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<ProductEvent>()
                {
                    new ProductEvent(
                        new ProductId(){Plan =expectedProductPlan, Type=expectedProductType, Version=expectedProductVersion},
                        ProductEventType.creation,
                        DateTime.UtcNow)
                    {
                        Type = ProductEventType.creation,
                        Values = JToken.Parse($"{{createdById: '{userId}'}}")
                    }
                });

            _authServiceMock.Setup(x => x.GetLoginsAsync(It.IsAny<string>(), It.IsAny<LoginWhere>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<Login>()
                {
                    new Login()
                    {
                        Id = userId,
                        Username = "62d7adc1d030c9fe79f7938d",
                        Email = "62d7adc1d030c9fe79f7938d",
                        EntityId = entityId
                    }
                });

            _usersClientMock.Setup(x => x.Entities_QueryIdsAndTypesAsync(It.IsAny<string>(), It.IsAny<EntityWhere>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<EntityId>()
                {
                    new EntityId() { Id = entityId, Type = "individual" }
                });

            _usersClientMock.Setup(x => x.Individuals_QueryAsync(It.IsAny<string>(), It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<Individual>()
                {
                    new Individual()
                    {
                        EntityId = entityId,
                        Name = name
                    }
                });

            _usersClientMock.Setup(x => x.Internals_QueryAsync(It.IsAny<string>(), It.IsAny<QueryArgumentsOfInternalWhere>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<Internal>()
                {

                });

            ProductEventLogsService productEventLogsService = new ProductEventLogsService(
                _productEventStoreMock.Object,
                _productBuilderEventStore.Object,
                productServiceMock.Object,
                _authServiceMock.Object,
                _usersClientMock.Object,
                NullLogger<ProductEventLogsService>.Instance);

            var logs = await productEventLogsService.GetProductEventLogsAsync("", expectedProductType, new ProductEventWhere(), null, 0, 10, CancellationToken.None);

            logs.Should().HaveCount(1);
            logs.FirstOrDefault().Type.Should().Be(ProductEventType.creation.ToString());
            logs.FirstOrDefault().UpdatedBy.Email.Should().Be(userId);
            logs.FirstOrDefault().UpdatedBy.Name.Should().Be(name);
        }

        [Theory, CustomAutoData]
        public async Task GIVEN_ProductEventLogs_WITH_update_WHEN_Query_Logs_THEN_Logs_are_returned(
            [Frozen] Mock<ProductService> productServiceMock
        )
        {
            Mock<ProductEventService> productEventServiceMock = new Mock<ProductEventService>(_productEventStoreMock.Object);

            const string userId = "62d7adc1d030c9fe79f7938d";
            const string name = "Logs Test";
            const string expectedProductType = "Test Product Type";
            const string expectedProductVersion = "Test Product Version";
            const string expectedProductPlan = "Test Product Plan";

            productServiceMock.Setup(x => x.GetAsync(It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<ProductWhere>(), It.IsAny<JToken>(), It.IsAny<QueryParameters>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<Product>()
                {
                    new Product()
                    {
                        Id = new ProductId()
                        {
                            Type = expectedProductType,
                            Plan = expectedProductPlan,
                            Version = expectedProductVersion
                        }
                    }
                });

            _productBuilderEventStore.Setup(x => x.GetProductNodes(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<string>());

            _productBuilderEventStore.Setup(x => x.GetNodeEvents(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<ProductInfo>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<NodeEvent>());

            _productEventStoreMock.Setup(x => x.GetEventsAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<ProductEventWhere>(), It.IsAny<OrderBy?>(), It.IsAny<int?>(), It.IsAny<int?>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<ProductEvent>()
                {
                    new ProductEvent(
                        new ProductId(){Plan =expectedProductPlan, Type=expectedProductType, Version=expectedProductVersion},
                        ProductEventType.creation,
                        DateTime.UtcNow)
                    {
                        Type = ProductEventType.update,
                        Values = JToken.Parse($"{{modifiedById: '{userId}'}}")
                    }
                });

            _authServiceMock.Setup(x => x.GetLoginsAsync(It.IsAny<string>(), It.IsAny<LoginWhere>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<Login>()
                {
                    new Login()
                    {
                        Id = "62d7adc1d030c9fe79f7938d",
                        Username = "62d7adc1d030c9fe79f7938d",
                        Email = "62d7adc1d030c9fe79f7938d",
                        EntityId = "62d7adc1d030c9fe79f7938d-62d7adc1d030c9fe79f7938d"

                    }
                });

            _usersClientMock.Setup(x => x.Entities_QueryIdsAndTypesAsync(It.IsAny<string>(), It.IsAny<EntityWhere>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<EntityId>()
                {
                    new EntityId() { Id = "", Type = "individual" }
                });

            _usersClientMock.Setup(x => x.Individuals_QueryAsync(It.IsAny<string>(), It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<Individual>()
                {
                    new Individual()
                    {
                        EntityId = "62d7adc1d030c9fe79f7938d-62d7adc1d030c9fe79f7938d",
                        Name = name
                    }
                });

            _usersClientMock.Setup(x => x.Internals_QueryAsync(It.IsAny<string>(), It.IsAny<QueryArgumentsOfInternalWhere>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<Internal>()
                {

                });

            ProductEventLogsService productEventLogsService = new ProductEventLogsService(
                _productEventStoreMock.Object,
                _productBuilderEventStore.Object,
                productServiceMock.Object,
                _authServiceMock.Object,
                _usersClientMock.Object,
                NullLogger<ProductEventLogsService>.Instance);

            var logs = await productEventLogsService.GetProductEventLogsAsync("", expectedProductType, new ProductEventWhere(), null, 0, 10, CancellationToken.None);

            logs.Should().HaveCount(1);
            logs.FirstOrDefault().Type.Should().Be(ProductEventType.update.ToString());
            logs.FirstOrDefault().UpdatedBy.Email.Should().Be(userId);
            logs.FirstOrDefault().UpdatedBy.Name.Should().Be(name);

        }

        [Theory, CustomAutoData]
        public async Task GIVEN_ProductEventLogs_WITH_deletion_WHEN_Query_Logs_THEN_Logs_are_returned(
            [Frozen] Mock<ProductService> productServiceMock
        )
        {
            Mock<ProductEventService> productEventServiceMock = new Mock<ProductEventService>(_productEventStoreMock.Object);

            const string userId = "62d7adc1d030c9fe79f7938d";
            const string entityId = "62d7adc1d030c9fe79f7938d-62d7adc1d030c9fe79f7938d";
            const string name = "Logs Test";
            const string expectedProductType = "Test Product Type";
            const string expectedProductVersion = "Test Product Version";
            const string expectedProductPlan = "Test Product Plan";

            productServiceMock.Setup(x => x.GetAsync(It.IsAny<string>(), It.IsAny<string?>(), It.IsAny<ProductWhere>(), It.IsAny<JToken>(), It.IsAny<QueryParameters>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<Product>()
                {
                    new Product()
                    {
                        Id = new ProductId()
                        {
                            Type = expectedProductType,
                            Plan = expectedProductPlan,
                            Version = expectedProductVersion
                        }
                    }
                });

            _productBuilderEventStore.Setup(x => x.GetProductNodes(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<string>());

            _productBuilderEventStore.Setup(x => x.GetNodeEvents(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<ProductInfo>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<NodeEvent>());

            _productEventStoreMock.Setup(x => x.GetEventsAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<ProductEventWhere>(), It.IsAny<OrderBy?>(), It.IsAny<int?>(), It.IsAny<int?>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<ProductEvent>()
                {
                    new ProductEvent(
                        new ProductId(){Plan =expectedProductPlan, Type=expectedProductType, Version=expectedProductVersion},
                        ProductEventType.delete,
                        DateTime.UtcNow)
                    {
                        Type = ProductEventType.delete,
                        Values = JToken.Parse($"{{deletedById: '{userId}'}}")
                    }
                });

            _authServiceMock.Setup(x => x.GetLoginsAsync(It.IsAny<string>(), It.IsAny<LoginWhere>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<Login>()
                {
                    new Login()
                    {
                        Id = "62d7adc1d030c9fe79f7938d",
                        Username = "62d7adc1d030c9fe79f7938d",
                        Email = "62d7adc1d030c9fe79f7938d",
                        EntityId = entityId

                    }
                });

            _usersClientMock.Setup(x => x.Entities_QueryIdsAndTypesAsync(It.IsAny<string>(), It.IsAny<EntityWhere>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<EntityId>()
                {
                    new EntityId() { Id = "", Type = "individual" }
                });

            _usersClientMock.Setup(x => x.Individuals_QueryAsync(It.IsAny<string>(), It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<Individual>()
                {
                    new Individual()
                    {
                        EntityId = entityId,
                        Name = name
                    }
                });

            _usersClientMock.Setup(x => x.Internals_QueryAsync(It.IsAny<string>(), It.IsAny<QueryArgumentsOfInternalWhere>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<Internal>()
                {

                });

            ProductEventLogsService productEventLogsService = new ProductEventLogsService(
                _productEventStoreMock.Object,
                _productBuilderEventStore.Object,
                productServiceMock.Object,
                _authServiceMock.Object,
                _usersClientMock.Object,
                NullLogger<ProductEventLogsService>.Instance);

            var logs = await productEventLogsService.GetProductEventLogsAsync("", expectedProductType, new ProductEventWhere(), null, 0, 10, CancellationToken.None);

            logs.Should().HaveCount(1);
            logs.FirstOrDefault().Type.Should().Be(ProductEventType.delete.ToString());
            logs.FirstOrDefault().UpdatedBy.Email.Should().Be(userId);
            logs.FirstOrDefault().UpdatedBy.Name.Should().Be(name);

        }
    }
}