using System;

using CoverGo.Products.Infrastructure.Products.Adapters;

namespace CoverGo.Products.Tests
{
    public class ChecksumTests
    {
        [Fact]
        public void GIVEN_no_values_added_WHEN_getting_checksum_THEN_empty_string_is_returned()
        {
            Checksum checksum = new();

            string actual = checksum.ToChecksum();

            actual.Should().BeEmpty();
        }

        [Fact]
        public void GIVEN_two_strings_WHEN_changing_the_order_THEN_checksum_is_changed()
        {
            Checksum checksum1 = new();
            checksum1.Add("a", "b");

            Checksum checksum2 = new();
            checksum2.Add("b", "a");

            checksum1.ToChecksum().Should().NotBe(checksum2.ToChecksum());
        }

        [Fact]
        public void GIVEN_nullable_datetime_null_and_default_datetime_WHEN_getting_checksums_THEN_different_values_returned()
        {
            Checksum checksum1 = new();
            checksum1.Add((DateTime?)null);

            Checksum checksum2 = new();
            checksum2.Add(default(DateTime));

            checksum1.ToChecksum().Should().NotBe(checksum2.ToChecksum());
        }

        [Fact]
        public void GIVEN_null_string_and_empty_string_WHEN_getting_checksums_THEN_the_same_value_is_returned()
        {
            Checksum checksum1 = new();
            checksum1.Add((string)null!);

            Checksum checksum2 = new();
            checksum2.Add(string.Empty);

            checksum1.ToChecksum().Should().NotBeNullOrEmpty()
                .And.Be(checksum2.ToChecksum());
        }
    }
}