[{"id": "028313359746754863", "label": "Product Template", "props": {"theme": "#e4b917"}, "productType": "pa", "minified": true, "ch": [{"id": "7082976987790806", "label": "Plan", "readonly": false, "ch": [{"id": "27598228769820143", "label": "Benefit", "ch": [{"id": "6095805560137555", "label": "Limit", "n": "limit", "p": [{"display": "radio", "n": "limitType", "c": "Radios", "d": "string", "v": "amountLimit"}, {"display": "formula", "isUsingFormula": false, "unitType": null, "unit": "", "n": "formula", "l": "Formula", "c": "Formula", "d": "formula", "v": []}, {"display": "amountLimit", "props": {"placeholder": "Enter amount"}, "n": "amount", "l": "Amount", "c": "Number", "d": "number", "v": 45}, {"display": "amountLimit", "n": "currency", "l": "<PERSON><PERSON><PERSON><PERSON>", "c": "Select", "d": "string", "v": "HKD"}, {"display": "numberLimit", "props": {"placeholder": "Enter a number"}, "n": "number", "l": "Number", "c": "Number", "d": "number"}, {"display": "numberLimit", "n": "unit", "l": "Unit", "c": "Select", "d": "string"}, {"n": "limitPerTime", "l": "Per time unit", "c": "Select", "d": "string", "v": "perDay"}, {"n": "limitPerVariable", "l": "Per variable unit", "c": "Select", "d": "string", "v": "perDelivery"}, {"display": "formula", "n": "description", "l": "Description", "c": "TextArea", "d": "string"}, {"display": "radio", "n": "networkType", "l": "Network Type", "c": "Radios", "d": "string", "v": "inNetwork"}, {"display": "none", "props": {}, "n": "networks", "l": "Networks", "c": "Text", "d": "string", "v": "[{\"id\":\"2a9f6692-2a4c-4daa-9015-7c2392898425\",\"name\":\"MediNet\"},{\"id\":\"d8362d9c-56bf-4ac9-8330-5f76fff6b3cc\",\"name\":\"U Care\"}]"}]}, {"id": "748636395973276", "label": "Limit", "n": "limit", "p": [{"display": "radio", "n": "limitType", "c": "Radios", "d": "string", "v": "numberLimit"}, {"display": "formula", "isUsingFormula": false, "unitType": null, "unit": "", "n": "formula", "l": "Formula", "c": "Formula", "d": "formula", "v": []}, {"display": "amountLimit", "props": {"placeholder": "Enter amount"}, "n": "amount", "l": "Amount", "c": "Number", "d": "number"}, {"display": "amountLimit", "n": "currency", "l": "<PERSON><PERSON><PERSON><PERSON>", "c": "Select", "d": "string"}, {"display": "numberLimit", "props": {"placeholder": "Enter a number"}, "n": "number", "l": "Number", "c": "Number", "d": "number", "v": 11}, {"display": "numberLimit", "n": "unit", "l": "Unit", "c": "Select", "d": "string", "v": "perItem"}, {"n": "limitPerTime", "l": "Per time unit", "c": "Select", "d": "string", "v": "per5Years"}, {"n": "limitPerVariable", "l": "Per variable unit", "c": "Select", "d": "string", "v": "perAccident"}, {"display": "formula", "n": "description", "l": "Description", "c": "TextArea", "d": "string"}, {"display": "radio", "n": "networkType", "l": "Network Type", "c": "Radios", "d": "string", "v": "outOfNetwork"}, {"display": "none", "props": {}, "n": "networks", "l": "Networks", "c": "Text", "d": "string"}]}, {"id": "33964952093642875", "label": "Limit", "n": "limit", "p": [{"display": "radio", "n": "limitType", "c": "Radios", "d": "string", "v": "amountLimit"}, {"display": "formula", "isUsingFormula": true, "unitType": "currency", "unit": "HKD", "n": "formula", "l": "Formula", "c": "Formula", "d": "formula", "v": [{"id": "9849349999373205", "label": "Formula", "name": "formula", "componentName": "NodeFormula", "children": [{"id": "3220203443479899", "label": "Value", "name": "value", "componentName": "NodeValue", "children": [], "props": {"dataType": "number", "value": "20"}, "editor": {"componentName": "NodeValueEditor"}}, {"id": "8746061548623891", "label": "Value", "name": "value", "componentName": "NodeValue", "children": [], "props": {"dataType": "number", "value": "30"}, "editor": {"componentName": "NodeValueEditor"}}], "props": {"formulaName": "MULTIPLY"}, "editor": {"componentName": "NodeFormulaEditor"}}]}, {"display": "amountLimit", "props": {"placeholder": "Enter amount"}, "n": "amount", "l": "Amount", "c": "Number", "d": "number"}, {"display": "amountLimit", "n": "currency", "l": "<PERSON><PERSON><PERSON><PERSON>", "c": "Select", "d": "string", "v": "HKD"}, {"display": "numberLimit", "props": {"placeholder": "Enter a number"}, "n": "number", "l": "Number", "c": "Number", "d": "number"}, {"display": "numberLimit", "n": "unit", "l": "Unit", "c": "Select", "d": "string"}, {"n": "limitPerTime", "l": "Per time unit", "c": "Select", "d": "string", "v": "perYear"}, {"n": "limitPerVariable", "l": "Per variable unit", "c": "Select", "d": "string", "v": "perTrip"}, {"display": "formula", "n": "description", "l": "Description", "c": "TextArea", "d": "string", "v": "Trips"}, {"display": "radio", "n": "networkType", "l": "Network Type", "c": "Radios", "d": "string", "v": "inNetworkAndOutOfNetwork"}, {"display": "none", "props": {}, "n": "networks", "l": "Networks", "c": "Text", "d": "string", "v": ""}]}, {"id": "75023112939872", "label": "Limit", "n": "limit", "p": [{"display": "radio", "n": "limitType", "c": "Radios", "d": "string", "v": "amountLimit"}, {"display": "formula", "isUsingFormula": true, "unitType": "currency", "unit": "HKD", "n": "formula", "l": "Formula", "c": "Formula", "d": "formula", "v": [{"id": "08293098470214089", "label": "Value", "name": "value", "componentName": "NodeValue", "children": [], "props": {"dataType": "number", "value": "44"}, "editor": {"componentName": "NodeValueEditor"}}]}, {"display": "amountLimit", "props": {"placeholder": "Enter amount"}, "n": "amount", "l": "Amount", "c": "Number", "d": "number"}, {"display": "amountLimit", "n": "currency", "l": "<PERSON><PERSON><PERSON><PERSON>", "c": "Select", "d": "string", "v": "HKD"}, {"display": "numberLimit", "props": {"placeholder": "Enter a number"}, "n": "number", "l": "Number", "c": "Number", "d": "number"}, {"display": "numberLimit", "n": "unit", "l": "Unit", "c": "Select", "d": "string"}, {"n": "limitPerTime", "l": "Per time unit", "c": "Select", "d": "string", "v": "12rollingMonths"}, {"n": "limitPerVariable", "l": "Per variable unit", "c": "Select", "d": "string", "v": "perLife"}, {"display": "formula", "n": "description", "l": "Description", "c": "TextArea", "d": "string"}, {"display": "radio", "n": "networkType", "l": "Network Type", "c": "Radios", "d": "string", "v": "inNetworkAndOutOfNetwork"}, {"display": "none", "props": {}, "n": "networks", "l": "Networks", "c": "Text", "d": "string"}]}, {"id": "870526823954304", "label": "Overseas Accidental Limit", "n": "overseasLimit", "p": [{"n": "percentage", "l": "Percentage", "c": "Number", "d": "number", "v": "444%"}]}, {"id": "027583802528052814", "label": "Grouped Benefit Limit", "n": "groupedBenefitLimit", "p": [{"display": "radio", "n": "limitType", "c": "Radio", "d": "string", "v": "amountLimit"}, {"display": "formula", "isUsingFormula": false, "unitType": null, "unit": "", "n": "formula", "l": "Formula", "c": "Formula", "d": "formula", "v": []}, {"display": "amountLimit", "props": {"placeholder": "Enter amount"}, "n": "amount", "l": "Amount", "c": "Number", "d": "number", "v": 44}, {"display": "amountLimit", "n": "currency", "l": "<PERSON><PERSON><PERSON><PERSON>", "c": "Select", "d": "string", "v": "HKD"}, {"display": "numberLimit", "props": {"placeholder": "Enter a number"}, "n": "number", "l": "Number", "c": "Number", "d": "number"}, {"display": "numberLimit", "n": "unit", "l": "Unit", "c": "Select", "d": "string"}, {"n": "limitPerTime", "l": "Per time unit", "c": "Select", "d": "string"}, {"n": "limitPerVariable", "l": "Per variable unit", "c": "Select", "d": "string", "v": "perDelivery"}, {"display": "formula", "n": "description", "l": "Description", "c": "TextArea", "d": "string"}, {"n": "groupedBenefits", "v": ["benefit.AD01.Accident Damage", "benefit.aidsPreg.Aids test for pregnant women", "benefitType.free.Additional Free Services"]}]}, {"id": "6192681807115621", "label": "Deductible", "ch": [], "n": "deductible", "p": [{"display": "radio", "n": "deductibleType", "c": "Radios", "d": "string", "v": "amount"}, {"display": "formula", "isUsingFormula": false, "unitType": null, "unit": "", "n": "formula", "l": "Formula", "c": "Formula", "d": "formula", "v": []}, {"n": "currency", "l": "<PERSON><PERSON><PERSON><PERSON>", "c": "Select", "d": "string", "v": "HKD"}, {"display": "amount", "props": {"placeholder": "Enter amount"}, "n": "amount", "l": "Amount", "c": "Number", "d": "number", "v": 33}, {"n": "perTime", "l": "Per time unit", "c": "Select", "d": "string", "v": "perLifetime"}, {"n": "perVariable", "l": "Per variable unit", "c": "Select", "d": "string", "v": "perDelivery"}, {"display": "formula", "n": "description", "l": "Description", "c": "TextArea", "d": "string"}]}, {"id": "9312892612255239", "label": "Deductible", "n": "deductible", "p": [{"display": "radio", "n": "deductibleType", "c": "Radios", "d": "string", "v": "amount"}, {"display": "formula", "isUsingFormula": true, "unitType": null, "unit": "", "n": "formula", "l": "Formula", "c": "Formula", "d": "formula", "v": []}, {"n": "currency", "l": "<PERSON><PERSON><PERSON><PERSON>", "c": "Select", "d": "string", "v": "HKD"}, {"display": "amount", "props": {"placeholder": "Enter amount"}, "n": "amount", "l": "Amount", "c": "Number", "d": "number"}, {"n": "perTime", "l": "Per time unit", "c": "Select", "d": "string", "v": "perYear"}, {"n": "perVariable", "l": "Per variable unit", "c": "Select", "d": "string", "v": "perDisability"}, {"display": "formula", "n": "description", "l": "Description", "c": "TextArea", "d": "string", "v": "Ded"}]}, {"id": "05767942567386797", "label": "Co-payment", "n": "coPayment", "p": [{"display": "radio", "n": "type", "c": "Radio", "d": "string", "v": "coPaymentAmount"}, {"display": "none", "n": "amount", "l": "Amount", "c": "Number", "d": "number", "v": 333}, {"display": "coPaymentAmount", "n": "coPaymentAmount", "l": "Amount", "c": "Number", "d": "number", "v": 333}, {"display": "coPaymentFormula", "n": "coPaymentFormula", "c": "Formula", "d": "formula"}, {"n": "currency", "l": "<PERSON><PERSON><PERSON><PERSON>", "c": "Select", "d": "string", "v": "HKD"}, {"display": "coPaymentFormula", "n": "description", "l": "Description", "c": "LongText", "de": "What is the formula about", "d": "string"}]}, {"id": "5670186333465874", "label": "Co-payment", "ch": [], "n": "coPayment", "p": [{"display": "radio", "n": "type", "c": "Radio", "d": "string", "v": "coPaymentFormula"}, {"display": "none", "n": "amount", "l": "Amount", "c": "Formula", "d": "formula", "v": [{"id": "09942114562093707", "label": "Value", "name": "value", "componentName": "NodeValue", "children": [], "props": {"dataType": "number", "value": "43333"}, "editor": {"componentName": "NodeValueEditor"}}]}, {"display": "coPaymentAmount", "n": "coPaymentAmount", "l": "Amount", "c": "Number", "d": "number"}, {"display": "coPaymentFormula", "n": "coPaymentFormula", "c": "Formula", "d": "formula", "v": [{"id": "09942114562093707", "label": "Value", "name": "value", "componentName": "NodeValue", "children": [], "props": {"dataType": "number", "value": "43333"}, "editor": {"componentName": "NodeValueEditor"}}]}, {"n": "currency", "l": "<PERSON><PERSON><PERSON><PERSON>", "c": "Select", "d": "string", "v": "HKD"}, {"display": "coPaymentFormula", "n": "description", "l": "Description", "c": "LongText", "de": "What is the formula about", "d": "string", "v": "Hello There"}]}, {"id": "8669824867803373", "label": "Panel Co-payment", "n": "panelCoPayment", "p": [{"n": "amount", "l": "Amount", "c": "Number", "d": "number", "v": 44}, {"n": "currency", "l": "<PERSON><PERSON><PERSON><PERSON>", "c": "Select", "d": "string", "v": "HKD"}, {"n": "limitPerTime", "l": "Per time unit", "c": "Select", "d": "string", "v": "perDay"}, {"n": "limitPerVariable", "l": "Per variable unit", "c": "Select", "d": "string", "v": "perPregnancy"}]}, {"id": "15931098009213462", "label": "Co-insurance", "ch": [], "n": "reimbursement", "p": [{"display": "radio", "n": "type", "c": "Radio", "d": "string", "v": "coInsurance"}, {"n": "inputType", "l": "Input Type", "c": "Select", "d": "string", "v": "reImbursementAmount"}, {"display": "none", "n": "percentage", "l": "Percentage", "c": "Number", "d": "number", "v": "30%"}, {"display": "reImbursementAmount", "n": "reImbursementAmount", "l": "Percentage", "c": "Number", "d": "number", "v": "30%"}, {"display": "reImbursementFormula", "n": "reImbursementFormula", "c": "Formula", "d": "formula", "v": []}, {"display": "reImbursementFormula", "n": "description", "l": "Description", "c": "LongText", "de": "What is the formula about", "d": "string"}, {"n": "maxLimitType", "l": "<PERSON>", "c": "Select", "d": "string", "v": "amount"}, {"display": "none", "n": "maxLimit", "l": "Amount", "c": "Number", "d": "number", "v": 88}, {"n": "maxLimitCurrency", "l": "<PERSON><PERSON><PERSON><PERSON>", "c": "Select", "d": "string", "v": "HKD"}]}, {"id": "6458669583671057", "label": "Co-insurance", "n": "reimbursement", "p": [{"display": "radio", "n": "type", "c": "Radio", "d": "string", "v": "coInsurance"}, {"n": "inputType", "l": "Input Type", "c": "Select", "d": "string", "v": "reImbursementFormula"}, {"display": "none", "n": "percentage", "l": "Percentage", "c": "Formula", "d": "formula", "v": [{"id": "5376232172035287", "label": "Value", "name": "value", "componentName": "NodeValue", "children": [], "props": {"dataType": "number", "value": "777%"}, "editor": {"componentName": "NodeValueEditor"}}]}, {"display": "reImbursementAmount", "n": "reImbursementAmount", "l": "Percentage", "c": "Number", "d": "number"}, {"display": "reImbursementFormula", "n": "reImbursementFormula", "c": "Formula", "d": "formula", "v": [{"id": "5376232172035287", "label": "Value", "name": "value", "componentName": "NodeValue", "children": [], "props": {"dataType": "number", "value": "777%"}, "editor": {"componentName": "NodeValueEditor"}}]}, {"display": "reImbursementFormula", "n": "description", "l": "Description", "c": "LongText", "de": "What is the formula about", "d": "string", "v": "Max Limit <PERSON>"}, {"n": "maxLimitType", "l": "<PERSON>", "c": "Select", "d": "string", "v": "formula"}, {"display": "none", "n": "maxLimit", "l": "Amount", "c": "Number", "d": "number", "v": [{"id": "9947662721318231", "label": "Value", "name": "value", "componentName": "NodeValue", "children": [], "props": {"dataType": "number", "value": "123123"}, "editor": {"componentName": "NodeValueEditor"}}]}, {"n": "maxLimitCurrency", "l": "<PERSON><PERSON><PERSON><PERSON>", "c": "Select", "d": "string", "v": "HKD"}]}, {"id": "839561730223837", "label": "Co-insurance", "n": "reimbursement", "p": [{"display": "radio", "n": "type", "c": "Radio", "d": "string", "v": "coInsurance"}, {"n": "inputType", "l": "Input Type", "c": "Select", "d": "string", "v": "reImbursementAmount"}, {"display": "none", "n": "percentage", "l": "Percentage", "c": "Number", "d": "number", "v": "77%"}, {"display": "reImbursementAmount", "n": "reImbursementAmount", "l": "Percentage", "c": "Number", "d": "number", "v": "77%"}, {"display": "reImbursementFormula", "n": "reImbursementFormula", "c": "Formula", "d": "formula", "v": []}, {"display": "reImbursementFormula", "n": "description", "l": "Description", "c": "LongText", "de": "What is the formula about", "d": "string"}, {"n": "maxLimitType", "l": "<PERSON>", "c": "Select", "d": "string", "v": "none"}, {"display": "none", "n": "maxLimit", "l": "Amount", "c": "Number", "d": "number"}, {"n": "maxLimitCurrency", "l": "<PERSON><PERSON><PERSON><PERSON>", "c": "Select", "d": "string", "v": "HKD"}]}, {"id": "413154779248593", "label": "Reimbursement", "n": "reimbursement", "p": [{"display": "radio", "n": "type", "c": "Radio", "d": "string", "v": "reImbursement"}, {"n": "inputType", "l": "Input Type", "c": "Select", "d": "string", "v": "reImbursementFormula"}, {"display": "none", "n": "percentage", "l": "Percentage", "c": "Formula", "d": "formula", "v": [{"id": "7185920908111179", "label": "Value", "name": "value", "componentName": "NodeValue", "children": [], "props": {"dataType": "number", "value": "6"}, "editor": {"componentName": "NodeValueEditor"}}]}, {"display": "reImbursementAmount", "n": "reImbursementAmount", "l": "Percentage", "c": "Number", "d": "number"}, {"display": "reImbursementFormula", "n": "reImbursementFormula", "c": "Formula", "d": "formula", "v": [{"id": "7185920908111179", "label": "Value", "name": "value", "componentName": "NodeValue", "children": [], "props": {"dataType": "number", "value": "6"}, "editor": {"componentName": "NodeValueEditor"}}]}, {"display": "reImbursementFormula", "n": "description", "l": "Description", "c": "LongText", "de": "What is the formula about", "d": "string", "v": "ReDescr"}, {"n": "maxLimitType", "l": "<PERSON>", "c": "Select", "d": "string", "v": "none"}, {"display": "none", "n": "maxLimit", "l": "Amount", "c": "Number", "d": "number"}, {"n": "maxLimitCurrency", "l": "<PERSON><PERSON><PERSON><PERSON>", "c": "Select", "d": "string", "v": "HKD"}]}, {"id": "5903254245078531", "label": "Reimbursement", "n": "reimbursement", "p": [{"display": "radio", "n": "type", "c": "Radio", "d": "string", "v": "reImbursement"}, {"n": "inputType", "l": "Input Type", "c": "Select", "d": "string", "v": "reImbursementAmount"}, {"display": "none", "n": "percentage", "l": "Percentage", "c": "Number", "d": "number", "v": "23.6%"}, {"display": "reImbursementAmount", "n": "reImbursementAmount", "l": "Percentage", "c": "Number", "d": "number", "v": "23.6%"}, {"display": "reImbursementFormula", "n": "reImbursementFormula", "c": "Formula", "d": "formula", "v": []}, {"display": "reImbursementFormula", "n": "description", "l": "Description", "c": "LongText", "de": "What is the formula about", "d": "string"}, {"n": "maxLimitType", "l": "<PERSON>", "c": "Select", "d": "string", "v": "none"}, {"display": "none", "n": "maxLimit", "l": "Amount", "c": "Number", "d": "number"}, {"n": "maxLimitCurrency", "l": "<PERSON><PERSON><PERSON><PERSON>", "c": "Select", "d": "string", "v": "HKD"}]}, {"id": "28695349899224576", "label": "Geographical Limit", "index": 14, "n": "geographicalLimit", "p": [{"n": "region", "l": "Region", "c": "Select", "d": "string", "v": "europe"}]}, {"id": "7508349747902523", "label": "Waiting Period", "index": 10, "n": "<PERSON><PERSON><PERSON><PERSON>", "p": [{"display": "radio", "n": "waitingPeriodType", "c": "Radios", "d": "string", "v": "amount"}, {"display": "formula", "isUsingFormula": false, "unitType": null, "unit": "", "n": "formula", "l": "Formula", "c": "Formula", "d": "formula", "v": []}, {"display": "amount", "props": {"placeholder": "Enter amount"}, "n": "amount", "l": "Amount", "c": "Number", "d": "number", "v": 77}, {"n": "unit", "l": "Unit", "c": "Select", "d": "string", "v": "hour(s)"}, {"display": "amount", "props": {"placeholder": "", "maxlength": 100, "rows": 3}, "n": "amountDescription", "l": "Amount Description", "c": "TextArea", "d": "string", "v": "W8"}, {"display": "formula", "n": "description", "l": "Description", "c": "TextArea", "d": "string"}]}, {"id": "7880254825172794", "label": "Waiting Period", "index": 10, "n": "<PERSON><PERSON><PERSON><PERSON>", "p": [{"display": "radio", "n": "waitingPeriodType", "c": "Radios", "d": "string", "v": "amount"}, {"display": "formula", "isUsingFormula": true, "unitType": null, "unit": "", "n": "formula", "l": "Formula", "c": "Formula", "d": "formula", "v": []}, {"display": "amount", "props": {"placeholder": "Enter amount"}, "n": "amount", "l": "Amount", "c": "Number", "d": "number"}, {"n": "unit", "l": "Unit", "c": "Select", "d": "string", "v": "hour(s)"}, {"display": "amount", "props": {"placeholder": "", "maxlength": 100, "rows": 3}, "n": "amountDescription", "l": "Amount Description", "c": "TextArea", "d": "string"}, {"display": "formula", "n": "description", "l": "Description", "c": "TextArea", "d": "string", "v": "Wait!"}]}, {"id": "18433763162839867", "label": "Qualification Period", "n": "qualificationPeriod", "p": [{"n": "length", "l": "Length", "c": "Number", "d": "string", "v": 667}, {"n": "formulaLength", "l": "Length"}, {"n": "type", "l": "Type", "v": "number"}, {"n": "periodUnit", "l": "Period", "c": "Select", "v": "day"}]}, {"id": "3686448707092289", "label": "Qualification Period", "n": "qualificationPeriod", "p": [{"n": "length", "l": "Length", "c": "Number", "d": "string", "v": ""}, {"n": "formulaLength", "l": "Length", "v": [{"id": "016340577573349835", "label": "Value", "name": "value", "componentName": "NodeValue", "children": [], "props": {"dataType": "number", "value": "123"}, "editor": {"componentName": "NodeValueEditor"}}]}, {"n": "type", "l": "Type", "d": null, "v": "formula"}, {"n": "periodUnit", "l": "Period", "c": "Select", "v": "day"}]}, {"id": "14261025625949197", "label": "Deferred Period", "n": "deferred<PERSON><PERSON><PERSON>", "p": [{"n": "length", "l": "Length", "c": "Number", "d": "string", "v": 787}, {"n": "formulaLength", "l": "Length"}, {"n": "type", "l": "Type", "v": "number"}, {"n": "periodUnit", "l": "Period", "c": "Select", "v": "day"}]}, {"id": "9154799561404836", "label": "Deferred Period", "n": "deferred<PERSON><PERSON><PERSON>", "p": [{"n": "length", "l": "Length", "c": "Number", "d": "string", "v": ""}, {"n": "formulaLength", "l": "Length", "v": [{"id": "837517410483112", "label": "Value", "name": "value", "componentName": "NodeValue", "children": [], "props": {"dataType": "number", "value": "123"}, "editor": {"componentName": "NodeValueEditor"}}]}, {"n": "type", "l": "Type", "d": null, "v": "formula"}, {"n": "periodUnit", "l": "Period", "c": "Select", "v": "day"}]}], "n": "benefit", "p": [{"n": "type", "c": "Radios", "d": "string"}, {"n": "name", "l": "Name", "c": "Select", "dv": "Chinese Herbalist, Bonesetter and Acupuncturist", "d": "string", "v": "CBA"}]}], "n": "plan", "p": [{"n": "id", "l": "ID", "c": "Text", "d": "string", "v": "plan1"}, {"n": "name", "l": "Name", "c": "Text", "d": "string", "v": "Plan 1"}]}], "n": "root", "pc": "HKD"}]