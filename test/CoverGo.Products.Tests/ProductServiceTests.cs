using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoFixture;
using AutoFixture.AutoMoq;
using CoverGo.DomainUtils;
using CoverGo.FeatureManagement;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.Ports;
using Moq;
using Newtonsoft.Json.Linq;
using CoverGo.Products.Domain.Facts;
using CoverGo.Products.Domain.Underwriting;

namespace CoverGo.Products.Tests.Domain.Products;

[Trait("Ticket", "CH-23867")]
public class ProductServiceTests
{
    private readonly IProductRepository _productRepository;
    private readonly Mock<IProductEventStore> _productEventStore;
    private readonly IMultiTenantFeatureManager _featureManager;
    private readonly List<Mock<IUnderwritingEngine>> _underwritingEngines;
    private readonly ProductService _productService;
    private readonly IFixture _fixture;

    public ProductServiceTests()
    {
        _fixture = new Fixture()
            .Customize(new AutoMoqCustomization());

        _productRepository = _fixture.Freeze<Mock<IProductRepository>>().Object;
        _productEventStore = _fixture.Freeze<Mock<IProductEventStore>>();
        _featureManager = _fixture.Freeze<Mock<IMultiTenantFeatureManager>>().Object;
        _underwritingEngines = [
            new Mock<IUnderwritingEngine>(),
            new Mock<IUnderwritingEngine>(),
            new Mock<IUnderwritingEngine>()
        ];
        _fixture.Register(() => _underwritingEngines.Select(x => x.Object));

        _productService = _fixture.Create<ProductService>();
    }

    [Theory]
    [CustomAutoData]
    public async Task UpdateAsync_WhenProductNotFound_ShouldReturnFailure(
        string tenantId,
        UpdateProductCommand command)
    {
        // Arrange
        Mock.Get(_productRepository)
            .Setup(x => x.GetAsync(
                tenantId,
                It.Is<ProductWhere>(w => w.Id_in.Contains(command.ProductId)),
                null,
                It.IsAny<QueryParameters>(),
                It.IsAny<bool>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(Array.Empty<Product>());

        // Act
        var result = await _productService.UpdateAsync(tenantId, command, CancellationToken.None);

        // Assert
        result.Status.Should().Be("failure");
        result.Errors.Should().Contain($"The product {command.ProductId} was not found.");
    }

    [Theory]
    [CustomAutoData]
    public async Task UpdateAsync_WhenProductIdNonExistent_ShouldReturnFailure(
        string tenantId,
        UpdateProductCommand command)
    {
        // Arrange
        Mock.Get(_featureManager)
            .Setup(x => x.IsEnabled("ValidatePdtId", tenantId))
            .ReturnsAsync(true);

        Mock.Get(_productRepository)
            .Setup(x => x.GetTotalCountAsync(
                tenantId,
                It.IsAny<ProductWhere>(),
                It.IsAny<ProductConfig>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(1); // Simulating existing PDT ID

        // Act
        var result = await _productService.UpdateAsync(tenantId, command, CancellationToken.None);

        // Assert
        result.Status.Should().Be("failure");
        result.Errors.Should().Match(errors => errors.Any(error => error.Contains("was not found")));
    }

    [Theory]
    [CustomAutoData]
    public async Task UpdateAsync_WhenUpdateFails_ShouldReturnFailure(
        string tenantId,
        UpdateProductCommand command,
        Product existingProduct)
    {
        // Arrange
        Mock.Get(_productRepository)
            .Setup(x => x.GetAsync(
                tenantId,
                It.Is<ProductWhere>(w => w.Id_in.Contains(command.ProductId)),
                null,
                It.IsAny<QueryParameters>(),
                It.IsAny<bool>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { existingProduct });

        Mock.Get(_productRepository)
            .Setup(x => x.UpdateAsync(
                tenantId,
                command,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "failure", Errors = new List<string> { "Update failed" } });

        // Act
        var result = await _productService.UpdateAsync(tenantId, command, CancellationToken.None);

        // Assert
        result.Status.Should().Be("failure");
        result.Errors.Should().Contain("Update failed");
    }

    [Theory]
    [CustomAutoData]
    public async Task UpdateAsync_WhenUpdateSucceeds_ShouldCreateEvent(
        string tenantId,
        UpdateProductCommand command,
        Product existingProduct)
    {
        // Arrange
        Mock.Get(_productRepository)
            .Setup(x => x.GetAsync(
                tenantId,
                It.Is<ProductWhere>(w => w.Id_in.Contains(command.ProductId)),
                null,
                It.IsAny<QueryParameters>(),
                It.IsAny<bool>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { existingProduct });

        Mock.Get(_productRepository)
            .Setup(x => x.UpdateAsync(
                tenantId,
                command,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        _productEventStore
            .Setup(x => x.AddEventAsync(
                tenantId,
                It.Is<ProductEvent>(e =>
                    e.ProductId == command.ProductId &&
                    e.Type == ProductEventType.update),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        // Act
        var result = await _productService.UpdateAsync(tenantId, command, CancellationToken.None);

        // Assert
        result.Status.Should().Be("success");
        _productEventStore.Verify(
            x => x.AddEventAsync(
                tenantId,
                It.Is<ProductEvent>(e =>
                    e.ProductId == command.ProductId &&
                    e.Type == ProductEventType.update),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Theory]
    [CustomAutoData]
    public async Task UpdateAsync_WhenEventCreationFails_ShouldReturnFailure(
        string tenantId,
        UpdateProductCommand command,
        Product existingProduct)
    {
        // Arrange
        Mock.Get(_productRepository)
            .Setup(x => x.GetAsync(
                tenantId,
                It.Is<ProductWhere>(w => w.Id_in.Contains(command.ProductId)),
                null,
                It.IsAny<QueryParameters>(),
                It.IsAny<bool>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { existingProduct });

        Mock.Get(_productRepository)
            .Setup(x => x.UpdateAsync(
                tenantId,
                command,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        _productEventStore
            .Setup(x => x.AddEventAsync(
                tenantId,
                It.Is<ProductEvent>(e =>
                    e.ProductId == command.ProductId &&
                    e.Type == ProductEventType.update),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "failure", Errors = new List<string> { "Event creation failed" } });

        // Act
        var result = await _productService.UpdateAsync(tenantId, command, CancellationToken.None);

        // Assert
        result.Status.Should().Be("failure");
        result.Errors.Should().Contain("Event creation failed");
    }
    [Theory]
    [CustomAutoData]
    public async Task UpdateAsync_WhenVersion1_ShouldAllowAllFields(
    string tenantId,
    UpdateProductCommand command,
    Product existingProduct)
    {
        // Arrange (Given)
        existingProduct.Id = new ProductId
        {
            Plan = existingProduct.Id.Plan,
            Type = existingProduct.Id.Type,
            Version = "1.0"
        };
        existingProduct.Fields = "{\"productName\":\"oldName\",\"productType\":\"oldType\"}";
        command.Fields = "{\"productName\":\"newName\",\"productType\":\"newType\"}";

        Mock.Get(_productRepository)
            .Setup(x => x.GetAsync(
                tenantId,
                It.Is<ProductWhere>(w => w.Id_in.Contains(command.ProductId)),
                null,
                It.IsAny<QueryParameters>(),
                It.IsAny<bool>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { existingProduct });

        Mock.Get(_productRepository)
            .Setup(x => x.UpdateAsync(
                tenantId,
                It.Is<UpdateProductCommand>(c =>
                    c.Fields.Contains("newName") &&
                    c.Fields.Contains("newType")),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });
        _productEventStore
            .Setup(x => x.AddEventAsync(
                tenantId,
                It.Is<ProductEvent>(e =>
                    e.ProductId == command.ProductId &&
                    e.Type == ProductEventType.update),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        // Act (When)
        var result = await _productService.UpdateAsync(tenantId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
    }

    [Theory]
    [CustomAutoData]
    public async Task UpdateAsync_WhenVersionGreaterThan1_ShouldRestrictLockedFields(
        string tenantId,
        UpdateProductCommand command,
        Product existingProduct)
    {
        // Arrange (Given)
        existingProduct.Id = new ProductId
        {
            Plan = existingProduct.Id.Plan,
            Type = existingProduct.Id.Type,
            Version = "2.0"
        };
        existingProduct.Fields = "{\"productName\":\"oldName\",\"productType\":\"oldType\",\"customField\":\"oldValue\"}";
        existingProduct.AllowCustomProduct = true;
        command.Fields = "{\"productName\":\"newName\",\"productType\":\"newType\",\"customField\":\"newValue\"}";

        Mock.Get(_featureManager)
            .Setup(x => x.IsEnabled("UseLegacyLifecycle", tenantId))
            .ReturnsAsync(false);

        Mock.Get(_productRepository)
            .Setup(x => x.GetAsync(
                tenantId,
                It.Is<ProductWhere>(w => w.Id_in.Contains(command.ProductId)),
                null,
                It.IsAny<QueryParameters>(),
                It.IsAny<bool>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { existingProduct });

        Mock.Get(_productRepository)
            .Setup(x => x.UpdateAsync(
                tenantId,
                It.Is<UpdateProductCommand>(c =>
                    c.Fields.Contains("oldName") &&
                    c.Fields.Contains("oldType") &&
                    c.Fields.Contains("newValue")),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });
        _productEventStore
            .Setup(x => x.AddEventAsync(
                tenantId,
                It.Is<ProductEvent>(e =>
                    e.ProductId == command.ProductId &&
                    e.Type == ProductEventType.update),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        // Act (When)
        var result = await _productService.UpdateAsync(tenantId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
    }

    [Theory]
    [CustomAutoData]
    public async Task UpdateAsync_WhenNotAlphaStage_ShouldRestrictAlphaFields(
        string tenantId,
        UpdateProductCommand command,
        Product existingProduct)
    {
        // Arrange (Given)
        existingProduct.Id = new ProductId
        {
            Plan = existingProduct.Id.Plan,
            Type = existingProduct.Id.Type,
            Version = "2.0"
        };
        existingProduct.LifecycleStage = "beta";
        existingProduct.AllowCustomProduct = true;
        existingProduct.Fields = "{\"pricingModel\":\"oldModel\",\"offerValidityPeriod\":\"oldPeriod\",\"customField\":\"oldValue\"}";
        command.Fields = "{\"pricingModel\":\"newModel\",\"offerValidityPeriod\":\"newPeriod\",\"customField\":\"newValue\"}";
        command.IsOfferValidityPeriodChanged = true;

        Mock.Get(_featureManager)
            .Setup(x => x.IsEnabled("UseLegacyLifecycle", tenantId))
            .ReturnsAsync(false);

        Mock.Get(_productRepository)
            .Setup(x => x.GetAsync(
                tenantId,
                It.Is<ProductWhere>(w => w.Id_in.Contains(command.ProductId)),
                null,
                It.IsAny<QueryParameters>(),
                It.IsAny<bool>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { existingProduct });

        Mock.Get(_productRepository)
            .Setup(x => x.UpdateAsync(
                tenantId,
                It.Is<UpdateProductCommand>(c =>
                    c.Fields.Contains("oldModel") &&
                    c.Fields.Contains("oldPeriod") &&
                    c.Fields.Contains("newValue") &&
                    !c.IsOfferValidityPeriodChanged),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });
        _productEventStore
            .Setup(x => x.AddEventAsync(
                tenantId,
                It.Is<ProductEvent>(e =>
                    e.ProductId == command.ProductId &&
                    e.Type == ProductEventType.update),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        // Act
        var result = await _productService.UpdateAsync(tenantId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
    }

    [Theory]
    [CustomAutoData]
    public async Task UpdateAsync_WhenAlphaStage_ShouldAllowAlphaFields(
        string tenantId,
        UpdateProductCommand command,
        Product existingProduct)
    {
        // Arrange (Given)
        existingProduct.Id = new ProductId
        {
            Plan = existingProduct.Id.Plan,
            Type = existingProduct.Id.Type,
            Version = "2.0"
        };
        existingProduct.LifecycleStage = "alpha";
        existingProduct.Fields = "{\"pricingModel\":\"oldModel\",\"offerValidityPeriod\":\"oldPeriod\",\"customField\":\"oldValue\"}";
        command.Fields = "{\"pricingModel\":\"newModel\",\"offerValidityPeriod\":\"newPeriod\",\"customField\":\"newValue\"}";
        command.IsOfferValidityPeriodChanged = true;

        Mock.Get(_productRepository)
            .Setup(x => x.GetAsync(
                tenantId,
                It.Is<ProductWhere>(w => w.Id_in.Contains(command.ProductId)),
                null,
                It.IsAny<QueryParameters>(),
                It.IsAny<bool>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { existingProduct });

        Mock.Get(_productRepository)
            .Setup(x => x.UpdateAsync(
                tenantId,
                It.Is<UpdateProductCommand>(c =>
                    c.Fields.Contains("newModel") &&
                    c.Fields.Contains("newPeriod") &&
                    c.Fields.Contains("newValue") &&
                    c.IsOfferValidityPeriodChanged),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });
        _productEventStore
            .Setup(x => x.AddEventAsync(
                tenantId,
                It.Is<ProductEvent>(e =>
                    e.ProductId == command.ProductId &&
                    e.Type == ProductEventType.update),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        // Act (When)
        var result = await _productService.UpdateAsync(tenantId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
    }

    [Theory]
    [CustomAutoData]
    public async Task UpdateAsync_WhenVersionGreaterThan1_ShouldAllowLifecycleStageUpdate(
        string tenantId,
        UpdateProductCommand command,
        Product existingProduct)
    {
        // Arrange (Given)
        existingProduct.Id = new ProductId
        {
            Plan = existingProduct.Id.Plan,
            Type = existingProduct.Id.Type,
            Version = "2.0"
        };
        existingProduct.LifecycleStage = "alpha";
        existingProduct.Fields = "{\"lifecycleStage\":\"alpha\"}";
        command.Fields = "{\"lifecycleStage\":\"preProduction\"}";
        command.LifecycleStage = "preProduction";
        command.IsLifecycleStageChanged = true;

        Mock.Get(_productRepository)
            .Setup(x => x.GetAsync(
                tenantId,
                It.Is<ProductWhere>(w => w.Id_in.Contains(command.ProductId)),
                null,
                It.IsAny<QueryParameters>(),
                It.IsAny<bool>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { existingProduct });

        Mock.Get(_productRepository)
            .Setup(x => x.UpdateAsync(
                tenantId,
                It.IsAny<UpdateProductCommand>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });
        _productEventStore
            .Setup(x => x.AddEventAsync(
                tenantId,
                It.Is<ProductEvent>(e =>
                    e.ProductId == command.ProductId &&
                    e.Type == ProductEventType.update),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        // Act (When)
        var result = await _productService.UpdateAsync(tenantId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
    }

    [Theory]
    [CustomAutoData]
    public async Task UpdateAsync_WhenNonLegacyAndCustomizationDisabled_ShouldRestrictFields(
        string tenantId,
        UpdateProductCommand command,
        Product existingProduct)
    {
        // Arrange (Given)
        existingProduct.Id = new ProductId
        {
            Plan = existingProduct.Id.Plan,
            Type = existingProduct.Id.Type,
            Version = "2.0"
        };
        existingProduct.AllowCustomProduct = false; // Customization disabled
        existingProduct.Fields = "{\"productName\":\"oldName\",\"productType\":\"oldType\",\"customField\":\"oldValue\"}";
        command.Fields = "{\"productName\":\"newName\",\"productType\":\"newType\",\"customField\":\"newValue\"}";

        Mock.Get(_featureManager)
            .Setup(x => x.IsEnabled("UseLegacyLifecycle", tenantId))
            .ReturnsAsync(false); // Non-legacy

        Mock.Get(_productRepository)
            .Setup(x => x.GetAsync(
                tenantId,
                It.Is<ProductWhere>(w => w.Id_in.Contains(command.ProductId)),
                null,
                It.IsAny<QueryParameters>(),
                It.IsAny<bool>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { existingProduct });

        Mock.Get(_productRepository)
            .Setup(x => x.UpdateAsync(
                tenantId,
                It.Is<UpdateProductCommand>(c =>
                    c.Fields.Contains("oldName") && // Should keep old locked fields
                    c.Fields.Contains("oldType") &&
                    c.Fields.Contains("newValue")), // Should allow non-locked fields
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        _productEventStore
            .Setup(x => x.AddEventAsync(
                tenantId,
                It.Is<ProductEvent>(e =>
                    e.ProductId == command.ProductId &&
                    e.Type == ProductEventType.update),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        // Act (When)
        var result = await _productService.UpdateAsync(tenantId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
    }

    [Theory]
    [CustomAutoData]
    public async Task UpdateAsync_WhenNonLegacyButCustomVersion_ShouldNotRestrictFields(
        string tenantId,
        UpdateProductCommand command,
        Product existingProduct)
    {
        // Arrange (Given)
        existingProduct.Id = new ProductId
        {
            Plan = existingProduct.Id.Plan,
            Type = existingProduct.Id.Type,
            Version = "2.0_custom" // Custom version
        };
        existingProduct.AllowCustomProduct = false; // Even with customization disabled
        existingProduct.Fields = "{\"productName\":\"oldName\",\"productType\":\"oldType\",\"customField\":\"oldValue\"}";
        command.Fields = "{\"productName\":\"newName\",\"productType\":\"newType\",\"customField\":\"newValue\"}";

        Mock.Get(_featureManager)
            .Setup(x => x.IsEnabled("UseLegacyLifecycle", tenantId))
            .ReturnsAsync(false); // Non-legacy

        Mock.Get(_productRepository)
            .Setup(x => x.GetAsync(
                tenantId,
                It.Is<ProductWhere>(w => w.Id_in.Contains(command.ProductId)),
                null,
                It.IsAny<QueryParameters>(),
                It.IsAny<bool>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { existingProduct });

        Mock.Get(_productRepository)
            .Setup(x => x.UpdateAsync(
                tenantId,
                It.Is<UpdateProductCommand>(c =>
                    c.Fields.Contains("newName") && // Should allow all field updates
                    c.Fields.Contains("newType") &&
                    c.Fields.Contains("newValue")),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        _productEventStore
            .Setup(x => x.AddEventAsync(
                tenantId,
                It.Is<ProductEvent>(e =>
                    e.ProductId == command.ProductId &&
                    e.Type == ProductEventType.update),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        // Act (When)
        var result = await _productService.UpdateAsync(tenantId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
    }

    [Theory]
    [CustomAutoData]
    public async Task UpdateAsync_WhenLegacyProduct_ShouldNotRestrictFields(
        string tenantId,
        UpdateProductCommand command,
        Product existingProduct)
    {
        // Arrange (Given)
        existingProduct.Id = new ProductId
        {
            Plan = existingProduct.Id.Plan,
            Type = existingProduct.Id.Type,
            Version = "2.0"
        };
        existingProduct.AllowCustomProduct = false;
        existingProduct.Fields = "{\"productName\":\"oldName\",\"productType\":\"oldType\",\"customField\":\"oldValue\"}";
        command.Fields = "{\"productName\":\"newName\",\"productType\":\"newType\",\"customField\":\"newValue\"}";

        Mock.Get(_featureManager)
            .Setup(x => x.IsEnabled("UseLegacyLifecycle", tenantId))
            .ReturnsAsync(true); // Legacy product

        Mock.Get(_productRepository)
            .Setup(x => x.GetAsync(
                tenantId,
                It.Is<ProductWhere>(w => w.Id_in.Contains(command.ProductId)),
                null,
                It.IsAny<QueryParameters>(),
                It.IsAny<bool>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { existingProduct });

        Mock.Get(_productRepository)
            .Setup(x => x.UpdateAsync(
                tenantId,
                It.Is<UpdateProductCommand>(c =>
                    c.Fields.Contains("newName") && // Should allow all field updates
                    c.Fields.Contains("newType") &&
                    c.Fields.Contains("newValue")),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        _productEventStore
            .Setup(x => x.AddEventAsync(
                tenantId,
                It.Is<ProductEvent>(e =>
                    e.ProductId == command.ProductId &&
                    e.Type == ProductEventType.update),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        // Act (When)
        var result = await _productService.UpdateAsync(tenantId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
    }

    #region GetAsync Tests

    [Theory]
    [CustomAutoData]
    [Trait("Ticket", "CH-25290")]
    public async Task GetAsync_WithValidParameters_ShouldReturnProducts(
        string tenantId,
        string clientId,
        QueryParameters queryParameters,
        List<Product> expectedProducts)
    {
        // Arrange (Given)
        var where = new ProductWhere();
        var productConfig = new ProductConfig();

        Mock.Get(_productRepository)
            .Setup(x => x.GetProductConfigAsync(tenantId, clientId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(productConfig);

        Mock.Get(_productRepository)
            .Setup(x => x.GetAsync(tenantId, where, productConfig, queryParameters, false, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedProducts);

        // Act (When)
        var result = await _productService.GetAsync(tenantId, clientId, where, queryParameters, false, CancellationToken.None);

        // Assert (Then)
        result.Should().BeEquivalentTo(expectedProducts);
    }

    [Theory]
    [CustomAutoData]
    [Trait("Ticket", "CH-25290")]
    public async Task GetAsync_WithFactors_ShouldFilterProductsByConditions(
        string tenantId,
        string clientId,
        QueryParameters queryParameters,
        List<Product> products)
    {
        // Arrange (Given)
        var where = new ProductWhere();
        var productConfig = new ProductConfig();
        var factors = JToken.FromObject(new { age = 25, premium = 1000 });

        // Create products with benefits that have conditions
        var i = 0;
        foreach (var product in products)
        {
            product.Benefits = new List<Benefit>
            {
                new Benefit
                {
                    TypeId = "benefit1",
                    Condition = new Condition
                    {
                        JsonLogicRule = JObject.FromObject(new { var1 = "test" })
                    }
                }
            };
            if (i < _underwritingEngines.Count)
            {
                var mockEngine = _underwritingEngines[i];
                mockEngine.SetupGet(x => x.ProviderId).Returns(product.Underwriting.SourceType);
                mockEngine
                    .Setup(x => x.EvaluateUnderwritingsAsync(tenantId, It.IsAny<IEnumerable<ProductId>>(), It.IsAny<JToken>(), It.IsAny<CancellationToken>()))
                    .ReturnsAsync([(product.Id, Result.Success())]);
            }
            i++;
        }

        Mock.Get(_productRepository)
            .Setup(x => x.GetProductConfigAsync(tenantId, clientId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(productConfig);

        Mock.Get(_productRepository)
            .Setup(x => x.GetAsync(tenantId, where, productConfig, queryParameters, false, It.IsAny<CancellationToken>()))
            .ReturnsAsync(products);

        // Act (When)
        var result = await _productService.GetAsync(tenantId, clientId, where, factors, queryParameters, false, CancellationToken.None);

        // Assert (Then)
        result.Should().NotBeEmpty();
        result.Should().HaveCount(products.Count);
    }

    #endregion

    #region GetTotalCountAsync Tests

    [Theory]
    [CustomAutoData]
    [Trait("Ticket", "CH-25290")]
    public async Task GetTotalCountAsync_WithValidParameters_ShouldReturnCount(
        string tenantId,
        string clientId,
        long expectedCount)
    {
        // Arrange (Given)
        var where = new ProductWhere();
        var productConfig = new ProductConfig();

        Mock.Get(_productRepository)
            .Setup(x => x.GetProductConfigAsync(tenantId, clientId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(productConfig);

        Mock.Get(_productRepository)
            .Setup(x => x.GetTotalCountAsync(tenantId, where, productConfig, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedCount);

        // Act (When)
        var result = await _productService.GetTotalCountAsync(tenantId, clientId, where, CancellationToken.None);

        // Assert (Then)
        result.Should().Be(expectedCount);
    }

    #endregion

    #region CloneAsync Tests

    [Theory]
    [CustomAutoData]
    [Trait("Ticket", "CH-25290")]
    public async Task CloneAsync_WhenSuccessful_ShouldReturnSuccessAndCreateEvent(
        string tenantId,
        CloneProductCommand command,
        Product originalProduct)
    {
        // Arrange (Given)
        command.IssuerProductId = "generated-id";
        command.ScriptIds = new List<string> { "script1", "script2" };

        var productVersionRepository = _fixture.Freeze<Mock<IProductVersionRepository>>();
        var productScriptsRepository = _fixture.Freeze<Mock<IProductScriptsRepository>>();
        var refGen = _fixture.Freeze<Mock<IReferenceGenerator>>();

        productVersionRepository
            .Setup(x => x.Get(command.ProductId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(originalProduct);

        productScriptsRepository
            .Setup(x => x.CloneProductScripts(originalProduct.ScriptIds, It.IsAny<CancellationToken>()))
            .ReturnsAsync(command.ScriptIds);

        Mock.Get(_productRepository)
            .Setup(x => x.CloneAsync(tenantId, command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        _productEventStore
            .Setup(x => x.AddEventAsync(tenantId, It.IsAny<ProductEvent>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        // Act (When)
        var result = await _productService.CloneAsync(tenantId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
        _productEventStore.Verify(
            x => x.AddEventAsync(tenantId, It.IsAny<ProductEvent>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Theory]
    [CustomAutoData]
    [Trait("Ticket", "CH-25290")]
    public async Task CloneAsync_WhenRepositoryFails_ShouldReturnFailure(
        string tenantId,
        CloneProductCommand command,
        Product originalProduct)
    {
        // Arrange (Given)
        var productVersionRepository = _fixture.Freeze<Mock<IProductVersionRepository>>();
        var productScriptsRepository = _fixture.Freeze<Mock<IProductScriptsRepository>>();

        productVersionRepository
            .Setup(x => x.Get(command.ProductId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(originalProduct);

        productScriptsRepository
            .Setup(x => x.CloneProductScripts(originalProduct.ScriptIds, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<string>());

        Mock.Get(_productRepository)
            .Setup(x => x.CloneAsync(tenantId, command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "failure", Errors = new List<string> { "Clone failed" } });

        // Act (When)
        var result = await _productService.CloneAsync(tenantId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("failure");
        result.Errors.Should().Contain("Clone failed");
    }

    #endregion

    #region CreateAsync Tests

    [Theory]
    [CustomAutoData]
    [Trait("Ticket", "CH-25290")]
    public async Task CreateAsync_WhenSuccessful_ShouldReturnSuccessAndCreateEvent(
        string tenantId,
        CreateProductCommand command)
    {
        // Arrange (Given)
        Mock.Get(_productRepository)
            .Setup(x => x.CreateAsync(tenantId, command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        _productEventStore
            .Setup(x => x.AddEventAsync(tenantId, It.IsAny<ProductEvent>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        // Act (When)
        var result = await _productService.CreateAsync(tenantId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
        _productEventStore.Verify(
            x => x.AddEventAsync(tenantId, It.IsAny<ProductEvent>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Theory]
    [CustomAutoData]
    [Trait("Ticket", "CH-25290")]
    public async Task CreateAsync_WhenRepositoryFails_ShouldReturnFailure(
        string tenantId,
        CreateProductCommand command)
    {
        // Arrange (Given)
        Mock.Get(_productRepository)
            .Setup(x => x.CreateAsync(tenantId, command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "failure", Errors = new List<string> { "Creation failed" } });

        // Act (When)
        var result = await _productService.CreateAsync(tenantId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("failure");
        result.Errors.Should().Contain("Creation failed");
    }

    [Theory]
    [CustomAutoData]
    [Trait("Ticket", "CH-25290")]
    public async Task CreateAsync_WhenEventCreationFails_ShouldReturnFailure(
        string tenantId,
        CreateProductCommand command)
    {
        // Arrange (Given)
        Mock.Get(_productRepository)
            .Setup(x => x.CreateAsync(tenantId, command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        _productEventStore
            .Setup(x => x.AddEventAsync(tenantId, It.IsAny<ProductEvent>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "failure", Errors = new List<string> { "Event creation failed" } });

        // Act (When)
        var result = await _productService.CreateAsync(tenantId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("failure");
        result.Errors.Should().Contain("Event creation failed");
    }

    #endregion

    #region DeleteAsync Tests

    [Theory]
    [CustomAutoData]
    [Trait("Ticket", "CH-25290")]
    public async Task DeleteAsync_WhenSuccessful_ShouldReturnSuccessAndCreateEvent(
        string tenantId,
        DeleteProductCommand command)
    {
        // Arrange (Given)
        Mock.Get(_productRepository)
            .Setup(x => x.DeleteAsync(tenantId, command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        _productEventStore
            .Setup(x => x.AddEventAsync(tenantId, It.IsAny<ProductEvent>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        // Act (When)
        var result = await _productService.DeleteAsync(tenantId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
        _productEventStore.Verify(
            x => x.AddEventAsync(tenantId, It.Is<ProductEvent>(e =>
                e.ProductId == command.ProductId &&
                e.Type == ProductEventType.delete), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Theory]
    [CustomAutoData]
    [Trait("Ticket", "CH-25290")]
    public async Task DeleteAsync_WhenRepositoryFails_ShouldReturnFailure(
        string tenantId,
        DeleteProductCommand command)
    {
        // Arrange (Given)
        Mock.Get(_productRepository)
            .Setup(x => x.DeleteAsync(tenantId, command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "failure", Errors = new List<string> { "Delete failed" } });

        // Act (When)
        var result = await _productService.DeleteAsync(tenantId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("failure");
        result.Errors.Should().Contain("Delete failed");
    }

    #endregion

    #region BenefitBatchAsync Tests

    [Theory]
    [CustomAutoData]
    [Trait("Ticket", "CH-25290")]
    public async Task BenefitBatchAsync_WhenProductNotFound_ShouldReturnFailure(
        string tenantId,
        ProductId productId,
        BenefitCommandBatch batch)
    {
        // Arrange (Given)
        Mock.Get(_productRepository)
            .Setup(x => x.GetAsync(tenantId, It.IsAny<ProductWhere>(), null, It.IsAny<QueryParameters>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Array.Empty<Product>());

        // Act (When)
        var result = await _productService.BenefitBatchAsync(tenantId, productId, batch, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("failure");
        result.Errors.Should().Contain($"The product {productId.ToString()} was not found.");
    }

    [Theory]
    [CustomAutoData]
    [Trait("Ticket", "CH-25290")]
    public async Task BenefitBatchAsync_WithAddBenefitCommands_ShouldProcessSuccessfully(
        string tenantId,
        ProductId productId,
        Product existingProduct,
        List<AddBenefitCommand> addBenefitCommands)
    {
        // Arrange (Given)
        var batch = new BenefitCommandBatch { AddBenefitCommands = addBenefitCommands };

        Mock.Get(_productRepository)
            .Setup(x => x.GetAsync(tenantId, It.IsAny<ProductWhere>(), null, It.IsAny<QueryParameters>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { existingProduct });

        Mock.Get(_productRepository)
            .Setup(x => x.AddManyBenefitsAsync(tenantId, productId, addBenefitCommands, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        _productEventStore
            .Setup(x => x.AddEventAsync(tenantId, It.IsAny<ProductEvent>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        // Act (When)
        var result = await _productService.BenefitBatchAsync(tenantId, productId, batch, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
        _productEventStore.Verify(
            x => x.AddEventAsync(tenantId, It.Is<ProductEvent>(e => e.Type == ProductEventType.addBenefit), It.IsAny<CancellationToken>()),
            Times.Exactly(addBenefitCommands.Count));
    }

    #endregion

    #region FactBatchAsync Tests

    [Theory]
    [CustomAutoData]
    [Trait("Ticket", "CH-25290")]
    public async Task FactBatchAsync_WhenProductNotFound_ShouldReturnFailure(
        string tenantId,
        ProductId productId,
        FactCommandBatch batch)
    {
        // Arrange (Given)
        Mock.Get(_productRepository)
            .Setup(x => x.GetAsync(tenantId, It.IsAny<ProductWhere>(), null, It.IsAny<QueryParameters>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Array.Empty<Product>());

        // Act (When)
        var result = await _productService.FactBatchAsync(tenantId, productId, batch, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("failure");
        result.Errors.Should().Contain($"The product {productId.ToString()} was not found.");
    }

    [Theory]
    [CustomAutoData]
    [Trait("Ticket", "CH-25290")]
    public async Task FactBatchAsync_WithAddFactCommands_ShouldProcessSuccessfully(
        string tenantId,
        ProductId productId,
        Product existingProduct,
        List<AddFactCommand> addFactCommands)
    {
        // Arrange (Given)
        var batch = new FactCommandBatch { AddFactCommands = addFactCommands };

        Mock.Get(_productRepository)
            .Setup(x => x.GetAsync(tenantId, It.IsAny<ProductWhere>(), null, It.IsAny<QueryParameters>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { existingProduct });

        Mock.Get(_productRepository)
            .Setup(x => x.AddManyFactsAsync(tenantId, productId, addFactCommands, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        _productEventStore
            .Setup(x => x.AddEventAsync(tenantId, It.IsAny<ProductEvent>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        // Act (When)
        var result = await _productService.FactBatchAsync(tenantId, productId, batch, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
        _productEventStore.Verify(
            x => x.AddEventAsync(tenantId, It.Is<ProductEvent>(e => e.Type == ProductEventType.addFact), It.IsAny<CancellationToken>()),
            Times.Exactly(addFactCommands.Count));
    }

    [Theory]
    [CustomAutoData]
    [Trait("Ticket", "CH-25290")]
    public async Task FactBatchAsync_WithUpdateFactCommands_WhenFactNotFound_ShouldReturnFailure(
        string tenantId,
        ProductId productId,
        Product existingProduct,
        List<UpdateFactCommand> updateFactCommands)
    {
        // Arrange (Given)
        existingProduct.Facts = new List<Fact>(); // Empty facts list
        var batch = new FactCommandBatch { UpdateFactCommands = updateFactCommands };

        Mock.Get(_productRepository)
            .Setup(x => x.GetAsync(tenantId, It.IsAny<ProductWhere>(), null, It.IsAny<QueryParameters>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { existingProduct });

        // Act (When)
        var result = await _productService.FactBatchAsync(tenantId, productId, batch, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("failure");
        result.Errors.Should().Contain(e => e.Contains("were not found for this product"));
    }

    #endregion

    #region RemoveFactAsync Tests

    [Theory]
    [CustomAutoData]
    [Trait("Ticket", "CH-25290")]
    public async Task RemoveFactAsync_WhenSuccessful_ShouldReturnSuccessAndCreateEvent(
        string tenantId,
        ProductId productId,
        RemoveCommand command)
    {
        // Arrange (Given)
        Mock.Get(_productRepository)
            .Setup(x => x.RemoveFactAsync(tenantId, productId, command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        _productEventStore
            .Setup(x => x.AddEventAsync(tenantId, It.IsAny<ProductEvent>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        // Act (When)
        var result = await _productService.RemoveFactAsync(tenantId, productId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
        _productEventStore.Verify(
            x => x.AddEventAsync(tenantId, It.Is<ProductEvent>(e =>
                e.ProductId == productId &&
                e.Type == ProductEventType.removeFact), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Theory]
    [CustomAutoData]
    [Trait("Ticket", "CH-25290")]
    public async Task RemoveFactAsync_WhenRepositoryFails_ShouldReturnFailure(
        string tenantId,
        ProductId productId,
        RemoveCommand command)
    {
        // Arrange (Given)
        Mock.Get(_productRepository)
            .Setup(x => x.RemoveFactAsync(tenantId, productId, command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "failure", Errors = new List<string> { "Remove failed" } });

        // Act (When)
        var result = await _productService.RemoveFactAsync(tenantId, productId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("failure");
        result.Errors.Should().Contain("Remove failed");
    }

    #endregion

    #region AddInternalReviewAsync Tests

    [Theory]
    [CustomAutoData]
    [Trait("Ticket", "CH-25290")]
    public async Task AddInternalReviewAsync_WhenProductNotFound_ShouldReturnFailure(
        string tenantId,
        ProductId productId,
        AddInternalReviewCommand command)
    {
        // Arrange (Given)
        Mock.Get(_productRepository)
            .Setup(x => x.GetAsync(tenantId, It.IsAny<ProductWhere>(), null, It.IsAny<QueryParameters>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Array.Empty<Product>());

        // Act (When)
        var result = await _productService.AddInternalReviewAsync(tenantId, productId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("failure");
        result.Errors.Should().Contain($"The product {productId.ToString()} was not found.");
    }

    [Theory]
    [CustomAutoData]
    [Trait("Ticket", "CH-25290")]
    public async Task AddInternalReviewAsync_WhenSuccessful_ShouldReturnSuccessWithId(
        string tenantId,
        ProductId productId,
        Product existingProduct,
        AddInternalReviewCommand command)
    {
        // Arrange (Given)
        Mock.Get(_productRepository)
            .Setup(x => x.GetAsync(tenantId, It.IsAny<ProductWhere>(), null, It.IsAny<QueryParameters>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { existingProduct });

        Mock.Get(_productRepository)
            .Setup(x => x.AddInternalReviewAsync(tenantId, productId, command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        _productEventStore
            .Setup(x => x.AddEventAsync(tenantId, It.IsAny<ProductEvent>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Result { Status = "success" });

        // Act (When)
        var result = await _productService.AddInternalReviewAsync(tenantId, productId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("success");
        result.Value.Id.Should().NotBeNullOrEmpty();
        _productEventStore.Verify(
            x => x.AddEventAsync(tenantId, It.Is<ProductEvent>(e => e.Type == ProductEventType.addInternalReview), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    #endregion

    #region UpdateInternalReviewAsync Tests

    [Theory]
    [CustomAutoData]
    [Trait("Ticket", "CH-25290")]
    public async Task UpdateInternalReviewAsync_WhenProductNotFound_ShouldReturnFailure(
        string tenantId,
        ProductId productId,
        UpdateInternalReviewCommand command)
    {
        // Arrange (Given)
        Mock.Get(_productRepository)
            .Setup(x => x.GetAsync(tenantId, It.IsAny<ProductWhere>(), null, It.IsAny<QueryParameters>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Array.Empty<Product>());

        // Act (When)
        var result = await _productService.UpdateInternalReviewAsync(tenantId, productId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("failure");
        result.Errors.Should().Contain($"The product {productId.ToString()} was not found.");
    }

    [Theory]
    [CustomAutoData]
    [Trait("Ticket", "CH-25290")]
    public async Task UpdateInternalReviewAsync_WhenInternalReviewNotFound_ShouldReturnFailure(
        string tenantId,
        ProductId productId,
        Product existingProduct,
        UpdateInternalReviewCommand command)
    {
        // Arrange (Given)
        existingProduct.InternalReviews = new List<InternalReview>(); // Empty list

        Mock.Get(_productRepository)
            .Setup(x => x.GetAsync(tenantId, It.IsAny<ProductWhere>(), null, It.IsAny<QueryParameters>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { existingProduct });

        // Act (When)
        var result = await _productService.UpdateInternalReviewAsync(tenantId, productId, command, CancellationToken.None);

        // Assert (Then)
        result.Status.Should().Be("failure");
        result.Errors.Should().Contain($"The internal review with Id '{command.Id}' was not found for this product.");
    }

    #endregion

    #region GetBenefitInfosAsync Tests

    [Theory]
    [CustomAutoData]
    [Trait("Ticket", "CH-25290")]
    public async Task GetBenefitInfosAsync_ShouldReturnBenefitInfos(
        string tenantId,
        string clientId,
        Dictionary<string, Dictionary<string, List<string>>> expectedBenefitInfos)
    {
        // Arrange (Given)
        Mock.Get(_productRepository)
            .Setup(x => x.GetBenefitInfosAsync(tenantId, clientId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedBenefitInfos);

        // Act (When)
        var result = await _productService.GetBenefitInfosAsync(tenantId, clientId, CancellationToken.None);

        // Assert (Then)
        result.Should().BeEquivalentTo(expectedBenefitInfos);
    }

    #endregion

    #region GetBenefitCategoriesAsync Tests

    [Theory]
    [CustomAutoData]
    [Trait("Ticket", "CH-25290")]
    public async Task GetBenefitCategoriesAsync_ShouldReturnBenefitCategories(
        string tenantId,
        string clientId,
        Dictionary<string, List<string>> expectedCategories)
    {
        // Arrange (Given)
        Mock.Get(_productRepository)
            .Setup(x => x.GetBenefitCategoriesAsync(tenantId, clientId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedCategories);

        // Act (When)
        var result = await _productService.GetBenefitCategoriesAsync(tenantId, clientId, CancellationToken.None);

        // Assert (Then)
        result.Should().BeEquivalentTo(expectedCategories);
    }

    #endregion

    #region GetProductConfigsAsync Tests

    [Theory]
    [CustomAutoData]
    [Trait("Ticket", "CH-25290")]
    public async Task GetProductConfigsAsync_ShouldReturnProductConfigs(
        string tenantId,
        OrderBy orderBy,
        int skip,
        int first,
        List<ProductConfig> expectedConfigs)
    {
        // Arrange (Given)
        var where = new ProductConfigWhere();
        Mock.Get(_productRepository)
            .Setup(x => x.GetProductConfigsAsync(tenantId, where, orderBy, skip, first, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedConfigs);

        // Act (When)
        var result = await _productService.GetProductConfigsAsync(tenantId, where, orderBy, skip, first, CancellationToken.None);

        // Assert (Then)
        result.Should().BeEquivalentTo(expectedConfigs);
    }

    #endregion
}