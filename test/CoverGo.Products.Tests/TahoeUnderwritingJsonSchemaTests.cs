﻿using CoverGo.Products.Infrastructure.Underwriting.Adapters.EBao;

using Newtonsoft.Json;

using System.Collections.Generic;
using System.IO;

namespace CoverGo.Products.Tests
{
    public class TahoeUnderwritingJsonSchemaTests
    {
        [Fact]
        public void GenerateSchema_Test()
        {
            string actual = FormatJson(EBaoUnderwritingEngine.GenerateSchema(GetProductSumAssured(), GetProductAge()));

            string expected = ReadFile("ProductSumAssuredSchema.json");

            Assert.Equal(actual, expected);
        }

        [Fact]
        public void GenerateSchema_With_InsuredAge_Test()
        {
            string actual = FormatJson(EBaoUnderwritingEngine.GenerateSchema(GetProductSumAssured(), null, 40));

            string expected = ReadFile("ProductSumAssuredSchema_WithAge.json");

            Assert.Equal(actual, expected);
        }

        [Fact]
        public void GenerateSchema_With_EmployedStatus_Test()
        {
            string actual = FormatJson(EBaoUnderwritingEngine.GenerateSchema(GetProductSumAssured(), GetProductAge(), null, "housewife"));

            string expected = ReadFile("ProductSumAssuredSchema_WithEmployedStatus.json");

            Assert.Equal(actual, expected);
        }

        [Fact]
        public void GenerateSchema_With_Age_And_EmployedStatus_Test()
        {
            string actual = FormatJson(EBaoUnderwritingEngine.GenerateSchema(GetProductSumAssured(), null, 40, "housewife"));

            string expected = ReadFile("ProductSumAssuredSchema_WithAgeAndEmpoyedStatus.json");

            Assert.Equal(actual, expected);
        }

        EBaoUnderwritingEngine.EBaoProductAge GetProductAge() =>
            new EBaoUnderwritingEngine.EBaoProductAge
            {
                InsuredAge = new List<EBaoUnderwritingEngine.EBaoAgeLimitInformation>
                    {
                            new EBaoUnderwritingEngine.EBaoAgeLimitInformation { MinAge = 18, MaxAge = 75 }
                    }
            };

        EBaoUnderwritingEngine.EBaoProductSumAssured GetProductSumAssured() =>
            JsonConvert.DeserializeObject<EBaoUnderwritingEngine.EBaoProductSumAssured>(ReadFile("ProductSumAssured.json"));

        string ReadFile(string fileName) =>
            File.OpenText(fileName).ReadToEnd();

        static string FormatJson(string json) =>
            JsonConvert.SerializeObject(JsonConvert.DeserializeObject(json), Formatting.Indented);
    }
}