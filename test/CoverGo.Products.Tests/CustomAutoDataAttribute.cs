using System;
using AutoFixture;
using AutoFixture.AutoMoq;
using AutoFixture.Xunit2;

namespace CoverGo.Products.Tests;

[AttributeUsage(AttributeTargets.Method)]
public class CustomAutoDataAttribute : AutoDataAttribute
{
    public CustomAutoDataAttribute() : base(CreateFixture)
    {}

    private static IFixture CreateFixture()
    {
        var fixture = new Fixture();
        fixture
            .Customize(new AutoMoqCustomization { ConfigureMembers = true, GenerateDelegates = true })
            .Customize<DateOnly>(composer => composer.FromFactory<DateTime>(DateOnly.FromDateTime));
        return fixture;
    }
}

public class CustomInlineAutoDataAttribute : InlineAutoDataAttribute
{
    public CustomInlineAutoDataAttribute(params object[] objects) : base(new CustomAutoDataAttribute(), objects) { }
}
