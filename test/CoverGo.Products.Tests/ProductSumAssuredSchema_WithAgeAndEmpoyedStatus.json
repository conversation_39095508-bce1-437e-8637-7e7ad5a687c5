﻿{
  "$schema": "http://json-schema.org/draft-07/schema",
  "$id": "http://example.com/example.json",
  "type": "object",
  "required": [
    "insureds"
  ],
  "properties": {
    "insureds": {
      "minItems": 1,
      "maxItems": 1,
      "items": {
        "allOf": [
          {
            "required": [
              "sumAssured"
            ],
            "properties": {
              "sumAssured": {
                "type": "integer",
                "minimum": 250000,
                "maximum": 3200000
              }
            }
          }
        ]
      }
    }
  }
}