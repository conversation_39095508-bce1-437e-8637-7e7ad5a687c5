FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS build-service
COPY --from=mcr.microsoft.com/dotnet/sdk:7.0-alpine /usr/share/dotnet /usr/share/dotnet
COPY --from=mcr.microsoft.com/dotnet/sdk:6.0-alpine /usr/share/dotnet /usr/share/dotnet
ARG BUILDCONFIG=Release
ARG CI_BUILD=true
ARG VERSION=1.0.0
ARG APP_VERSION=1.0.0
ARG GH_ACCOUNT
ARG GH_TOKEN
WORKDIR /app

COPY nuget.config .
COPY Directory.Packages.props .
COPY Directory.Build.props .
COPY Directory.Build.targets .

RUN dotnet nuget update source github --username ${GH_ACCOUNT} --password ${GH_TOKEN} --store-password-in-clear-text

COPY ["src/CoverGo.Products.Application/CoverGo.Products.Application.csproj", "src/CoverGo.Products.Application/"]
COPY ["src/CoverGo.Products.Domain/CoverGo.Products.Domain.csproj", "src/CoverGo.Products.Domain/"]
COPY ["src/CoverGo.Products.Infrastructure/CoverGo.Products.Infrastructure.csproj", "src/CoverGo.Products.Infrastructure/"]
COPY ["src/CoverGo.Products.Infrastructure.GatewayClient/CoverGo.Products.Infrastructure.GatewayClient.csproj", "src/CoverGo.Products.Infrastructure.GatewayClient/"]
COPY ["src/CoverGo.Products.Tests.GatewayClient/CoverGo.Products.Tests.GatewayClient.csproj", "src/CoverGo.Products.Tests.GatewayClient/"]
COPY ["src/CoverGo.Products.Client/CoverGo.Products.Client.csproj", "src/CoverGo.Products.Client/"]
COPY ["test/CoverGo.Products.Tests/CoverGo.Products.Tests.csproj", "test/CoverGo.Products.Tests/"]
COPY ["test/CoverGo.Products.Tests.Integration/CoverGo.Products.Tests.Integration.csproj", "test/CoverGo.Products.Tests.Integration/"]
COPY ["CoverGo.Products.sln", "CoverGo.Products.sln"]
RUN dotnet restore
COPY . .
RUN dotnet publish ./src/CoverGo.Products.Application/CoverGo.Products.Application.csproj -c $BUILDCONFIG -o ./out /p:Version=$APP_VERSION --no-restore

FROM mcr.microsoft.com/dotnet/aspnet:7.0-alpine AS runtime
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
RUN apk add --no-cache icu-libs

WORKDIR /app
COPY --from=build-service /app/out .

RUN adduser -D buildadmin
RUN chown buildadmin:buildadmin /app /app/*
USER buildadmin

ENV ASPNETCORE_URLS http://*:8080
EXPOSE 8080
ARG COMMIT_SHA
ENV SENTRY_RELEASE=${COMMIT_SHA} REVISION=${COMMIT_SHA}
ENTRYPOINT ["dotnet", "CoverGo.Products.Application.dll"]

FROM build-service as build-client
ARG FILE_VERSION="1.0.0.0"
ARG INFORMATIONAL_VERSION="1.0"
RUN dotnet build ./src/CoverGo.Products.Client/CoverGo.Products.Client.csproj -c $BUILDCONFIG --no-restore -v Minimal -p:Version="$APP_VERSION" /p:FileVersion="$FILE_VERSION" /p:InformationVersion="$INFORMATIONAL_VERSION"

FROM build-client as nuget
RUN dotnet pack ./src/CoverGo.Products.Client/CoverGo.Products.Client.csproj -c $BUILDCONFIG -o nuget --no-build -p:IncludeSymbols=true -p:SymbolPackageFormat=snupkg /p:PackageVersion="$APP_VERSION"  /p:Version="$APP_VERSION" /p:FileVersion="$FILE_VERSION" /p:InformationVersion="$INFORMATIONAL_VERSION"

FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS tests
COPY --from=mcr.microsoft.com/dotnet/sdk:7.0-alpine /usr/share/dotnet /usr/share/dotnet
COPY --from=mcr.microsoft.com/dotnet/sdk:6.0-alpine /usr/share/dotnet /usr/share/dotnet
ARG BUILDCONFIG=Debug
ARG GH_ACCOUNT
ARG GH_TOKEN
WORKDIR /app
COPY . .
RUN dotnet nuget update source github --username ${GH_ACCOUNT} --password ${GH_TOKEN} --store-password-in-clear-text

RUN dotnet restore
ARG APP_VERSION=1.0.0

RUN dotnet build -c $BUILDCONFIG /p:Version=$APP_VERSION --no-restore

ENTRYPOINT dotnet test --collect:"XPlat Code Coverage" --no-build --verbosity normal --settings coverlet.runsettings\
  --logger:"junit;LogFileName=TestResults.{assembly}.{framework}.xml;verbosity=normal"\
  --logger:"console;verbosity=normal"

FROM mongo AS mongo-products
COPY mongo-products-init.js /docker-entrypoint-initdb.d/
EXPOSE 27017
