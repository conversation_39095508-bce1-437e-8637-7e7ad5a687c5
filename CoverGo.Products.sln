﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.9.34728.123
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{C87FDCAB-ED08-40B7-A736-BF6AC1B43D4B}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		Directory.Build.props = Directory.Build.props
		Directory.Build.targets = Directory.Build.targets
		Directory.Packages.props = Directory.Packages.props
		docker-compose.ci.yml = docker-compose.ci.yml
		Dockerfile = Dockerfile
		Mongo.Dockerfile = Mongo.Dockerfile
		nuget.config = nuget.config
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CoverGo.Products.Domain", "src\CoverGo.Products.Domain\CoverGo.Products.Domain.csproj", "{D23C0A1D-4644-471D-B02E-19AFDA6785B2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CoverGo.Products.Infrastructure", "src\CoverGo.Products.Infrastructure\CoverGo.Products.Infrastructure.csproj", "{E257F8B3-E521-4ACE-84CA-E33369A20900}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CoverGo.Products.Application", "src\CoverGo.Products.Application\CoverGo.Products.Application.csproj", "{194619F0-C40C-40F9-B532-E2AC181F52C9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CoverGo.Products.Tests", "test\CoverGo.Products.Tests\CoverGo.Products.Tests.csproj", "{46F99AC3-E75B-436C-AEC7-3AD7CFAB15A2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CoverGo.Products.Tests.Integration", "test\CoverGo.Products.Tests.Integration\CoverGo.Products.Tests.Integration.csproj", "{E477540E-6891-431E-B2F5-2631454178C4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CoverGo.Products.Client", "src\CoverGo.Products.Client\CoverGo.Products.Client.csproj", "{2D74C86F-05B4-42B6-9A54-604F7870113D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CoverGo.Products.Tests.GatewayClient", "src\CoverGo.Products.Tests.GatewayClient\CoverGo.Products.Tests.GatewayClient.csproj", "{4DF9E51B-36CB-4694-9DCC-5E71BF5E35DA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CoverGo.Products.Infrastructure.GatewayClient", "src\CoverGo.Products.Infrastructure.GatewayClient\CoverGo.Products.Infrastructure.GatewayClient.csproj", "{171CEFA0-9F3C-452E-8491-8566568F6C96}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{D23C0A1D-4644-471D-B02E-19AFDA6785B2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D23C0A1D-4644-471D-B02E-19AFDA6785B2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D23C0A1D-4644-471D-B02E-19AFDA6785B2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D23C0A1D-4644-471D-B02E-19AFDA6785B2}.Release|Any CPU.Build.0 = Release|Any CPU
		{E257F8B3-E521-4ACE-84CA-E33369A20900}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E257F8B3-E521-4ACE-84CA-E33369A20900}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E257F8B3-E521-4ACE-84CA-E33369A20900}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E257F8B3-E521-4ACE-84CA-E33369A20900}.Release|Any CPU.Build.0 = Release|Any CPU
		{194619F0-C40C-40F9-B532-E2AC181F52C9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{194619F0-C40C-40F9-B532-E2AC181F52C9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{194619F0-C40C-40F9-B532-E2AC181F52C9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{194619F0-C40C-40F9-B532-E2AC181F52C9}.Release|Any CPU.Build.0 = Release|Any CPU
		{46F99AC3-E75B-436C-AEC7-3AD7CFAB15A2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{46F99AC3-E75B-436C-AEC7-3AD7CFAB15A2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{46F99AC3-E75B-436C-AEC7-3AD7CFAB15A2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{46F99AC3-E75B-436C-AEC7-3AD7CFAB15A2}.Release|Any CPU.Build.0 = Release|Any CPU
		{E477540E-6891-431E-B2F5-2631454178C4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E477540E-6891-431E-B2F5-2631454178C4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E477540E-6891-431E-B2F5-2631454178C4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E477540E-6891-431E-B2F5-2631454178C4}.Release|Any CPU.Build.0 = Release|Any CPU
		{2D74C86F-05B4-42B6-9A54-604F7870113D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2D74C86F-05B4-42B6-9A54-604F7870113D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2D74C86F-05B4-42B6-9A54-604F7870113D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2D74C86F-05B4-42B6-9A54-604F7870113D}.Release|Any CPU.Build.0 = Release|Any CPU
		{4DF9E51B-36CB-4694-9DCC-5E71BF5E35DA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4DF9E51B-36CB-4694-9DCC-5E71BF5E35DA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4DF9E51B-36CB-4694-9DCC-5E71BF5E35DA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4DF9E51B-36CB-4694-9DCC-5E71BF5E35DA}.Release|Any CPU.Build.0 = Release|Any CPU
		{171CEFA0-9F3C-452E-8491-8566568F6C96}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{171CEFA0-9F3C-452E-8491-8566568F6C96}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{171CEFA0-9F3C-452E-8491-8566568F6C96}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{171CEFA0-9F3C-452E-8491-8566568F6C96}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {98BE5D63-F146-4ADC-83DB-D302AE71B4D3}
	EndGlobalSection
EndGlobal
