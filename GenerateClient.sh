#!/bin/sh

dotnet tool install --global GraphQlClientGenerator.Tool.Simplified
graphql-client-generator --header Tenant:system --idTypeMapping Guid --serviceUrl http://localhost:60020/graphql --outputPath "CoverGo.Products.Client/GraphqlClient.cs" --namespace CoverGo.Products.Client --jsonPropertyAttribute Never --partialClasses False;
dotnet tool uninstall --global GraphQlClientGenerator.Tool.Simplified