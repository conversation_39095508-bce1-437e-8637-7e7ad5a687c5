# Default values for Products<PERSON>hart in dev environment.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

ProductsParentChart: 
  labels:
    team: backend
    k8s-app: covergo-products

  selectorLabels:
    k8s-app: covergo-products

  podAnnotations:
    linkerd.io/inject: disabled

  annotations:
    reloader.stakater.com/auto: "true"

  appName: covergo-products

  ## Deployment ##
  replicaCount: 1
  
  deployment:
    name: covergo-products
  nameOverride: ""
  fullnameOverride: ""

  env:
    - name: datacenterId
      value: 12factor
    - name: terminationTimeout
      value: "30"
    - name: ASPNETCORE_ENVIRONMENT
      value: "Production"
    - name: TRACING_ENABLED
      value: "true"
    - name: TRACING_CONNECTION_STRING
      value: "http://opentelemetry-collector.open-telemetry:4317"
    - name: TRACING_EXPORT_TIMEOUT
      value: "1000"

  envFrom:
    - secretRef:
      name: covergo-database-legacy

  # readinessProbe:
  #   httpGet:
  #     path: /readyz
  #     port: 8080

  # livenessProbe:
  #   httpGet:
  #     path: /healthz
  #     port: 8080

  resources:
    limits:
      cpu: 300m
      memory: 3000M
    requests:
      cpu: 100m
      memory: 100M

  ## End of Deployments ##

  ## Service ##
  service:
    ports:
      - name: http
        port: 8080
        targetPort: 8080
        protocol: TCP
    type: ClusterIP
  ## End of Service ##

  ## Hpa ##
  autoscaling:
    enabled: false
    minReplicas: <VALUE> #eg. 2
    maxReplicas: <VALUE> #eg. 6
    # targetCPUUtilizationPercentage: <VALUE> #eg. 80
    # targetMemoryUtilizationPercentage: <VALUE> #eg.  80
    cooldownPeriod: <VALUE> #eg. 300
    labels:
      app.kubernetes.io/name: microservice
      app.kubernetes.io/instance: <VALUE> #eg. auth
    triggers:
      - metadata:
          ... ##Note: Replace trigger array with existing values found in service/hpa.yaml in https://github.com/CoverGo/k8s-fleet/tree/main/clusters/covergo-ap-east-1/services/backend
        type: prometheus
    pollingInterval: <VALUE> #eg. 60
  ## End of Hpa ##

  nodeSelector:
    backend: active

  tolerations: []

  affinity: {}

  terminationGracePeriodSeconds: 60
