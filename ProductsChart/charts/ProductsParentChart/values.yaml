# Default values for ProductsParentChart.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

labels:
  team: backend
  k8s-app: covergo-products

selectorLabels:
  k8s-app: covergo-products

podAnnotations:
  linkerd.io/inject: disabled

annotations:
  reloader.stakater.com/auto: "true"

appName: covergo-products

## Values for deployments.yaml ##
replicaCount: 2

image:
  repository: registry-intl.cn-hongkong.aliyuncs.com/covergo/products
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: "latest"

imagePullSecrets:
  name: registry-intl.cn-hongkong.aliyuncs.com

deployment:
  name: covergo-products
nameOverride: ""
fullnameOverride: ""

env:
  - name: datacenterId
    value: covergo-aliyun-hk
  - name: terminationTimeout
    value: "30"
  - name: ASPNETCORE_ENVIRONMENT
    value: "Production"
  - name: TRACING_ENABLED
    value: "true"
  - name: TRACING_CONNECTION_STRING
    value: "http://opentelemetry-collector.open-telemetry:4317"
  - name: TRACING_EXPORT_TIMEOUT
    value: "1000"

envFrom:
  - secretRef:
    name: covergo-database
  - secretRef:
    name: covergo-products-credentials

readinessProbe:
  httpGet:
    path: /readyz
    port: 8080

livenessProbe:
  httpGet:
    path: /healthz
    port: 8080

resources:
  limits:
    cpu: 400m
    memory: 1000M
  requests:
    cpu: 200m
    memory: 500M
## End of values for deployments.yaml ##


## Values for service.yaml ##
service:
  ports:
    - name: http
      port: 8080
      targetPort: 8080
      protocol: TCP
  type: ClusterIP
## End of values for service.yaml ##


## Values for hpa.yaml ##

autoscaling:
  enabled: false
  minReplicas: 2
  maxReplicas: 6
  # targetCPUUtilizationPercentage: <VALUE> #eg. 80
  # targetMemoryUtilizationPercentage: 80
  cooldownPeriod: 300
  labels:
    app.kubernetes.io/name: microservice
    app.kubernetes.io/instance: products
  triggers:
    - metadata:
        metricName: http_requests_total
        query: sum(rate(request_total{workload_ns="default",deployment="covergo-products"}[1m])) by (deployment)
        serverAddress: http://prometheus-kube-prometheus-prometheus.k8s-component-monitoring:9090
        threshold: "10"
      type: prometheus
  pollingInterval: 30

## End of values for hpa.yaml ##


nodeSelector:
  backend: active

tolerations: []

affinity: {}

terminationGracePeriodSeconds: 60  
