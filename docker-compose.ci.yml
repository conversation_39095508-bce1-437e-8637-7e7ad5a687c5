version: '2.4'
services:
  covergo-mongo:
    image: ghcr.io/covergo/products-mongo:latest
    restart: always
    build:
      dockerfile: ./Dockerfile
      target: mongo-products
      context: .
    environment:
      - MONGO_INITDB_ROOT_USERNAME=root
      - MONGO_INITDB_ROOT_PASSWORD=local_dev
      - MONGO_INITDB_DATABASE=products
    ports:
      - 27017:27017

  eventstore:
    image: eventstore/eventstore:21.10.5-buster-slim
    # image: ghcr.io/eventstore/eventstore:21.10.5-alpha-arm64v8
    container_name: eventstore
    restart: always
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "curl -sf http://localhost:2113/stats || exit 1"
        ]
      interval: 5s
      timeout: 2s
    environment:
      - EVENTSTORE_CLUSTER_SIZE=1
      - EVENTSTORE_RUN_PROJECTIONS=All
      - EVENTSTORE_START_STANDARD_PROJECTIONS=true
      - EVENTSTORE_EXT_TCP_PORT=1113
      - EVENTSTORE_HTTP_PORT=2113
      - EVENTSTORE_INSECURE=true
      - EVENTSTORE_ENABLE_EXTERNAL_TCP=true
      - EVENTSTORE_ENABLE_ATOM_PUB_OVER_HTTP=true
    ports:
      - "1113:1113"
      - "2113:2113"

  covergo-gateway:
    image: ghcr.io/covergo/gateway:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=developer-machine
      - REDIS_CONNECTION_STRING=redis:6379
    ports:
      - "60060:8080" # To access localhost:60060/graphql
    depends_on:
      covergo-auth:
        condition: service_started
      covergo-auth-health:
        condition: service_healthy
      covergo-scripts:
        condition: service_started
      covergo-scripts-health:
        condition: service_healthy
      covergo-filesystem:
        condition: service_started
      covergo-product-builder:
        condition: service_started
      covergo-l10n:
        condition: service_started
      covergo-products:
        condition: service_started
      covergo-products-health:
        condition: service_healthy
      redis:
        condition: service_started

  covergo-channel-management:
    image: ghcr.io/covergo/channel-management:master
    environment:
      - DATABASE_CONNECT_STRING=**************************************
      - DATABASE_DRIVER=mongo
      - HOST_ENVIRONMENT=Staging
      - WAIT_HOSTS=covergo-mongo:27017,covergo-auth:8080,covergo-reference:80
    ports:
      - "80"
    depends_on:
      - covergo-mongo
      - covergo-auth
      - covergo-reference

  covergo-reference:
    image: ghcr.io/covergo/reference:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - isDeployment=true
      - datacenterId=developer-machine
      - DATABASE_DRIVER=mongo
      - DATABASE_CONNECT_STRING=**************************************
      - WAIT_HOSTS=covergo-mongo:27017,covergo-auth:8080
    ports:
      - "80"
    depends_on:
      - covergo-mongo

  redis:
    image: redis:latest
    restart: always
    ports:
      - "6379:6379"

  covergo-gateway-health:
    image: busybox
    #use trap to exit gracefully from sleep
    command:
      [
        "/bin/sh",
        "-c",
        "trap \"echo exiting; exit 0\" TERM; sleep 1d & wait"
      ]
    healthcheck:
      test: "wget http://covergo-gateway:8080/healthz -q -O -  || exit 1"
      interval: 1s
      timeout: 1s
      retries: 30
    depends_on:
      covergo-gateway:
        condition: service_started

  covergo-auth:
    image: ghcr.io/covergo/auth:latest
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - isDeployment=true
      - datacenterId=developer-machine
      - DBCONFIG-providerId=mongoDb
      - DATABASE_CONNECT_STRING=**************************************
      - OTP_LOGIN_CIPHER_KEY=zaWgPou46nNmfMrYivTS2waJO9VKI277iLlPkGL56yc=
      - OTP_LOGIN_CIPHER_IV=94jCf53NO1acZ3pO7UE+gA==
      - OTP_LOGIN_HASHER_KEY=key
      - COVERGO_PASSWORD=V9K&KobcZO3
    ports:
      - "8080"
    depends_on:
      - covergo-mongo

  covergo-auth-health:
    image: busybox
    #use trap to exit gracefully from sleep
    command:
      [
        "/bin/sh",
        "-c",
        "trap \"echo exiting; exit 0\" TERM; sleep 1d & wait"
      ]
    healthcheck:
      test: "wget http://covergo-auth:8080/healthz -q -O -  || exit 1"
      interval: 1s
      timeout: 1s
      retries: 30
    depends_on:
      covergo-auth:
        condition: service_started

  covergo-scripts:
    image: ghcr.io/covergo/scripts-node:master
    ports:
      - "8080"

  covergo-scripts-health:
    image: busybox
    #use trap to exit gracefully from sleep
    command:
      [
        "/bin/sh",
        "-c",
        "trap \"echo exiting; exit 0\" TERM; sleep 1d & wait"
      ]
    healthcheck:
      test: "wget http://covergo-scripts:8080/healthz -q -O -  || exit 1"
      interval: 1s
      timeout: 1s
      retries: 30
    depends_on:
      covergo-scripts:
        condition: service_started

  covergo-filesystem:
    image: ghcr.io/covergo/filesystem:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=developer-machine
      - DATABASE_DRIVER=mongoDb
      - DBCONFIG-providerId=mongoDb
      - DBCONFIG-endpoint=covergo-mongo
      - DBCONFIG-username=root
      - DBCONFIG-password=local_dev
    ports:
      - "8080"
    depends_on:
      - covergo-mongo
      - covergo-auth

  covergo-product-builder:
    image: ghcr.io/covergo/product-builder:master
    environment:
      - EVENTSTORE_CONNECT_STRING=esdb://admin:changeit@eventstore?tls=false
      - DATABASE_CONNECT_STRING=**************************************
      - HOST_ENVIRONMENT=Staging
    ports:
      - "80"
    depends_on:
      - eventstore
      - covergo-mongo
      - covergo-auth

  covergo-l10n:
    image: ghcr.io/covergo/l10n:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=covergo-dockerComposeOnJenkins-hk
      - DBCONFIG-providerId=mongoDb
      - DBCONFIG-endpoint=covergo-mongo
      - DBCONFIG-username=root
      - DBCONFIG-password=local_dev
    ports:
      - "8080"
    depends_on:
      - covergo-mongo

  covergo-templates:
    image: ghcr.io/covergo/templates:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=covergo-dockerComposeOnJenkins-hk
      - DATABASE_DRIVER=mongoDb
      - DBCONFIG-providerId=mongoDb
      - DBCONFIG-endpoint=covergo-mongo
      - DBCONFIG-username=root
      - DBCONFIG-password=local_dev
      - DISABLE_HANGFIRE=true
    ports:
      - "8080"
    depends_on:
      - covergo-mongo

  covergo-cases:
    image: ghcr.io/covergo/cases:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=covergo-dockerComposeOnJenkins-hk
      - DATABASE_DRIVER=mongoDb
      - DBCONFIG-providerId=mongoDb
      - DBCONFIG-endpoint=covergo-mongo
      - DBCONFIG-username=root
      - DBCONFIG-password=local_dev
    ports:
      - "80"
    depends_on:
      - covergo-mongo

  covergo-products:
    image: ghcr.io/covergo/products:latest
    restart: always
    build:
      dockerfile: ./Dockerfile
      target: runtime
      args:
        GH_ACCOUNT: ${GH_ACCOUNT}
        GH_TOKEN: ${GH_TOKEN}
      context: .
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging.CI
      - isDeployment=true
      - datacenterId=developer-machine
      - DATABASE_DRIVER=mongoDb
      - DATABASE_CONNECT_STRING=**************************************
      - FeatureManagement__ValidatePdtId__EnabledFor__0__Name=Tenants
      - FeatureManagement__ValidatePdtId__EnabledFor__0__Parameters__Tenants__0=validatePdtId_tenant
      - FeatureManagement__UseLegacyLifecycle=false
    ports:
      - 8080:8080
    depends_on:
      - covergo-mongo
      - covergo-product-builder
      - covergo-filesystem
      - covergo-l10n

  #alpine-based asp.net core does not have neither apk neither curl\wget,
  #so need to replace it somehow
  covergo-products-health:
    image: busybox
    #use trap to exit gracefully from sleep
    command:
      [
        "/bin/sh",
        "-c",
        "trap \"echo exiting; exit 0\" TERM; sleep 1d & wait"
      ]
    healthcheck:
      test: "wget http://covergo-products:8080/healthz -q -O -  || exit 1"
      interval: 1s
      timeout: 1s
      retries: 30
    depends_on:
      covergo-products:
        condition: service_started

  covergo-products-tests:
    image: ghcr.io/covergo/products-test:latest
    restart: "no"
    build:
      dockerfile: ./Dockerfile
      args:
        GH_ACCOUNT: ${GH_ACCOUNT}
        GH_TOKEN: ${GH_TOKEN}
      context: .
      target: tests
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging.CI
      - PRODUCTS_INTEGRATION_TEST-AuthUrl=http://covergo-auth:8080
      - PRODUCTS_INTEGRATION_TEST-ProductsUrl=http://covergo-products:8080
      - PRODUCTS_INTEGRATION_TEST-ProductBuilderUrl=http://covergo-product-builder:80
      - PRODUCTS_INTEGRATION_TEST-FileSystemUrl=http://covergo-filesystem:8080
      - PRODUCTS_INTEGRATION_TEST-L10nUrl=http://covergo-l10n:8080
      - PRODUCTS_INTEGRATION_TEST-GatewayUrl=http://covergo-gateway:8080/
      - PRODUCTS_INTEGRATION_TEST-ReferenceUrl=http://covergo-reference/
      - PRODUCTS_INTEGRATION_TEST-ChannelManagementUrl=http://covergo-channel-management
      - GATEWAY_URL=http://covergo-gateway:8080/graphql
      - GATEWAY_INTEGRATION_TEST-GatewayGraphQLUrl=http://covergo-gateway:8080/graphql
      - DATABASE_CONNECT_STRING=**************************************
    depends_on:
      covergo-templates:
        condition: service_started
      covergo-products-health:
        condition: service_healthy
      covergo-auth-health:
        condition: service_healthy
      covergo-scripts-health:
        condition: service_healthy
      covergo-gateway-health:
        condition: service_healthy
      covergo-reference:
        condition: service_started
      covergo-channel-management:
        condition: service_started
      covergo-cases:
        condition: service_started
