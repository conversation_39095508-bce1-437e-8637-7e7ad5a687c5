{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:base"], "dependencyDashboardApproval": true, "helm-values": {"enabled": false}, "hostRules": [{"matchHost": "https://nuget.pkg.github.com/covergo", "encrypted": {"token": "wcFMA/xDdHCJBTolAQ/9HiuesD+cRvXb3b0z0eGgeUUvJFfIwO2pnyVBxZPojpK1Npo3+8yGLdGQfmulEofhCgrIB8gznOrDifz/2x1LPxFWdI//BbDwQG/lcNsnjb7n7AGIhG5ApOkhaRj969nd2MNmVldlBSu+HKUbQKPZF3n1gDPhraRzgeAmzSP9PHG4mLvQ2Dc/0URjS7Kuplro8aXG8nUX7hFIRWm4DAzAhmaAqGP3CwmC5P29Vb+CGsZeCoyXgkZ7kheQEOOPznuOTjx0mVpm+20s+M8qeQX8znOyCP87ScCfnycxik/duGhou0k1abMkzcW3r8VMYyvL/fsjL+KMG75jx9jexgf1Qdf3Y3xhEZatr4LVzSgYyxEjZCnhcn46qQq6T+9T9LliYS12b/4mexOWMCpcGqeDdO/xGj6Gcdb5R7tcK7BK5GbBM0BpJlz6oJiOSqS/bGG5HP2ebcxGuJnaIpRw4iT9eLS0ap/Oe1Ttvdf5MkIDzwuLDKGCqxIyFLpTDjip7kP9io8kDld5X/TI9iV3d858QuFnF6QVkA6RJJ9hzWgcdifXvctI/wEaCbEVGQyFlsVp/Vq/w6mWMTaX313AltW2m/ENXLKFB0ak1b4juVcLZVitFiUR+T63w7KgXkSLwUOBTHJRjlzfiFkG1IeQl7GTVE8SoTjmFyzGLQvFUAko3W7SdgH0wLdkaaJsONkCipz8yguD0Qe8BI1PmVICUWJlGah/fP3xKMUVt2XmX4JxAptGO6hqFnxdeIZjN3UZe2KvuEQGLXeQ3F15MgdTY/mtZ+9Z/KDPH6eP+eT3TkcvQzAGTnFJ5XxXAwotC5NQ8bfVLB7222fxnDg"}}, {"matchHost": "https://ghcr.io/covergo", "encrypted": {"token": "wcFMA/xDdHCJBTolAQ/9HiuesD+cRvXb3b0z0eGgeUUvJFfIwO2pnyVBxZPojpK1Npo3+8yGLdGQfmulEofhCgrIB8gznOrDifz/2x1LPxFWdI//BbDwQG/lcNsnjb7n7AGIhG5ApOkhaRj969nd2MNmVldlBSu+HKUbQKPZF3n1gDPhraRzgeAmzSP9PHG4mLvQ2Dc/0URjS7Kuplro8aXG8nUX7hFIRWm4DAzAhmaAqGP3CwmC5P29Vb+CGsZeCoyXgkZ7kheQEOOPznuOTjx0mVpm+20s+M8qeQX8znOyCP87ScCfnycxik/duGhou0k1abMkzcW3r8VMYyvL/fsjL+KMG75jx9jexgf1Qdf3Y3xhEZatr4LVzSgYyxEjZCnhcn46qQq6T+9T9LliYS12b/4mexOWMCpcGqeDdO/xGj6Gcdb5R7tcK7BK5GbBM0BpJlz6oJiOSqS/bGG5HP2ebcxGuJnaIpRw4iT9eLS0ap/Oe1Ttvdf5MkIDzwuLDKGCqxIyFLpTDjip7kP9io8kDld5X/TI9iV3d858QuFnF6QVkA6RJJ9hzWgcdifXvctI/wEaCbEVGQyFlsVp/Vq/w6mWMTaX313AltW2m/ENXLKFB0ak1b4juVcLZVitFiUR+T63w7KgXkSLwUOBTHJRjlzfiFkG1IeQl7GTVE8SoTjmFyzGLQvFUAko3W7SdgH0wLdkaaJsONkCipz8yguD0Qe8BI1PmVICUWJlGah/fP3xKMUVt2XmX4JxAptGO6hqFnxdeIZjN3UZe2KvuEQGLXeQ3F15MgdTY/mtZ+9Z/KDPH6eP+eT3TkcvQzAGTnFJ5XxXAwotC5NQ8bfVLB7222fxnDg"}}]}