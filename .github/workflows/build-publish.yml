name: Build and publish
on:
- push
jobs:
  build-and-publish:
    uses: CoverGo/reusable-workflows/.github/workflows/build-and-publish.yml@master
    with:
      serviceName: products
      hasClient: true
      hasCustomMongoImage: true
      integrationTestsLegacySmallTenants: test_uat,test_uat1, # WARNING: Do not remove the comma at the end.
      integrationTestsLegacyBigTenants: test_uat2,test_uat3,test_uat4, # WARNING: Do not remove the comma at the end.
      isDotnetSonarScannerEnabled: true
      sonarDotnetVersion: 8.x
      testsRunOn: ubuntu-latest-4-cores
    secrets:
      PAT_USER_READ_PACKAGES: ${{ secrets.PAT_USER_READ_PACKAGES }}
      PAT_READ_PACKAGES: ${{ secrets.PAT_READ_PACKAGES }}
      COSIGN_PASSWORD: ${{ secrets.COSIGN_PASSWORD }}
      COSIGN_PRIVATE_KEY: ${{ secrets.SIGNING_SECRET }}
      ALI_CONTAINER_REGISTRY_USER: ${{ secrets.ALI_CONTAINER_REGISTRY_USER }}
      ALI_CONTAINER_REGISTRY_PASSWORD: ${{ secrets.ALI_CONTAINER_REGISTRY_PASSWORD }}
      PRIVATE_ACTION_APP_ID: ${{ secrets.PRIVATE_ACTION_APP_ID }}
      PRIVATE_ACTION_APP_PRIVATE_KEY: ${{ secrets.PRIVATE_ACTION_APP_PRIVATE_KEY }}
      DIAGNOSTIC_PASSWORD: ${{ secrets.DIAGNOSTIC_PASSWORD }}
      INTEGRATION_TESTS_LEGACY_SSH_READONLY: ${{ secrets.INTEGRATION_TESTS_LEGACY_SSH_READONLY }}
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
