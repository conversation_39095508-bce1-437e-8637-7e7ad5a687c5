﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Applications.Infrastructure;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.DiscountCodes;
using CoverGo.Products.Domain.Products;
using MongoDB.Driver;

namespace CoverGo.Products.Infrastructure.DiscountCodes.Adapters.Mongo;

public class MongoDiscountCodeRepository : CoverGoGenericMongoRepositoryBase<DiscountCode, DiscountCodeUpsert, DiscountCodeFilter>, IDiscountCodeRepository
{
    protected override string DbName => "products";
    
    protected override string CollectionNameGet(string tenantId) => $"{tenantId}-discountCodes";

    protected override DiscountCode EntityCreate(DiscountCodeUpsert create)  => new()
    {
        Id = create.Id ?? Guid.NewGuid().ToString(),
        Name = create.Name,
        Description = create.Description,
        Value = create.Value ?? default,
        ValidFrom = create.ValidFrom ,
        ValidTo = create.ValidTo,
        ProductIds = create.ProductIds,
        ProductTypeId = create.ProductTypeId,
        RedemptionLimit = create.RedemptionLimit,
        Type = create.Type ?? default,
        CreatedAt = DateTime.UtcNow,
        CreatedById = create.ById
    };

    protected override UpdateDefinition<DiscountCode> EntityUpdate(DiscountCodeUpsert update)
    {
        UpdateDefinition<DiscountCode> result = UpdateField(null, x => x.LastModifiedAt, DateTime.UtcNow);
        
        if (update.ById != null) result = UpdateField(result, x => x.LastModifiedById, update.ById);
        if (update.Name != null) result = UpdateField(result, x => x.Name, update.Name);
        if (update.Description != null) result = UpdateField(result, x => x.Description, update.Description);
        if (update.Value != null) result = UpdateField(result, x => x.Value, update.Value);
        if (update.ValidFrom != null) result = UpdateField(result, x => x.ValidFrom, update.ValidFrom);
        if (update.ValidTo != null) result = UpdateField(result, x => x.ValidTo, update.ValidTo);
        if (update.RedemptionLimit != null) result = UpdateField(result, x => x.RedemptionLimit, update.RedemptionLimit);
        if (update.Type != null) result = UpdateField(result, x => x.Type, update.Type);
        if (update.ProductTypeId != null) result = UpdateField(result, x => x.ProductTypeId, update.ProductTypeId);
        if (update.ProductIds != null) result = UpdateField(result, x => x.ProductIds, update.ProductIds);

        return result;
        
        UpdateDefinition<DiscountCode> UpdateField<TField>(UpdateDefinition<DiscountCode> updateDefinition,  Expression<Func<DiscountCode,TField>> field, TField value)
        {
            return updateDefinition == null ?  Update.Set(field, value) : updateDefinition.Set(field, value);
        }
    }

    protected override FilterDefinition<DiscountCode> FilterApplyInternal(FilterDefinition<DiscountCode> filter, Filter<DiscountCodeFilter> @where)
    {
        if (where == null || where.Where == null) return filter;
        
        if (where.Where.ProductIds_in != null) return Builders<DiscountCode>.Filter.AnyIn(c => c.ProductIds, @where.Where.ProductIds_in);

        if (where.Where.ProductIds_contains != null)
        {
            if(where.Where.ProductIds_contains.Type != null)
                return Builders<DiscountCode>.Filter.Eq($"{nameof(DiscountCode.ProductIds)}.{nameof(ProductId.Type)}", where.Where.ProductIds_contains.Type);
            if(where.Where.ProductIds_contains.Plan != null)
                return Builders<DiscountCode>.Filter.Eq($"{nameof(DiscountCode.ProductIds)}.{nameof(ProductId.Plan)}", where.Where.ProductIds_contains.Plan);
        }
        
        return base.FilterApplyInternal(filter, @where);
    }
}