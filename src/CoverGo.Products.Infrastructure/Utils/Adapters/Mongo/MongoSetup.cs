﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Conventions;

namespace CoverGo.Products.Infrastructure.Utils.Adapters.Mongo;

public static class MongoSetup
{
    public static void RegisterConventions()
    {
        var conventionPack = new ConventionPack {
            new CamelCaseElementNameConvention(),
            new EnumRepresentationConvention(BsonType.String),
            new IgnoreExtraElementsConvention(true),
            new IgnoreIfNullConvention(true)
        };

        ConventionRegistry.Register("all", conventionPack, t => true);
    }

    public static void UnregisterConventions() => ConventionRegistry.Remove("all");
}