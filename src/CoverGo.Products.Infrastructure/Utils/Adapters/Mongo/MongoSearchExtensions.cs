﻿using CoverGo.MongoUtils;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Infrastructure.Products.Adapters.Mongo;
using MongoDB.Bson;
using MongoDB.Driver;
using Newtonsoft.Json;
using System.Linq;
using System.Text.RegularExpressions;

namespace CoverGo.Products.Infrastructure.Utils.Adapters.Mongo
{
    public static class MongoSearchExtensions
    {
        public static FilterDefinition<MongoProductDao2> AddProductSearchFilters2(ProductWhere filter)
        {
            FilterDefinition<MongoProductDao2> mongoFilter = FilterDefinition<MongoProductDao2>.Empty.AddProductSearchFilters2(filter);

            if (filter?.Or != null)
                foreach (ProductWhere orFilter in filter.Or)
                    mongoFilter = mongoFilter != FilterDefinition<MongoProductDao2>.Empty
                        ? mongoFilter | AddProductSearchFilters2(orFilter)
                        : AddProductSearchFilters2(orFilter);

            if (filter?.And != null)
                foreach (ProductWhere andFilter in filter.And)
                    mongoFilter &= AddProductSearchFilters2(andFilter);

            return mongoFilter;
        }

        public static FilterDefinition<ProductType> AddProductTypeSearchFilters(ProductTypeWhere filter)
        {
            FilterDefinition<ProductType> mongoFilter = FilterDefinition<ProductType>.Empty;

            mongoFilter = mongoFilter.AddProductTypeSearchFilters(filter);

            if (filter.Or != null)
                foreach (ProductTypeWhere orFilter in filter.Or)
                {
                    FilterDefinition<ProductType> orMongoFilter = AddProductTypeSearchFilters(orFilter);
                    mongoFilter = mongoFilter != FilterDefinition<ProductType>.Empty ? mongoFilter | orMongoFilter : orMongoFilter;
                }

            if (filter.And != null)
                foreach (ProductTypeWhere andFilter in filter.And)
                    mongoFilter &= AddProductTypeSearchFilters(andFilter);

            return mongoFilter;
        }

        public static FilterDefinition<MongoProductDao2> AddProductSearchFilters2(this FilterDefinition<MongoProductDao2> mongoFilter, ProductWhere filter)
        {
            if (filter is null)
            {
                return mongoFilter;
            }

            if (filter.ProductId != null)
            {
                if (filter.ProductId.Plan != null && filter.ProductId.Type != null)
                {
                    if (filter.ProductId.Version is null)
                    {
                        return mongoFilter & Builders<MongoProductDao2>.Filter.Where(c =>
                            c.ProductId.Plan == filter.ProductId.Plan && c.ProductId.Type == filter.ProductId.Type);
                    }
                    else
                    {
                        return mongoFilter & Builders<MongoProductDao2>.Filter.In(c => c.ProductId, new[] { new ProductId { Plan = filter.ProductId.Plan, Type = filter.ProductId.Type, Version = filter.ProductId.Version } });
                    }
                }

                if (filter.ProductId.Plan != null)
                    return mongoFilter & Builders<MongoProductDao2>.Filter.Eq(c => c.ProductId.Plan, filter.ProductId.Plan);
                if (filter.ProductId.Plan_in != null)
                    return mongoFilter & Builders<MongoProductDao2>.Filter.In(c => c.ProductId.Plan, filter.ProductId.Plan_in);
                if (filter.ProductId.Plan_contains != null)
                    return mongoFilter & Builders<MongoProductDao2>.Filter.Regex(c => c.ProductId.Plan, new BsonRegularExpression(filter.ProductId.Plan_contains, "i"));

                if (filter.ProductId.Version != null)
                    return mongoFilter & Builders<MongoProductDao2>.Filter.Eq(c => c.ProductId.Version, filter.ProductId.Version);
                if (filter.ProductId.Version_in != null)
                    return mongoFilter & Builders<MongoProductDao2>.Filter.In(c => c.ProductId.Version, filter.ProductId.Version_in);

                if (filter.ProductId.Version_contains != null)
                    return mongoFilter & Builders<MongoProductDao2>.Filter.Regex(c => c.ProductId.Version, new BsonRegularExpression(filter.ProductId.Version_contains));

                if (filter.ProductId.Version_ncontains != null)
                    if (IsRegex(filter.ProductId.Version_ncontains))
                        return mongoFilter & Builders<MongoProductDao2>.Filter.Not(Builders<MongoProductDao2>.Filter.Regex(c => c.ProductId.Version, new BsonRegularExpression(filter.ProductId.Version_ncontains)));
                    else
                        // If filter is not a regex itself, then we can use negative lookahead to ensure that Mongo uses IXSCAN instead of COLLSCAN.
                        // COLLSCAN works extremely slow for Mongo collections with large documents (e.g. 500KB size per document).
                        return mongoFilter & Builders<MongoProductDao2>.Filter.Regex(c => c.ProductId.Version, new BsonRegularExpression($"^((?!{filter.ProductId.Version_ncontains}).)*$"));

                if (filter.ProductId.Type != null)
                    return mongoFilter & Builders<MongoProductDao2>.Filter.Eq(c => c.ProductId.Type, filter.ProductId.Type);
                if (filter.ProductId.Type_in != null)
                    return mongoFilter & Builders<MongoProductDao2>.Filter.In(c => c.ProductId.Type, filter.ProductId.Type_in);
            }

            if (filter.Insurer != null)
                if (filter.Insurer.Id != null)
                    return mongoFilter & Builders<MongoProductDao2>.Filter.Eq(c => c.InsurerId, filter.Insurer.Id);
                else if (filter.Insurer.Id_in != null)
                    return mongoFilter & Builders<MongoProductDao2>.Filter.In(c => c.InsurerId, filter.Insurer.Id_in);
                else if (filter.Insurer.Id_contains != null)
                    return mongoFilter & Builders<MongoProductDao2>.Filter.Regex(c => c.InsurerId, new BsonRegularExpression(filter.Insurer.Id_contains));

            if (filter.Id_in != null)
                return mongoFilter & Builders<MongoProductDao2>.Filter.In(c => c.ProductId, filter.Id_in);

            if (filter.IssuerProductId != null)
                return mongoFilter & Builders<MongoProductDao2>.Filter.Eq(c => c.IssuerProductId, filter.IssuerProductId);

            if (filter.LifecycleStage != null)
                return mongoFilter & Builders<MongoProductDao2>.Filter.Eq(c => c.LifecycleStage, filter.LifecycleStage);

            if (filter.Benefits_some != null)
                return mongoFilter.AddBenefitFilters(filter.Benefits_some);

            if (filter.Representation != null && filter.Representation.Path.StartsWith(nameof(MongoProductDao2.Representation).ToLower() + "."))
                return mongoFilter & filter.Representation.Build<MongoProductDao2>();

            if (filter.Fields != null && filter.Fields.Path.StartsWith(nameof(MongoProductDao2.Fields).ToLower() + "."))
                return mongoFilter & filter.Fields.Build<MongoProductDao2>();

            if (filter.Tags_some != null)
            {
                if (filter.Tags_some.Id != null)
                    return mongoFilter & Builders<MongoProductDao2>.Filter.Where(c => c.Tags.Any(a => filter.Tags_some.Id == a.Id));
                if (filter.Tags_some.Id_in != null)
                    return mongoFilter & Builders<MongoProductDao2>.Filter.Where(c => c.Tags.Any(a => filter.Tags_some.Id_in.Contains(a.Id)));
                if (filter.Tags_some.Type != null)
                    return mongoFilter & Builders<MongoProductDao2>.Filter.Where(c => c.Tags.Any(a => filter.Tags_some.Type == a.Type));
                if (filter.Tags_some.Type_in != null)
                    return mongoFilter & Builders<MongoProductDao2>.Filter.Where(c => c.Tags.Any(a => filter.Tags_some.Type_in.Contains(a.Type)));
            }

            if (filter.CreatedAt_gt != null)
                return mongoFilter & Builders<MongoProductDao2>.Filter.Gt(c => c.CreatedAt, filter.CreatedAt_gt);
            if (filter.CreatedAt_lt != null)
                return mongoFilter & Builders<MongoProductDao2>.Filter.Lt(c => c.CreatedAt, filter.CreatedAt_lt);

            if (filter.LaunchPeriodStartDate_gt != null)
                return mongoFilter & Builders<MongoProductDao2>.Filter.Gt(c => c.LaunchPeriodStartDate, filter.LaunchPeriodStartDate_gt);
            if (filter.LaunchPeriodStartDate_lt != null)
                return mongoFilter & Builders<MongoProductDao2>.Filter.Lt(c => c.LaunchPeriodStartDate, filter.LaunchPeriodStartDate_lt);
            if (filter.LaunchPeriodEndDate_gt != null)
                return mongoFilter & Builders<MongoProductDao2>.Filter.Gt(c => c.LaunchPeriodEndDate, filter.LaunchPeriodEndDate_gt);
            if (filter.LaunchPeriodEndDate_lt != null)
                return mongoFilter & Builders<MongoProductDao2>.Filter.Lt(c => c.LaunchPeriodEndDate, filter.LaunchPeriodEndDate_lt);
            if (filter.ChangeEffectiveDate_gt != null)
                return mongoFilter & Builders<MongoProductDao2>.Filter.Gt(c => c.ChangeEffectiveDate, filter.ChangeEffectiveDate_gt);
            if (filter.ChangeEffectiveDate_lt != null)
                return mongoFilter & Builders<MongoProductDao2>.Filter.Lt(c => c.ChangeEffectiveDate, filter.ChangeEffectiveDate_lt);

            if (filter.Status != null)
                return mongoFilter & Builders<MongoProductDao2>.Filter.Eq(c => c.Status, filter.Status);
            if (filter.Status_in != null)
                return mongoFilter & Builders<MongoProductDao2>.Filter.In(p => p.Status, filter.Status_in);
            if (filter.Status_not_in != null)
                return mongoFilter & Builders<MongoProductDao2>.Filter.Nin(p => p.Status, filter.Status_not_in);

            if (filter.InternalReviews_some != null)
            {
                if (filter.InternalReviews_some.Status != null)
                    return mongoFilter & Builders<MongoProductDao2>.Filter.Where(c => c.InternalReviews.Any(ir => ir.Status == filter.InternalReviews_some.Status));
                if (filter.InternalReviews_some.Status_in != null)
                    return mongoFilter & Builders<MongoProductDao2>.Filter.Where(c => c.InternalReviews.Any(ir => filter.InternalReviews_some.Status_in.Contains(ir.Status)));
            }

            if (filter.ProductTreeId_exists != null)
                return mongoFilter & Builders<MongoProductDao2>.Filter.Exists(c => c.ProductTreeId, filter.ProductTreeId_exists.GetValueOrDefault());

            return mongoFilter;
        }

        private static bool IsRegex(string str)
        {
            string strWithoutSpaces = str.Replace(" ", "");
            return Regex.Escape(strWithoutSpaces) != strWithoutSpaces;
        }

        //ToDo: check if 'or' and 'and' are needed, we already have 'or' and 'and' on productWhere level
        private static FilterDefinition<MongoProductDao2> AddBenefitFilters(this FilterDefinition<MongoProductDao2> mongoFilter, BenefitWhere filter)
        {
            if (filter.Or != null)
                foreach (BenefitWhere orFilter in filter.Or)
                    mongoFilter = mongoFilter != FilterDefinition<MongoProductDao2>.Empty
                        ? mongoFilter | mongoFilter.AddBenefitFilters(orFilter)
                        : mongoFilter.AddBenefitFilters(orFilter);

            if (filter.And != null)
                foreach (BenefitWhere andFilter in filter.And)
                    mongoFilter &= mongoFilter.AddBenefitFilters(andFilter);

            if (filter.TypeId != null)
                return mongoFilter & Builders<MongoProductDao2>.Filter.Where(c => c.Benefits.Any(a => filter.TypeId == a.TypeId));

            if (filter.TypeId_in != null)
                return mongoFilter & Builders<MongoProductDao2>.Filter.Where(p => p.Benefits.Any(i => filter.TypeId_in.Contains(i.TypeId)));

            if (filter.RawData_gt != null)
            {
                object value = JsonConvert.DeserializeObject(filter.RawData_gt);
                return mongoFilter & Builders<MongoProductDao2>.Filter.Gte("benefits.value", value);
            }

            if (filter.RawData_lt != null)
            {
                object value = JsonConvert.DeserializeObject(filter.RawData_lt);
                return mongoFilter & Builders<MongoProductDao2>.Filter.Lte("benefits.value", value);
            }

            return mongoFilter;
        }

        public static FilterDefinition<ProductType> AddProductTypeSearchFilters(this FilterDefinition<ProductType> mongoFilter, ProductTypeWhere filter)
        {
            if (filter.TypeId != null)
                return mongoFilter & Builders<ProductType>.Filter.Eq(c => c.TypeId, filter.TypeId);

            if (filter.TypeId_in != null)
                return mongoFilter & Builders<ProductType>.Filter.In(c => c.TypeId, filter.TypeId_in);

            return mongoFilter;
        }
    }
}
