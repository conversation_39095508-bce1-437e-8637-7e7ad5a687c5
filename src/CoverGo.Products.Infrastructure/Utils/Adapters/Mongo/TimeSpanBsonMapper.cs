using MongoDB.Bson;

using System;

namespace CoverGo.Products.Infrastructure.Utils.Adapters.Mongo
{
    public class TimeSpanBsonMapper : ICustomBsonTypeMapper
    {
        /// <summary>
        /// Tries to map an object (like TimeSpan) to a BsonValue for serialization.
        /// </summary>
        public bool TryMapToBsonValue(object value, out BsonValue bsonValue)
        {
            if (value is TimeSpan timeSpan)
            {
                bsonValue = new BsonInt64(timeSpan.Ticks);

                return true;
            }

            // Mapping was unsuccessful (not a TimeSpan)
            bsonValue = null;
            return false;
        }

        /// <summary>
        /// Deserializes a BsonValue back into a TimeSpan object.
        /// </summary>
        public static object FromBsonValue(BsonValue bsonValue) => TimeSpan.FromTicks(bsonValue.AsInt64);
    }
}
