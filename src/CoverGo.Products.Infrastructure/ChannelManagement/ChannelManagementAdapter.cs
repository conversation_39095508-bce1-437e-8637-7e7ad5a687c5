﻿using CoverGo.ChannelManagement.Client;
using CoverGo.Products.Domain.Scripts;
using StrawberryShake;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Infrastructure.ChannelManagement;

public class ChannelManagementAdapter : IChannelManagementAdapter
{
    ChannelManagementClient _channelManagementClient;
    public ChannelManagementAdapter(ChannelManagementClient channelManagementClient)
    {
        _channelManagementClient = channelManagementClient;
    }

    public async Task<IReadOnlyList<ICommissionCandidates_CommissionCandidates>> GetCommissionCandidates(CommissionCandidatesInput input, CancellationToken cancellationToken)
    {
        IOperationResult<ICommissionCandidatesResult> commissionCandidatesResult = await _channelManagementClient.CommissionCandidates.ExecuteAsync(input, cancellationToken);
        if (commissionCandidatesResult.IsErrorResult())
            throw new InvalidOperationException(string.Join(',',commissionCandidatesResult.Errors));
        IReadOnlyList<ICommissionCandidates_CommissionCandidates> cc = commissionCandidatesResult.Data.CommissionCandidates;
        return cc;
    }

    public async Task<IReadOnlyList<ICampaigns_Campaigns_Items>> GetCampaigns(CampaignFilterInput where, int take, CancellationToken cancellationToken)
    {
        IOperationResult<ICampaignsResult> campaignsResult = await _channelManagementClient.Campaigns.ExecuteAsync(where, null, 0, take, cancellationToken);
        if (campaignsResult.IsErrorResult())
            throw new InvalidOperationException(string.Join(',', campaignsResult.Errors));
        return campaignsResult.Data.Campaigns.Items;
    }
}
