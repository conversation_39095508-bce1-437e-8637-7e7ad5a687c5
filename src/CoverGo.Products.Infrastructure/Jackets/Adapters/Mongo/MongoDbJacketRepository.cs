using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Configuration;
using CoverGo.DomainUtils;
using CoverGo.MongoUtils;
using CoverGo.Products.Domain.Jackets.Entities;
using CoverGo.Products.Domain.Jackets.Filters;
using CoverGo.Products.Domain.Jackets.Repositories;
using MongoDB.Driver;

namespace CoverGo.Products.Infrastructure.Jackets.Adapters.Mongo
{
    public class MongoDbJacketRepository : IJacketRepository
    {
        private const string DbName = "products";

        public async Task<IEnumerable<Jacket>> GetAsync(string tenantId, JacketWhere where, OrderBy orderBy = null, int? skip = null, int? first = null, CancellationToken cancellationToken = default)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<Jacket> clientCollection = db.GetCollection<Jacket>($"{tenantId}-jackets");

            FilterDefinition<Jacket> mongoFilter = FilterDefinition<Jacket>.Empty;
            if (where != null)
                mongoFilter = where.GetFilterDefinition();

            IFindFluent<Jacket, Jacket> find = clientCollection.Find(mongoFilter);

            if (orderBy != null)
                find = orderBy.Type == OrderByType.ASC
                    ? find.Sort(Builders<Jacket>.Sort.Ascending(orderBy.FieldName))
                    : find.Sort(Builders<Jacket>.Sort.Descending(orderBy.FieldName));

            IEnumerable<Jacket> jackets = await find.Skip(skip).Limit(first).ToListAsync(cancellationToken);
            return jackets;
        }

        public async Task<IEnumerable<string>> GetIdsAsync(string tenantId, JacketWhere where, OrderBy orderBy, int? skip, int? first, DateTime? asOf = null, CancellationToken cancellationToken = default)
        {
            FilterDefinition<Jacket> mongoFilter = FilterDefinition<Jacket>.Empty;
            if (where != null)
                mongoFilter = where.GetFilterDefinition();

            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<Jacket> clientCollection = db.GetCollection<Jacket>($"{tenantId}-jackets");
            IFindFluent<Jacket, Jacket> find = clientCollection.Find(mongoFilter);

            if (orderBy != null)
                find = orderBy.Type == OrderByType.ASC
                    ? find.Sort(Builders<Jacket>.Sort.Ascending(orderBy.FieldName))
                    : find.Sort(Builders<Jacket>.Sort.Descending(orderBy.FieldName));

            List<string> ids = await find.Project(p => p.Id).Skip(skip).Limit(first).ToListAsync(cancellationToken);
            return ids;
        }

        public async Task DeleteAsync(string tenantId, string jacketId, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<Jacket> clientCollection = db.GetCollection<Jacket>($"{tenantId}-jackets");
            
            await clientCollection.DeleteOneAsync(Builders<Jacket>.Filter.Eq(p => p.Id, jacketId), cancellationToken);
        }

        public async Task<long> GetTotalCountAsync(string tenantId, JacketWhere where, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<Jacket> clientCollection = db.GetCollection<Jacket>($"{tenantId}-jackets");

            FilterDefinition<Jacket> mongoFilter = FilterDefinition<Jacket>.Empty;
            if (where != null)
                mongoFilter = where.GetFilterDefinition();

            long totalCount = await clientCollection.CountDocumentsAsync(mongoFilter, cancellationToken: cancellationToken);
            return totalCount;
        }

        public async Task UpsertAsync(string tenantId, Jacket jacket, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<Jacket> clientCollection = db.GetCollection<Jacket>($"{tenantId}-jackets");

            await clientCollection.ReplaceOneAsync(
                Builders<Jacket>.Filter.Eq(t => t.Id, jacket.Id),
                jacket,
                new ReplaceOptions() { IsUpsert = true }, cancellationToken);
        }

    }
}