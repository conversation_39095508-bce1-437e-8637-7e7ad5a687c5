using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Configuration;
using CoverGo.DomainUtils;
using CoverGo.MongoUtils;
using CoverGo.Products.Domain.Jackets.EventStores;
using MongoDB.Driver;

namespace CoverGo.Products.Infrastructure.Jackets.Adapters.Mongo
{
    public class MongoDbJacketEventStore : IEventStore<JacketEvent, JacketEventType>
    {
        private const string DbName = "products";

        public async Task<Result> AddEventAsync(string tenantId, JacketEvent @event, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<JacketEvent> clientCollection = db.GetCollection<JacketEvent>($"{tenantId}-jacket_events");

            await clientCollection.Indexes.CreateManyAsync(new[]
            {
                new CreateIndexModel<JacketEvent>(Builders<JacketEvent>.IndexKeys.Ascending(f => f.JacketId)),
                new CreateIndexModel<JacketEvent>(Builders<JacketEvent>.IndexKeys.Ascending(f => f.Timestamp)),
            });

            await clientCollection.InsertOneAsync(@event, cancellationToken: cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<IEnumerable<JacketEvent>> GetEventsAsync(string tenantId, IEnumerable<JacketEventType> types, IEnumerable<string> ids, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<JacketEvent> clientCollection = db.GetCollection<JacketEvent>($"{tenantId}-jacket_events");

            FilterDefinition<JacketEvent> mongoFilter = FilterDefinition<JacketEvent>.Empty;
            if (types != null)
                mongoFilter &= Builders<JacketEvent>.Filter.In(c => c.Type, types);
            if (ids != null)
                mongoFilter &= Builders<JacketEvent>.Filter.In(c => c.JacketId, ids);
            if (fromDate.HasValue)
                mongoFilter &= Builders<JacketEvent>.Filter.Gte(c => c.Timestamp, fromDate.Value);
            if (toDate.HasValue)
                mongoFilter &= Builders<JacketEvent>.Filter.Lte(c => c.Timestamp, toDate.Value);

            IEnumerable<JacketEvent> events = await clientCollection
                .Find(mongoFilter)
                .SortBy(e => e.Timestamp)
                .ToListAsync(cancellationToken);

            return events;
        }

    }
}