using CoverGo.Products.Domain.Jackets.Entities;
using CoverGo.Products.Domain.Jackets.Filters;
using MongoDB.Driver;

namespace CoverGo.Products.Infrastructure.Jackets.Adapters.Mongo
{
    public static class FilterDefinitionExtensions
    {
        public static FilterDefinition<Jacket> GetFilterDefinition(this JacketWhere where)
        {
            FilterDefinition<Jacket> mongoFilter = FilterDefinition<Jacket>.Empty;

            if (where.Or != null)
                foreach (JacketWhere orFilter in where.Or)
                    mongoFilter = mongoFilter != FilterDefinition<Jacket>.Empty
                        ? mongoFilter | GetFilterDefinition(orFilter)
                        : GetFilterDefinition(orFilter);

            else if (where.And != null)
                foreach (JacketWhere andFilter in where.And)
                    mongoFilter &= GetFilterDefinition(andFilter);

            else
                mongoFilter = mongoFilter.AddToFilterDefinition(where);

            return mongoFilter;
        }

        public static FilterDefinition<Jacket> AddToFilterDefinition(this FilterDefinition<Jacket> mongoFilter, JacketWhere where)
        {
            if (where.Id != null)
                mongoFilter &= Builders<Jacket>.Filter.Eq(p => p.Id, where.Id);
            if (where.Id_in != null)
                mongoFilter &= Builders<Jacket>.Filter.In(p => p.Id, where.Id_in);

            if (where.Title != null)
                mongoFilter &= Builders<Jacket>.Filter.Eq(p => p.Title, where.Title);
            if (where.Title_contains != null)
                mongoFilter &= Builders<Jacket>.Filter.Where(p => p.Title.Contains(where.Title_contains));

            if (where.Status != null)
                mongoFilter &= Builders<Jacket>.Filter.Eq(p => p.Status, where.Status);
            if (where.Status_contains != null)
                mongoFilter &= Builders<Jacket>.Filter.Where(p => p.Status.Contains(where.Status_contains));
            if (where.Status_in != null)
                mongoFilter &= Builders<Jacket>.Filter.In(p => p.Status, where.Status_in);

            if (where.CreatedAt_gt != null)
                mongoFilter &= Builders<Jacket>.Filter.Gt(c => c.CreatedAt, where.CreatedAt_gt);
            if (where.CreatedAt_lt != null)
                mongoFilter &= Builders<Jacket>.Filter.Lt(c => c.CreatedAt, where.CreatedAt_lt);

            if (where.LastModifiedAt_gt != null)
                mongoFilter &= Builders<Jacket>.Filter.Gt(c => c.LastModifiedAt, where.LastModifiedAt_gt);
            if (where.LastModifiedAt_lt != null)
                mongoFilter &= Builders<Jacket>.Filter.Lt(c => c.LastModifiedAt, where.LastModifiedAt_lt);

            if (where.CreatedById != null)
                mongoFilter &= Builders<Jacket>.Filter.Eq(p => p.CreatedById, where.CreatedById);
            if (where.CreatedById_in != null)
                mongoFilter &= Builders<Jacket>.Filter.In(p => p.CreatedById, where.CreatedById_in);

            if (where.LastModifiedById != null)
                mongoFilter &= Builders<Jacket>.Filter.Eq(p => p.LastModifiedById, where.LastModifiedById);
            if (where.LastModifiedById_in != null)
                mongoFilter &= Builders<Jacket>.Filter.In(p => p.LastModifiedById, where.LastModifiedById_in);

            return mongoFilter;
        }
    }
}