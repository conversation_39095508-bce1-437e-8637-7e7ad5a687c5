﻿using CoverGo.DomainUtils;
using CoverGo.MongoUtils;
using CoverGo.Products.Domain.BenefitDefinitions;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Configuration;
using CoverGo.Products.Domain.BenefitDefinitionTypes;
using System.Threading;
using MongoDB.Bson;
using System.Text.RegularExpressions;
using Newtonsoft.Json.Linq;

namespace CoverGo.Products.Infrastructure.BenefitDefinitions.Adapters.Mongo
{
    public class MongoBenefitDefinitionRepository : IBenefitDefinitionRepository
    {
        const string DbName = "products";

        public string ProviderId { get; } = "mongoDb";

        public async Task<IReadOnlyCollection<BenefitDefinition>> GetBenefitDefinitionAsync(string tenantId,
            BenefitDefinitionWhere where, OrderBy orderBy = null, int? skip = null, int? first = null,
            CancellationToken cancellationToken = default)
        {
            IFindFluent<BenefitDefinition, BenefitDefinition> searchBenefitDefinitionResult =
                BenefitDefinitionsFindFluentBuildInternal(tenantId, where);

            if (orderBy != null)
                searchBenefitDefinitionResult = orderBy.Type == OrderByType.ASC
                    ? searchBenefitDefinitionResult.Sort(Builders<BenefitDefinition>.Sort.Ascending(orderBy.FieldName))
                    : searchBenefitDefinitionResult.Sort(
                        Builders<BenefitDefinition>.Sort.Descending(orderBy.FieldName));

            return await searchBenefitDefinitionResult.Skip(skip).Limit(first).ToListAsync(cancellationToken);
        }

        public Task<long> CountBenefitDefinitionAsync(string tenantId, BenefitDefinitionWhere @where,
            CancellationToken cancellationToken = default)
        {
            IFindFluent<BenefitDefinition, BenefitDefinition> searchBenefitDefinitionResult =
                BenefitDefinitionsFindFluentBuildInternal(tenantId, where);

            return searchBenefitDefinitionResult.CountDocumentsAsync(cancellationToken);
        }

        private IFindFluent<BenefitDefinition, BenefitDefinition> BenefitDefinitionsFindFluentBuildInternal(
            string tenantId, BenefitDefinitionWhere where)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<BenefitDefinition> collection =
                db.GetCollection<BenefitDefinition>($"{tenantId}-benefitDefinitions");

            FilterDefinition<BenefitDefinition> filter = CreateFilter(where);

            return collection.Find(filter);
        }

        FilterDefinition<BenefitDefinition> CreateFilter(BenefitDefinitionWhere where)
        {
            FilterDefinition<BenefitDefinition> filter = Builders<BenefitDefinition>.Filter.Empty;
            if (where?.Or?.Any() == true)
                return CreateOrFilter(filter, where);

            if (where?.And?.Any() == true)
                return CreateAndFilter(filter, where);

            return CreateFieldsFilter(filter, where);
        }

        private FilterDefinition<BenefitDefinition> CreateAndFilter(FilterDefinition<BenefitDefinition> filter, BenefitDefinitionWhere where) =>
            where.And.Aggregate(filter, (current, andBenefitDefinitionWhere) => current & CreateFilter(andBenefitDefinitionWhere));

        private FilterDefinition<BenefitDefinition> CreateOrFilter(FilterDefinition<BenefitDefinition> filter, BenefitDefinitionWhere where) =>
            where.Or.Aggregate(filter, (current, benefitDefinitionFilter) => current != FilterDefinition<BenefitDefinition>.Empty
                ? current | CreateFilter(benefitDefinitionFilter)
                : CreateFilter(benefitDefinitionFilter));

        private static FilterDefinition<BenefitDefinition> CreateFieldsFilter(FilterDefinition<BenefitDefinition> filter, BenefitDefinitionWhere where)
        {
            if (where == null) return filter;
            if (where.Id_in != null) return Builders<BenefitDefinition>.Filter.In(x => x.Id, where.Id_in);
            if (where.BusinessId_in?.Any() == true)
            {
                FilterDefinition<BenefitDefinition> result = null;
                foreach (string businessId in @where.BusinessId_in)
                {
                    FilterDefinition<BenefitDefinition> businessIdFilter = Builders<BenefitDefinition>.Filter.Regex(x => x.BusinessId, new BsonRegularExpression($"/^{Regex.Escape(businessId)}$/i"));
                    result = result != null ? result | businessIdFilter : businessIdFilter;
                }
                return result;
            }
            if (where.Name_contains?.Any() == true) return Builders<BenefitDefinition>.Filter.Where(x => x.Name.ToLower().Contains(where.Name_contains.ToLower()));
            if (where.BusinessId_contains?.Any() == true) return Builders<BenefitDefinition>.Filter.Where(x => x.BusinessId.ToLower().Contains(where.BusinessId_contains.ToLower()));
            if (where.Status != null) return Builders<BenefitDefinition>.Filter.Eq(x => x.Status, where.Status);
            if (where.TypeId_in?.Any() ?? false)
            {
                return Builders<BenefitDefinition>.Filter.AnyIn(x => x.BenefitDefinitionTypeIds, where.TypeId_in);
            }
            if (where.Fields != null &&
                where.Fields.Path.StartsWith($"{nameof(BenefitDefinition.Fields).ToLower()}."))
                return where.Fields.Build<BenefitDefinition>();
            return filter;
        }

        public async Task<Result<CreatedStatus>> CreateBenefitDefinitionAsync(string tenantId, string id, CreateBenefitDefinitionCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<BenefitDefinition> collection = db.GetCollection<BenefitDefinition>($"{tenantId}-benefitDefinitions");
            await collection.Indexes.CreateOneAsync(
                new CreateIndexModel<BenefitDefinition>(Builders<BenefitDefinition>.IndexKeys.Ascending("Id"))
            );

            var benefitDefinition = new BenefitDefinition
            {
                Id = id,
                Name = command.Name,
                BusinessId = command.BusinessId,
                Description = command.Description,
                Status = command.Status,
                BenefitDefinitionTypeIds = command.BenefitDefinitionTypeIds,
                LastModifiedById = command.CreatedById,
                LastModifiedAt = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow,
                Fields = command.Fields == null ? null : JToken.Parse(command.Fields)
            };

            await collection.InsertOneAsync(benefitDefinition, cancellationToken: cancellationToken);

            return new Result<CreatedStatus> { Status = "success", Value = new CreatedStatus { Id = benefitDefinition.Id } };
        }

        public async Task<Result> UpdateBenefitDefinitionAsync(string tenantId, UpdateBenefitDefinitionCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<BenefitDefinition> collection = db.GetCollection<BenefitDefinition>($"{tenantId}-benefitDefinitions");

            FilterDefinition<BenefitDefinition> filter = Builders<BenefitDefinition>.Filter
                .Eq(p => p.Id, command.BenefitDefinitionId);

            UpdateDefinitionBuilder<BenefitDefinition> update = Builders<BenefitDefinition>.Update;

            var updates = new List<UpdateDefinition<BenefitDefinition>>
            {
                update.Set(a => a.LastModifiedAt, DateTime.UtcNow),
                update.Set(a => a.LastModifiedById, command.ModifiedById)
            };
            if (command.Name != null) updates.Add(update.Set(a => a.Name, command.Name));
            if (command.BusinessId != null) updates.Add(update.Set(a => a.BusinessId, command.BusinessId));
            if (command.Description != null) updates.Add(update.Set(a => a.Description, command.Description));
            if (command.Status != null) updates.Add(update.Set(a => a.Status, command.Status));
            if (command.BenefitDefinitionTypeIds != null) updates.Add(update.Set(a => a.BenefitDefinitionTypeIds, command.BenefitDefinitionTypeIds));
            if (command.Fields != null)
            {
                JToken jTokenFields = command.Fields == null ? null : JToken.Parse(command.Fields);
                updates.Add(update.Set(a => a.Fields, jTokenFields));
            }

            UpdateResult result = await collection.UpdateOneAsync(filter, update.Combine(updates), cancellationToken: cancellationToken);

            return new Result { Status = result.MatchedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> DeleteBenefitDefinitionAsync(string tenantId, string benefitDefinitionId, DeleteCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "benefitDefinitions"));
            IMongoDatabase db = client.GetDatabase(DbName);

            DeleteResult result = await db.GetCollection<BenefitDefinition>($"{tenantId}-benefitDefinitions")
                .DeleteOneAsync(Builders<BenefitDefinition>.Filter.Eq(p => p.Id, benefitDefinitionId), cancellationToken);

            return result.DeletedCount > 0
                ? new Result { Status = "success" }
                : new Result { Status = "failure", Errors = new List<string> { "BenefitDefinition with such id doesn't exist" } };
        }

        public async Task<Result> BatchBenefitDefinitionAsync(string tenantId, BatchBenefitDefinitionCommand command,
            CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<BenefitDefinition> collection = db.GetCollection<BenefitDefinition>($"{tenantId}-benefitDefinitions");
            await collection.Indexes.CreateOneAsync(
                new CreateIndexModel<BenefitDefinition>(Builders<BenefitDefinition>.IndexKeys.Ascending("Id")), cancellationToken: cancellationToken);

            if (command.CreateBenefitDefinitionCommands.Any())
            {
                var benefitDefinitions =  command.CreateBenefitDefinitionCommands.Select(c => new BenefitDefinition
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = c.Name,
                    BusinessId = c.BusinessId,
                    Description = c.Description,
                    Status = c.Status,
                    BenefitDefinitionTypeIds = c.BenefitDefinitionTypeIds,
                    LastModifiedById = c.CreatedById,
                    LastModifiedAt = DateTime.UtcNow,
                    CreatedAt = DateTime.UtcNow,
                    Fields = c.Fields == null ? null : JToken.Parse(c.Fields)
                }).ToList();

                await collection.InsertManyAsync(benefitDefinitions, cancellationToken: cancellationToken);
            }
            

            var updateResults = new List<Result>();
            
            command.UpdateBenefitDefinitionCommands.ForEach(async updateCommand =>
            {
                updateResults.Add(await UpdateBenefitDefinitionAsync(tenantId, updateCommand, cancellationToken));
            });
                
            return new Result { Status = updateResults.Any(x => x.Status == "failure") ?  "failure" :"success" };
        }

        public async Task<IReadOnlyCollection<BenefitDefinitionType>> GetBenefitDefinitionTypeAsync(string tenantId, BenefitDefinitionTypeWhere where, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<BenefitDefinitionType> collection = db.GetCollection<BenefitDefinitionType>($"{tenantId}-benefitDefinitionTypes");

            FilterDefinition<BenefitDefinitionType> filter = Builders<BenefitDefinitionType>.Filter.Empty;
            if (where.BusinessId_in?.Any() == true)
            {
                FilterDefinition<BenefitDefinitionType> result = null;
                foreach (string businessId in @where.BusinessId_in)
                {
                    FilterDefinition<BenefitDefinitionType> businessIdFilter = Builders<BenefitDefinitionType>.Filter.Regex(x => x.BusinessId, new BsonRegularExpression($"/^{Regex.Escape(businessId)}$/i"));
                    result = result != null ? result | businessIdFilter : businessIdFilter;
                }
                filter &= result;
            }

            return await collection.Find(filter).ToListAsync(cancellationToken);
        }
    }
}