﻿using CoverGo.DomainUtils;
using CoverGo.HttpUtils;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Underwriting;
using Manatee.Json.Schema;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using CoverGo.Configuration;
using System.Threading;
using System.Globalization;
using Newtonsoft.Json.Converters;
using System.IO;
using CoverGo.Threading.Tasks;

namespace CoverGo.Products.Infrastructure.Underwriting.Adapters.EBao
{
    public class EBaoUnderwritingEngine : IUnderwritingEngine
    {
        public string ProviderId => "eBao";

        private const string EBAO_AUTH_ENDPOINT = "https://sandbox-hk.insuremo.com/cas/ebao/v1/json/tickets";
        private const string EBAO_KEY = "tahoe-b2c.api";
        private const string EBAO_VALUE = "FDTyda9X";

        public async Task<IEnumerable<(ProductId, Result)>> EvaluateUnderwritingsAsync(string tenantId, IEnumerable<ProductId> productIds, JToken factors, CancellationToken cancellationToken)
        {
            var config = DbConfig.GetConfig(tenantId, "underwriting");

            string endpoint = config.Endpoint;

            (ProductId id, Result result)[] evaluationResults = await productIds.ParallelSelectAsync(async (p, ct) =>
            {
                var errors = new List<string> { };

                Result<string> productCodeResult = GetEbaoProductCodes(p);
                if (productCodeResult.Status != "success")
                    return (p, Result.Failure(productCodeResult.Errors?.ToList()));

                string productCode = productCodeResult.Value;

                using var authHttpClient = new HttpClient();

                EBaoAuthResponse authResponse = await authHttpClient.GenericPostAsync<EBaoAuthResponse, EBaoAuthRequest>(
                    EBAO_AUTH_ENDPOINT,
                new EBaoAuthRequest
                {
                    Username = EBAO_KEY,
                    Password = EBAO_VALUE
                }, ct);

                using var httpClient = new HttpClient();
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", authResponse.Access_token);

                JToken insured = factors?.Value<JArray>("insureds")?.Children().FirstOrDefault();
                if (insured == null)
                    return (p, Result.Success());

                int? insuredAge = insured.Value<int?>("age");
                string employmentStatus = insured.Value<string>("employmentStatus");
                int? sumAssured = insured.Value<int?>("sumAssured");

                if (insuredAge != null)
                {
                    var insuredAgeRequest = new EBaoAgeLimitRequest
                    {
                        ProductCode = productCode
                    };

                    EBaoAgeLimitResponse response = await httpClient.GenericPostAsync<EBaoAgeLimitResponse, EBaoAgeLimitRequest>(endpoint + "/tahoe-bs/products/limit/age", insuredAgeRequest, ct);

                    if (response.Result == -1)
                        ThrowFailedRequest(insuredAgeRequest, response, "Underwriting age validation failed");

                    bool? isInsuredAgeValid = response.ProductAge?.InsuredAge?.Any(a => a.MinAge <= insuredAge && insuredAge <= a.MaxAge);
                    if (isInsuredAgeValid != true)
                        errors.Add("insuredAge is not valid.");
                }

                if (sumAssured != null)
                {
                    var sALimitRequest = new EBaoSALimitRequest
                    {
                        EmployedIndi = GetEmployedIndiCode(employmentStatus),
                        InsuredAgeMonth = insuredAge * 12,
                        ProductCode = productCode
                    };

                    EBaoSALimitResponse response = await httpClient.GenericPostAsync<EBaoSALimitResponse, EBaoSALimitRequest>(endpoint + "/tahoe-bs/products/limit/sa", sALimitRequest, ct);

                    if (response.Result == -1)
                        ThrowFailedRequest(sALimitRequest, response, "Underwriting sum assured validation failed");

                    bool? isSumAssuredValid = response.ProductSumAssured?.SaLimit?.Any(s => s.MinAmount <= sumAssured && sumAssured <= s.MaxAmount);
                    if (isSumAssuredValid != true)
                        errors.Add("'sumAssured' is not valid.");
                }

                return errors.Any()
                    ? (p, Result.Failure(errors))
                    : (p, Result.Success());
            }, new ParallelRunOptions(){MaxDegreeOfParallelism = 10, CancellationToken = cancellationToken});

            return evaluationResults;
        }

        public async Task<IEnumerable<(ProductId, string)>> GetUnderwritingJsonSchema(string tenantId, IEnumerable<ProductId> productIds, JToken factors, CancellationToken cancellationToken)
        {
            var config = DbConfig.GetConfig(tenantId, "underwriting");

            string endpoint = config.Endpoint;

            using var authHttpClient = new HttpClient();

            EBaoAuthResponse authResponse = await authHttpClient.GenericPostAsync<EBaoAuthResponse, EBaoAuthRequest>(
                EBAO_AUTH_ENDPOINT,
            new EBaoAuthRequest
            {
                Username = EBAO_KEY,
                Password = EBAO_VALUE
            }, cancellationToken);

            int? insuredAge = null;
            string employmentStatus = null;
            JToken insured = factors?.Value<JArray>("insureds")?.Children().FirstOrDefault();
            if (insured != null)
            {
                insuredAge = insured.Value<int?>("age");
                employmentStatus = insured.Value<string>("employmentStatus");
            }

            (ProductId id, string schema)[] schemas = await productIds.ParallelSelectAsync(async (p, ct) =>
            {
                var errors = new List<string> { };

                Result<string> productCodeResult = GetEbaoProductCodes(p);
                if (productCodeResult.Status != "success")
                    return (p, null); //handleError case

                string productCode = productCodeResult.Value;

                using var httpClient = new HttpClient();
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", authResponse.Access_token);

                var insuredAgeRequest = new EBaoAgeLimitRequest
                {
                    ProductCode = productCode
                };

                EBaoAgeLimitResponse ageResponse = null;
                if (insuredAge == null)
                {
                    ageResponse = await httpClient.GenericPostAsync<EBaoAgeLimitResponse, EBaoAgeLimitRequest>(endpoint + "/tahoe-bs/products/limit/age", insuredAgeRequest, ct);

                    if (ageResponse.Result == -1)
                        ThrowFailedRequest(insuredAgeRequest, ageResponse, "Underwriting age validation failed");

                    //from age response get entire age range
                }

                var sALimitRequest = new EBaoSALimitRequest
                {
                    ProductCode = productCode,
                    InsuredAgeMonth = insuredAge * 12,
                    EmployedIndi = GetEmployedIndiCode(employmentStatus)
                };

                EBaoSALimitResponse saLimitResponse = await httpClient.GenericPostAsync<EBaoSALimitResponse, EBaoSALimitRequest>(endpoint + "/tahoe-bs/products/limit/sa", sALimitRequest, ct);

                if (saLimitResponse.Result == -1)
                    ThrowFailedRequest(sALimitRequest, saLimitResponse, "Underwriting sum assured validation failed");

                try
                {
                    string schema = GenerateSchema(saLimitResponse.ProductSumAssured, ageResponse?.ProductAge, insuredAge, employmentStatus);
                    return (p, schema);
                }
                catch
                {
                    return (p, null);
                }
            }, new ParallelRunOptions(){MaxDegreeOfParallelism = 10, CancellationToken = cancellationToken});
            
            return schemas;
        }

        void ThrowFailedRequest(object request, object response, string message)
        {

            var serializationSettings = new JsonSerializerSettings
            {
                ContractResolver = new DefaultContractResolver
                {
                    NamingStrategy = new CamelCaseNamingStrategy()
                },
                Formatting = Formatting.Indented
            };

            string requestJson = JsonConvert.SerializeObject(request, serializationSettings);
            string responseJson = JsonConvert.SerializeObject(response, serializationSettings);
            throw new Exception($"{message}: REQUEST: {requestJson} ||| RESPONSE: {responseJson}");
        }

        public static string GenerateSchema(
            EBaoProductSumAssured productSumAssured,
            EBaoProductAge productAge = null,
            int? insuredAge = null,
            string employedStatus = null)
        {
            IReadOnlyCollection<EBaoSALimit> relevantProducts = productSumAssured.SaLimit
                .Where(sl => insuredAge == null || (sl.LowAgeMonth <= insuredAge * 12 && sl.HighAgeMonth >= insuredAge * 12))
                .Where(sl => employedStatus == null || GetEmployedStatus(sl.EmployedIndi) == employedStatus)
                .ToList();

            static string GetEmployedStatus(string employmentIndCode) =>
              employmentIndCode switch
              {
                  "01" => "salariedEmployee",
                  "05" => "retired",
                  "06" => "housewife",
                  "07" => "student",
                  "09" => "employed",
                  "12" => "unEmployed",
                  _ => null,
              };

            JsonSchema CreateRulesEmployment(string employmentInd, IEnumerable<EBaoSALimit> saLimits)
            {
                JsonSchema result = new JsonSchema()
                    .If(new JsonSchema()
                        .Property("employmentStatus", new JsonSchema()
                            .Const(employmentInd)
                        )
                    );

                if (insuredAge == null)
                    return result.Then(new JsonSchema()
                        .AllOf(saLimits.Select(CreateRulesAge).ToArray())
                    );

                return result
                    .Then(new JsonSchema()
                        .Property("sumAssured", new JsonSchema()
                            .Minimum(saLimits.First().MinAmount)
                            .Maximum(saLimits.First().MaxAmount)
                        )
                    );
            }

            static JsonSchema CreateRulesAge(EBaoSALimit saLimit) =>
                new JsonSchema()
                    .If(new JsonSchema()
                        .Property("age", new JsonSchema()
                            .Minimum(saLimit.LowAgeMonth / 12)
                            .Maximum(saLimit.HighAgeMonth / 12)
                        )
                    )
                    .Then(new JsonSchema()
                        .Property("sumAssured", new JsonSchema()
                            .Minimum(saLimit.MinAmount)
                            .Maximum(saLimit.MaxAmount)
                        )
                    );

            IEnumerable<JsonSchema> CreateRules()
            {
                if (employedStatus == null)
                {
                    return relevantProducts
                        .GroupBy(sl => sl.EmployedIndi)
                        .Select(g => CreateRulesEmployment(GetEmployedStatus(g.Key), g));
                }

                return relevantProducts.Select(CreateRulesAge);
            }

            JsonSchema AddAgeRequirements(JsonSchema input)
            {
                if (productAge == null) return input;

                if (productAge.InsuredAge.Any())
                    input.Minimum(productAge.InsuredAge.First().MinAge);

                if (productAge.InsuredAge.Any())
                    input.Maximum(productAge.InsuredAge.First().MaxAge);

                return input;
            }

            JsonSchema GetRequiredForAll()
            {
                var result = new JsonSchema();
                var required = new List<string>();
                if (insuredAge == null)
                    required.Add("age");
                if (employedStatus == null)
                    required.Add("employmentStatus");

                required.Add("sumAssured");

                result.Required(required.ToArray());

                if (insuredAge == null)
                    result.Property("age", AddAgeRequirements(new JsonSchema().Type(JsonSchemaType.Integer)));

                if (employedStatus == null)
                    result.Property("employmentStatus", new JsonSchema()
                        .Type(JsonSchemaType.String)
                        .Enum(
                            "salariedEmployee",
                            "retired",
                            "housewife",
                            "student",
                            "employed",
                            "unEmployed"
                        )
                    );

                return result.Property("sumAssured", new JsonSchema()
                    .Type(JsonSchemaType.Integer)
                    .Minimum(relevantProducts.Min(sl => sl.MinAmount))
                    .Maximum(relevantProducts.Max(sl => sl.MaxAmount))
                );
            }

            JsonSchema requiredForAll = GetRequiredForAll();

            JsonSchema schema = new JsonSchema()
                .Schema("http://json-schema.org/draft-07/schema")
                .Id("http://example.com/example.json")
                .Type(JsonSchemaType.Object)
                .Required("insureds")
                .Property("insureds", new JsonSchema()
                    .MinItems(1)
                    .MaxItems(1)
                    .Items(new JsonSchema()
                        .AllOf(relevantProducts.Count > 1 ? CreateRules().Prepend(requiredForAll).ToArray() : new[] { requiredForAll })
                    )
                );


            return schema.ToJson(new Manatee.Json.Serialization.JsonSerializer()).ToString();
        }

        private Result<string> GetEbaoProductCodes(ProductId productId)
        {
            if (productId == new ProductId { Plan = "easy_protector_term_insurance_plan_5_years_usd", Type = "term_life" })
                return new Result<string> { Status = "success", Value = "B050505BU" };

            else if (productId == new ProductId { Plan = "easy_protector_term_insurance_plan_5_years_hkd", Type = "term_life" })
                return new Result<string> { Status = "success", Value = "B050505BH" };

            else if (productId == new ProductId { Plan = "easy_protector_term_insurance_plan_10_years_usd", Type = "term_life" })
                return new Result<string> { Status = "success", Value = "B051010BU" };

            else if (productId == new ProductId { Plan = "easy_protector_term_insurance_plan_10_years_hkd", Type = "term_life" })
                return new Result<string> { Status = "success", Value = "B051010BH" };

            else if (productId == new ProductId { Plan = "brilliant_saver_3_protection_plan_5_years_usd", Type = "endowment" })
                return new Result<string> { Status = "success", Value = "I100205BU" };

            return new Result<string> { Status = "failure", Errors = new List<string> { $"No product Code found for `{productId.ToString()}`." } };
        }

        private class EBaoAuthRequest
        {
            public string Username { get; set; }
            public string Password { get; set; }
        }

        private class EBaoAuthResponse
        {
            public string Access_token { get; set; }
            public int Expire_in { get; set; }
        }

        private class EBaoAgeLimitRequest
        {
            public string ProductCode { get; set; }
        }

        public class EBaoAgeLimitResponse
        {
            public int Result { get; set; }
            public string ProductCode { get; set; }
            public IEnumerable<string> Exceptions { get; set; }
            public EBaoProductAge ProductAge { get; set; }
        }

        public class EBaoProductAge
        {
            public IEnumerable<EBaoAgeLimitInformation> InsuredAge { get; set; }
        }

        public class EBaoAgeLimitInformation
        {
            public int MinAge { get; set; }
            public int MaxAge { get; set; }
        }

        private class EBaoSALimitRequest
        {
            public string ProductCode { get; set; }
            public int? InsuredAgeMonth { get; set; }
            public string EmployedIndi { get; set; }
        }

        private class EBaoSALimitResponse
        {
            public int Result { get; set; }
            public string ProductCode { get; set; }
            public IEnumerable<string> Exceptions { get; set; }
            public EBaoProductSumAssured ProductSumAssured { get; set; }
        }

        public class EBaoProductSumAssured
        {
            public List<EBaoSALimit> SaLimit { get; set; }
        }

        public class EBaoSALimit
        {
            public string LimitUnit { get; set; }
            public long MaxAmount { get; set; }
            public long MinAmount { get; set; }
            public int Currency { get; set; }  //probs not needed
            public int LowAgeMonth { get; set; }
            public int HighAgeMonth { get; set; }
            public int OccupationCode { get; set; } //probs not needed
            public int PaymentMethod { get; set; }
            public string EmployedIndi { get; set; }
        }

        private string GetEmployedIndiCode(string employmentStatus) =>
          employmentStatus switch
          {
              "salariedEmployee" => "01",
              "retired" => "05",
              "housewife" => "06",
              "student" => "07",
              "employed" => "09",
              "unEmployed" => "12",
              _ => null,
          };


    }
}
