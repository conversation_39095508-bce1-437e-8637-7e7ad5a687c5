using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.Configuration;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Underwriting;

using JsonLogic.Net;

using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;

namespace CoverGo.Products.Infrastructure.Underwriting.Adapters.JsonLogic
{
    public class JsonLogicUnderwritingEngine(
        IProductRepository productRepository,
        JsonLogicEvaluator evaluator) : IUnderwritingEngine
    {
        public string ProviderId => "jsonLogic";

        public async Task<IEnumerable<(ProductId, Result)>> EvaluateUnderwritingsAsync(string tenantId, IEnumerable<ProductId> productIds, JToken factors, CancellationToken cancellationToken)
        {
            var dbconfig = DbConfig.GetConfig(tenantId, "products");

            IEnumerable<ProductUnderwritingJsonLogicRules> productUnderwritings = await productRepository.GetUnderwritingJsonLogicRulesAsync(tenantId, new ProductWhere { Id_in = productIds?.ToList() }, cancellationToken);

            var evaluationResults = new ConcurrentBag<(ProductId, Result)>();

            Parallel.ForEach(productIds, p =>
            {
                ProductUnderwritingJsonLogicRules productUnderwriting = productUnderwritings?.FirstOrDefault(u => u.Id == p);

                if (productUnderwriting == null)
                {
                    evaluationResults.Add((p, Result.Success()));
                    return;
                }

                ExpandoObject dataToken = JsonConvert.DeserializeObject<ExpandoObject>(JsonConvert.SerializeObject(factors), new ExpandoObjectConverter());

                JToken rules = productUnderwriting.JsonLogicRules;
                if (rules?.HasValues ?? false)
                {
                    try
                    {
                        object eval = evaluator.Apply(rules, dataToken);
                        if (eval is bool isAllowed && isAllowed == false)
                        {
                            evaluationResults.Add((p, Result.Failure(new List<string> { })));
                            return;
                        }
                    }
                    catch
                    {
                        evaluationResults.Add((p, Result.Failure(new List<string> { })));
                        return;
                    }
                }

                evaluationResults.Add((p, Result.Success()));
            });

            return evaluationResults;
        }

        public async Task<IEnumerable<(ProductId, string)>> GetUnderwritingJsonSchema(string tenantId, IEnumerable<ProductId> productIds, JToken factors, CancellationToken cancellationToken)
            => Enumerable.Empty<(ProductId, string)>();
    }
}
