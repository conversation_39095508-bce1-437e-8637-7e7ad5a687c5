﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Configuration;
using CoverGo.DomainUtils;
using CoverGo.MongoUtils;
using CoverGo.Products.Domain.UiSchemas;
using MongoDB.Driver;
using Newtonsoft.Json.Linq;

namespace CoverGo.Products.Infrastructure.UiSchemas.Adapters.Mongo
{
    public class MongoUiSchemaRepository : IUiSchemaRepository
    {
        private const string DbName = "products";
        private const string ConfigType = "products";
        private const string CollectionNamePostfix = "uiSchemas";

        public async Task<Result<CreatedStatus>> CreateAsync(string tenantId, string uiSchemaId, CreateUiSchemaCommand command, CancellationToken cancellationToken)
        {
            IMongoCollection<UiSchema> collection = GetMongoCollection(tenantId);
            await collection.Indexes.CreateOneAsync(
                new CreateIndexModel<UiSchema>(Builders<UiSchema>.IndexKeys.Ascending("Id")));
            UiSchema dataSchema = BuildUiSchema(command, uiSchemaId);
            await collection.InsertOneAsync(dataSchema, cancellationToken: cancellationToken);
            return new Result<CreatedStatus>
            {
                Status = "success",
                Value = new CreatedStatus { Id = dataSchema.Id }
            };
        }

        public async Task<IReadOnlyCollection<UiSchema>> GetAsync(string tenantId, UiSchemaWhere where, CancellationToken cancellationToken)
        {
            IMongoCollection<UiSchema> collection = GetMongoCollection(tenantId);

            FilterDefinition<UiSchema> idFilter = CreateFilter(where);

            return await collection.Find(idFilter).ToListAsync(cancellationToken);
        }

        public async Task<Result> DeleteAsync(string tenantId, string uiSchemaId, DeleteCommand command, CancellationToken cancellationToken)
        {
            IMongoCollection<UiSchema> collection = GetMongoCollection(tenantId);
            FilterDefinition<UiSchema> filter = Builders<UiSchema>.Filter.Eq(p => p.Id, uiSchemaId);
            DeleteResult result = await collection.DeleteOneAsync(filter, cancellationToken);

            return result.DeletedCount > 0
                ? new Result { Status = "success" }
                : new Result
                {
                    Status = "failure",
                    Errors = new List<string>
                    { "UiSchema with such name doesn't exist" }
                };
        }

        public async Task<Result> UpdateAsync(string tenantId, UpdateUiSchemaCommand command, CancellationToken cancellationToken)
        {
            IMongoCollection<UiSchema> collection = GetMongoCollection(tenantId);

            FilterDefinition<UiSchema> filter = Builders<UiSchema>.Filter.Eq(p => p.Id, command.Id);

            UpdateDefinitionBuilder<UiSchema> update = Builders<UiSchema>.Update;

            IEnumerable<UpdateDefinition<UiSchema>> updates = UpdateCommand(command, update);

            UpdateResult result = await collection.UpdateOneAsync(filter, update.Combine(updates), cancellationToken: cancellationToken);

            return new Result { Status = result.MatchedCount != 0 ? "success" : "failure" };
        }

        private IMongoCollection<UiSchema> GetMongoCollection(string tenantId)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, ConfigType));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<UiSchema> collection = db.GetCollection<UiSchema>($"{tenantId}-{CollectionNamePostfix}");
            return collection;
        }

        private UiSchema BuildUiSchema(CreateUiSchemaCommand command, string uiSchemaId) =>
                    new UiSchema
                    {
                        Id = uiSchemaId,
                        Name = command.Name,
                        Schema = JToken.Parse(command.Schema ?? "{}"),
                        Standard = command.Standard,
                        LastModifiedById = command.CreatedById,
                        LastModifiedAt = DateTime.UtcNow,
                        CreatedAt = DateTime.UtcNow,
                    };

        private IEnumerable<UpdateDefinition<UiSchema>> UpdateCommand(UpdateUiSchemaCommand command,
            UpdateDefinitionBuilder<UiSchema> update)
        {
            var updates = new List<UpdateDefinition<UiSchema>>
            {
                update.Set(a => a.LastModifiedAt, DateTime.UtcNow),
                update.Set(a => a.LastModifiedById, command.ModifiedById)
            };
            if (command.Name != null) updates.Add(update.Set(a => a.Name, command.Name));
            if (command.Schema != null) updates.Add(update.Set(a => a.Schema, JToken.Parse(command.Schema ?? "{}")));
            if (command.Standard != null) updates.Add(update.Set(a => a.Standard, command.Standard));
            return updates;
        }

        private FilterDefinition<UiSchema> CreateFilter(UiSchemaWhere where)
        {
            FilterDefinition<UiSchema> result = Builders<UiSchema>.Filter.Empty;

            if (where?.Or?.Any() == true)
                return CreateOrFilter(result, where);

            if (where?.And?.Any() == true)
                return CreateAndFilter(result, where);

            return CreateFieldFilter(result, where);
        }

        private FilterDefinition<UiSchema> CreateOrFilter(FilterDefinition<UiSchema> filter,
            UiSchemaWhere where)
        {
            foreach (UiSchemaWhere orFilter in where.Or)
                filter = filter != FilterDefinition<UiSchema>.Empty
                    ? filter | CreateFilter(orFilter)
                    : CreateFilter(orFilter);

            return filter;
        }

        private FilterDefinition<UiSchema> CreateAndFilter(FilterDefinition<UiSchema> filter,
            UiSchemaWhere where)
        {
            foreach (UiSchemaWhere andFilter in where.And)
                filter &= CreateFilter(andFilter);

            return filter;
        }

        private FilterDefinition<UiSchema> CreateFieldFilter(FilterDefinition<UiSchema> filter,
            UiSchemaWhere where)
        {
            if (where == null)
                return filter;
            if (where.Id != null)
                return Builders<UiSchema>.Filter.Eq(x => x.Id, where.Id);
            if (where.Id_in != null)
                return Builders<UiSchema>.Filter.In(x => x.Id, where.Id_in);
            if (where.Name != null)
                return Builders<UiSchema>.Filter.Eq(x => x.Name, where.Name);
            if (where.Name_in != null)
                return Builders<UiSchema>.Filter.In(x => x.Name, where.Name_in);
            if (where.CreatedAt_gt != null)
                return Builders<UiSchema>.Filter.Gt(c => c.CreatedAt, where.CreatedAt_gt);
            if (where.CreatedAt_lt != null)
                return Builders<UiSchema>.Filter.Lt(c => c.CreatedAt, where.CreatedAt_lt);
            if (where.Schema != null && where.Schema.Path.StartsWith(nameof(UiSchema.Schema).ToLower() + "."))
                return filter & where.Schema.Build<UiSchema>();
            return filter;
        }
    }
}
