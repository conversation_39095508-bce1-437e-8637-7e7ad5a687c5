<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CoverGo.Multitenancy" />
    <PackageReference Include="DotNetZip" />
    <PackageReference Include="Manatee.Json" />
    <PackageReference Include="System.Text.Encoding.CodePages" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CoverGo.Applications.Infrastructure" />
    <PackageReference Include="CoverGo.FeatureManagement" />
    <PackageReference Include="CoverGo.HttpUtils" />
    <PackageReference Include="CoverGo.MongoUtils" />
    <PackageReference Include="CoverGo.JsonUtils" />
    <PackageReference Include="CoverGo.L10n.Client" />
    <PackageReference Include="CoverGo.Auth.Client" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CoverGo.Products.Domain\CoverGo.Products.Domain.csproj" />
    <ProjectReference Include="..\CoverGo.Products.Infrastructure.GatewayClient\CoverGo.Products.Infrastructure.GatewayClient.csproj" />
  </ItemGroup>

</Project>
