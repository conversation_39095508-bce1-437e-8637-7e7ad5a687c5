﻿using CoverGo.DomainUtils;
using CoverGo.HttpUtils;
using CoverGo.Products.Domain.Illustrations;
using CoverGo.Products.Domain.Products;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using CoverGo.Configuration;
using System.Threading;
using CoverGo.Products.Infrastructure.Underwriting.Adapters.EBao;

namespace CoverGo.Products.Infrastructure.Illustrations.Adapters.EBao
{
    public class EBaoIllustrationRepository : IIllustrationRepository
    {
        public string ProviderId => "eBao";
        private const string EBAO_AUTH_ENDPOINT = "https://sandbox-hk.insuremo.com/cas/ebao/v1/json/tickets";
        private const string EBAO_KEY = "tahoe-b2c.api";
        private const string EBAO_VALUE = "FDTyda9X";

        public async Task<Result<IEnumerable<Illustration>>> GetIllustrationsAsync(string tenantId, ProductWhere filter, JToken factors, CancellationToken cancellationToken)
        {
            var dbConfig = DbConfig.GetConfig(tenantId, "illustrations");

            if (filter.Id_in.Count != 1) return Result<IEnumerable<Illustration>>.Success(Enumerable.Empty<Illustration>());
            ProductId productId = filter.Id_in.First();

            (bool isCorrect, int coverageSerialId, string productCode, CurrencyCode currencyCode) = GetEbaoProductCodes(productId);
            if (!isCorrect) return Result<IEnumerable<Illustration>>.Success(Enumerable.Empty<Illustration>());

            if (factors == null) return Result<IEnumerable<Illustration>>.Success(Enumerable.Empty<Illustration>());

            JArray insureds = factors.Value<JArray>("insureds");
            JToken insured = insureds.First();
            int? age = insured.Value<int?>("age");
            if (age == null) return Result<IEnumerable<Illustration>>.Success(Enumerable.Empty<Illustration>());
            string gender = insured.Value<string>("gender");
            if (gender == null) return Result<IEnumerable<Illustration>>.Success(Enumerable.Empty<Illustration>());
            bool? isSmoking = insured.Value<bool?>("isSmoking");
            if (isSmoking == null) return Result<IEnumerable<Illustration>>.Success(Enumerable.Empty<Illustration>());
            decimal? sumAssured = insured.Value<decimal?>("sumAssured");
            if (sumAssured == null) return Result<IEnumerable<Illustration>>.Success(Enumerable.Empty<Illustration>());

            bool prepayment = factors.Value<bool?>("prepayment") ?? false;

            using var authHttpClient = new HttpClient();
            EBaoAuthResponse authResponse = await authHttpClient.GenericPostAsync<EBaoAuthResponse, EBaoAuthRequest>(
                EBAO_AUTH_ENDPOINT,
                new EBaoAuthRequest
                {
                    Username = EBAO_KEY,
                    Password = EBAO_VALUE
                }, cancellationToken);

            using var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", authResponse.Access_token);
            var request = new EBaoIllustrationRequest
            {
                CalculatePolicy = new EBaoCalculatePolicy
                {
                    InceptionDate = DateTime.UtcNow,
                    PrePaymentIndi = prepayment ? "Y" : "N",
                    Coverages = new List<EbaoCoverage>
                    {
                        new EbaoCoverage
                        {
                            CoverageSerialId = coverageSerialId,
                            ProductCode = productCode,
                            ChargePeriod = "2",
                            ChargeYear = 2,
                            CoveragePeriod = "2",
                            CoverageYear = 5,
                            CalculatePremium = new EBaoCalculatePremium
                            {
                                SumAssured = sumAssured.Value,
                                PaymentFreq = 1,
                                BenefitLevel = "1"
                            },
                            Insureds = new List<EBaoInsured>
                            {
                                new EBaoInsured
                                {
                                    Age = age.Value,
                                    Gender = gender == "male"? "M": "F",
                                    Smoking = isSmoking.Value ? "Y": "N",
                                    OrderId = 1
                                }
                            }
                        }
                    },
                },
            };

            EBaoIllustrationResponse response = await httpClient.GenericPostAsync<EBaoIllustrationResponse, EBaoIllustrationRequest>(dbConfig.Endpoint + "/tahoe-bs/products/benefitIllustration", request, cancellationToken);
            if (response.Result == -1)
            {
                var serializationSettings = new JsonSerializerSettings
                {
                    ContractResolver = new DefaultContractResolver
                    {
                        NamingStrategy = new CamelCaseNamingStrategy()
                    },
                    Formatting = Formatting.Indented
                };

                string requestJson = JsonConvert.SerializeObject(request, serializationSettings);
                string responseJson = JsonConvert.SerializeObject(response, serializationSettings);
                return Result<IEnumerable<Illustration>>.Failure($"InsureMo is down: REQUEST: {requestJson} ||| RESPONSE: {responseJson}");
            }

            IEnumerable<Illustration> illustrations = new List<string> { "totalPremiumPaid", "guaranteedCashValue", "guaranteedDeathBenefit", "levy" }
                .Select(i => new Illustration
                {
                    Id = i,
                    Points = response.Illustrations.IllustrationResultList.GroupBy(i => i.PolicyYear)
                        .Select(g =>
                            new Point
                            {
                                Y = CalculateFromIllustrationId(i, prepayment, g.ToList()),
                                X = g.Key,
                            })
                });

            return Result<IEnumerable<Illustration>>.Success(illustrations);
        }

        private (bool isCorrect, int coverageSerialId, string productCode, CurrencyCode currencyCode) GetEbaoProductCodes(ProductId productId)
        {
            if (productId == new ProductId { Plan = "brilliant_saver_3_protection_plan_5_years_usd", Type = "endowment" })
                return (true, 3000982, "I100205BU", CurrencyCode.USD);

            return (false, 0, null, CurrencyCode.Undefined);
        }

        private decimal CalculateFromIllustrationId(string illustrationId, bool prepayment, List<EBaoIllustrationResult> illsPerYear)
        {
            if (illustrationId == "totalPremiumPaid")
            {
                return prepayment
                    ? illsPerYear.First(i => i.CalcType == 14).Amount + illsPerYear.First(i => i.CalcType == 996).Amount
                    : illsPerYear.First(i => i.CalcType == 2).Amount;
            }

            if (illustrationId == "guaranteedCashValue")
            {
                return illsPerYear.First(i => i.CalcType == 21).Amount;
            }

            if (illustrationId == "guaranteedDeathBenefit")
            {
                return illsPerYear.First(i => i.CalcType == 288).Amount;
            }

            if (illustrationId == "levy")
            {
                return illsPerYear.First(i => i.CalcType == 76).Amount;
            }

            throw new NotSupportedException($"Issue with ebao illustration calculation.");
        }

        private class EBaoAuthRequest
        {
            public string Username { get; set; }
            public string Password { get; set; }
        }

        private class EBaoAuthResponse
        {
            public string Access_token { get; set; }
            public int Expire_in { get; set; }
        }

        private class EBaoIllustrationRequest
        {
            public EBaoCalculatePolicy CalculatePolicy { get; set; }
        }

        private class EBaoCalculatePolicy
        {
            public DateTime InceptionDate { get; set; }
            public List<EbaoCoverage> Coverages { get; set; }
            public string PrePaymentIndi { get; set; }
        }

        private class EbaoCoverage
        {
            public int CoverageSerialId { get; set; }
            public string ChargePeriod { get; set; }
            public int ChargeYear { get; set; }
            public string CoveragePeriod { get; set; }
            public int CoverageYear { get; set; }
            public string ProductCode { get; set; }
            public EBaoCalculatePremium CalculatePremium { get; set; }
            public List<EBaoInsured> Insureds { get; set; }
        }

        private class EBaoCalculatePremium
        {
            public int PaymentFreq { get; set; }
            public decimal SumAssured { get; set; }
            public string BenefitLevel { get; set; }
        }

        private class EBaoInsured
        {
            public string Gender { get; set; }
            public int Age { get; set; }
            public string Smoking { get; set; }
            public int OrderId { get; set; } = 1;
        }

        private class EBaoIllustrationResponse
        {
            public int Result { get; set; } // 1 for success
            public EBaoIllustrations Illustrations { get; set; }
        }

        private class EBaoIllustrations
        {
            public List<EBaoIllustrationResult> IllustrationResultList { get; set; }
        }

        private class EBaoIllustrationResult
        {
            public string Name { get; set; }
            public int PolicyYear { get; set; }
            public int CalcType { get; set; }
            public decimal Amount { get; set; }
        }
    }
}
