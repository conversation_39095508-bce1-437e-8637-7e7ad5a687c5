using CoverGo.Products.Domain.Sbus;
using MongoDB.Driver;
using System;
using CoverGo.Applications.Infrastructure;
using System.Linq.Expressions;

namespace CoverGo.Products.Infrastructure.Sbus.Adapters.Mongo
{
    public class MongoSbuRepository : CoverGoGenericMongoRepositoryBase<Sbu, SbuUpsert, SbuFilter>, ISbuRepository
    {
        protected override string DbName => "products";

        protected override string CollectionNameGet(string tenantId) => $"{tenantId}-sbus";

        protected override Sbu EntityCreate(SbuUpsert create) => new()
        {
            Id = create.Id ?? Guid.NewGuid().ToString(),
            Amount = create.Amount ?? default,
            StartDate = create.StartDate ?? default,
            EndDate = create.EndDate ?? default,
            CreatedAt = DateTime.UtcNow,
            CreatedById = create.ById
        };

        protected override UpdateDefinition<Sbu> EntityUpdate(SbuUpsert update)
        {
            UpdateDefinition<Sbu> result = UpdateField(null, x => x.LastModifiedAt, DateTime.UtcNow);

            if (update.ById != null) result = UpdateField(result, x => x.LastModifiedById, update.ById);
            if (update.Amount != null) result = UpdateField(result, x => x.Amount, update.Amount);
            if (update.StartDate != null) result = UpdateField(result, x => x.StartDate, update.StartDate);
            if (update.EndDate != null) result = UpdateField(result, x => x.EndDate, update.EndDate);

            return result;

            UpdateDefinition<Sbu> UpdateField<TField>(UpdateDefinition<Sbu> updateDefinition, Expression<Func<Sbu, TField>> field, TField value)
            {
                return updateDefinition == null ? Update.Set(field, value) : updateDefinition.Set(field, value);
            }
        }
    }
}