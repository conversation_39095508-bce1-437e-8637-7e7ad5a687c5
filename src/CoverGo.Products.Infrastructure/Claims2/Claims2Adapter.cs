﻿using CoverGo.HttpUtils;
using CoverGo.Products.Domain.Claims2;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Infrastructure.Claims2;

public class Claims2Adapter
{
    private readonly HttpClient _httpClient;

    public Claims2Adapter(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task<List<Network>> GetActiveNetworks(string searchInput, CancellationToken cancellationToken)
    {
        int page = 1;
        NetworksResult networksResult = await QueryActiveNetworks(searchInput, page, cancellationToken);
        List<Network> allNetworks = [.. networksResult.Values];

        if (networksResult.TotalPages > page)
        {
            for (int i = page + 1; i < networksResult.TotalPages; i++)
            {
                NetworksResult results = await QueryActiveNetworks(searchInput, i, cancellationToken);
                allNetworks.AddRange(results.Values);
            }
        }

        return allNetworks;
    }

    async Task<NetworksResult> QueryActiveNetworks(string searchInput, int page, CancellationToken cancellationToken)
    {
        var queryParams = new Dictionary<string, string>()
            {
                {"page",$"{page}"},
                {"size","100"},
                {"status","APPROVED"},
            };

        if (searchInput != null)
            queryParams.Add("search", searchInput);

        var builder = new UriBuilder(_httpClient.BaseAddress)
        {
            Path = "v1/networks",
            Query = string.Join("&", queryParams.Select(kvp => $"{kvp.Key}={kvp.Value}"))
        };

        return await (await _httpClient.GetAsync(builder.Uri, cancellationToken)).Content.ReadAsJsonAsync<NetworksResult>();
    }
}
