using MongoDB.Driver;
using CoverGo.Templates.Client;
using System.Threading.Tasks;
using System.Collections.Generic;
using CoverGo.Products.Domain.Products;
using System.Linq;
using CoverGo.Products.Domain.Templates;
using System.Net.Http;
using System;
using Newtonsoft.Json;
using CoverGo.DomainUtils;
using CoverGo.Users.GraphQl.Client;

namespace CoverGo.Products.Infrastructure.Templates
{
    public class TemplatesAdapter : ITemplatesAdapter
    {
        private readonly ITemplatesClient _templatesClient;

        public TemplatesAdapter(ITemplatesClient templatesClient)
        {
            _templatesClient = templatesClient;
        }

        public async Task<List<Template?>> GetTemplates(string tenantId, List<AttachedDocumentId> documentIds)
        {
            if (documentIds == null || documentIds.Count == 0) return [];
            var result = await _templatesClient.Template_GetAsync(tenantId, new QueryArgumentsOfTemplateWhere()
            {
                Where = new TemplateWhere
                {
                    Id_in = documentIds!.Select(d => d.TemplateId)!.ToList()
                }
            });
            return result;
        }
    }
}