﻿using CoverGo.DomainUtils;
using CoverGo.MongoUtils;
using CoverGo.Products.Domain.Insurers;

using MongoDB.Driver;

using System.Threading.Tasks;
using CoverGo.Configuration;

namespace CoverGo.Products.Infrastructure.Insurers.Adapters.Mongo
{
    public class MongoInsurerEventStore : IInsurerEventStore
    {
        private const string DbName = "products";

        public async Task<Result> AddEventAsync(string tenantId, InsurerEvent insurerEvent)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<InsurerEvent> clientCollection = db.GetCollection<InsurerEvent>($"{tenantId}-insurers_events");
            await clientCollection.Indexes.CreateOneAsync(new CreateIndexModel<InsurerEvent>(Builders<InsurerEvent>.IndexKeys.Ascending(f => f.InsurerId)));

            await clientCollection.InsertOneAsync(insurerEvent);

            return new Result { Status = "success" };
        }
    }
}