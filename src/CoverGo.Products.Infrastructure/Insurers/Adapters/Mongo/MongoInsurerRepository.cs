﻿using CoverGo.DomainUtils;
using CoverGo.MongoUtils;
using CoverGo.Products.Domain.Insurers;
using CoverGo.Products.Domain.Products;

using MongoDB.Driver;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Configuration;
using System.Threading;


namespace CoverGo.Products.Infrastructure.Insurers.Adapters.Mongo
{
    public class MongoInsurerRepository : IInsurerRepository
    {
        private const string DbName = "products";

        public async Task<IEnumerable<Insurer>> GetInsurersAsync(string tenantId, string clientId, InsurerFilter filter, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            ProductConfig config = await db.GetCollection<ProductConfig>($"{tenantId}").Find(Builders<ProductConfig>.Filter.Eq(c => c.Type, "configs") & Builders<ProductConfig>.Filter.Eq(c => c.ClientId, clientId)).SingleOrDefaultAsync(cancellationToken);

            IMongoCollection<MongoInsurerDao> tenantCollection = db.GetCollection<MongoInsurerDao>($"{tenantId}-insurers");
            IMongoCollection<MongoInsurerDao> defaultCollection = db.GetCollection<MongoInsurerDao>($"default-insurers");

            FilterDefinition<MongoInsurerDao> idFilter = FilterDefinition<MongoInsurerDao>.Empty;
            if (config?.DisplayedInsurers != null)
            {
                var allowedInsurerIds = new List<string>();
                foreach (string type in config.DisplayedInsurers.Keys)
                {
                    if (filter.ProductTypes?.Contains(type) ?? true)
                        allowedInsurerIds.AddRange(config.DisplayedInsurers?.GetValueOrDefault($"{type}") ?? Array.Empty<string>());
                }

                IEnumerable<string> ids = (filter.Ids ?? allowedInsurerIds).Intersect(allowedInsurerIds);
                idFilter = Builders<MongoInsurerDao>.Filter.In(x => x.InsurerId, ids);
            }

            Task<List<MongoInsurerDao>> defaultInsurersTask = defaultCollection.Find(idFilter).ToListAsync(cancellationToken);
            Task<List<MongoInsurerDao>> tenantInsurersTask = tenantCollection.Find(
                filter.ProductTypes != null
                ? idFilter
                : filter.Ids != null
                    ? Builders<MongoInsurerDao>.Filter.In(x => x.InsurerId, filter.Ids)
                    : FilterDefinition<MongoInsurerDao>.Empty
                ).ToListAsync(cancellationToken);

            await Task.WhenAll(defaultInsurersTask, tenantInsurersTask);

            return defaultInsurersTask.Result?.Select(i => i.ToDomain("default"))?.Concat(tenantInsurersTask.Result?.Select(i => i.ToDomain(tenantId)));
        }

        public async Task<Result> CreateInsurerAsync(string tenantId, CreateInsurerCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<MongoInsurerDao> clientCollection = db.GetCollection<MongoInsurerDao>($"{tenantId}-insurers");
            IMongoCollection<MongoInsurerDao> coverGoCollection = db.GetCollection<MongoInsurerDao>($"default-insurers");

            FilterDefinition<MongoInsurerDao> filter = Builders<MongoInsurerDao>.Filter.Eq(e => e.InsurerId, command.Id);
            Task<MongoInsurerDao> checkTenantCollectionTask = clientCollection.Find(filter).SingleOrDefaultAsync(cancellationToken);
            Task<MongoInsurerDao> checkCoverGoCollectionTask = coverGoCollection.Find(filter).SingleOrDefaultAsync(cancellationToken);
            await Task.WhenAll(checkTenantCollectionTask, checkCoverGoCollectionTask);
            if (checkTenantCollectionTask.Result != null || checkCoverGoCollectionTask.Result != null)
                return new Result { Status = "failure", Errors = new List<string> { $"An insurer with the Id {command.Id} already exists" } };

            await clientCollection.Indexes.CreateOneAsync(new CreateIndexModel<MongoInsurerDao>(Builders<MongoInsurerDao>.IndexKeys.Ascending(f => f.InsurerId)), cancellationToken: cancellationToken);

            await clientCollection.InsertOneAsync(
                 new MongoInsurerDao
                 {
                     InsurerId = command.Id,
                     CreatedAt = DateTime.UtcNow,
                     LastModifiedAt = DateTime.UtcNow,
                     CreatedById = command.CreatedById,
                     LastModifiedById = command.CreatedById
                 });

            return new Result { Status = "success" };
        }

        public async Task<Result> DeleteInsurerAsync(string tenantId, string id, string deletedById, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<MongoInsurerDao> clientCollection = db.GetCollection<MongoInsurerDao>($"{tenantId}-insurers");
            FilterDefinition<MongoInsurerDao> filter = Builders<MongoInsurerDao>.Filter.Eq(e => e.InsurerId, id);

            DeleteResult result = await clientCollection.DeleteOneAsync(filter, cancellationToken);

            return result.DeletedCount == 0
                ? new Result { Status = "failure", Errors = new List<string> { $"The custom insurer '{id}' doesn't exist." } }
                : new Result { Status = "success" };
        }

        public async Task<Result> MigrateInsurersAsync(string tenantId, MigrateInsurersCommand command, CancellationToken cancellationToken)
        {
            MongoClient newClient = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase newDb = newClient.GetDatabase(DbName);

            foreach (CreateInsurerCommand input in command.InsurerInputs)
            {
                var insurerDao = new MongoInsurerDao
                {
                    InsurerId = input.Id,
                    LogoUrls = input.LogoUrls
                };

                await newDb.GetCollection<MongoInsurerDao>("default-insurers").InsertOneAsync(insurerDao, cancellationToken: cancellationToken);
            }

            return new Result { Status = "success" };
        }
    }

  
    public class MongoInsurerDao : SystemObject
    {
        public string InsurerId { get; set; }
        public InsurerLogoUrls LogoUrls { get; set; }
    }

  
    public static class Mappers
    {
        public static Insurer ToDomain(this MongoInsurerDao insurer, string tenantId) =>
            new Insurer
            {
                Id = insurer.InsurerId,
                TenantId = tenantId,
                LogoUrls = insurer.LogoUrls,
                CreatedAt = insurer.CreatedAt,
                CreatedById = insurer.CreatedById,
                LastModifiedAt = insurer.LastModifiedAt,
                LastModifiedById = insurer.LastModifiedById
            };
    }
}
