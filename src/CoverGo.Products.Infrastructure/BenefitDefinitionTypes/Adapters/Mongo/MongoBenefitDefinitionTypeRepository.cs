﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Configuration;
using CoverGo.DomainUtils;
using CoverGo.MongoUtils;
using CoverGo.Products.Domain.BenefitDefinitions;
using CoverGo.Products.Domain.BenefitDefinitionTypes;
using MongoDB.Driver;
using Newtonsoft.Json.Linq;

namespace CoverGo.Products.Infrastructure.BenefitDefinitionTypes.Adapters.Mongo
{
    public class MongoBenefitDefinitionTypeRepository : IBenefitDefinitionTypeRepository
    {
        const string DbName = "products";

        public string ProviderId { get; } = "mongoDb";

        public async Task<IReadOnlyCollection<BenefitDefinitionType>> GetBenefitDefinitionTypeAsync(string tenantId, BenefitDefinitionTypeWhere where, OrderBy orderBy = null, int? skip = null, int? first = null, CancellationToken cancellationToken = default)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<BenefitDefinitionType> collection = db.GetCollection<BenefitDefinitionType>($"{tenantId}-benefitDefinitionTypes");

            FilterDefinition<BenefitDefinitionType> filter = CreateFilter(where);

            IFindFluent<BenefitDefinitionType, BenefitDefinitionType> searchBenefitDefinitionResult = collection.Find(filter);

            if (orderBy != null)
                searchBenefitDefinitionResult = orderBy.Type == OrderByType.ASC
                    ? searchBenefitDefinitionResult.Sort(Builders<BenefitDefinitionType>.Sort.Ascending(orderBy.FieldName))
                    : searchBenefitDefinitionResult.Sort(Builders<BenefitDefinitionType>.Sort.Descending(orderBy.FieldName));

            return await searchBenefitDefinitionResult.Skip(skip).Limit(first).ToListAsync(cancellationToken);
        }

        FilterDefinition<BenefitDefinitionType> CreateFilter(BenefitDefinitionTypeWhere where)
        {
            FilterDefinition<BenefitDefinitionType> filter = Builders<BenefitDefinitionType>.Filter.Empty;
            if (where?.Or?.Any() == true)
                return CreateOrFilter(filter, where);

            if (where?.And?.Any() == true)
                return CreateAndFilter(filter, where);

            return CreateFieldsFilter(filter, where);
        }

        private FilterDefinition<BenefitDefinitionType> CreateAndFilter(FilterDefinition<BenefitDefinitionType> filter, BenefitDefinitionTypeWhere where) =>
            where.And.Aggregate(filter, (current, andBenefitDefinitionWhere) => current & CreateFilter(andBenefitDefinitionWhere));

        private FilterDefinition<BenefitDefinitionType> CreateOrFilter(FilterDefinition<BenefitDefinitionType> filter, BenefitDefinitionTypeWhere where) =>
            where.Or.Aggregate(filter, (current, benefitDefinitionFilter) => current != FilterDefinition<BenefitDefinitionType>.Empty
                ? current | CreateFilter(benefitDefinitionFilter)
                : CreateFilter(benefitDefinitionFilter));

        private static FilterDefinition<BenefitDefinitionType> CreateFieldsFilter(FilterDefinition<BenefitDefinitionType> filter, BenefitDefinitionTypeWhere where)
        {
            if (where == null) return filter;
            if (where.Id_in != null) return Builders<BenefitDefinitionType>.Filter.In(x => x.Id, where.Id_in);
            if (where.BusinessId_in?.Any() == true) return Builders<BenefitDefinitionType>.Filter.In(x => x.BusinessId, where.BusinessId_in);
            if (where.Name_contains?.Any() == true) return Builders<BenefitDefinitionType>.Filter.Where(x => x.Name.ToLower().Contains(where.Name_contains.ToLower()));
            if (where.BusinessId_contains?.Any() == true) return Builders<BenefitDefinitionType>.Filter.Where(x => x.BusinessId.ToLower().Contains(where.BusinessId_contains.ToLower()));
            if (where.Status != null) return Builders<BenefitDefinitionType>.Filter.Eq(x => x.Status, where.Status);
            if (where.Fields != null &&
                where.Fields.Path.StartsWith($"{nameof(BenefitDefinitionType.Fields).ToLower()}."))
                return where.Fields.Build<BenefitDefinitionType>();
            return filter;
        }

        public async Task<IReadOnlyCollection<BenefitDefinition>> GetBenefitDefinitionAsync(string tenantId, BenefitDefinitionWhere filter, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<BenefitDefinition> collection = db.GetCollection<BenefitDefinition>($"{tenantId}-benefitDefinitions");

            FilterDefinition<BenefitDefinition> benefitTypeFilter = Builders<BenefitDefinition>.Filter.Empty;
            if (filter.BusinessId_in?.Any() == true) benefitTypeFilter &= Builders<BenefitDefinition>.Filter.In(x => x.BusinessId, filter.BusinessId_in);

            return await collection.Find(benefitTypeFilter).ToListAsync(cancellationToken);
        }

        public async Task<Result<CreatedStatus>> CreateBenefitDefinitionTypeAsync(string tenantId, string id, CreateBenefitDefinitionTypeCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<BenefitDefinitionType> collection = db.GetCollection<BenefitDefinitionType>($"{tenantId}-benefitDefinitionTypes");
            await collection.Indexes.CreateOneAsync(
                new CreateIndexModel<BenefitDefinitionType>(Builders<BenefitDefinitionType>.IndexKeys.Ascending("Id"))
            );

            var benefitDefinitionType = new BenefitDefinitionType
            {
                Id = id,
                Name = command.Name,
                BusinessId = command.BusinessId,
                Description = command.Description,
                Status = command.Status,
                LastModifiedById = command.CreatedById,
                LastModifiedAt = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow,
                Fields = command.Fields == null ? null : JToken.Parse(command.Fields)
            };

            await collection.InsertOneAsync(benefitDefinitionType, cancellationToken: cancellationToken);

            return new Result<CreatedStatus> { Status = "success", Value = new CreatedStatus { Id = benefitDefinitionType.Id } };
        }

        public async Task<Result> UpdateBenefitDefinitionTypeAsync(string tenantId, UpdateBenefitDefinitionTypeCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<BenefitDefinitionType> collection = db.GetCollection<BenefitDefinitionType>($"{tenantId}-benefitDefinitionTypes");

            FilterDefinition<BenefitDefinitionType> filter = Builders<BenefitDefinitionType>.Filter.Eq(p => p.Id, command.BenefitDefinitionTypeId);

            UpdateDefinitionBuilder<BenefitDefinitionType> update = Builders<BenefitDefinitionType>.Update;

            var updates = new List<UpdateDefinition<BenefitDefinitionType>>
            {
                update.Set(a => a.LastModifiedAt, DateTime.UtcNow),
                update.Set(a => a.LastModifiedById, command.ModifiedById)
            };
            if (command.Name != null) updates.Add(update.Set(a => a.Name, command.Name));
            if (command.BusinessId != null) updates.Add(update.Set(a => a.BusinessId, command.BusinessId));
            if (command.Description != null) updates.Add(update.Set(a => a.Description, command.Description));
            if (command.Status != null) updates.Add(update.Set(a => a.Status, command.Status));
            if (command.Fields != null)
            {
                var jTokenFields = JToken.Parse(command.Fields);
                updates.Add(update.Set(a => a.Fields, jTokenFields));
            }

            UpdateResult result = await collection.UpdateOneAsync(filter, update.Combine(updates), cancellationToken: cancellationToken);

            return new Result { Status = result.MatchedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> DeleteBenefitDefinitionTypeAsync(string tenantId, string benefitDefinitionTypeId, DeleteCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            DeleteResult result = await db.GetCollection<BenefitDefinitionType>($"{tenantId}-benefitDefinitionTypes")
                .DeleteOneAsync(Builders<BenefitDefinitionType>.Filter.Eq(p => p.Id, benefitDefinitionTypeId), cancellationToken);

            return result.DeletedCount > 0
                ? new Result { Status = "success" }
                : new Result { Status = "failure", Errors = new List<string> { "BenefitDefinitionType with such id doesn't exist" } };
        }

        public async Task<Result> BatchBenefitDefinitionTypeAsync(string tenantId, BatchBenefitDefinitionTypeCommand command,
            CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<BenefitDefinitionType> collection = db.GetCollection<BenefitDefinitionType>($"{tenantId}-benefitDefinitionTypes");
            
            await collection.Indexes.CreateOneAsync(
                new CreateIndexModel<BenefitDefinitionType>(Builders<BenefitDefinitionType>.IndexKeys.Ascending("Id"))
            );

            if (command.CreateBenefitDefinitionTypeCommands.Any())
            {
                var benefitDefinitions =  command.CreateBenefitDefinitionTypeCommands.Select(c => new BenefitDefinitionType()
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = c.Name,
                    BusinessId = c.BusinessId,
                    Description = c.Description,
                    Status = c.Status,
                    LastModifiedById = c.CreatedById,
                    LastModifiedAt = DateTime.UtcNow,
                    CreatedAt = DateTime.UtcNow,
                    Fields = c.Fields == null ? null : JToken.Parse(c.Fields)
                }).ToList();

                await collection.InsertManyAsync(benefitDefinitions, cancellationToken: cancellationToken);
            }
            
            var updateResults = new List<Result>();
            
            command.UpdateBenefitDefinitionTypeCommands.ForEach(async updateCommand =>
            {
                updateResults.Add(await UpdateBenefitDefinitionTypeAsync(tenantId, updateCommand, cancellationToken));
            });
                
            return new Result { Status = updateResults.Any(x => x.Status == "failure") ?  "failure" :"success" };
        }
    }
}
