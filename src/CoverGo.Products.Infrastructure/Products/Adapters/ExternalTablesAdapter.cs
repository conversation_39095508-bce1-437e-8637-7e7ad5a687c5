using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.FileSystem.Client;
using CoverGo.Products.Domain.Products;

namespace CoverGo.Products.Infrastructure.Products.Adapters
{
    public class ExternalTablesAdapter : IExternalTablesAdapter
    {
        const string PathPrefix = "externalTables/";
        const string Extension = ".txt";

        readonly IFileSystemClient _fileSystemClient;

        public ExternalTablesAdapter(IFileSystemClient fileSystemClient) => _fileSystemClient = fileSystemClient;

        public async Task<Result<string>> DownloadExternalTable(string tenantId, string externalTableName, CancellationToken cancellationToken)
        {
            GetFileCommand downloadFileCommand = new() { Key = GetRelativePath(externalTableName) };
            ResultOfByteOf result = await _fileSystemClient.FileSystem_GetAsync(tenantId, null, downloadFileCommand, cancellationToken);
            if (!result.IsSuccess) return Result<string>.Failure(result.Errors);
            string content = Encoding.UTF8.GetString(result.Value ?? Array.Empty<byte>());
            return Result<string>.Success(content);
        }

        public async Task<Result<IReadOnlyCollection<string>>> ListExternalTables(string tenantId, CancellationToken cancellationToken)
        {
            ListFilesCommand listFilesCommand = new() { Path = PathPrefix };
            ResultOfObjectListing result = await _fileSystemClient.FileSystem_ListAsync(tenantId, null, listFilesCommand, cancellationToken);
            if (!result.IsSuccess) return Result<IReadOnlyCollection<string>>.Failure(result.Errors);
            return Result<IReadOnlyCollection<string>>.Success(result.Value?.ObjectSummaries?.Select(s => GetExternalTableName(s.Key))
                .Where(t => !string.IsNullOrEmpty(t))
                .ToArray() ?? Array.Empty<string>());
        }

        public Task<Result> UploadExternalTable(string tenantId, string externalTableName, string content, CancellationToken cancellationToken)
        {
            UploadFileCommand uploadFileCommand = new()
            {
                Key = GetRelativePath(externalTableName),
                Content = Encoding.UTF8.GetBytes(content)
            };

            return _fileSystemClient.FileSystem_AddFileAsync(tenantId, null, uploadFileCommand, cancellationToken);
        }

        static string GetRelativePath(string externalTableName) => $"{PathPrefix}{externalTableName}{Extension}";

        static string GetExternalTableName(string relativePath)
        {
            if (string.IsNullOrEmpty(relativePath)) return null;
            int length = relativePath.Length - PathPrefix.Length - Extension.Length;
            return length > 0 ? relativePath.Substring(PathPrefix.Length, length) : null;
        }
    }
}
