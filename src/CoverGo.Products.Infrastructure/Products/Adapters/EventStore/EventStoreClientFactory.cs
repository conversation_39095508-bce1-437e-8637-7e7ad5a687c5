﻿using CoverGo.Products.Domain;
using CoverGo.Products.Domain.Products.Ports;

namespace CoverGo.Products.Infrastructure.Products.Adapters.EventStore
{
    public class EventStoreClientFactory : IEventStoreClientFactory
    {
        public IEventStoreClientAdapter GetEventStoreClient(EventStoreSettings eventStoreSettings)
        {
            return new EventStoreClientAdapter(eventStoreSettings);
        }
    }
}
