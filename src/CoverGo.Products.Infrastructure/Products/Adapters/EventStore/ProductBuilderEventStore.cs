﻿using CoverGo.DomainUtils;
using CoverGo.Products.Domain;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.Ports;
using EventStore.Client;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using JsonConvert = Newtonsoft.Json.JsonConvert;

namespace CoverGo.Products.Infrastructure.Products.Adapters.EventStore
{
    public class ProductBuilderEventStore : IProductBuilderEventStore
    {
        private readonly IEventStoreClientFactory _eventStoreClientFactory;
        private readonly EventStoreSettings _eventStoreSettings;
        private readonly ILogger<ProductBuilderEventStore> _logger;
        private readonly string _defaultProviderId = "pb";
        public ProductBuilderEventStore(IEventStoreClientFactory eventStoreClientFactory,EventStoreSettings eventStoreSettings, ILogger<ProductBuilderEventStore> logger)
        {
            _eventStoreClientFactory = eventStoreClientFactory;
            _eventStoreSettings = eventStoreSettings;
            _logger = logger;
            ProviderId = eventStoreSettings.ProviderId ?? _defaultProviderId;
        }

        public string ProviderId { get; }
        public async Task<List<string>> GetProductNodes(string tenantId, string productTreeId, CancellationToken cancellationToken)
        {
            List<string> nodes = new List<string>();

            var client = _eventStoreClientFactory.GetEventStoreClient(_eventStoreSettings);

            var events = await client.ReadStreamAsync(
                Direction.Forwards,
                $"{ProviderId}-{tenantId}-product-{productTreeId}",
                StreamPosition.Start,
                long.MaxValue,
                cancellationToken: cancellationToken
                );
            _logger.LogInformation("Getting Product Node Events");
            _logger.LogInformation("Finished Getting Product Node Events");
            foreach (var evt in events)
            {
                string metaData = Encoding.UTF8.GetString(evt.Event.Metadata.ToArray());
                string data = Encoding.UTF8.GetString(evt.Event.Data.ToArray());
                if (IsValidJson(data))
                {

                    EventMetaData eventMetaData1 = JsonConvert.DeserializeObject<EventMetaData>(metaData);
                    EventData eventData1 = JsonConvert.DeserializeObject<EventData>(data);

                    // This event happens when we clone a product to create another
                    // All the existing products nodes are created with this event 
                    // under the Children field in the data of event
                    if (evt.Event.EventType == "NodeCreated")
                    {
                        var children = JToken.Parse(data).SelectToken("Children")?.Values<string>();
                        if (children != null)
                        {
                            nodes.AddRange(children);
                        }
                    }

                    var tempChildStreamId = JToken.Parse(data).SelectToken("Child")?.Value<string>();

                    if (!string.IsNullOrEmpty(tempChildStreamId))
                    {
                        nodes.Add(tempChildStreamId);
                        continue;
                    }

                }
            }

            return nodes.Distinct().ToList();

            bool IsValidJson(string eventData)
            {
                try
                {
                    JObject.Parse(eventData);
                    return true;
                }
                catch (JsonReaderException)
                {
                    return false;
                }
            }

        }


        public async Task<IEnumerable<NodeEvent>> GetNodeEvents(
            string tenantId,
            string nodeId,
            ProductInfo product,
            CancellationToken cancellationToken)
        {
            var nodeEventLogs = new List<NodeEvent>();
            var client = _eventStoreClientFactory.GetEventStoreClient(_eventStoreSettings);

            var events = await client.ReadStreamAsync(
                Direction.Forwards,
                $"{ProviderId}-{nodeId}",
                StreamPosition.Start,
            long.MaxValue,
            cancellationToken: cancellationToken
            );

            string Ref = null;
            string Alias = null;

            foreach (var evt in events)
            {
                string metaData = Encoding.UTF8.GetString(evt.Event.Metadata.ToArray());
                string data = Encoding.UTF8.GetString(evt.Event.Data.ToArray());

                if (string.IsNullOrEmpty(metaData))
                    continue;

                EventMetaData eventMetaData = JsonConvert.DeserializeObject<EventMetaData>(metaData);
                EventData eventData = JsonConvert.DeserializeObject<EventData>(data);

                if (evt.Event.EventType == "NodeChildAdded")
                {
                    var childNodeId = JToken.Parse(data).SelectToken("Child")?.Value<string>();
                    nodeEventLogs.AddRange(await GetNodeEvents(tenantId, childNodeId, product, cancellationToken));
                }

                if (evt.Event.EventType == "NodeCreated")
                {
                    Ref = eventData.Ref;
                    Alias = eventData.Alias;

                    // This is to take care of nested childs in the product tree
                    // when we are cloning the product tree having nested child nodes
                    var childrenNodeIds = JToken.Parse(data).SelectToken("Children")?.Values<string>();

                    if (childrenNodeIds?.Any() ?? false)
                    {
                        foreach (var childNodeId in childrenNodeIds)
                            nodeEventLogs.AddRange(await GetNodeEvents(tenantId, childNodeId, product, cancellationToken));
                    }
                }

                nodeEventLogs.Add(
                     new NodeEvent()
                     {
                         Id = evt.Event.EventId.ToString(),
                         ProductId = product.Id,
                         NodeId = nodeId,
                         Ref = Ref,
                         Alias = Alias,
                         Timestamp = eventMetaData.Occurred,
                         Type = evt.Event.EventType,
                         Values = JToken.Parse(data),
                         UpdatedById = eventMetaData.userId,
                     }
                    );
            }

            return nodeEventLogs;
        }
    }
}