﻿using System.Collections.Generic;

namespace CoverGo.Products.Infrastructure.Products.Adapters.EventStore
{
    public class Field
    {
        public string Ref { get; set; }
        public string Type { get; set; }
        public Resolver Resolver { get; set; }
    }

    public class LockStatus
    {
        public bool IsLocked { get; set; }
    }

    public class Resolver
    {
        public string Text { get; set; }
        public string Language { get; set; }
    }

    public class EventData
    {
        public string Ref { get; set; }
        public string Alias { get; set; }
        public string Parent { get; set; }
        public List<Field> Fields { get; set; }
        public LockStatus LockStatus { get; set; }
    }
}
