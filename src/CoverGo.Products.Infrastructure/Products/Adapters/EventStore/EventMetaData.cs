﻿using Newtonsoft.Json;
using System;

namespace CoverGo.Products.Infrastructure.Products.Adapters.EventStore
{
    public class EventMetaData
    {
        [JsonProperty("$Occurred")]
        public DateTime Occurred { get; set; }

        [JsonProperty("$ConventionEventClrType")]
        public string ConventionEventClrType { get; set; }

        [JsonProperty("$createdByCommand")]
        public string createdByCommand { get; set; }

        [JsonProperty("$correlationId")]
        public string correlationId { get; set; }

        [JsonProperty("$causationId")]
        public string causationId { get; set; }

        [JsonProperty("$userId")]
        public string userId { get; set; }

        [JsonProperty("$DeserializationClrType")]
        public string DeserializationClrType { get; set; }
    }
}
