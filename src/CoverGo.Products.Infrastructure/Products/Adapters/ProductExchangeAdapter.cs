using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.FileSystem.Client;
using CoverGo.Products.Domain.Products;
using Ionic.Zip;
using Newtonsoft.Json;

namespace CoverGo.Products.Infrastructure.Products.Adapters
{
    public class ProductExchangeAdapter : IProductExchangeAdapter
    {
        const int UploadedFileTtlHours = 1;

        const string PathPrefix = "exchange/";
        const string Extension = ".cgx";
        const char Separator = '/';
        const string InvalidFilePathMessage = "Please upload the file to '/api/v1/files/exchange%2Ffilename.cgx' and pass 'exchange%2Ffilename.cgx'.";

        readonly IFileSystemClient _fileSystemClient;

        static ProductExchangeAdapter() => Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

        public ProductExchangeAdapter(IFileSystemClient fileSystemClient) => _fileSystemClient = fileSystemClient;

        public async Task<Result<string>> Upload(string tenantId, ProductPackage productPackage, CancellationToken cancellationToken)
        {
            string fileName = Guid.NewGuid().ToString();
            string relativePath = GetRelativePath(fileName);

            UploadFileCommand uploadFileCommand = new()
            {
                Key = relativePath,
                Content = Archive(productPackage),
                Ttl = TimeSpan.FromHours(UploadedFileTtlHours)
            };

            Result result = await _fileSystemClient.FileSystem_AddFileAsync(tenantId, null, uploadFileCommand, cancellationToken);
            if (!result.IsSuccess) return Result<string>.Failure(result.Errors);

            return Result<string>.Success(UrlEncode(relativePath));
        }

        public async Task<Result<ProductPackage>> Download(string tenantId, string filePath, CancellationToken cancellationToken, bool validateFileCheckSum = true)
        {
            filePath = UrlDecode(filePath);
            if (!Validate(filePath, out string validationMessage))
                return Result<ProductPackage>.Failure(validationMessage);

            GetFileCommand downloadFileCommand = new() { Key = filePath };
            ResultOfByteOf downloadFileResult = await _fileSystemClient.FileSystem_GetAsync(tenantId, null, downloadFileCommand, cancellationToken);
            if (!downloadFileResult.IsSuccess) return Result<ProductPackage>.Failure(downloadFileResult.Errors);

            try
            {
                ProductPackage productPackage = Extract(downloadFileResult.Value);
                if (validateFileCheckSum && (string.IsNullOrEmpty(productPackage.Metadata.RequestId) || productPackage.Metadata.RequestId != productPackage.GetChecksum()))
                    return Result<ProductPackage>.Failure("tampered_file");

                return Result<ProductPackage>.Success(productPackage);
            }
            catch (ZipException)
            {
                return Result<ProductPackage>.Failure("invalid_file");
            }
        }

        static string GetRelativePath(string fileName) => $"{PathPrefix}{fileName}{Extension}";

        static string UrlEncode(string str) => str.Replace("/", "%2F");

        static string UrlDecode(string str) => str.Replace("%2F", "/");

        static bool Validate(string relativePath, out string validationMessage)
        {
            if (!string.IsNullOrEmpty(relativePath) && relativePath.StartsWith(PathPrefix) && relativePath.EndsWith(Extension))
            {
                string fileName = GetFileName(relativePath);
                if (!string.IsNullOrEmpty(fileName) && !fileName.Contains(Separator))
                {
                    validationMessage = null;
                    return true;
                }
            }

            validationMessage = InvalidFilePathMessage;
            return false;
        }

        static string GetFileName(string relativePath) => relativePath.Substring(PathPrefix.Length, relativePath.Length - PathPrefix.Length - Extension.Length);

        static byte[] Archive(ProductPackage productPackage)
        {
            using MemoryStream output = new();

            using (ZipOutputStream zipOutput = new(output))
            {
                List<string> includedEntries = new() { EntryNames.ProductTreeEntryName };
                AddToArchive(zipOutput, EntryNames.ProductTreeEntryName, productPackage.ProductTreeContent);

                if (!string.IsNullOrEmpty(productPackage.DataSchemaContent))
                {
                    includedEntries.Add(EntryNames.DataSchemaEntryName);
                    AddToArchive(zipOutput, EntryNames.DataSchemaEntryName, productPackage.DataSchemaContent);
                }

                if (!string.IsNullOrEmpty(productPackage.UISchemaContent))
                {
                    includedEntries.Add(EntryNames.UISchemaEntryName);
                    AddToArchive(zipOutput, EntryNames.UISchemaEntryName, productPackage.UISchemaContent);
                }

                if (!string.IsNullOrEmpty(productPackage.ValidationScriptContent))
                {
                    includedEntries.Add(EntryNames.ValidationScriptEntryName);
                    AddToArchive(zipOutput, EntryNames.ValidationScriptEntryName, productPackage.ValidationScriptContent);
                }

                foreach (KeyValuePair<string, string> kvp in productPackage.ExternalTablesContent)
                {
                    string entryName = EntryNames.GetExternalTableEntryName(kvp.Key);
                    includedEntries.Add(entryName);
                    AddToArchive(zipOutput, entryName.Substring(EntryNames.ExternalTableEntryNamePrefix.Length), kvp.Value);
                }

                productPackage.Metadata.IncludedEntries = includedEntries.ToArray();
                productPackage.Metadata.RequestId = productPackage.GetChecksum();
                AddToArchive(zipOutput, EntryNames.MetadataEntryName, JsonConvert.SerializeObject(productPackage.Metadata));
            }

            return output.ToArray();
        }

        static void AddToArchive(ZipOutputStream stream, string fileName, string contents)
        {
            stream.PutNextEntry(fileName);
            byte[] buffer = Encoding.UTF8.GetBytes(contents);
            stream.Write(buffer, 0, buffer.Length);
        }

        static ProductPackage Extract(byte[] buffer)
        {
            using MemoryStream input = new(buffer);
            using ZipFile zipFile = ZipFile.Read(input);

            ProductPackage productPackage = new()
            {
                Metadata = JsonConvert.DeserializeObject<ProductMetadata>(Extract(zipFile, EntryNames.MetadataEntryName)),
                ProductTreeContent = Extract(zipFile, EntryNames.ProductTreeEntryName)
            };

            if (productPackage.Metadata.IncludedEntries.Contains(EntryNames.DataSchemaEntryName))
                productPackage.DataSchemaContent = Extract(zipFile, EntryNames.DataSchemaEntryName);

            if (productPackage.Metadata.IncludedEntries.Contains(EntryNames.UISchemaEntryName))
                productPackage.UISchemaContent = Extract(zipFile, EntryNames.UISchemaEntryName);

            if (productPackage.Metadata.IncludedEntries.Contains(EntryNames.ValidationScriptEntryName))
                productPackage.ValidationScriptContent = Extract(zipFile, EntryNames.ValidationScriptEntryName);

            productPackage.ExternalTablesContent = productPackage.Metadata.IncludedEntries
                .Where(e => e.StartsWith(EntryNames.ExternalTableEntryNamePrefix))
                .ToDictionary(
                    e => EntryNames.GetExternalTableName(e),
                    e => Extract(zipFile, e.Substring(EntryNames.ExternalTableEntryNamePrefix.Length))
                );

            return productPackage;
        }

        static string Extract(ZipFile zipFile, string fileName)
        {
            using MemoryStream output = new();
            zipFile[fileName].Extract(output);
            byte[] bytes = output.ToArray();
            return Encoding.UTF8.GetString(bytes);
        }
    }
}