using System;
using System.Net.Http;
using System.Net.Http.Json;
using System.Net.Mime;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Products;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace CoverGo.Products.Infrastructure.Products.Adapters
{
    public class ProductBuilderAdapter : IProductBuilderAdapter
    {
        readonly IHttpClientFactory _httpClientFactory;
        readonly ILogger<ProductBuilderAdapter> _logger;

        public ProductBuilderAdapter(IHttpClientFactory httpClientFactory, ILogger<ProductBuilderAdapter> logger)
        {
            _httpClientFactory = httpClientFactory;
            _logger = logger;
        }

        public async Task<Result<string>> CloneProductTree(string productTreeId, CancellationToken cancellationToken)
        {
            using HttpClient httpClient = _httpClientFactory.CreateClient(nameof(ProductBuilderAdapter));
            httpClient.Timeout = TimeSpan.FromMinutes(30);
            using HttpResponseMessage response = await httpClient.PostAsJsonAsync("graphql", new
            {
                query = @"mutation cloneTree($nodeId: ID!) {
                            cloneTree(nodeId: $nodeId)
                          }",
                variables = new
                {
                    nodeId = productTreeId
                }
            }, cancellationToken);

            string content = await response.Content.ReadAsStringAsync(cancellationToken);
            string clonedTreeId = ExtractFromJson<string>(content, "data.cloneTree");

            if (string.IsNullOrEmpty(clonedTreeId))
            {
                _logger.LogError("cloneTree query failed with {statusCode} status code: {content}", response.StatusCode, content);
                return Result<string>.Failure("Unable to clone the product tree");
            }

            return Result<string>.Success(clonedTreeId);
        }

        public async Task<Result> CloneProduct(ProductId productId,ProductName productName, ProductId cloneProductId, CancellationToken cancellationToken)
        {
            using HttpClient httpClient = _httpClientFactory.CreateClient(nameof(ProductBuilderAdapter));
            httpClient.Timeout = TimeSpan.FromMinutes(30);
            using HttpResponseMessage response = await httpClient.PostAsJsonAsync("graphql", new
            {
                query = @"mutation cloneProduct(
                            $productId: productIdInput!
                            $input: cloneProductInput!
                            $name: upsertL10nCloneProductNameInput
                          ) {
                            cloneProduct(productId: $productId, input: $input, name: $name) {
                             status
                             errors
                            }
                          }",
                variables = new
                {
                    productId = new
                    {
                        plan = productId.Plan,
                        type = productId.Type,
                        version = productId.Version,
                    },
                    name = new {
                        locale = productName.Locale,
                        value = productName.Value
                    },
                    input = new
                    {
                        cloneProductId = new
                        {
                            plan = cloneProductId.Plan,
                            type = cloneProductId.Type,
                            version = cloneProductId.Version
                        }
                    }
                }
            }, cancellationToken);
            string content = await response.Content.ReadAsStringAsync(cancellationToken);
            CloneProduct cloneProduct = ExtractFromJson<CloneProduct>(content, "data.cloneProduct");
            if (cloneProduct.Status != "success")
            {
                _logger.LogError("cloneProduct mutation failed with {statusCode} status code: {content}", response.StatusCode, content);
                return Result.Failure("Unable to clone the product");
            }
            return Result.Success();
        }

        public async Task<Result<CreateProductSchema>> CreateProductSchema(string nodeId,ProductSchema productSchema, CancellationToken cancellationToken)
        {
            using HttpClient httpClient = _httpClientFactory.CreateClient(nameof(ProductBuilderAdapter));
            httpClient.Timeout = TimeSpan.FromMinutes(30);
            using HttpResponseMessage response = await httpClient.PostAsJsonAsync("graphql", new
            {
                query = @"mutation createProductSchema($input: ProductSchemaInput!) {
                    createProductSchema(input: $input) {
                        status
                        value
                        errors
                    }
                }",
                variables = new
                {
                    input = new
                    {
                        nodeId = nodeId,
                        dataSchema = productSchema.DataSchema
                    }
                }
            }, cancellationToken);
            string content = await response.Content.ReadAsStringAsync();
            CreateProductSchema createProductSchema = ExtractFromJson<CreateProductSchema>(content, "data.createProductSchema");
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("createProductSchema mutation failed with {statusCode} status code: {content}", response.StatusCode, content);
                return Result<CreateProductSchema>.Failure("Unable to create the product schema");
            }

            if (response.IsSuccessStatusCode && createProductSchema == null)
            {
                _logger.LogError("createProductSchema mutation failed with {statusCode} status code: {content}", response.StatusCode, content);
                return Result<CreateProductSchema>.Failure("createProductSchema Failed");
            }

            if (string.IsNullOrEmpty(createProductSchema?.Status) || createProductSchema.Status != "success")
            {
                _logger.LogError("createProductSchema mutation failed with {statusCode} status code: {content}", response.StatusCode, content);
                return Result<CreateProductSchema>.Failure("Unable to create the product schema");
            }

            return Result<CreateProductSchema>.Success(createProductSchema);
        }

        public async Task<Result> AddUiToProductSchema(string productSchemaId, UISchema uiSchema, CancellationToken cancellationToken)
        {
            using HttpClient httpClient = _httpClientFactory.CreateClient(nameof(ProductBuilderAdapter));
            httpClient.Timeout = TimeSpan.FromMinutes(30);
            using HttpResponseMessage response = await httpClient.PostAsJsonAsync("graphql", new
            {
                query = @"mutation addUiSchemaToProductSchema($productSchemaId: ID!, $input: UiSchemaInput!) {
                    addUiSchemaToProductSchema(productSchemaId: $productSchemaId, input: $input) {
                        status
                        errors
                    }
                }",
                variables = new
                {
                    productSchemaId = productSchemaId,
                    input = new
                    {
                        name = uiSchema.Name,
                        schema = uiSchema.Schema
                    }
                }
            }, cancellationToken);
            string content = await response.Content.ReadAsStringAsync(cancellationToken);
            string status = ExtractStringFromJson(content, "data.addUiSchemaToProductSchema.status");
            if (status != "success")
            {
                _logger.LogError("addUiSchemaToProductSchema mutation failed with {statusCode} status code: {content}", response.StatusCode, content);
                return Result.Failure("Unable to add UI schema to the product schema");
            }

            return Result.Success();
        }

        public async Task<Result> UpdateProductTreeId(ProductId productId, string productTreeId, CancellationToken cancellationToken)
        {
            using HttpClient httpClient = _httpClientFactory.CreateClient(nameof(ProductBuilderAdapter));
            httpClient.Timeout = TimeSpan.FromMinutes(30);
            using HttpResponseMessage response = await httpClient.PostAsJsonAsync("graphql", new
            {
                query = @"mutation updateProduct($productId: productIdInput!, $input: updateProductInput) {
                    updateProduct(productId: $productId, input: $input) {
                        underwritingRulesJson
                        pricing {
                            amountLogic
                        }
                    }
                }",
                variables = new
                {
                    productId = productId,
                    input = new
                    {
                        productTreeId = productTreeId
                    }
                }
            }, cancellationToken);
            string content = await response.Content.ReadAsStringAsync(cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("updateProduct mutation for productTreeId failed with {statusCode} status code: {content}", response.StatusCode, content);
                return Result.Failure("Unable to update the product");
            }

            return Result.Success();
        }

        public async Task<Result> UpdateProductLifeCycleStage(ProductId productId, string lifeCycleStage, CancellationToken cancellationToken)
        {
            using HttpClient httpClient = _httpClientFactory.CreateClient(nameof(ProductBuilderAdapter));
            httpClient.Timeout = TimeSpan.FromMinutes(30);
            using HttpResponseMessage response = await httpClient.PostAsJsonAsync("graphql", new
            {
                query = @"mutation updateProduct($productId: productIdInput!, $input: updateProductInput) {
                    updateProduct(productId: $productId, input: $input) {
                        underwritingRulesJson
                        pricing {
                            amountLogic
                        }
                    }
                }",
                variables = new
                {
                    productId = productId,
                    input = new
                    {
                        lifecycleStage = lifeCycleStage
                    }
                }
            }, cancellationToken);
            string content = await response.Content.ReadAsStringAsync(cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("updateProduct mutation failed for lifeCycleStage with {statusCode} status code: {content}", response.StatusCode, content);
                return Result.Failure("Unable to update the product");
            }

            return Result.Success();
        }

        public async Task<Result<ExportedTree>> ExportProductTree(string productTreeId, CancellationToken cancellationToken)
        {
            using HttpClient httpClient = _httpClientFactory.CreateClient(nameof(ProductBuilderAdapter));
            httpClient.Timeout = TimeSpan.FromMinutes(30);
            using HttpResponseMessage response = await httpClient.PostAsJsonAsync("graphql", new
            {
                query = @"query exportProductTree($productTreeId: ID!) {
                    exportTree(nodeId: $productTreeId) {
                        raw
                        lockStatus {
                            isLocked
                            lockedById
                        }
                        referencedExternalTables
                    }
			    }",
                variables = new
                {
                    productTreeId = productTreeId
                }
            }, cancellationToken);

            string content = await response.Content.ReadAsStringAsync(cancellationToken);
            ExportedTree exportedTree = ExtractFromJson<ExportedTree>(content, "data.exportTree");

            if (response.IsSuccessStatusCode && exportedTree == null)
            {
                return Result<ExportedTree>.Failure("Product tree does not exist");
            }

            if (string.IsNullOrEmpty(exportedTree?.Raw))
            {
                _logger.LogError("exportTree query failed with {statusCode} status code: {content}", response.StatusCode, content);
                return Result<ExportedTree>.Failure("Unable to export the product tree");
            }

            return Result<ExportedTree>.Success(exportedTree);
        }

        public async Task<Result<string>> ImportProductTree(string productTreeContent, CancellationToken cancellationToken)
        {
            using HttpClient httpClient = _httpClientFactory.CreateClient(nameof(ProductBuilderAdapter));
            httpClient.Timeout = TimeSpan.FromMinutes(30);
            using HttpResponseMessage response = await httpClient.PostAsync("graphql", new StringContent($@"{{
                ""query"": ""mutation importProductTree($productTreeContent: CreateNodeInput!) {{ createNode(node: $productTreeContent) }}"",
                ""variables"": {{
                    ""productTreeContent"": {productTreeContent}
                }}
            }}", Encoding.UTF8, MediaTypeNames.Application.Json), cancellationToken);

            string content = await response.Content.ReadAsStringAsync(cancellationToken);
            string productTreeId = ExtractStringFromJson(content, "data.createNode");

            if (string.IsNullOrEmpty(productTreeId))
            {
                _logger.LogError("createNode mutation failed with {statusCode} status code: {content}", response.StatusCode, content);
                return Result<string>.Failure("Unable to import the product tree");
            }

            return Result<string>.Success(productTreeId);
        }

        public async Task<Result<LockStatus>> LockProductTree(string productTreeId, int expiresInSeconds, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Locking product tree {productTreeId} for {expiresInSeconds} seconds", productTreeId, expiresInSeconds);
            using HttpClient httpClient = _httpClientFactory.CreateClient(nameof(ProductBuilderAdapter));
            httpClient.Timeout = TimeSpan.FromMinutes(30);
            using HttpResponseMessage response = await httpClient.PostAsJsonAsync("graphql", new
            {
                query = @"mutation lockProductTree($productTreeId: ID!, $expiresInSeconds: Int!) {
                    lockNode(nodeId: $productTreeId expiresInSeconds: $expiresInSeconds) {
                        isLocked
                    }
			    }",
                variables = new
                {
                    productTreeId = productTreeId,
                    expiresInSeconds = expiresInSeconds
                }
            }, cancellationToken);

            string content = await response.Content.ReadAsStringAsync(cancellationToken);
            _logger.LogInformation("Locking product tree {productTreeId} response : {content}", productTreeId, content);
            LockStatus lockStatus = ExtractFromJson<LockStatus>(content, "data.lockNode");
            _logger.LogInformation("Locking status for product tree {productTreeId} is : {status}", productTreeId, lockStatus?.IsLocked);
            if (lockStatus?.IsLocked != true)
            {
                string lockedById = ExtractLockedById(content);
                if (!string.IsNullOrEmpty(lockedById)) return Result<LockStatus>.Success(new LockStatus { IsLocked = true, LockedById = lockedById });

                _logger.LogError("lockNode mutation failed with {statusCode} status code: {content}", response.StatusCode, content);
                return Result<LockStatus>.Failure("Unable to lock the product tree");
            }

            return Result<LockStatus>.Success(new LockStatus { IsLocked = true });

            string ExtractLockedById(string content)
            {
                const string ErrorMessagePrefix = "Node is already locked by user ";
                string errorMessage = ExtractStringFromJson(content, "errors[0].message");
                int index = errorMessage?.IndexOf(ErrorMessagePrefix) ?? -1;
                if (index == -1) return null;
                return errorMessage.Substring(index + ErrorMessagePrefix.Length).TrimEnd();
            }
        }

        public async Task<Result> UnlockProductTree(string productTreeId, CancellationToken cancellationToken)
        {
            using HttpClient httpClient = _httpClientFactory.CreateClient(nameof(ProductBuilderAdapter));
            httpClient.Timeout = TimeSpan.FromMinutes(30);
            using HttpResponseMessage response = await httpClient.PostAsJsonAsync("graphql", new
            {
                query = @"mutation unlockProductTree($productTreeId: ID!) {
                    unlockNode(nodeId: $productTreeId) {
                        isLocked
                    }
			    }",
                variables = new
                {
                    productTreeId = productTreeId
                }
            }, cancellationToken);

            string content = await response.Content.ReadAsStringAsync(cancellationToken);
            LockStatus lockStatus = ExtractFromJson<LockStatus>(content, "data.unlockNode");

            if (lockStatus?.IsLocked != false)
            {
                _logger.LogError("unlockNode mutation failed with {statusCode} status code: {content}", response.StatusCode, content);
                return Result.Failure("Unable to unlock the product tree");
            }

            return Result.Success();
        }

        public async Task<Result<ProductSchema>> GetProductSchema(string productTreeId, CancellationToken cancellationToken)
        {
            using HttpClient httpClient = _httpClientFactory.CreateClient(nameof(ProductBuilderAdapter));
            httpClient.Timeout = TimeSpan.FromMinutes(30);
            using HttpResponseMessage response = await httpClient.PostAsJsonAsync("graphql", new
            {
                query = @"query getProductSchema($productTreeId: ID!) {
                    productSchema(nodeId: $productTreeId) {
                        dataSchema
                        uiSchemas {
                            name
                            schema
                        }
                    }
                }",
                variables = new
                {
                    productTreeId = productTreeId
                }
            }, cancellationToken);

            string content = await response.Content.ReadAsStringAsync(cancellationToken);
            ProductSchema productSchema = ExtractFromJson<ProductSchema>(content, "data.productSchema");

            if (productSchema == null)
            {
                _logger.LogWarning("productSchema query responded with {statusCode} status code: {content}", response.StatusCode, content);
            }

            return Result<ProductSchema>.Success(productSchema);
        }

        public async Task<Result> ImportProductSchema(string productTreeId, ProductSchema productSchema, CancellationToken cancellationToken)
        {
            Result<string> importProductDataSchemaResult = await ImportProductDataSchema(productTreeId, productSchema.DataSchema, cancellationToken);
            if (!importProductDataSchemaResult.IsSuccess) return Result.Failure(importProductDataSchemaResult.Errors);
            string productSchemaId = importProductDataSchemaResult.Value;

            if (productSchema.UISchemas == null) return Result.Success();

            foreach (UISchema uiSchema in productSchema.UISchemas)
            {
                Result importProductUISchemaResult = await ImportProductUISchema(productSchemaId, uiSchema, cancellationToken);
                if (!importProductUISchemaResult.IsSuccess) return Result.Failure(importProductUISchemaResult.Errors);
            }

            return Result.Success();
        }

        async Task<Result<string>> ImportProductDataSchema(string productTreeId, string dataSchema, CancellationToken cancellationToken)
        {
            using HttpClient httpClient = _httpClientFactory.CreateClient(nameof(ProductBuilderAdapter));
            httpClient.Timeout = TimeSpan.FromMinutes(30);
            using HttpResponseMessage response = await httpClient.PostAsJsonAsync("graphql", new
            {
                query = @"mutation importProductDataSchema($productTreeId: ID!, $dataSchema: String!) {
                    createProductSchema(input: {
                        nodeId: $productTreeId,
                        dataSchema: $dataSchema
                    }) {
                        value
                        status
                        errors
                    }
                }",
                variables = new
                {
                    productTreeId = productTreeId,
                    dataSchema = dataSchema
                }
            }, cancellationToken);

            string content = await response.Content.ReadAsStringAsync(cancellationToken);
            string productSchemaId = ExtractStringFromJson(content, "data.createProductSchema.value");

            if (string.IsNullOrEmpty(productSchemaId))
            {
                _logger.LogError("createProductSchema mutation failed with {statusCode} status code: {content}", response.StatusCode, content);
                return Result<string>.Failure("Unable to create the product schema");
            }

            return Result<string>.Success(productSchemaId);
        }

        async Task<Result> ImportProductUISchema(string productSchemaId, UISchema uiSchema, CancellationToken cancellationToken)
        {
            using HttpClient httpClient = _httpClientFactory.CreateClient(nameof(ProductBuilderAdapter));
            httpClient.Timeout = TimeSpan.FromMinutes(30);
            using HttpResponseMessage response = await httpClient.PostAsJsonAsync("graphql", new
            {
                query = @"mutation importProductUISchema($productSchemaId: ID!, $name: String!, $schema: String!) {
                    addUiSchemaToProductSchema(
                        productSchemaId: $productSchemaId
                        input: {
                            name: $name,
                            schema: $schema
                        }
                    ) {
                        status
                        errors
                    }
                }",
                variables = new
                {
                    productSchemaId = productSchemaId,
                    name = uiSchema.Name,
                    schema = uiSchema.Schema
                }
            }, cancellationToken);

            string content = await response.Content.ReadAsStringAsync(cancellationToken);
            string status = ExtractStringFromJson(content, "data.addUiSchemaToProductSchema.status");

            if (status != "success")
            {
                _logger.LogError("addUiSchemaToProductSchema mutation failed with {statusCode} status code: {content}", response.StatusCode, content);
                return Result.Failure("Unable to add UI schema to the product schema");
            }

            return Result.Success();
        }

        public async Task<Result<string>> GetValidationScript(string productTreeId, CancellationToken cancellationToken)
        {
            using HttpClient httpClient = _httpClientFactory.CreateClient(nameof(ProductBuilderAdapter));
            httpClient.Timeout = TimeSpan.FromMinutes(30);
            using HttpResponseMessage response = await httpClient.PostAsJsonAsync("graphql", new
            {
                query = @"query getValidationScript($productTreeId: ID!) {
                    nodeScript(nodeId: $productTreeId) {
                        text
                    }
                }",
                variables = new
                {
                    productTreeId = productTreeId
                }
            }, cancellationToken);

            string content = await response.Content.ReadAsStringAsync(cancellationToken);
            string validationScriptContent = ExtractStringFromJson(content, "data.nodeScript.text");

            if (validationScriptContent == null)
            {
                _logger.LogWarning("nodeScript query responded with {statusCode} status code: {content}", response.StatusCode, content);
            }

            return Result<string>.Success(validationScriptContent);
        }

        public async Task<Result> ImportValidationScript(string productTreeId, string validationScriptContent, CancellationToken cancellationToken)
        {
            using HttpClient httpClient = _httpClientFactory.CreateClient(nameof(ProductBuilderAdapter));
            httpClient.Timeout = TimeSpan.FromMinutes(30);
            using HttpResponseMessage response = await httpClient.PostAsJsonAsync("graphql", new
            {
                query = @"mutation importValidationScript($productTreeId: ID!, $validationScriptContent: String!) {
                    attachNodeScript(
                        nodeId: $productTreeId
                        script: {
                            text: $validationScriptContent
                        }
                    ) {
                        status
                        errors
                    }
                }",
                variables = new
                {
                    productTreeId = productTreeId,
                    validationScriptContent = validationScriptContent
                }
            }, cancellationToken);

            string content = await response.Content.ReadAsStringAsync(cancellationToken);
            string status = ExtractStringFromJson(content, "data.attachNodeScript.status");

            if (status != "success")
            {
                _logger.LogError("attachNodeScript mutation failed with {statusCode} status code: {content}", response.StatusCode, content);
                return Result.Failure("Unable to attach the node script to the node");
            }

            return Result.Success();
        }

        static string ExtractStringFromJson(string content, string path)
        {
            JToken data = content == null ? null : JsonConvert.DeserializeObject<JToken>(content);
            return (string)data?.SelectToken(path, errorWhenNoMatch: false);
        }

        static T ExtractFromJson<T>(string content, string path) where T : class
        {
            JToken data = content == null ? null : JsonConvert.DeserializeObject<JToken>(content);
            return data?.SelectToken(path, errorWhenNoMatch: false)?.ToObject<T>();
        }
    }
}