using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.L10n.Client;
using CoverGo.Products.Domain.Products;

namespace CoverGo.Products.Infrastructure.Products.Adapters
{
    public class ProductL10NAdapter : IProductL10NAdapter
    {
        readonly IL10nClient _l10nClient;

        public ProductL10NAdapter(IL10nClient l10nClient) => _l10nClient = l10nClient;

        public async Task<Result<string>> GetProductName(string tenantId, string clientId, Domain.Products.ProductId productId, CancellationToken cancellationToken)
        {
            Response<IDictionary<string, string>> response = await _l10nClient.L10nAsync(tenantId, "en-US", clientId, new[]
            {
                $"products-{productId.Plan}|{productId.Version}|{productId.Type}-name",
                $"products-{productId.Plan}-name"
            }, cancellationToken);

            string productName = response?.Result?.Values?.FirstOrDefault();

            return Result<string>.Success(productName);
        }
        public async Task<DomainUtils.Result> UpsertAsync(string tenantId, string locale, string key, string value, CancellationToken cancellationToken)
        {
            try
            {
                Response response = await _l10nClient.L10n2Async(tenantId, new UpsertL10nCommand() { Locale = locale, Key = key, Value = value }, cancellationToken);

                return response.StatusCode != 200 ? DomainUtils.Result.Failure("Unable to update the Product name") : DomainUtils.Result.Success();
            }
            catch(Exception ex)
            {
                return DomainUtils.Result.Failure("Unable to update the Product name");
            }
        }
    }
}