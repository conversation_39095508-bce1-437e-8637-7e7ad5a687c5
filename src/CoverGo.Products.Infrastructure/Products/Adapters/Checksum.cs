using System;
using System.Security.Cryptography;
using System.Text;

namespace CoverGo.Products.Infrastructure.Products.Adapters
{
    public class Checksum
    {
        private readonly StringBuilder _buffer = new();

        public string ToChecksum()
        {
            if (_buffer.Length == 0) return string.Empty;

            return Compute(_buffer.ToString());
        }

        public static string Compute(string s)
        {
            if (string.IsNullOrEmpty(s)) return string.Empty;

            using HashAlgorithm hashAlgorithm = MD5.Create();
            byte[] data = hashAlgorithm.ComputeHash(Encoding.UTF8.GetBytes(s));
            return BytesToString(data);
        }

        public void Add(string value) => _buffer.AppendLine(value);

        public void Add(params string[] values)
        {
            foreach (string value in values)
                _buffer.AppendLine(value);
        }

        public void Add(DateTime? value) => _buffer.Append(value).AppendLine();

        private static string BytesToString(byte[] data)
        {
            StringBuilder result = new();

            for (int i = 0; i < data.Length; i++)
                result.Append(data[i].ToString("x2"));

            return result.ToString();
        }
    }
}
