﻿using CoverGo.Products.Domain;
using CoverGo.Products.Domain.Products.Ports;
using EventStore.Client;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Infrastructure.Products.Adapters
{
    public class EventStoreClientAdapter : IEventStoreClientAdapter
    {
        private readonly EventStoreSettings _eventStoreSettings;

        public EventStoreClientAdapter(EventStoreSettings eventStoreSettings)
        {
            _eventStoreSettings = eventStoreSettings;
        }
        public async Task<List<ResolvedEvent>> ReadStreamAsync(
            Direction direction, 
            string streamName, 
            StreamPosition revision, 
            long maxCount = long.MaxValue, 
            bool resolveLinkTos = false, 
            TimeSpan? deadline = null, 
            UserCredentials userCredentials = null, 
            CancellationToken cancellationToken = default)
        {

            var eventStoreClientSettings = EventStoreClientSettings.Create(_eventStoreSettings.ConnectionString);
            using var client = new EventStoreClient(eventStoreClientSettings);

            var eventStream =  client.ReadStreamAsync(
                direction,
                streamName,
                revision,
                maxCount,
                resolveLinkTos,
                deadline,
                userCredentials,
                cancellationToken
            );

            return await eventStream.ToListAsync(cancellationToken);
        }
    }
}
