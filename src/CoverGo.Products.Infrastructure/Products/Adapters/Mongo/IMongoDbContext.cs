using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Products.Domain.Products;
using MongoDB.Driver;

namespace CoverGo.Products.Infrastructure.Products.Adapters.Mongo;

public interface IMongoDbContext
{
    IMongoCollection<TDao> GetCollection<TDao>(string tenantId, string dbName, string productType);
    Task<IAggregateFluent<MongoProductDao2>?> GetProductsCollectionsAggregation(
        string tenantId, ProductWhere where, ProductConfig config, CancellationToken cancellationToken);
    Task<TDao> FindAsync<TDao>(IMongoCollection<TDao> collection, FilterDefinition<TDao> filter, CancellationToken cancellationToken);
    IMongoDatabase GetDatabase(string tenantId, string dbName);
    Task<List<TDao>> FindListAsync<TDao>(IMongoDatabase db, string n, FilterDefinition<TDao> defaultFilter, CancellationToken ct);
    Task<List<TDao>> FindListAsync<TDao>(IMongoDatabase db, string n, FilterDefinition<TDao> mongoFilter, ProjectionDefinition<TDao> projectionDefinition, CancellationToken ct);
}