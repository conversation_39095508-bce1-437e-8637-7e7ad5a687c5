using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Configuration;
using CoverGo.MongoUtils;
using CoverGo.Products.Domain.Products;
using MongoDB.Driver;


namespace CoverGo.Products.Infrastructure.Products.Adapters.Mongo
{
    public class MongoDbReferenceGenerator : IReferenceGenerator
    {
        public string ProviderId { get; } = "mongoDb";
        private const string dbName = "products";

        private readonly MongoProductEventStore _eventStore;

        public MongoDbReferenceGenerator(MongoProductEventStore eventStore)
        {
            _eventStore = eventStore;
        }

        public async Task<string> GenerateAsync(IProductRepository repo, string tenantId, string type,
            CreateProductCommand command = null, CancellationToken cancellationToken = default)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(dbName);

            IMongoCollection<ReferenceGeneratorConfig> clientCollection =
                db.GetCollection<ReferenceGeneratorConfig>($"{tenantId}-refgen");
            FilterDefinition<ReferenceGeneratorConfig> filter =
                Builders<ReferenceGeneratorConfig>.Filter.Eq(p => p.Type, type);
            ReferenceGeneratorConfig config =
                await clientCollection.Find(filter).FirstOrDefaultAsync(cancellationToken);


            if (config == null)
            {
                config = new ReferenceGeneratorConfig
                {
                    Type = type,
                    Format = "{0}",
                    Arguments = new List<FormatArgument>
                    {
                        new FormatArgument
                        {
                            Type = "incrementor", Format = "D8", CurrentIncrement = 0, Order = 0
                        }
                    }
                };
                await clientCollection.InsertOneAsync(config, cancellationToken: cancellationToken);
            }

            string[] formattedArguments = new string[config.Arguments.Max(a => a.Order) + 1];
            foreach (FormatArgument argument in config.Arguments)
            {
                string formattedArgument = null;

                DateTime currentDate = DateTime.Now;

                if (argument.Type == "currentDate")
                {
                    formattedArgument = currentDate.ToString(argument.Format);
                }

                else if (argument.Type == "createdProductsOfCurrentDay")
                {
                    ProductConfig productConfig = await repo.GetProductConfigAsync(tenantId, null, cancellationToken);

                    // Get the number of products created within the day
                    string productIssuerId;
                    int count;

                    count = await GetProductsOfTodayCount(repo, productConfig, tenantId, cancellationToken);

                    formattedArgument = count.ToString(argument.Format);
                }
                else if (argument.Type == "incrementor") //this being used for SUNLIFE for policy number
                {
                    int newIncrement = ++argument.CurrentIncrement;

                    FilterDefinition<ReferenceGeneratorConfig> argFilter =
                        Builders<ReferenceGeneratorConfig>.Filter.And(filter,
                            Builders<ReferenceGeneratorConfig>.Filter.ElemMatch(x => x.Arguments,
                                f => f.Order == argument.Order));

                    UpdateDefinition<ReferenceGeneratorConfig> update =
                        Builders<ReferenceGeneratorConfig>.Update.Set(config => config.Arguments[-1].CurrentIncrement,
                            newIncrement);
                    await clientCollection.UpdateOneAsync(argFilter, update, cancellationToken: cancellationToken);

                    formattedArgument = newIncrement.ToString(argument.Format);
                }

                formattedArguments[argument.Order] = formattedArgument;
            }

            string formattedNumber = string.Format(config.Format, formattedArguments);
            return formattedNumber;
        }

        async Task<int> GetProductsOfTodayCount(IProductRepository repo, ProductConfig productConfig, string tenantId, CancellationToken cancellationToken)
        {
            DateTime currentDate = DateTime.Now;
            DateTime startOfTodayInUtc = currentDate.Date.ToUniversalTime();

            ProductWhere where = new ProductWhere { CreatedAt_gt = startOfTodayInUtc };
            IEnumerable<Product> productsOfToday = await repo.GetAsync(tenantId, where, productConfig, new QueryParameters(), cancellationToken: cancellationToken);

            int count = productsOfToday.Count() + 1;

            return count;
        }
    }
}
