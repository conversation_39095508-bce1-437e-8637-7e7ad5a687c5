using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Configuration;
using CoverGo.MongoUtils;
using CoverGo.Products.Domain.Products;
using MongoDB.Driver;

namespace CoverGo.Products.Infrastructure.Products.Adapters.Mongo;

[ExcludeFromCodeCoverage]
class MongoDbContext : IMongoDbContext
{
    public Task<TDao> FindAsync<TDao>(IMongoCollection<TDao> collection, FilterDefinition<TDao> filter, CancellationToken cancellationToken)
    => collection.Find(filter).FirstOrDefaultAsync(cancellationToken);

    public Task<List<TDao>> FindListAsync<TDao>(IMongoDatabase db, string n, FilterDefinition<TDao> defaultFilter, CancellationToken ct)
    => db.GetCollection<TDao>(n).Find(defaultFilter).ToListAsync(ct);

    public Task<List<TDao>> FindListAsync<TDao>(IMongoDatabase db, string n, FilterDefinition<TDao> mongoFilter, ProjectionDefinition<TDao> projectionDefinition, CancellationToken ct)
    => db.GetCollection<TDao>(n).Find(mongoFilter).Project<TDao>(projectionDefinition).ToListAsync(ct);

    public IMongoCollection<TDao> GetCollection<TDao>(string tenantId, string dbName, string productType)
    {
        var client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
        var db = client.GetDatabase(dbName);
        var collection = db.GetCollection<TDao>($"{tenantId}-{productType}");

        return collection;
    }

    public IMongoDatabase GetDatabase(string tenantId, string dbName)
    {
        MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
        IMongoDatabase db = client.GetDatabase(dbName);
        return db;
    }

    public Task<IAggregateFluent<MongoProductDao2>> GetProductsCollectionsAggregation(string tenantId, ProductWhere where, ProductConfig config, CancellationToken cancellationToken)
        => MongoProductRepository.GetProductsCollectionsAggregation(tenantId, where, config, cancellationToken);
}