using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Facts;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.ProductTreeParsing;
using MongoDB.Bson;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CoverGo.Products.Infrastructure.Products.Adapters.Mongo
{
    public class MongoProductDao2 : SystemObject
    {
        public string Id { get; set; }
        public ProductId ProductId { get; set; }

        public string InsurerId { get; set; }
        public string IssuerProductId { get; set; }
        public string ClaimFactTemplateId { get; set; }

        public List<Benefit> Benefits { get; set; }
        [Obsolete]
        public BsonDocument UnderwritingRules { get; set; }
        public ProductSettings RejectionSettings { get; set; }
        public LoadingSettings LoadingSettings { get; set; }
        public ProductSettings ExclusionSettings { get; set; }
        public ClaimSettings ClaimSettings { get; set; }

        public Domain.Products.Underwriting Underwriting { get; set; }
        public IEnumerable<Tag> Tags { get; set; }
        public List<Fact> Facts { get; set; }

        public DateTime? LaunchPeriodStartDate { get; set; }
        public DateTime? LaunchPeriodEndDate { get; set; }
        public DateTime? ChangeEffectiveDate { get; set; }
        public string Status { get; set; }

        public List<InternalReview>? InternalReviews { get; set; }

        public string TenantId { get; set; } // Not used in db

        public string LifecycleStage { get; set; }

        public string Representation { get; set; }

        /// <summary>
        /// Is used for Data Extraction.
        /// Currently, No FE logic related to this
        /// </summary>
        public IReadOnlyCollection<Plan> Plans { get; set; }

        public JToken Fields { get; set; }

        public IReadOnlyCollection<string> ScriptIds { get; set; }

        public IReadOnlyCollection<string> TemplateRelationshipIds { get; set; }
        public string ProductTreeId { get; set; }
        public string TermsAndConditionsTemplateId { get; set; }
        public string TermsAndConditionsJacketId { get; set; }
        public string RatingFactorsTable { get; set; }
        public string Segments { get; set; }
        public PolicyIssuanceMethod? PolicyIssuanceMethod { get; set; }
        public TimeSpan? OfferValidityPeriod { get; set; }
        public bool? AllowCustomProduct { get; set; }
        public ProductUpdateTypes UpdateTypes { get; set; }
        public List<AttachedDocumentId>? AttachedDocumentsIds { get; set; } = [];
        public bool AutoRenewal { get; set; }
        public bool RenewalNotification { get; set; }
        public TaxConfiguration? TaxConfiguration { get; set; }
    }

    public static class ProductDaoExtensions
    {
        public static Product ToDomain(this MongoProductDao2 dao) =>
            new Product
            {
                Id = dao.ProductId,

                TenantId = dao.TenantId,
                InsurerId = dao.InsurerId,
                IssuerProductId = dao.IssuerProductId,

                ClaimFactTemplateId = dao.ClaimFactTemplateId,

                Benefits = dao.Benefits,
                RejectionSettings = dao.RejectionSettings,
                LoadingSettings = dao.LoadingSettings,
                ExclusionSettings = dao.ExclusionSettings,
                ClaimSettings = dao.ClaimSettings,
                Underwriting = dao.Underwriting ?? new Domain.Products.Underwriting { SourceType = "jsonLogic" },
                Tags = dao.Tags,
                Facts = dao.Facts,
                LaunchPeriodStartDate = dao.LaunchPeriodStartDate,
                LaunchPeriodEndDate = dao.LaunchPeriodEndDate,
                ChangeEffectiveDate = dao.ChangeEffectiveDate,
                Status = dao.Status,
                InternalReviews = dao.InternalReviews,

                Representation = dao.Representation,
                Plans = dao.Plans?.Count > 0 ? dao.Plans : GetExtractedPlans(dao.ProductId, dao.Representation),
                Fields = dao.Fields?.ToString(),

                LifecycleStage = dao.LifecycleStage,
                ScriptIds = dao.ScriptIds,
                TemplateRelationshipIds = dao.TemplateRelationshipIds,
                ProductTreeId = dao.ProductTreeId,
                TermsAndConditionsTemplateId = dao.TermsAndConditionsTemplateId,
                TermsAndConditionsJacketId = dao.TermsAndConditionsJacketId,
                RatingFactorsTable = dao.RatingFactorsTable,
                Segments = dao.Segments,

                CreatedById = dao.CreatedById,
                CreatedAt = dao.CreatedAt,
                LastModifiedById = dao.LastModifiedById,
                LastModifiedAt = dao.LastModifiedAt,
                PolicyIssuanceMethod = dao.PolicyIssuanceMethod,
                OfferValidityPeriod = dao.OfferValidityPeriod,
                AllowCustomProduct = dao.AllowCustomProduct,
                UpdateTypes = dao.UpdateTypes,
                AttachedDocumentsIds = dao.AttachedDocumentsIds,
                AutoRenewal = dao.AutoRenewal,
                RenewalNotification = dao.RenewalNotification,
                TaxConfiguration = dao.TaxConfiguration ?? new TaxConfiguration(),
            };

        public static List<Plan> GetExtractedPlans(ProductId productId, string representation)
        {
            try
            {
                if (string.IsNullOrEmpty(representation)) return new List<Plan>();
                var plans = PlanDetailBuilder.GetProductPlanDetails(
                    productId,
                    representation,
                    // [] since we don't care about limits.
                    [],
                    [],
                    [],
                    []);

                if (plans is null) return new List<Plan>();

                var updatedPlans = plans.PlanDetails.Select(p => new Plan
                {
                    Id = p.Id,
                    Name = p.Name,
                    BenefitCodes = p.BenefitTypes?
                        .Select(bt => bt.Code)
                        .Distinct()
                        .ToList() ?? Enumerable.Empty<string>().ToList()
                }).ToList();
                return updatedPlans;
            }
            catch
            {
                return new List<Plan>();
            }
        }
    }
}
