﻿using CoverGo.DomainUtils;
using CoverGo.MongoUtils;
using CoverGo.Products.Domain.Products;

using MongoDB.Driver;

using System.Collections.Generic;
using System.Threading.Tasks;
using CoverGo.Configuration;
using System.Threading;


namespace CoverGo.Products.Infrastructure.Products.Adapters.Mongo
{
    public class MongoProductEventStore : IProductEventStore
    {
        private const string DbName = "products";

        public string ProviderId { get; } = "mongoDb";

        public async Task<Result> AddEventAsync(string tenantId, ProductEvent productEvent, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<ProductEvent> clientCollection = db.GetCollection<ProductEvent>($"{tenantId}-{productEvent.ProductId.Type}_events");
            await clientCollection.Indexes.CreateOneAsync(new CreateIndexModel<ProductEvent>(Builders<ProductEvent>.IndexKeys.Ascending(f => f.ProductId)), cancellationToken: cancellationToken);

            await clientCollection.InsertOneAsync(productEvent, cancellationToken: cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<IEnumerable<ProductEvent>> GetEventsAsync(
            string tenantId,
            string productType,
            ProductEventWhere where,
            OrderBy? orderBy = null,
            int? skip = null,
            int? first = null,
            CancellationToken cancellationToken = default)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<ProductEvent> clientCollection = db.GetCollection<ProductEvent>($"{tenantId}-{productType}_events");

            FilterDefinition<ProductEvent> filter = where.GetFilterDefinition();

            IFindFluent<ProductEvent, ProductEvent> find = clientCollection.Find(filter);

            // orderby
            if (orderBy != null)
                find = orderBy.Type == OrderByType.ASC
                    ? find.Sort(Builders<ProductEvent>.Sort.Ascending(orderBy.FieldName))
                    : find.Sort(Builders<ProductEvent>.Sort.Descending(orderBy.FieldName));
            else find = find.Sort(Builders<ProductEvent>.Sort.Descending(e => e.Timestamp));

            if (skip != null)
                find = find.Skip(skip.Value);
            if (first != null)
                find = find.Limit(first.Value);

            return await find.ToListAsync(cancellationToken);
        }

        public async Task<Result> AddTypeEventAsync(string tenantId, ProductTypeEvent productTypeEvent, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<ProductTypeEvent> clientCollection = db.GetCollection<ProductTypeEvent>($"{tenantId}-types_events");
            await clientCollection.Indexes.CreateOneAsync(new CreateIndexModel<ProductTypeEvent>(Builders<ProductTypeEvent>.IndexKeys.Ascending(f => f.TypeId)), cancellationToken: cancellationToken);

            await clientCollection.InsertOneAsync(productTypeEvent, cancellationToken: cancellationToken);

            return new Result { Status = "success" };
        }
    }

    public static class ProductEventFilterDefinitionExtensions
    {
        public static FilterDefinition<ProductEvent> GetFilterDefinition(this ProductEventWhere where)
        {
            FilterDefinition<ProductEvent> mongoFilter = FilterDefinition<ProductEvent>.Empty;

            if (where.Or != null)
                foreach (ProductEventWhere orFilter in where.Or)
                    mongoFilter = mongoFilter != FilterDefinition<ProductEvent>.Empty
                        ? mongoFilter | GetFilterDefinition(orFilter)
                        : GetFilterDefinition(orFilter);

            else if (where.And != null)
                foreach (ProductEventWhere andFilter in where.And)
                    mongoFilter &= GetFilterDefinition(andFilter);

            else
                mongoFilter = mongoFilter.AddToFilterDefinition(where);

            return mongoFilter;
        }

        public static FilterDefinition<ProductEvent> AddToFilterDefinition(this FilterDefinition<ProductEvent> mongoFilter, ProductEventWhere where)
        {
            if (where.ProductIds_in != null)
                mongoFilter &= Builders<ProductEvent>.Filter.In(e => e.ProductId, where.ProductIds_in);
            if (where.Types_in != null)
                mongoFilter &= Builders<ProductEvent>.Filter.In(e => e.Type, where.Types_in);
            if (where.FromDate != null)
                mongoFilter &= Builders<ProductEvent>.Filter.Gte(e => e.Timestamp, where.FromDate);
            if (where.ToDate != null)
                mongoFilter &= Builders<ProductEvent>.Filter.Lte(e => e.Timestamp, where.ToDate);

            return mongoFilter;
        }
    }
}
