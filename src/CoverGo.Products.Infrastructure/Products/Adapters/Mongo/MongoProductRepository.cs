#nullable enable

using CoverGo.Configuration;
using CoverGo.DomainUtils;
using CoverGo.FeatureManagement;
using CoverGo.JsonUtils;
using CoverGo.MongoUtils;
using CoverGo.Products.Domain.Facts;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.Commands;
using CoverGo.Products.Domain.Products.ProductTreeParsing;
using CoverGo.Products.Infrastructure.Utils.Adapters.Mongo;
using CoverGo.Threading.Tasks;
using Microsoft.AspNetCore.JsonPatch;
using MongoDB.Bson;
using MongoDB.Driver;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Infrastructure.Products.Adapters.Mongo
{
    public class MongoProductRepository(
        IMultiTenantFeatureManager featureManager,
        IMongoDbContext mongoDbContext
    ) : IProductRepository
    {
        private const string DbName = "products";
        private const string DefaultTenantPrefix = "default";
        private const string OriginalVersion = "1.0";

        public string ProviderId { get; } = "mongoDb";

        public async Task<Result> MigrateProductsAsync(string tenantId, MigrateProductsCommand command, CancellationToken cancellationToken)
        {
            MongoClient newClient = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase newDb = newClient.GetDatabase(DbName);

            foreach (CreateProductCommand input in command.ProductInputs)
            {
                var productDao = new MongoProductDao2
                {
                    InsurerId = input.InsurerId,
                    IssuerProductId = input.IssuerProductId,
                    Benefits = input.BenefitInputs?.Select(b => new Benefit
                    {
                        TypeId = b.TypeId,
                        ParentTypeId = b.ParentTypeId,
                        CurrencyCode = b.CurrencyCode,
                        Value = b.Value,
                        Condition = b.Condition
                    })?.ToList(),
                    ExclusionSettings = input.ExclusionSettings,
                    LoadingSettings = input.LoadingSettings,
                    RejectionSettings = input.RejectionSettings,
                    ClaimSettings = input.ClaimSettings,
                    Underwriting = input.Underwriting,
                    OfferValidityPeriod = input.OfferValidityPeriod,
                    PolicyIssuanceMethod = input.PolicyIssuanceMethod,
                };

                await newDb.GetCollection<MongoProductDao2>($"default-{input.ProductId.Type}").InsertOneAsync(productDao, cancellationToken);
            }

            return new Result { Status = "success" };
        }

        public async Task<Result> CleanProductTest(string tenantId, string typeId, CancellationToken cancellationToken)
        {
            bool isValid = Guid.TryParse(typeId, out _);
            if (!isValid)
                return new Result { Status = "failure", Errors = new List<string> { $"The typeId `{typeId}` is not a valid guid." } }; //the generated ids for types are guids

            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<BsonDocument> collectionToDrop = db.GetCollection<BsonDocument>($"{tenantId}-{typeId}");
            if (collectionToDrop == null)
                return new Result { Status = "failure", Errors = new List<string> { $"The collection `{tenantId}-{typeId}` was not found." } };

            await db.DropCollectionAsync($"{tenantId}-{typeId}", cancellationToken);
            IMongoCollection<BsonDocument> eventsCollectionToDrop = db.GetCollection<BsonDocument>($"{tenantId}-{typeId}_events");
            if (eventsCollectionToDrop == null)
                return new Result { Status = "failure", Errors = new List<string> { $"The collection `{tenantId}-{typeId}_events` was not found." } };
            await db.DropCollectionAsync($"{tenantId}-{typeId}_events", cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<ProductConfig> GetProductConfigAsync(string tenantId, string clientId, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            ProductConfig config = await db.GetCollection<ProductConfig>($"{tenantId}").Find(Builders<ProductConfig>.Filter.Eq(c => c.Type, "configs") & Builders<ProductConfig>.Filter.Eq(c => c.ClientId, clientId)).SingleOrDefaultAsync(cancellationToken);

            return config;
        }

        public async Task<IEnumerable<ProductConfig>> GetProductConfigsAsync(string tenantId, ProductConfigWhere where, OrderBy orderBy, int? skip = null, int? first = null, CancellationToken cancellationToken = default)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            FilterDefinition<ProductConfig> mongoFilter = FilterDefinition<ProductConfig>.Empty;
            if (where != null)
                mongoFilter = where.GetFilterDefinition();

            IMongoCollection<ProductConfig> collection = db.GetCollection<ProductConfig>($"{tenantId}");
            IFindFluent<ProductConfig, ProductConfig> find = collection.Find(Builders<ProductConfig>.Filter.Eq(c => c.Type, "configs") & mongoFilter);

            IEnumerable<ProductConfig> configs = await find.Skip(skip).Limit(first).ToListAsync(cancellationToken);

            return configs;
        }

        private static async Task<IEnumerable<Product>> GetAsyncNotOptimized(string tenantId, ProductWhere where, ProductConfig config, bool loadRepresentation = false, CancellationToken cancellationToken = default)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            string typeRegex = string.Join("|", GetTypesFromFilter(where));
            IEnumerable<string> filteredTenants = GetCollectionTenants(where);
            IEnumerable<string> tenantCollectionNames = Enumerable.Empty<string>();
            IEnumerable<string> covergoCollectionNames = Enumerable.Empty<string>();

            if (!filteredTenants.Any() || filteredTenants.Any(t => t == $"{tenantId}"))
            {
                tenantCollectionNames = await (await db.ListCollectionNamesAsync(new ListCollectionNamesOptions
                {
                    Filter = Builders<BsonDocument>.Filter.Regex("name", $"/^{tenantId}-({typeRegex})((?!_events|types|insurers|scripts|benefitDefinitionTypes|benefitDefinitions|jackets|jacket_events|refgen|discountCodes|sbus).)*$/")
                })).ToListAsync(cancellationToken);
            }

            if (!filteredTenants.Any() || filteredTenants.Any(t => t == "default"))
            {
                covergoCollectionNames = await (await db.ListCollectionNamesAsync(new ListCollectionNamesOptions
                {
                    Filter = Builders<BsonDocument>.Filter.Regex("name", $"/^default-({typeRegex})((?!_events|types|insurers|scripts|benefitDefinitionTypes|benefitDefinitions|jackets|jacket_events|refgen|discountCodes|sbus).)*$/")
                })).ToListAsync(cancellationToken);
            }

            FilterDefinition<MongoProductDao2> mongoFilter = FilterDefinition<MongoProductDao2>.Empty;
            if (where != null)
                mongoFilter = MongoSearchExtensions.AddProductSearchFilters2(where);

            Task<List<MongoProductDao2>[]> getDefaultProductsTask = null;
            if (config?.DisplayedProducts != null)
            {
                IEnumerable<ProductId> defaultProductIds = config.DisplayedProducts?.GetValueOrDefault("default");
                IEnumerable<ProductId> tenantProductIds = config.DisplayedProducts?.GetValueOrDefault($"{tenantId}");

                if (defaultProductIds != null)
                {
                    FilterDefinition<MongoProductDao2> defaultFilter = mongoFilter & Builders<MongoProductDao2>.Filter.In(x => x.ProductId, defaultProductIds);
                    getDefaultProductsTask = covergoCollectionNames.ParallelSelectAsync((n, ct) => db.GetCollection<MongoProductDao2>(n).Find(defaultFilter).ToListAsync(ct),
                        new ParallelRunOptions() { MaxDegreeOfParallelism = 5, CancellationToken = cancellationToken });
                }
                if (tenantProductIds != null)
                    mongoFilter &= Builders<MongoProductDao2>.Filter.In(x => x.ProductId, tenantProductIds);
            }

            Task<List<MongoProductDao2>[]> tenantProductsTask = loadRepresentation ?
                tenantCollectionNames.ParallelSelectAsync((n, ct) => db.GetCollection<MongoProductDao2>(n).Find(mongoFilter).ToListAsync(ct),
                    new ParallelRunOptions() { MaxDegreeOfParallelism = 5, CancellationToken = cancellationToken }) :
                tenantCollectionNames.ParallelSelectAsync((n, ct) => db.GetCollection<MongoProductDao2>(n).Find(mongoFilter).Project<MongoProductDao2>(Builders<MongoProductDao2>.Projection.Exclude(x => x.Representation)).ToListAsync(ct),
                    new ParallelRunOptions() { MaxDegreeOfParallelism = 5, CancellationToken = cancellationToken });

            List<MongoProductDao2>[] tenantProductsLists = await tenantProductsTask;
            List<MongoProductDao2>[] defaultProductsLists = getDefaultProductsTask != null ? await getDefaultProductsTask : Array.Empty<List<MongoProductDao2>>();

            IEnumerable<MongoProductDao2> tenantProductDaos = tenantProductsLists.SelectMany(c => c).ToList();
            IEnumerable<MongoProductDao2> defaultProductDaos = defaultProductsLists.SelectMany(c => c).ToList();

            foreach (MongoProductDao2 productDao in tenantProductDaos)
                productDao.TenantId = tenantId;
            foreach (MongoProductDao2 productDao in defaultProductDaos)
                productDao.TenantId = "default";

            IEnumerable<MongoProductDao2> allProductDaos = Enumerable.Concat(tenantProductDaos, defaultProductDaos);

            return allProductDaos.Select(p => p.ToDomain());
        }

        public async Task<IEnumerable<Product>> GetAsync(string tenantId, ProductWhere where, ProductConfig config, QueryParameters? queryParameters,
            bool loadRepresentation = false, CancellationToken cancellationToken = default)
        {
            bool optimizedEnabled = await featureManager.IsEnabled("OptimizedProductsQueryEnabled");

            if (!optimizedEnabled)
            {
                return await GetAsyncNotOptimized(tenantId, where, config, loadRepresentation, cancellationToken);
            }

            IAggregateFluent<MongoProductDao2>? collectionAggregation = await mongoDbContext.GetProductsCollectionsAggregation(tenantId, where, config, cancellationToken);
            if (collectionAggregation == null) return Enumerable.Empty<Product>();

            collectionAggregation = AddSorting(collectionAggregation, queryParameters?.OrderBy);
            collectionAggregation = AddSkip(collectionAggregation, queryParameters?.Skip);
            collectionAggregation = AddLimit(collectionAggregation, queryParameters?.Limit);

            Task<List<MongoProductDao2>> getProductsTask = loadRepresentation
                ? collectionAggregation.ToListAsync(cancellationToken)
                : collectionAggregation.Project<MongoProductDao2>(Builders<MongoProductDao2>.Projection.Exclude(x => x.Representation)).ToListAsync(cancellationToken);

            List<MongoProductDao2> products = await getProductsTask;

            SetTenantId(products, tenantId);

            return products.Select(p => p.ToDomain());
        }

        public async Task<long> GetTotalCountAsync(string tenantId, ProductWhere where, ProductConfig config, CancellationToken cancellationToken = default)
        {
            IAggregateFluent<MongoProductDao2>? collectionAggregation = await GetProductsCollectionsAggregation(tenantId, where, config, cancellationToken);
            if (collectionAggregation == null) return 0;

            AggregateCountResult productsCountResult = await collectionAggregation.Count().FirstOrDefaultAsync(cancellationToken);
            return productsCountResult?.Count ?? 0;
        }

        public static async Task<IAggregateFluent<MongoProductDao2>?> GetProductsCollectionsAggregation(string tenantId, ProductWhere where, ProductConfig config, CancellationToken cancellationToken)
        {
            IMongoDatabase db = GetProductsDatabase(tenantId);

            string typeRegex = GetTypeRegex(where);
            var allowedTenants = GetCollectionTenants(where).ToList();
            FilterDefinition<MongoProductDao2> whereFilter = GetProductWhereFilter(where);

            IReadOnlyCollection<string> tenantProductCollectionNames = await GetProductCollectionNames(db, tenantId, allowedTenants, typeRegex, cancellationToken);

            var tenantProductsFilterStage = GetTenantProductsFilterStage(tenantId, config, whereFilter);
            var tenantProductsPipeline = PipelineDefinition<MongoProductDao2, MongoProductDao2>.Create(new[] { tenantProductsFilterStage });

            IAggregateFluent<MongoProductDao2>? collectionAggregation = null;

            if (tenantProductCollectionNames.Any())
            {
                collectionAggregation = db.GetCollection<MongoProductDao2>(tenantProductCollectionNames.First()).Aggregate(new AggregateOptions { AllowDiskUse = true });
                collectionAggregation = collectionAggregation.AppendStage(tenantProductsFilterStage);
                collectionAggregation = AggregateCollections(db, collectionAggregation, tenantProductCollectionNames.Skip(1), tenantProductsPipeline);
            }

            IReadOnlyCollection<string> defaultProductCollectionNames = await GetProductCollectionNames(db, DefaultTenantPrefix, allowedTenants, typeRegex, cancellationToken);
            FilterDefinition<MongoProductDao2> defaultDisplayedProductsFilter = GetFilterWithDisplayedProducts(DefaultTenantPrefix, config);

            if (defaultDisplayedProductsFilter != FilterDefinition<MongoProductDao2>.Empty && defaultProductCollectionNames.Any())
            {
                FilterDefinition<MongoProductDao2> defaultProductsFilter = whereFilter & defaultDisplayedProductsFilter;
                PipelineStageDefinition<MongoProductDao2, MongoProductDao2> defaultProductsFilterStage = PipelineStageDefinitionBuilder.Match(defaultProductsFilter);
                var defaultProductsPipeline = PipelineDefinition<MongoProductDao2, MongoProductDao2>.Create(new[] { defaultProductsFilterStage });

                if (collectionAggregation == null)
                {
                    collectionAggregation = db.GetCollection<MongoProductDao2>(defaultProductCollectionNames.First()).Aggregate(new AggregateOptions { AllowDiskUse = true });
                    collectionAggregation = collectionAggregation.AppendStage(defaultProductsFilterStage);
                    collectionAggregation = AggregateCollections(db, collectionAggregation, defaultProductCollectionNames.Skip(1), defaultProductsPipeline);
                }
                else
                {
                    collectionAggregation = AggregateCollections(db, collectionAggregation, defaultProductCollectionNames, defaultProductsPipeline);
                }
            }

            return collectionAggregation;
        }

        private static IAggregateFluent<MongoProductDao2> AggregateCollections(IMongoDatabase db, IAggregateFluent<MongoProductDao2> collectionAggregation,
            IEnumerable<string> collectionNames, PipelineDefinition<MongoProductDao2, MongoProductDao2> pipeline)
        {
            foreach (string collectionName in collectionNames)
            {
                collectionAggregation = collectionAggregation.UnionWith(db.GetCollection<MongoProductDao2>(collectionName), pipeline);
            }

            return collectionAggregation;
        }

        private static FilterDefinition<MongoProductDao2> GetProductWhereFilter(ProductWhere? where) => where != null
            ? MongoSearchExtensions.AddProductSearchFilters2(where)
            : FilterDefinition<MongoProductDao2>.Empty;

        private static IAggregateFluent<MongoProductDao2> AddSorting(IAggregateFluent<MongoProductDao2> collectionAggregation, OrderBy? orderBy)
        {
            if (orderBy == null) return collectionAggregation;

            SortDefinition<MongoProductDao2> sort = orderBy.Type == OrderByType.ASC
                ? Builders<MongoProductDao2>.Sort.Ascending(orderBy.FieldName)
                : Builders<MongoProductDao2>.Sort.Descending(orderBy.FieldName);

            return collectionAggregation.Sort(sort);
        }

        private static IAggregateFluent<MongoProductDao2> AddSkip(
            IAggregateFluent<MongoProductDao2> collectionAggregation, int? skip) =>
            skip is > 0 ? collectionAggregation.Skip(skip.Value) : collectionAggregation;

        private static IAggregateFluent<MongoProductDao2> AddLimit(
            IAggregateFluent<MongoProductDao2> collectionAggregation, int? limit) =>
            limit is > 0 ? collectionAggregation.Limit(limit.Value) : collectionAggregation;

        private static void SetTenantId(IEnumerable<MongoProductDao2> products, string tenantId)
        {
            foreach (MongoProductDao2 productDao in products)
            {
                productDao.TenantId = tenantId;
            }
        }

        private static async Task<IReadOnlyCollection<string>> GetProductCollectionNames(IMongoDatabase db, string tenantId,
            IReadOnlyCollection<string> allowedTenants, string typeRegex, CancellationToken cancellationToken)
        {
            if (allowedTenants.Any() && allowedTenants.All(t => t != $"{tenantId}")) return Array.Empty<string>();

            IAsyncCursor<string> cursor = await db.ListCollectionNamesAsync(
                new ListCollectionNamesOptions
                {
                    Filter = Builders<BsonDocument>.Filter.Regex("name",
                        $"/^{tenantId}-({typeRegex})((?!_events|types|insurers|scripts|benefitDefinitionTypes|benefitDefinitions|jackets|jacket_events|refgen|discountCodes|sbus).)*$/")
                }, cancellationToken);

            List<string> tenantCollectionNames = await cursor.ToListAsync(cancellationToken);
            return tenantCollectionNames;
        }

        private static FilterDefinition<MongoProductDao2> GetFilterWithDisplayedProducts(string tenantId, ProductConfig? config)
        {
            IEnumerable<ProductId>? tenantProductIds = config?.DisplayedProducts?.GetValueOrDefault($"{tenantId}");
            return tenantProductIds != null ? Builders<MongoProductDao2>.Filter.In(x => x.ProductId, tenantProductIds) : FilterDefinition<MongoProductDao2>.Empty;
        }

        private static PipelineStageDefinition<MongoProductDao2, MongoProductDao2> GetTenantProductsFilterStage(string tenantId, ProductConfig config, FilterDefinition<MongoProductDao2> whereFilter)
        {
            FilterDefinition<MongoProductDao2> tenantDisplayedProductsFilter = GetFilterWithDisplayedProducts(tenantId, config);
            FilterDefinition<MongoProductDao2> tenantProductsFilter = whereFilter & tenantDisplayedProductsFilter;
            PipelineStageDefinition<MongoProductDao2, MongoProductDao2> tenantProductsFilterStage = PipelineStageDefinitionBuilder.Match(tenantProductsFilter);
            return tenantProductsFilterStage;
        }

        public async Task<Result> CloneAsync(string tenantId, CloneProductCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> originalProductTypeCollection
                = db.GetCollection<MongoProductDao2>($"{tenantId}-{command.ProductId.Type}");
            IMongoCollection<MongoProductDao2> cloneProductTypeCollection
                = db.GetCollection<MongoProductDao2>($"{tenantId}-{command.CloneProductId.Type}");

            MongoProductDao2 existingPlan = await cloneProductTypeCollection.Find(
               Builders<MongoProductDao2>.Filter.Eq(p => p.ProductId, command.CloneProductId))
            .FirstOrDefaultAsync(cancellationToken);

            if (existingPlan != null)
                return new Result
                {
                    Status = "failure",
                    Errors = new List<string> { "This plan with this version already exists" }
                };

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter.Eq(e => e.ProductId, command.ProductId);
            MongoProductDao2 productDao = await originalProductTypeCollection.Find(filter).FirstOrDefaultAsync(cancellationToken);
            if (productDao == null)
                return Result.Failure("products not found");

            DateTime currentTime = DateTime.UtcNow;
            productDao.Id = null;
            productDao.ProductId = command.CloneProductId;
            productDao.IssuerProductId = command.IssuerProductId;
            productDao.CreatedById = command.CreatedById;
            productDao.CreatedAt = currentTime;
            productDao.LastModifiedById = command.CreatedById;
            productDao.LastModifiedAt = currentTime;
            productDao.ScriptIds = command.ScriptIds;

            await cloneProductTypeCollection.InsertOneAsync(productDao, new InsertOneOptions(), cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<IReadOnlyCollection<string>> GetProductScriptIds(string tenantId, ProductId productId, CancellationToken cancellationToken)
        {

            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> originalProductTypeCollection
                = db.GetCollection<MongoProductDao2>($"{tenantId}-{productId.Type}");

            MongoProductDao2 existingPlan = await originalProductTypeCollection.Find(
              Builders<MongoProductDao2>.Filter.Eq(p => p.ProductId, productId))
           .FirstOrDefaultAsync(cancellationToken);

            return existingPlan.ScriptIds;
        }

        public async Task<IEnumerable<ProductType>> GetTypesAsync(string tenantId, string? clientId, ProductTypeWhere where, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            ProductConfig config = await db.GetCollection<ProductConfig>($"{tenantId}").Find(Builders<ProductConfig>.Filter.Eq(c => c.Type, "configs") & Builders<ProductConfig>.Filter.Eq(c => c.ClientId, clientId)).SingleOrDefaultAsync(cancellationToken);

            IEnumerable<string> filteredTenants = GetCollectionTenants(where);

            IMongoCollection<ProductType> tenantCollection = db.GetCollection<ProductType>($"{tenantId}-types");
            IMongoCollection<ProductType> coverGoCollection = db.GetCollection<ProductType>($"default-types");

            IEnumerable<ProductType> tenantProductTypes = Enumerable.Empty<ProductType>();
            IEnumerable<ProductType> defaultProductTypes = Enumerable.Empty<ProductType>();

            FilterDefinition<ProductType> mongoFilter = MongoSearchExtensions.AddProductTypeSearchFilters(where);

            if (!filteredTenants.Any() || filteredTenants.Any(t => t == tenantId))
                tenantProductTypes = await tenantCollection.Find(mongoFilter).ToListAsync(cancellationToken);

            if (!filteredTenants.Any() || filteredTenants.Any(t => t == "default"))
            {
                if (config?.DisplayedTypes != null)
                    mongoFilter &= Builders<ProductType>.Filter.In(c => c.TypeId, config.DisplayedTypes);
                defaultProductTypes = await coverGoCollection.Find(mongoFilter).ToListAsync(cancellationToken);
            }

            foreach (ProductType tenantProductType in tenantProductTypes)
                tenantProductType.TenantId = tenantId;

            foreach (ProductType defaultProductType in defaultProductTypes)
                defaultProductType.TenantId = "default";

            return tenantProductTypes.Concat(defaultProductTypes);
        }

        public async Task<Result> CreateTypeAsync(string tenantId, CreateProductTypeCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<ProductType> clientCollection = db.GetCollection<ProductType>($"{tenantId}-types");
            IMongoCollection<ProductType> coverGoCollection = db.GetCollection<ProductType>($"default-types");

            FilterDefinition<ProductType> filter = Builders<ProductType>.Filter.Eq(e => e.TypeId, command.TypeId);
            Task<ProductType> checkTenantCollectionTask = clientCollection.Find(filter).SingleOrDefaultAsync(cancellationToken);
            Task<ProductType> checkCoverGoCollectionTask = coverGoCollection.Find(filter).SingleOrDefaultAsync(cancellationToken);
            await Task.WhenAll(checkTenantCollectionTask, checkCoverGoCollectionTask);
            if (checkTenantCollectionTask.Result != null || checkCoverGoCollectionTask.Result != null)
                return new Result { Status = "failure", Errors = new List<string> { $"A type with the typeId {command.TypeId} already exists" } };

            await clientCollection.Indexes.CreateOneAsync(new CreateIndexModel<ProductType>(Builders<ProductType>.IndexKeys.Ascending(f => f.TypeId)), cancellationToken: cancellationToken);

            await clientCollection.InsertOneAsync(
                 new ProductType
                 {
                     TypeId = command.TypeId,
                     LogoUrl = command.LogoUrl,
                     CreatedAt = DateTime.UtcNow,
                     LastModifiedAt = DateTime.UtcNow,
                     CreatedById = command.CreatedById,
                     LastModifiedById = command.CreatedById,
                 });

            return new Result { Status = "success" };
        }

        public async Task<Result> DeleteTypeAsync(string tenantId, string typeId, string deletedById, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<ProductType> clientCollection = db.GetCollection<ProductType>($"{tenantId}-types");
            FilterDefinition<ProductType> filter = Builders<ProductType>.Filter.Eq(e => e.TypeId, typeId);

            DeleteResult result = await clientCollection.DeleteOneAsync(filter, cancellationToken);

            return result.DeletedCount == 0
                ? new Result { Status = "failure", Errors = new List<string> { $"The custom type '{typeId}' doesn't exist." } }
                : new Result { Status = "success" };
        }

        private static IEnumerable<string> GetTypesFromFilter(ProductWhere where)
        {
            if (where?.ProductId?.Type_in != null)
                return where.ProductId.Type_in.Distinct();
            if (where?.ProductId?.Type != null)
                return new List<string> { where.ProductId.Type };

            if (where?.And != null)
            {
                IEnumerable<string> andTypes = Enumerable.Empty<string>();
                foreach (ProductWhere andWhere in where.And)
                {
                    andTypes = andTypes == Enumerable.Empty<string>()
                        ? GetTypesFromFilter(andWhere)
                        : andTypes.Intersect(GetTypesFromFilter(andWhere));
                }
                return andTypes;
            }

            return new List<string> { };
        }

        private static IEnumerable<string> GetCollectionTenants(ProductWhere where)
        {
            if (where?.TenantId_in != null)
                return where.TenantId_in.Distinct();
            if (where?.TenantId != null)
                return new List<string> { where.TenantId };

            if (where?.Or != null)
            {
                IEnumerable<string> orTenants = Enumerable.Empty<string>();
                foreach (ProductWhere orWhere in where.Or)
                    orTenants = orTenants.Union(GetCollectionTenants(orWhere));
                return orTenants;
            }

            if (where?.And != null)
            {
                IEnumerable<string> andTenants = Enumerable.Empty<string>();
                foreach (ProductWhere andWhere in where.And)
                {
                    andTenants = andTenants == Enumerable.Empty<string>()
                        ? GetCollectionTenants(andWhere)
                        : andTenants.Intersect(GetCollectionTenants(andWhere));
                }
                return andTenants;
            }

            return new List<string> { };
        }

        private IEnumerable<string> GetCollectionTenants(ProductTypeWhere where)
        {
            if (where.TenantId_in != null)
                return where.TenantId_in.Distinct();
            if (where.TenantId != null)
                return new List<string> { where.TenantId };

            if (where.Or != null)
            {
                IEnumerable<string> orTenants = Enumerable.Empty<string>();
                foreach (ProductTypeWhere orWhere in where.Or)
                    orTenants = orTenants.Union(GetCollectionTenants(orWhere));
                return orTenants;
            }

            if (where.And != null)
            {
                IEnumerable<string> andTenants = Enumerable.Empty<string>();
                foreach (ProductTypeWhere andWhere in where.And)
                {
                    andTenants = andTenants == Enumerable.Empty<string>()
                        ? GetCollectionTenants(andWhere)
                        : andTenants.Intersect(GetCollectionTenants(andWhere));
                }
                return andTenants;
            }

            return new List<string> { };
        }

        public async Task<Result> CreateAsync(string tenantId, CreateProductCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<MongoProductDao2> collection = db.GetCollection<MongoProductDao2>($"{tenantId}-{command.ProductId.Type}");
            MongoProductDao2 existingPlan = await collection.Find(
               Builders<MongoProductDao2>.Filter.Eq(p => p.ProductId, command.ProductId))
           .FirstOrDefaultAsync(cancellationToken);

            if (existingPlan != null)
                return new Result
                {
                    Status = "failure",
                    Errors = new List<string> { "This plan with this version already exists" }
                };
            var representation = command.Representation;
            if (command.ProductId.Version != OriginalVersion && (string.IsNullOrEmpty(representation) || representation == "[]"))
            {
                var projection = Builders<MongoProductDao2>.Projection.Include(p => p.Representation);

                MongoProductDao2 originalProduct = await collection.Find(
                    Builders<MongoProductDao2>.Filter.Eq(p => p.ProductId, new ProductId { Type = command.ProductId.Type, Version = OriginalVersion, Plan = command.ProductId.Plan }))
                    .Project<MongoProductDao2>(projection)
                    .FirstOrDefaultAsync(cancellationToken);
                representation = originalProduct != null
                    ? originalProduct.Representation
                    : representation;
            }

            var product = new MongoProductDao2
            {
                ProductId = command.ProductId,
                InsurerId = command.InsurerId,
                IssuerProductId = command.IssuerProductId,
                CreatedById = command.CreatedById,
                Benefits = command.BenefitInputs?.Select(b => new Benefit
                {
                    TypeId = b.TypeId,
                    ParentTypeId = b.ParentTypeId,
                    OptionKey = b.OptionKey,
                    ParentOptionKeys = b.ParentOptionKeys,
                    CurrencyCode = b.CurrencyCode,
                    Value = b.Value,
                    Condition = b.Condition,
                    IsValueInput = b.IsValueInput
                })?.ToList(),
                ExclusionSettings = command.ExclusionSettings,
                LoadingSettings = command.LoadingSettings,
                RejectionSettings = command.RejectionSettings,
                ClaimSettings = command.ClaimSettings,
                Underwriting = command.Underwriting,
                Tags = command.Tags,
                Facts = command.Facts?.Select(f => new Fact { Id = f.Id, Type = f.Type, Value = f.Value }).ToList(),
                LaunchPeriodStartDate = command.LaunchPeriodStartDate,
                LaunchPeriodEndDate = command.LaunchPeriodEndDate,
                ChangeEffectiveDate = command.ChangeEffectiveDate,
                Status = command.Status,
                Representation = representation,
                Plans = await featureManager.IsEnabled("ExtractPlansFromProductTreeEnabled", tenantId)
                    ? ProductDaoExtensions.GetExtractedPlans(command.ProductId, representation)
                    : [],
                Fields = string.IsNullOrEmpty(command.Fields) ? null : JToken.Parse(command.Fields),
                LifecycleStage = command.LifecycleStage,
                ProductTreeId = command.ProductTreeId,
                ScriptIds = command.ScriptIds,
                TermsAndConditionsTemplateId = command.TermsAndConditionsTemplateId,
                TermsAndConditionsJacketId = command.TermsAndConditionsJacketId,
                RatingFactorsTable = command.RatingFactorsTable,
                Segments = command.Segments,
                AttachedDocumentsIds = command.AttachedDocumentsIds,


                LastModifiedById = command.CreatedById,
                LastModifiedAt = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow,
                PolicyIssuanceMethod = command.PolicyIssuanceMethod,
                OfferValidityPeriod = command.OfferValidityPeriod,
                AllowCustomProduct = command.AllowCustomProduct,
                AutoRenewal = command.AutoRenewal ?? false,
                RenewalNotification = command.RenewalNotification ?? false,
                TaxConfiguration = command.TaxConfiguration ?? new TaxConfiguration(),
            };

            await collection.InsertOneAsync(product, new(), cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<Result> UpdateAsync(string tenantId, UpdateProductCommand command, CancellationToken cancellationToken)
        {
            IMongoCollection<MongoProductDao2> collection = mongoDbContext.GetCollection<MongoProductDao2>(tenantId, DbName, command.ProductId.Type);

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter.Eq(p => p.ProductId, command.ProductId);

            UpdateDefinitionBuilder<MongoProductDao2> update = Builders<MongoProductDao2>.Update;

            var updates = new List<UpdateDefinition<MongoProductDao2>>
            {
                update.Set(a => a.LastModifiedAt, DateTime.UtcNow),
                update.Set(a => a.LastModifiedById, command.ModifiedById)
            };
            if (command.IsInsurerIdChanged) updates.Add(update.Set(a => a.InsurerId, command.InsurerId));

            if (command.IsRepresentationChanged)
            {
                updates.Add(update.Set(a => a.Representation, command.Representation));

                if (await featureManager.IsEnabled("ExtractPlansFromProductTreeEnabled", tenantId))
                    updates.Add(update.Set(a => a.Plans, ProductDaoExtensions.GetExtractedPlans(command.ProductId, command.Representation)));
            }

            if (command.IsFieldsChanged) updates.Add(update.Set(a => a.Fields, string.IsNullOrEmpty(command.Fields) ? null : JToken.Parse(command.Fields)));

            if (string.IsNullOrEmpty(command.RepresentationPatch) is false || string.IsNullOrEmpty(command.FieldsPatch) is false)
            {
                MongoProductDao2 product = await collection.Find(filter).FirstOrDefaultAsync(cancellationToken);

                if (string.IsNullOrEmpty(command.RepresentationPatch) is false && product.Representation != null)
                {
                    string updatedRepresentation = new JsonProjector().Write(product.Representation,
                        JsonConvert.DeserializeObject<JsonPatchDocument>(command.RepresentationPatch));

                    updates.Add(update.Set(a => a.Representation, updatedRepresentation));

                    if (await featureManager.IsEnabled("ExtractPlansFromProductTreeEnabled", tenantId))
                    {
                        IEnumerable<Plan> updatedPlans = ProductDaoExtensions.GetExtractedPlans(command.ProductId, updatedRepresentation);
                        updates.Add(update.Set(a => a.Plans, updatedPlans));
                    }
                }

                if (string.IsNullOrEmpty(command.FieldsPatch) is false && product.Fields != null)
                {
                    var updatedFields = new JsonProjector().Write(product.Fields,
                        JsonConvert.DeserializeObject<JsonPatchDocument>(command.FieldsPatch));
                    updates.Add(update.Set(a => a.Fields, updatedFields));
                }
            }

            if (command.IsLifecycleStageChanged) updates.Add(update.Set(a => a.LifecycleStage, command.LifecycleStage));
            if (command.IsUnderwritingChanged)
            {
                if (command.Underwriting.IsSourceTypeChanged)
                    updates.Add(update.Set(a => a.Underwriting.SourceType, command.Underwriting.SourceType));
                if (command.Underwriting.IsJsonLogicRulesChanged)
                    updates.Add(update.Set(a => a.Underwriting.JsonLogicRules, command.Underwriting.JsonLogicRules));
                if (command.Underwriting.IsExcelPathChanged)
                    updates.Add(update.Set(a => a.Underwriting.ExcelPath, command.Underwriting.ExcelPath));
                if (command.Underwriting.IsExcelRulesChanged)
                    updates.Add(update.Set(a => a.Underwriting.ExcelRules, command.Underwriting.ExcelRules));
            }
            if (command.IsRejectionSettingsChanged)
            {
                if (command.RejectionSettings == null)
                    updates.Add(update.Unset(a => a.RejectionSettings));
                else if (command.RejectionSettings.IsCodesChanged)
                    updates.Add(update.Set(a => a.RejectionSettings.Codes, command.RejectionSettings.Codes));
            }
            ;
            if (command.IsExclusionSettingsChanged)
            {
                if (command.ExclusionSettings == null)
                    updates.Add(update.Unset(a => a.ExclusionSettings));
                else if (command.ExclusionSettings.IsCodesChanged)
                    updates.Add(update.Set(a => a.ExclusionSettings.Codes, command.ExclusionSettings.Codes));
            }
            ;
            if (command.IsLoadingSettingsChanged)
            {
                if (command.LoadingSettings == null)
                    updates.Add(update.Unset(a => a.LoadingSettings));
                else
                {
                    if (command.LoadingSettings.IsMaxLoadingMultiplierChanged)
                        updates.Add(update.Set(a => a.LoadingSettings.MaxLoadingMultiplier, command.LoadingSettings.MaxLoadingMultiplier));
                    if (command.LoadingSettings.IsCodesChanged)
                        updates.Add(update.Set(a => a.LoadingSettings.Codes, command.LoadingSettings.Codes));
                }
            }
            ;
            if (command.IsClaimSettingsChanged)
            {
                if (command.ClaimSettings == null)
                    updates.Add(update.Unset(a => a.ClaimSettings));
                else
                {
                    if (command.ClaimSettings.IsDiagnosisSettingsChanged)
                    {
                        if (command.ClaimSettings.DiagnosisSettings == null)
                            updates.Add(update.Unset(a => a.ClaimSettings.DiagnosisSettings));
                        else if (command.ClaimSettings.DiagnosisSettings.IsCodesChanged)
                            updates.Add(update.Set(a => a.ClaimSettings.DiagnosisSettings.Codes, command.ClaimSettings.DiagnosisSettings.Codes));
                    }

                    if (command.ClaimSettings.IsOperationSettingsChanged)
                    {
                        if (command.ClaimSettings.OperationSettings == null)
                            updates.Add(update.Unset(a => a.ClaimSettings.OperationSettings));
                        else if (command.ClaimSettings.OperationSettings.IsCodesChanged)
                            updates.Add(update.Set(a => a.ClaimSettings.OperationSettings.Codes, command.ClaimSettings.OperationSettings.Codes));
                    }

                    if (command.ClaimSettings.IsRejectionSettingsChanged)
                    {
                        if (command.ClaimSettings.RejectionSettings == null)
                            updates.Add(update.Unset(a => a.ClaimSettings.RejectionSettings));
                        else if (command.ClaimSettings.RejectionSettings.IsCodesChanged)
                            updates.Add(update.Set(a => a.ClaimSettings.RejectionSettings.Codes, command.ClaimSettings.RejectionSettings.Codes));
                    }

                    if (command.ClaimSettings.IsProviderSettingsChanged)
                    {
                        if (command.ClaimSettings.ProviderSettings == null)
                            updates.Add(update.Unset(a => a.ClaimSettings.ProviderSettings));
                        else if (command.ClaimSettings.ProviderSettings.IsEntityIdsChanged)
                            updates.Add(update.Set(a => a.ClaimSettings.ProviderSettings.EntityIds, command.ClaimSettings.ProviderSettings.EntityIds));
                    }
                }
            }
            if (command.IsLaunchPeriodStartDateChanged) updates.Add(update.Set(a => a.LaunchPeriodStartDate, command.LaunchPeriodStartDate));
            if (command.IsLaunchPeriodEndDateChanged) updates.Add(update.Set(a => a.LaunchPeriodEndDate, command.LaunchPeriodEndDate));
            if (command.IsChangeEffectiveDateChanged) updates.Add(update.Set(a => a.ChangeEffectiveDate, command.ChangeEffectiveDate));
            if (command.IsStatusChanged) updates.Add(update.Set(a => a.Status, command.Status));
            if (command.IsOfferValidityPeriodChanged) updates.Add(update.Set(a => a.OfferValidityPeriod, command.OfferValidityPeriod));
            if (command.IsProductTreeIdChanged) updates.Add(update.Set(a => a.ProductTreeId, command.ProductTreeId));
            if (command.IsPolicyIssuanceMethodChanged) updates.Add(update.Set(a => a.PolicyIssuanceMethod, command.PolicyIssuanceMethod));
            if (command.IsAllowCustomProductChanged) updates.Add(update.Set(a => a.AllowCustomProduct, command.AllowCustomProduct));
            if (command.IsUpdateTypesChanged) updates.Add(update.Set(a => a.UpdateTypes, command.UpdateTypes));
            if (command.AutoRenewal is not null) updates.Add(update.Set(a => a.AutoRenewal, command.AutoRenewal.Value));
            if (command.RenewalNotification is not null) updates.Add(update.Set(a => a.RenewalNotification, command.RenewalNotification.Value));
            if (command.TaxConfiguration is not null) updates.Add(update.Set(a => a.TaxConfiguration, command.TaxConfiguration));

            UpdateResult result = await collection.UpdateOneAsync(filter, update.Combine(updates), cancellationToken: cancellationToken);

            return result.MatchedCount != 0 ? new Result { Status = "success" } : new Result { Status = "failure", Errors = new List<string> { $"The product {command.ProductId.ToString()} was not found." } };
        }

        public async Task<Result> AddBenefitAsync(string tenantId, ProductId productId, AddBenefitCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db.GetCollection<MongoProductDao2>($"{tenantId}-{productId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter.Where(e => e.ProductId.Plan == productId.Plan && e.ProductId.Version == productId.Version);
            MongoProductDao2 product = await collection.Find(filter).FirstOrDefaultAsync(cancellationToken);

            if (product == null)
                return new Result { Status = "failure", Errors = new List<string> { $"The product {productId.ToString()} was not found." } };
            else if (product.Benefits?.Any(b => b.TypeId == command.TypeId && b.OptionKey == command.OptionKey) ?? false)
                return new Result { Status = "failure", Errors = new List<string> { $"The benefit with typeId '{command.TypeId}' and option key '{command.OptionKey}' already exists for this product." } };

            UpdateDefinition<MongoProductDao2> update = Builders<MongoProductDao2>.Update
                .Set(p => p.LastModifiedAt, DateTime.UtcNow)
                .Set(p => p.LastModifiedById, command.AddedById)
                .AddToSet(p => p.Benefits, new Benefit
                {
                    TypeId = command.TypeId,
                    ParentTypeId = command.ParentTypeId,
                    ParentOptionKeys = command.ParentOptionKeys,
                    OptionKey = command.OptionKey,
                    Value = command.Value,
                    Condition = command.Condition,
                    CurrencyCode = command.CurrencyCode,
                    IsValueInput = command.IsValueInput
                });

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> AddManyBenefitsAsync(string tenantId, ProductId productId, List<AddBenefitCommand> commands, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db.GetCollection<MongoProductDao2>($"{tenantId}-{productId.Type}");

            FilterDefinition<MongoProductDao2> productFilter = Builders<MongoProductDao2>.Filter.Where(p => p.ProductId == productId);
            DateTime currentTime = DateTime.UtcNow;

            var updates = new List<UpdateDefinition<MongoProductDao2>> {
                Builders<MongoProductDao2>.Update.Set(p => p.LastModifiedAt, currentTime),
                Builders<MongoProductDao2>.Update.Set(p => p.LastModifiedById, commands.FirstOrDefault().AddedById),
                Builders<MongoProductDao2>.Update.AddToSetEach(
                    p => p.Benefits,
                    commands.Select(
                        c => new Benefit
                        {
                            TypeId = c.TypeId,
                            ParentTypeId = c.ParentTypeId,
                            ParentOptionKeys = c.ParentOptionKeys,
                            OptionKey = c.OptionKey,
                            Value = c.Value,
                            Condition = c.Condition,
                            CurrencyCode = c.CurrencyCode,
                            IsValueInput = c.IsValueInput
                    }))
            };

            UpdateResult result = await collection.UpdateOneAsync(productFilter, Builders<MongoProductDao2>.Update.Combine(updates), cancellationToken: cancellationToken);
            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> UpdateBenefitAsync(string tenantId, ProductId productId, UpdateBenefitCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db.GetCollection<MongoProductDao2>($"{tenantId}-{productId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter.Eq(e => e.ProductId, productId);
            MongoProductDao2 product = await collection.Find(filter).FirstOrDefaultAsync(cancellationToken);

            if (product == null)
                return new Result { Status = "failure", Errors = new List<string> { $"The product {productId.ToString()} was not found." } };
            else if (!product.Benefits?.Any(b => b.TypeId == command.TypeId && b.OptionKey == command.OptionKey) ?? true)
                return new Result { Status = "failure", Errors = new List<string> { $"The benefit with typeId '{command.TypeId}' and option key '{command.OptionKey}' was not found for this product." } };

            FilterDefinition<MongoProductDao2> updateFilter = Builders<MongoProductDao2>.Filter.Where(e => e.ProductId.Plan == productId.Plan && e.ProductId.Version == productId.Version && e.Benefits.Any(u => u.TypeId == command.TypeId && u.OptionKey == command.OptionKey));
            var updates = new List<UpdateDefinition<MongoProductDao2>>
            {
                Builders<MongoProductDao2>.Update.Set(a => a.LastModifiedAt, DateTime.UtcNow),
                Builders<MongoProductDao2>.Update.Set(a => a.LastModifiedById, command.ModifiedById)
            };

            if (command.IsParentTypeIdChanged)
                updates.Add(Builders<MongoProductDao2>.Update.Set(a => a.Benefits[-1].ParentTypeId, command.ParentTypeId));
            if (command.IsParentOptionKeysChanged)
                updates.Add(Builders<MongoProductDao2>.Update.Set(a => a.Benefits[-1].ParentOptionKeys, command.ParentOptionKeys));
            if (command.IsConditionChanged)
                updates.Add(Builders<MongoProductDao2>.Update.Set(a => a.Benefits[-1].Condition, command.Condition));
            if (command.IsCurrencyCodeChanged)
                updates.Add(Builders<MongoProductDao2>.Update.Set(a => a.Benefits[-1].CurrencyCode, command.CurrencyCode));
            if (command.IsValueChanged)
                updates.Add(Builders<MongoProductDao2>.Update.Set(a => a.Benefits[-1].Value, command.Value));
            if (command.IsIsValueInputChanged)
                updates.Add(Builders<MongoProductDao2>.Update.Set(a => a.Benefits[-1].IsValueInput, command.IsValueInput));


            UpdateResult result = await collection.UpdateOneAsync(updateFilter, Builders<MongoProductDao2>.Update.Combine(updates), cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> UpdateManyBenefitsAsync(string tenantId, ProductId productId, List<UpdateBenefitCommand> commands, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db.GetCollection<MongoProductDao2>($"{tenantId}-{productId.Type}");

            var listUpdates = new List<WriteModel<MongoProductDao2>>();

            var currentTime = DateTime.UtcNow;
            foreach (UpdateBenefitCommand command in commands)
            {
                FilterDefinition<MongoProductDao2> updateFilter
                    = Builders<MongoProductDao2>.Filter.Where(
                        p => p.ProductId == productId && p.Benefits.Any(b => b.TypeId == command.TypeId && b.OptionKey == command.OptionKey)
                    );

                var updates = new List<UpdateDefinition<MongoProductDao2>>
                {
                    Builders<MongoProductDao2>.Update.Set(a => a.LastModifiedAt, currentTime),
                    Builders<MongoProductDao2>.Update.Set(a => a.LastModifiedById, command.ModifiedById)
                };
                if (command.IsParentTypeIdChanged)
                    updates.Add(Builders<MongoProductDao2>.Update.Set(a => a.Benefits[-1].ParentTypeId, command.ParentTypeId));
                if (command.IsParentOptionKeysChanged)
                    updates.Add(Builders<MongoProductDao2>.Update.Set(a => a.Benefits[-1].ParentOptionKeys, command.ParentOptionKeys));
                if (command.IsConditionChanged)
                    updates.Add(Builders<MongoProductDao2>.Update.Set(a => a.Benefits[-1].Condition, command.Condition));
                if (command.IsCurrencyCodeChanged)
                    updates.Add(Builders<MongoProductDao2>.Update.Set(a => a.Benefits[-1].CurrencyCode, command.CurrencyCode));
                if (command.IsValueChanged)
                    updates.Add(Builders<MongoProductDao2>.Update.Set(a => a.Benefits[-1].Value, command.Value));
                if (command.IsIsValueInputChanged)
                    updates.Add(Builders<MongoProductDao2>.Update.Set(a => a.Benefits[-1].IsValueInput, command.IsValueInput));

                listUpdates.Add(new UpdateManyModel<MongoProductDao2>(updateFilter, Builders<MongoProductDao2>.Update.Combine(updates)));
            }

            BulkWriteResult result = await collection.BulkWriteAsync(listUpdates, cancellationToken: cancellationToken);
            return new Result { Status = result.MatchedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> RemoveBenefitAsync(string tenantId, ProductId productId, RemoveBenefitCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db.GetCollection<MongoProductDao2>($"{tenantId}-{productId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter.Eq(e => e.ProductId, productId);
            MongoProductDao2 product = await collection.Find(filter).FirstOrDefaultAsync(cancellationToken);

            if (product == null)
                return new Result { Status = "failure", Errors = new List<string> { $"The product {productId.ToString()} was not found." } };
            else if (!product.Benefits?.Any(b => b.TypeId == command.TypeId && b.OptionKey == command.OptionKey) ?? true)
                return new Result { Status = "failure", Errors = new List<string> { $"The benefit with typeId '{command.TypeId}' and option key '{command.OptionKey}' was not found for this product." } };

            UpdateDefinition<MongoProductDao2> update = Builders<MongoProductDao2>.Update
                .Set(a => a.LastModifiedAt, DateTime.UtcNow)
                .PullFilter(a => a.Benefits, c => c.TypeId == command.TypeId && c.OptionKey == command.OptionKey);

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> RemoveManyBenefitsAsync(string tenantId, ProductId productId, List<RemoveBenefitCommand> commands, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db.GetCollection<MongoProductDao2>($"{tenantId}-{productId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter.Eq(e => e.ProductId, productId);
            MongoProductDao2 product = await collection.Find(filter).FirstOrDefaultAsync(cancellationToken);

            if (product == null)
                return new Result { Status = "failure", Errors = new List<string> { $"The product {productId.ToString()} was not found." } };

            //TODO: make work in one update definition
            UpdateResult[] results = await commands.ParallelSelectAsync(c => collection.UpdateOneAsync(filter, Builders<MongoProductDao2>.Update.PullFilter(p =>
                    p.Benefits, b => b.TypeId == c.TypeId && b.OptionKey == c.OptionKey)),
                new ParallelRunOptions() { MaxDegreeOfParallelism = 10 });
            UpdateResult updateLastModifiedResult = await collection.UpdateOneAsync(filter, Builders<MongoProductDao2>.Update.Set(a => a.LastModifiedAt, DateTime.UtcNow), cancellationToken: cancellationToken);

            bool hasFailedUpdates = results.Append(updateLastModifiedResult).Any(a => a.ModifiedCount != 0);

            return hasFailedUpdates ? Result.Failure("One or more benefits could not be removed.") : Result.Success();
        }

        public async Task<Result> DeleteAsync(string tenantId, DeleteProductCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            DeleteResult result = await db.GetCollection<MongoProductDao2>($"{tenantId}-{command.ProductId.Type}").DeleteOneAsync(Builders<MongoProductDao2>.Filter.Eq(p => p.ProductId, command.ProductId), cancellationToken);

            return result.DeletedCount > 0
                ? new Result { Status = "success" }
                : new Result { Status = "failure", Errors = new List<string> { "This plan with this version doesn't exist" } };
        }

        public async Task<Result> AddTagAsync(string tenantId, ProductId productId, AddTagCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db.GetCollection<MongoProductDao2>($"{tenantId}-{productId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter.Where(e => e.ProductId.Plan == productId.Plan && e.ProductId.Version == productId.Version);
            MongoProductDao2 product = await collection.Find(filter).FirstOrDefaultAsync(cancellationToken);

            if (product == null)
                return new Result { Status = "failure", Errors = new List<string> { $"The product {productId.ToString()} was not found." } };

            UpdateDefinition<MongoProductDao2> update = Builders<MongoProductDao2>.Update
                .Set(p => p.LastModifiedAt, DateTime.UtcNow)
                .Set(p => p.LastModifiedById, command.AddedById)
                .AddToSet(p => p.Tags, new Domain.Products.Tag
                {
                    Id = command.Id,
                    Type = command.Type,
                });

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> RemoveTagAsync(string tenantId, ProductId productId, RemoveCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db.GetCollection<MongoProductDao2>($"{tenantId}-{productId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter.Eq(e => e.ProductId, productId);
            UpdateDefinition<MongoProductDao2> update = Builders<MongoProductDao2>.Update
                .Set(a => a.LastModifiedAt, DateTime.UtcNow)
                .PullFilter(a => a.Tags, c => c.Id == command.Id);

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        public async Task<Dictionary<string, Dictionary<string, List<string>>>> GetBenefitInfosAsync(string tenantId, string clientId, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IEnumerable<ProductConfig> configs = await db.GetCollection<ProductConfig>($"{tenantId}").Find(Builders<ProductConfig>.Filter.Eq(c => c.Type, "configs")).ToListAsync(cancellationToken);
            ProductConfig config = configs.FirstOrDefault(c => c.ClientId == clientId) ?? configs.FirstOrDefault();

            return config?.DisplayedBenefits;
        }

        public async Task<Dictionary<string, List<string>>> GetBenefitCategoriesAsync(string tenantId, string clientId, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IEnumerable<ProductConfig> configs = await db.GetCollection<ProductConfig>($"{tenantId}").Find(Builders<ProductConfig>.Filter.Eq(c => c.Type, "configs")).ToListAsync(cancellationToken);
            ProductConfig config = configs.FirstOrDefault(c => c.ClientId == clientId) ?? configs.FirstOrDefault();

            return config.DisplayedBenefits?.ToDictionary(dbe => dbe.Key, dbe => dbe.Value.Keys.ToList());
        }

        public async Task<Result<CreatedStatus>> CreateConfigAsync(string tenantId, ProductConfig productConfig, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<ProductConfig> collection = db.GetCollection<ProductConfig>(tenantId);
            productConfig.Type = "configs";
            await collection.InsertOneAsync(productConfig, cancellationToken);

            ProductConfig newConfig = await collection.Find(Builders<ProductConfig>.Filter.Eq(p => p.ClientId, productConfig.ClientId)).SingleOrDefaultAsync(cancellationToken);

            return new Result<CreatedStatus> { Status = "success", Value = new CreatedStatus { Id = newConfig.Id } };
        }

        public async Task<Result> UpdateConfigAsync(string tenantId, ProductConfig productConfig, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<ProductConfig> collection = db.GetCollection<ProductConfig>(tenantId);

            await collection.ReplaceOneAsync(Builders<ProductConfig>.Filter.Eq(p => p.Id, productConfig.Id), productConfig, cancellationToken: cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<Result> DeleteConfigAsync(string tenantId, string id, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<ProductConfig> collection = db.GetCollection<ProductConfig>(tenantId);
            ProductConfig existingConfig = await collection.Find(Builders<ProductConfig>.Filter.Eq(p => p.Id, id)).SingleOrDefaultAsync(cancellationToken);

            if (existingConfig == null)
                return new Result
                {
                    Status = "failure",
                    Errors = new List<string> { $"A config with id `{id}` was not found." }
                };

            DeleteResult result = await collection.DeleteOneAsync(Builders<ProductConfig>.Filter.Eq(p => p.Id, id), cancellationToken);

            return result.DeletedCount > 0
                ? new Result { Status = "success" }
                : new Result { Status = "failure" };
        }

        public async Task<Result> AddUnderwritingVariableAsync(string tenantId, ProductId productId, AddUnderwritingVariableCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db.GetCollection<MongoProductDao2>($"{tenantId}-{productId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter.Where(e => e.ProductId.Plan == productId.Plan && e.ProductId.Version == productId.Version);
            MongoProductDao2 product = await collection.Find(filter).FirstOrDefaultAsync(cancellationToken);

            if (product == null)
                return new Result { Status = "failure", Errors = new List<string> { $"The product {productId.ToString()} was not found." } };

            string id = Guid.NewGuid().ToString();
            command.Id = id;

            UpdateDefinition<MongoProductDao2> update = Builders<MongoProductDao2>.Update
                .Set(p => p.LastModifiedAt, DateTime.UtcNow)
                .Set(p => p.LastModifiedById, command.AddedById)
                .AddToSet(p => p.Underwriting.Variables, new UnderwritingVariable
                {
                    Id = command.Id,
                    Name = command.Name,
                    Description = command.Description,
                    JsonSchemaValidation = command.JsonSchemaValidation
                });

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount > 0 ? "success" : "failure" };
        }

        public async Task<Result> UpdateUnderwritingVariableAsync(string tenantId, ProductId productId, UpdateUnderwritingVariableCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db.GetCollection<MongoProductDao2>($"{tenantId}-{productId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter.Where(e => e.ProductId.Plan == productId.Plan && e.ProductId.Version == productId.Version && e.Underwriting.Variables.Any(u => u.Id == command.Id));
            MongoProductDao2 product = await collection.Find(filter).FirstOrDefaultAsync(cancellationToken);

            if (product == null)
                return new Result { Status = "failure", Errors = new List<string> { $"The product {productId.ToString()} with variable {command.Id} was not found." } };

            var updates = new List<UpdateDefinition<MongoProductDao2>>
            {
                Builders<MongoProductDao2>.Update.Set(a => a.LastModifiedAt, DateTime.UtcNow),
                Builders<MongoProductDao2>.Update.Set(a => a.LastModifiedById, command.ModifiedById)
            };

            if (command.IsDescriptionChanged)
                updates.Add(Builders<MongoProductDao2>.Update.Set(a => a.Underwriting.Variables[-1].Description, command.Description));
            if (command.IsNameChanged)
                updates.Add(Builders<MongoProductDao2>.Update.Set(a => a.Underwriting.Variables[-1].Name, command.Name));
            if (command.IsJsonSchemaValidationChanged)
                updates.Add(Builders<MongoProductDao2>.Update.Set(a => a.Underwriting.Variables[-1].JsonSchemaValidation, command.JsonSchemaValidation));

            UpdateResult result = await collection.UpdateOneAsync(filter, Builders<MongoProductDao2>.Update.Combine(updates), cancellationToken: cancellationToken);

            return result.ModifiedCount > 0
                ? new Result { Status = "success" }
                : new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> RemoveUnderwritingVariableAsync(string tenantId, ProductId productId, RemoveCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db.GetCollection<MongoProductDao2>($"{tenantId}-{productId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter.Where(e => e.ProductId.Plan == productId.Plan && e.ProductId.Version == productId.Version);
            MongoProductDao2 product = await collection.Find(filter).FirstOrDefaultAsync(cancellationToken);

            if (product == null)
                return new Result { Status = "failure", Errors = new List<string> { $"The product {productId.ToString()} was not found." } };

            UpdateDefinition<MongoProductDao2> update = Builders<MongoProductDao2>.Update
                .Set(p => p.LastModifiedAt, DateTime.UtcNow)
                .Set(p => p.LastModifiedById, command.RemovedById)
                .PullFilter(a => a.Underwriting.Variables, c => c.Id == command.Id);

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return result.ModifiedCount > 0
                ? new Result { Status = "success" }
                : new Result { Status = "failure" };
        }

        public async Task<IEnumerable<ProductUnderwritingJsonLogicRules>> GetUnderwritingJsonLogicRulesAsync(string tenantId, ProductWhere where, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            string typeRegex = GetTypeRegex(where);
            IEnumerable<string> filteredTenants = GetCollectionTenants(where);
            IEnumerable<string> tenantCollectionNames = Enumerable.Empty<string>();
            IEnumerable<string> covergoCollectionNames = Enumerable.Empty<string>();

            if (!filteredTenants.Any() || filteredTenants.Any(t => t == $"{tenantId}"))
            {
                tenantCollectionNames = await (await db.ListCollectionNamesAsync(new ListCollectionNamesOptions
                {
                    Filter = Builders<BsonDocument>.Filter.Regex("name", $"/^{tenantId}-({typeRegex})((?!_events|types|insurers).)*$/")
                })).ToListAsync(cancellationToken);
            }

            if (!filteredTenants.Any() || filteredTenants.Any(t => t == "default"))
            {
                covergoCollectionNames = await (await db.ListCollectionNamesAsync(new ListCollectionNamesOptions
                {
                    Filter = Builders<BsonDocument>.Filter.Regex("name", $"/^default-({typeRegex})((?!_events|types|insurers).)*$/")
                })).ToListAsync(cancellationToken);
            }

            FilterDefinition<MongoProductDao2> mongoFilter = FilterDefinition<MongoProductDao2>.Empty;
            if (where != null)
                mongoFilter = MongoSearchExtensions.AddProductSearchFilters2(where);

            var getDefaultProductsTask = covergoCollectionNames.ParallelSelectAsync((n, ct) => db.GetCollection<MongoProductDao2>(n).Find(mongoFilter).Project(p => new MongoProductDao2 { ProductId = p.ProductId, Underwriting = p.Underwriting, UnderwritingRules = p.UnderwritingRules }).ToListAsync(ct),
                new ParallelRunOptions() { MaxDegreeOfParallelism = 5, CancellationToken = cancellationToken });
            var tenantProductsTask = tenantCollectionNames.ParallelSelectAsync((n, ct) => db.GetCollection<MongoProductDao2>(n).Find(mongoFilter).Project(p => new MongoProductDao2 { ProductId = p.ProductId, Underwriting = p.Underwriting, UnderwritingRules = p.UnderwritingRules }).ToListAsync(ct),
                new ParallelRunOptions() { MaxDegreeOfParallelism = 5, CancellationToken = cancellationToken });

            List<MongoProductDao2>[] defaultProductsLists = await getDefaultProductsTask;
            List<MongoProductDao2>[] tenantProductsLists = await tenantProductsTask;

            IEnumerable<MongoProductDao2> tenantProductDaos = tenantProductsLists.SelectMany(c => c);
            IEnumerable<MongoProductDao2> defaultProductDaos = defaultProductsLists.SelectMany(c => c);
            IEnumerable<MongoProductDao2> allProductDaos = Enumerable.Concat(tenantProductDaos, defaultProductDaos);

            return allProductDaos.Select(d => new ProductUnderwritingJsonLogicRules
            {
                Id = d.ProductId,
                JsonLogicRules = d.Underwriting?.JsonLogicRules ??
                    (d.UnderwritingRules != null
                       ? JObject.Parse(d.UnderwritingRules.ToJson())
                       : null)
            });
        }

        public async Task<Result> AddFactAsync(string tenantId, ProductId productId, AddFactCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db.GetCollection<MongoProductDao2>($"{tenantId}-{productId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter.Where(e => e.ProductId.Plan == productId.Plan && e.ProductId.Version == productId.Version);
            MongoProductDao2 product = await collection.Find(filter).FirstOrDefaultAsync(cancellationToken);

            if (product == null)
                return new Result { Status = "failure", Errors = new List<string> { $"The product {productId.ToString()} was not found." } };

            UpdateDefinition<MongoProductDao2> update = Builders<MongoProductDao2>.Update
                .Set(p => p.LastModifiedAt, DateTime.UtcNow)
                .Set(p => p.LastModifiedById, command.AddedById)
                .AddToSet(p => p.Facts, new Fact
                {
                    Id = command.Id,
                    Type = command.Type,
                    Value = command.Value
                });

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> AddManyFactsAsync(string tenantId, ProductId productId, List<AddFactCommand> commands, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db.GetCollection<MongoProductDao2>($"{tenantId}-{productId.Type}");
            FilterDefinition<MongoProductDao2> productFilter = Builders<MongoProductDao2>.Filter.Where(p => p.ProductId == productId);
            DateTime currentTime = DateTime.UtcNow;
            var updates = new List<UpdateDefinition<MongoProductDao2>> {
                Builders<MongoProductDao2>.Update.Set(p => p.LastModifiedAt, currentTime),
                Builders<MongoProductDao2>.Update.Set(p => p.LastModifiedById, commands.FirstOrDefault().AddedById),
                Builders<MongoProductDao2>.Update.AddToSetEach(p => p.Facts, commands.Select(c => new Fact { Id = c.Id, Type = c.Type, Value = c.Value }))
            };

            UpdateResult result = await collection.UpdateOneAsync(productFilter, Builders<MongoProductDao2>.Update.Combine(updates), cancellationToken: cancellationToken);
            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> UpdateFactAsync(string tenantId, ProductId productId, UpdateFactCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db.GetCollection<MongoProductDao2>($"{tenantId}-{productId.Type}");

            UpdateDefinitionBuilder<MongoProductDao2> update = Builders<MongoProductDao2>.Update;

            FilterDefinition<MongoProductDao2> updateFilter = Builders<MongoProductDao2>.Filter.Where(p => p.ProductId == productId && p.Facts.Any(f => f.Id == command.Id));

            var currentTime = DateTime.UtcNow;
            var updates = new List<UpdateDefinition<MongoProductDao2>>
            {
                Builders<MongoProductDao2>.Update.Set(a => a.LastModifiedAt, currentTime),
                Builders<MongoProductDao2>.Update.Set(a => a.LastModifiedById, command.ModifiedById)
            };

            if (command.IsTypeChanged)
                updates.Add(Builders<MongoProductDao2>.Update.Set(p => p.Facts[-1].Type, command.Type));
            if (command.IsValueChanged)
                updates.Add(Builders<MongoProductDao2>.Update.Set(p => p.Facts[-1].Value, command.Value));

            UpdateResult result = await collection.UpdateOneAsync(updateFilter, Builders<MongoProductDao2>.Update.Combine(updates), cancellationToken: cancellationToken);

            return new Result { Status = result.MatchedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> UpdateManyFactsAsync(string tenantId, ProductId productId, List<UpdateFactCommand> commands, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db.GetCollection<MongoProductDao2>($"{tenantId}-{productId.Type}");

            var listUpdates = new List<WriteModel<MongoProductDao2>>();

            var currentTime = DateTime.UtcNow;
            foreach (UpdateFactCommand command in commands)
            {
                FilterDefinition<MongoProductDao2> updateFilter = Builders<MongoProductDao2>.Filter.Where(p => p.ProductId == productId && p.Facts.Any(f => f.Id == command.Id));

                var updates = new List<UpdateDefinition<MongoProductDao2>>
                {
                    Builders<MongoProductDao2>.Update.Set(a => a.LastModifiedAt, currentTime),
                    Builders<MongoProductDao2>.Update.Set(a => a.LastModifiedById, command.ModifiedById)
                };
                if (command.IsTypeChanged)
                    updates.Add(Builders<MongoProductDao2>.Update.Set(p => p.Facts[-1].Type, command.Type));
                if (command.IsValueChanged)
                    updates.Add(Builders<MongoProductDao2>.Update.Set(p => p.Facts[-1].Value, command.Value));

                listUpdates.Add(new UpdateManyModel<MongoProductDao2>(updateFilter, Builders<MongoProductDao2>.Update.Combine(updates)));
            }

            BulkWriteResult result = await collection.BulkWriteAsync(listUpdates, cancellationToken: cancellationToken);
            return new Result { Status = result.MatchedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> RemoveFactAsync(string tenantId, ProductId productId, RemoveCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db.GetCollection<MongoProductDao2>($"{tenantId}-{productId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter.Eq(e => e.ProductId, productId);
            UpdateDefinition<MongoProductDao2> update = Builders<MongoProductDao2>.Update
                .Set(a => a.LastModifiedAt, DateTime.UtcNow)
                .PullFilter(a => a.Facts, c => c.Id == command.Id);

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> AddScriptToProduct(string tenantId, AddScriptToProductCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db.GetCollection<MongoProductDao2>($"{tenantId}-{command.ProductId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter.Where(e => e.ProductId.Plan == command.ProductId.Plan && e.ProductId.Version == command.ProductId.Version);
            MongoProductDao2 product = await collection.Find(filter).FirstOrDefaultAsync(cancellationToken);

            if (product == null)
                return new Result { Status = "failure", Errors = new List<string> { $"The product {command.ProductId} was not found." } };

            UpdateDefinition<MongoProductDao2> update = Builders<MongoProductDao2>.Update
                .Set(p => p.LastModifiedAt, DateTime.UtcNow)
                .Set(p => p.LastModifiedById, command.AddedById)
                .AddToSet(p => p.ScriptIds, command.ScriptId);

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> RemoveScriptFromProduct(string tenantId, RemoveScriptFromProductCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<MongoProductDao2> collection = db.GetCollection<MongoProductDao2>($"{tenantId}-{command.ProductId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter.Eq(e => e.ProductId, command.ProductId);
            UpdateDefinition<MongoProductDao2> update = Builders<MongoProductDao2>.Update
                .Set(a => a.LastModifiedAt, DateTime.UtcNow)
                .Pull(a => a.ScriptIds, command.ScriptId);

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> AddTemplateRelationshipToProduct(string tenantId, AddTemplateRelationshipToProductCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db
                .GetCollection<MongoProductDao2>($"{tenantId}-{command.ProductId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter
                .Where(e => e.ProductId.Plan == command.ProductId.Plan && e.ProductId.Version == command.ProductId.Version);
            MongoProductDao2 product = await collection.Find(filter).FirstOrDefaultAsync(cancellationToken);

            if (product == null)
                return new Result
                {
                    Status = "failure",
                    Errors = new List<string> { $"The product {command.ProductId} was not found." }
                };

            UpdateDefinition<MongoProductDao2> update = Builders<MongoProductDao2>.Update
                .Set(p => p.LastModifiedAt, DateTime.UtcNow)
                .Set(p => p.LastModifiedById, command.AddedById)
                .AddToSet(p => p.TemplateRelationshipIds, command.TemplateRelationshipId);

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> RemoveTemplateRelationshipFromProduct(string tenantId, RemoveTemplateRelationshipFromProductCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<MongoProductDao2> collection = db
                .GetCollection<MongoProductDao2>($"{tenantId}-{command.ProductId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter
                .Eq(e => e.ProductId, command.ProductId);
            UpdateDefinition<MongoProductDao2> update = Builders<MongoProductDao2>.Update
                .Set(a => a.LastModifiedAt, DateTime.UtcNow)
                .Set(p => p.LastModifiedById, command.RemovedById)
                .Pull(a => a.TemplateRelationshipIds, command.TemplateRelationshipId);

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> AddInternalReviewAsync(string tenantId, ProductId productId, AddInternalReviewCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db.GetCollection<MongoProductDao2>($"{tenantId}-{productId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter.Eq(e => e.ProductId, productId);

            var currentTime = DateTime.UtcNow;
            UpdateDefinition<MongoProductDao2> update = Builders<MongoProductDao2>.Update
                .Set(p => p.LastModifiedAt, currentTime)
                .Set(p => p.LastModifiedById, command.AddedById)
                .AddToSet(p => p.InternalReviews, new InternalReview
                {
                    Id = command.Id,
                    Status = command.Status,
                    Comment = command.Comment,
                    CreatedById = command.AddedById,
                    CreatedAt = currentTime
                });

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> UpdateInternalReviewAsync(string tenantId, ProductId productId, UpdateInternalReviewCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db.GetCollection<MongoProductDao2>($"{tenantId}-{productId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter.Where(e => e.ProductId.Plan == productId.Plan && e.ProductId.Version == productId.Version);

            UpdateDefinitionBuilder<MongoProductDao2> update = Builders<MongoProductDao2>.Update;

            FilterDefinition<MongoProductDao2> updateFilter = Builders<MongoProductDao2>.Filter.Where(p => p.ProductId == productId && p.InternalReviews.Any(i => i.Id == command.Id));

            var currentTime = DateTime.UtcNow;
            var updates = new List<UpdateDefinition<MongoProductDao2>>
            {
                Builders<MongoProductDao2>.Update.Set(a => a.LastModifiedAt, currentTime),
                Builders<MongoProductDao2>.Update.Set(a => a.LastModifiedById, command.ModifiedById)
            };

            if (command.IsStatusChanged)
                updates.Add(Builders<MongoProductDao2>.Update.Set(p => p.InternalReviews[-1].Status, command.Status));
            if (command.IsCommentChanged)
                updates.Add(Builders<MongoProductDao2>.Update.Set(p => p.InternalReviews[-1].Comment, command.Comment));

            updates.Add(Builders<MongoProductDao2>.Update.Set(p => p.InternalReviews[-1].LastModifiedAt, DateTime.UtcNow));
            updates.Add(Builders<MongoProductDao2>.Update.Set(p => p.InternalReviews[-1].LastModifiedById, command.ModifiedById));

            UpdateResult result = await collection.UpdateOneAsync(updateFilter, Builders<MongoProductDao2>.Update.Combine(updates), cancellationToken: cancellationToken);

            return new Result { Status = result.MatchedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> RemoveInternalReviewAsync(string tenantId, ProductId productId, RemoveCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db.GetCollection<MongoProductDao2>($"{tenantId}-{productId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter.Eq(e => e.ProductId, productId);
            UpdateDefinition<MongoProductDao2> update = Builders<MongoProductDao2>.Update
                .Set(a => a.LastModifiedAt, DateTime.UtcNow)
                .PullFilter(a => a.InternalReviews, c => c.Id == command.Id);

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> AddDataSchemaToProductTypeToProduct(string tenantId, AddDataSchemaToProductTypeCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<ProductType> collection = db.GetCollection<ProductType>($"{tenantId}-types");

            FilterDefinition<ProductType> filter = Builders<ProductType>.Filter.Eq(e => e.TypeId, command.ProductTypeId);
            ProductType productType = await collection.Find(filter).FirstOrDefaultAsync(cancellationToken);

            if (productType == null)
                return new Result
                {
                    Status = "failure",
                    Errors = new List<string> { $"The productType {command.ProductTypeId} was not found." }
                };

            UpdateDefinition<ProductType> update = Builders<ProductType>.Update
                .Set(p => p.LastModifiedAt, DateTime.UtcNow)
                .Set(p => p.LastModifiedById, command.AddedById)
                .AddToSet(p => p.DataSchemaIds, command.DataSchemaId);

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> RemoveDataSchemaToProductTypeFromProduct(string tenantId, RemoveDataSchemaFromProductTypeCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<ProductType> collection = db.GetCollection<ProductType>($"{tenantId}-types");

            FilterDefinition<ProductType> filter = Builders<ProductType>.Filter.Eq(e => e.TypeId, command.ProductTypeId);
            ProductType productType = await collection.Find(filter).FirstOrDefaultAsync(cancellationToken);

            if (productType == null)
                return new Result
                {
                    Status = "failure",
                    Errors = new List<string> { $"The productType {command.ProductTypeId} was not found." }
                };

            UpdateDefinition<ProductType> update = Builders<ProductType>.Update
                .Set(a => a.LastModifiedAt, DateTime.UtcNow)
                .Set(p => p.LastModifiedById, command.RemovedById)
                .Pull(a => a.DataSchemaIds, command.DataSchemaId);

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        private static IMongoDatabase GetProductsDatabase(string tenantId)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            return client.GetDatabase(DbName);
        }

        private static string GetTypeRegex(ProductWhere where) => string.Join("|", GetTypesFromFilter(where));

        public async Task<Result> SetTermsAndConditionsTemplateToProduct(string tenantId, SetTermsAndConditionsCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db
                .GetCollection<MongoProductDao2>($"{tenantId}-{command.ProductId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter
                .Where(e => e.ProductId.Plan == command.ProductId.Plan && e.ProductId.Version == command.ProductId.Version);
            MongoProductDao2 product = await collection.Find(filter).FirstOrDefaultAsync(cancellationToken);

            if (product == null)
                return new Result
                {
                    Status = "failure",
                    Errors = new List<string> { $"The product {command.ProductId} was not found." }
                };

            UpdateDefinition<MongoProductDao2> update = Builders<MongoProductDao2>.Update
                .Set(p => p.LastModifiedAt, DateTime.UtcNow)
                .Set(p => p.LastModifiedById, command.SetById)
                .Set(p => p.TermsAndConditionsTemplateId, command.TemplateId)
                .Set(p => p.TermsAndConditionsJacketId, command.JacketId);

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }
        public async Task<Result> RemoveTermsAndConditionsTemplateFromProduct(string tenantId, RemoveTermsAndConditionsCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db
                .GetCollection<MongoProductDao2>($"{tenantId}-{command.ProductId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter
                .Where(e => e.ProductId.Plan == command.ProductId.Plan && e.ProductId.Version == command.ProductId.Version);
            MongoProductDao2 product = await collection.Find(filter).FirstOrDefaultAsync(cancellationToken);

            if (product == null)
                return new Result
                {
                    Status = "failure",
                    Errors = new List<string> { $"The product {command.ProductId} was not found." }
                };

            UpdateDefinition<MongoProductDao2> update = Builders<MongoProductDao2>.Update
                .Set(p => p.LastModifiedAt, DateTime.UtcNow)
                .Set(p => p.LastModifiedById, command.RemovedById)
                .Unset(p => p.TermsAndConditionsTemplateId)
                .Unset(p => p.TermsAndConditionsJacketId);

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> SetRatingFactorsTableToProduct(string tenantId, SetRatingFactorsTableCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db
                .GetCollection<MongoProductDao2>($"{tenantId}-{command.ProductId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter
                .Where(e => e.ProductId.Plan == command.ProductId.Plan && e.ProductId.Version == command.ProductId.Version);
            MongoProductDao2 product = await collection.Find(filter).FirstOrDefaultAsync(cancellationToken);

            if (product == null)
                return new Result
                {
                    Status = "failure",
                    Errors = new List<string> { $"The product {command.ProductId} was not found." }
                };

            UpdateDefinition<MongoProductDao2> update = Builders<MongoProductDao2>.Update
                .Set(p => p.LastModifiedAt, DateTime.UtcNow)
                .Set(p => p.LastModifiedById, command.SetById)
                .Set(p => p.RatingFactorsTable, command.RatingFactorsTable);

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> RemoveRatingFactorsTableFromProduct(string tenantId, RemoveRatingFactorsTableCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db
                .GetCollection<MongoProductDao2>($"{tenantId}-{command.ProductId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter
                .Where(e => e.ProductId.Plan == command.ProductId.Plan && e.ProductId.Version == command.ProductId.Version);
            MongoProductDao2 product = await collection.Find(filter).FirstOrDefaultAsync(cancellationToken);

            if (product == null)
                return new Result
                {
                    Status = "failure",
                    Errors = new List<string> { $"The product {command.ProductId} was not found." }
                };

            UpdateDefinition<MongoProductDao2> update = Builders<MongoProductDao2>.Update
                .Set(p => p.LastModifiedAt, DateTime.UtcNow)
                .Set(p => p.LastModifiedById, command.RemovedById)
                .Unset(p => p.RatingFactorsTable);

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> SetSegmentsToProduct(string tenantId, SetSegmentsCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db
                .GetCollection<MongoProductDao2>($"{tenantId}-{command.ProductId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter
                .Where(e => e.ProductId.Plan == command.ProductId.Plan && e.ProductId.Version == command.ProductId.Version);
            MongoProductDao2 product = await collection.Find(filter).FirstOrDefaultAsync(cancellationToken);

            if (product == null)
                return new Result
                {
                    Status = "failure",
                    Errors = new List<string> { $"The product {command.ProductId} was not found." }
                };

            UpdateDefinition<MongoProductDao2> update = Builders<MongoProductDao2>.Update
                .Set(p => p.LastModifiedAt, DateTime.UtcNow)
                .Set(p => p.LastModifiedById, command.SetById)
                .Set(p => p.Segments, command.Segments);

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> AttachDocumentsToProduct(string tenantId, AttachDocumentsCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db
                .GetCollection<MongoProductDao2>($"{tenantId}-{command.ProductId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter
                .Where(e => e.ProductId.Plan == command.ProductId.Plan && e.ProductId.Version == command.ProductId.Version);
            MongoProductDao2 product = await collection.Find(filter).FirstOrDefaultAsync(cancellationToken);

            if (product == null)
                return new Result
                {
                    Status = "failure",
                    Errors = new List<string> { $"The product {command.ProductId} was not found." }
                };

            UpdateDefinition<MongoProductDao2> update = Builders<MongoProductDao2>.Update
                .Set(p => p.LastModifiedAt, DateTime.UtcNow)
                .Set(p => p.LastModifiedById, command.SetById)
                .PushEach(p => p.AttachedDocumentsIds, command.AttachedDocumentsIds);

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> RemoveAttachedDocumentFromProduct(string tenantId, RemoveAttachedDocumentCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<MongoProductDao2> collection = db
                .GetCollection<MongoProductDao2>($"{tenantId}-{command.ProductId.Type}");

            FilterDefinition<MongoProductDao2> filter = Builders<MongoProductDao2>.Filter
                .Where(e => e.ProductId.Plan == command.ProductId.Plan && e.ProductId.Version == command.ProductId.Version);
            MongoProductDao2 product = await collection.Find(filter).FirstOrDefaultAsync(cancellationToken);

            if (product == null)
                return new Result
                {
                    Status = "failure",
                    Errors = new List<string> { $"The product {command.ProductId} was not found." }
                };

            UpdateDefinition<MongoProductDao2> update = Builders<MongoProductDao2>.Update
                .Set(p => p.LastModifiedAt, DateTime.UtcNow)
                .Set(p => p.LastModifiedById, command.RemovedById)
                .PullFilter(p => p.AttachedDocumentsIds, c => c.TemplateId == command.AttachedDocumentId);

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }
    }

    public static class FilterDefinitionExtensions
    {
        public static FilterDefinition<ProductConfig> GetFilterDefinition(this ProductConfigWhere where)
        {
            FilterDefinition<ProductConfig> mongoFilter = FilterDefinition<ProductConfig>.Empty;

            if (where.Or != null)
                foreach (ProductConfigWhere orFilter in where.Or)
                    mongoFilter = mongoFilter != FilterDefinition<ProductConfig>.Empty
                        ? mongoFilter | GetFilterDefinition(orFilter)
                        : GetFilterDefinition(orFilter);

            else if (where.And != null)
                foreach (ProductConfigWhere andFilter in where.And)
                    mongoFilter &= GetFilterDefinition(andFilter);

            else
                mongoFilter = mongoFilter.AddToFilterDefinition(where);

            return mongoFilter;
        }

        public static FilterDefinition<ProductConfig> AddToFilterDefinition(this FilterDefinition<ProductConfig> mongoFilter, ProductConfigWhere where)
        {
            if (where.Id != null)
                mongoFilter &= Builders<ProductConfig>.Filter.Eq(p => p.Id, where.Id);

            else if (where.ClientId != null)
                mongoFilter &= Builders<ProductConfig>.Filter.Eq(p => p.ClientId, where.ClientId);


            else if (where.CreatedAt_gt != null)
                mongoFilter &= Builders<ProductConfig>.Filter.Gt(c => c.CreatedAt, where.CreatedAt_gt);
            else if (where.CreatedAt_lt != null)
                mongoFilter &= Builders<ProductConfig>.Filter.Lt(c => c.CreatedAt, where.CreatedAt_gt);
            else if (where.LastModifiedAt_gt != null)
                mongoFilter &= Builders<ProductConfig>.Filter.Gt(c => c.LastModifiedAt, where.LastModifiedAt_gt);
            else if (where.LastModifiedAt_lt != null)
                mongoFilter &= Builders<ProductConfig>.Filter.Lt(c => c.LastModifiedAt, where.LastModifiedAt_lt);

            else if (where.CreatedById != null)
                mongoFilter &= Builders<ProductConfig>.Filter.Eq(p => p.CreatedById, where.CreatedById);
            else if (where.CreatedById_in != null)
                mongoFilter &= Builders<ProductConfig>.Filter.In(p => p.CreatedById, where.CreatedById_in);
            else if (where.LastModifiedById != null)
                mongoFilter &= Builders<ProductConfig>.Filter.Eq(p => p.LastModifiedById, where.LastModifiedById);
            else if (where.LastModifiedById_in != null)
                mongoFilter &= Builders<ProductConfig>.Filter.In(p => p.LastModifiedById, where.LastModifiedById_in);

            return mongoFilter;
        }
    }
}
