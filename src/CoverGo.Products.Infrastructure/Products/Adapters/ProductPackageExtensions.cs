using CoverGo.Products.Domain.Products;

namespace CoverGo.Products.Infrastructure.Products.Adapters
{
    public static class ProductPackageExtensions
    {
        public static string GetChecksum(this ProductPackage productPackage)
        {
            Checksum result = new();

            ProductMetadata productMetadata = productPackage.Metadata;

            result.Add(
                productMetadata?.ProductId?.Plan,
                productMetadata?.ProductId?.Type,
                "875a869f-7b60-418b-a136-9596329c8694",
                productMetadata?.ProductId?.Version
            );

            result.Add(productMetadata?.ProductLifecycleStage);
            result.Add(productMetadata?.ProductLastModifiedAt);
            result.Add(productMetadata?.ProductLastModifiedBy);
            result.Add(productMetadata?.Tenant);
            result.Add(productMetadata?.ExportedAt);
            result.Add(productMetadata?.ExportedBy);
            if (productMetadata?.IncludedEntries != null)
                result.Add(productMetadata.IncludedEntries);

            result.Add("e643638a-733c-4c7d-b980-13da430d1b1c");

            result.Add(
                Checksum.Compute(productPackage.ProductTreeContent),
                Checksum.Compute(productPackage.DataSchemaContent),
                "a0c7498e-216a-4aae-869d-fa00ac8cb672",
                Checksum.Compute(productPackage.UISchemaContent),
                Checksum.Compute(productPackage.ValidationScriptContent)
            );

            return result.ToChecksum();
        }
    }
}