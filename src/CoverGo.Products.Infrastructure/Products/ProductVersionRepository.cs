#nullable enable

using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.Multitenancy;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.Ports;

namespace CoverGo.Products.Infrastructure.Products;

public sealed class ProductVersionRepository(
    IProductRepository productRepository,
    TenantId tenantId): IProductVersionRepository
{
    public async Task<Product> Get(ProductId id, CancellationToken cancellationToken = default)
    {
        var products = await productRepository.GetAsync(
            tenantId.Value,
            new ProductWhere()
            {
                ProductId = new()
                {
                    Plan = id.Plan,
                    Type = id.Type,
                    Version = id.Version,
                }
            },
            null,
            new()
            {
                Limit = 1,
            },
            true,
            cancellationToken);
        return products.First();
    }

    public async Task<Product> Update(Product product, CancellationToken cancellationToken = default)
    {
        await productRepository.UpdateAsync(
            tenantId.Value,
            new()
            {
                ProductId = product.Id,
                OfferValidityPeriod = product.OfferValidityPeriod,
                IsOfferValidityPeriodChanged = true,
            },
            cancellationToken);
        return product;
    }
}
