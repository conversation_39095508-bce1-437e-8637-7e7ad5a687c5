using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using CoverGo.Products.Domain.Products;

namespace CoverGo.Products.Infrastructure.Products
{
    public static class RefGenArgumentExtensions
    {
        public static async Task<(string, int)> GetDBSProductsOfTodayCount(IProductRepository repo, IProductEventStore eventStore, ProductConfig productConfig, string tenantId, CreateProductCommand command)
        {
            var productIdWhere = new ProductWhere { ProductId = new ProductIdWhere { Type = command.ProductId.Type } };
            IEnumerable<Product> products = await repo.GetAsync(tenantId, productIdWhere, productConfig, new QueryParameters());
            IEnumerable<Product> productsWithSamePlanAndId = products?.Where(p => p.Id?.Plan == command.ProductId.Plan);
            if (productsWithSamePlanAndId?.Any() == true)
                return (productsWithSamePlanAndId.FirstOrDefault().IssuerProductId, 0);

            DateTime currentDate = DateTime.Now;
            DateTime startOfTodayInUtc = currentDate.Date.ToUniversalTime();
            string dateFormatted = currentDate.ToString("yyMMdd");

            IEnumerable<ProductEvent> @events = await eventStore.GetEventsAsync(
                tenantId,
                null,
                new ProductEventWhere
                {
                    Types_in = new List<ProductEventType> { ProductEventType.creation },
                    CreatedAt_gt = startOfTodayInUtc
                });

            IEnumerable<ProductEvent> eventsForNewPlans = @events.Where(e => e.Values.Value<string>("issuerProductId")?.Contains(dateFormatted) ?? false);

            int count = eventsForNewPlans.Count() + 1;

            return (null, count);
        }
    }
}
