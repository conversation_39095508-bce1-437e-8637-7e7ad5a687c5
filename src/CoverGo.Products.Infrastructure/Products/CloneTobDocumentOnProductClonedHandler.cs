using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.FileSystem.Client;
using CoverGo.Multitenancy;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.Events;

using MediatR;

namespace CoverGo.Products.Infrastructure.Products;

public class CloneTobDocumentOnProductClonedHandler(
    IFileSystemClient fileSystemClient,
    TenantId _tenantId) : INotificationHandler<ProductCloned>
{
    public async Task Handle(ProductCloned notification, CancellationToken cancellationToken)
    {
        Product originalProduct = notification.OriginalProduct;
        Product clonedProduct = notification.ClonedProduct;

        var sourcePath = $"products/{originalProduct.Id.Plan}/{originalProduct.Id.Type}/{originalProduct.Id.Version}/tob/";
        var targetPath = $"products/{clonedProduct.Id.Plan}/{clonedProduct.Id.Type}/{clonedProduct.Id.Version}/tob/";

        IReadOnlyList<ObjectSummary> sourceFiles = await ListSourceFilesAsync(sourcePath, cancellationToken);
        await CloneFilesAsync(sourceFiles, targetPath, cancellationToken);
    }

    private async Task<IReadOnlyList<ObjectSummary>> ListSourceFilesAsync(string sourcePath, CancellationToken cancellationToken)
    {
        var command = new ListFilesCommand
        {
            Path = sourcePath,
            AllowedPrefixes = null,
        };

        var result = await fileSystemClient.FileSystem_ListAsync(_tenantId.Value, bucketName: null, command, cancellationToken);
        if (result is null || !result.IsSuccess)
            return [];

        return result.Value?.ObjectSummaries ?? [];
    }

    private async Task CloneFilesAsync(IReadOnlyList<ObjectSummary> sourceFiles, string targetPath, CancellationToken cancellationToken)
    {
        if (sourceFiles == null || sourceFiles.Count == 0)
            return;

        // Process all files in parallel with concurrency control
        const int maxConcurrentOperations = 5;
        using var semaphore = new SemaphoreSlim(maxConcurrentOperations);
        
        Task[] concurrentTasks = sourceFiles.Where(file => file?.Key != null).Select(async file =>
        {
            await semaphore.WaitAsync(cancellationToken);
            try
            {
                string sourceKey = file.Key;
                string fileName = sourceKey.Substring(sourceKey.LastIndexOf('/') + 1);
                CopyFileCommand copyFileCommand = new()
                {
                    Key = file.Key,
                    NewKey = $"{targetPath}{fileName}",
                };
                
                await fileSystemClient.FileSystem_CopyAsync(_tenantId.Value, bucketName: null, copyFileCommand, cancellationToken);
            }
            finally
            {
                semaphore.Release();
            }
        }).ToArray();

        await Task.WhenAll(concurrentTasks);
    }
}