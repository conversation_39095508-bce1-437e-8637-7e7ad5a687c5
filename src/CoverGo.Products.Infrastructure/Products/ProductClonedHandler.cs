#nullable enable

using System;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.Multitenancy;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.Events;

using MediatR;

using Microsoft.Extensions.Logging;

namespace CoverGo.Products.Infrastructure.Products;

public class ProductClonedHandler(
    ProductService productService,
    TenantId tenantId,
    ILogger<ProductClonedHandler> logger) : INotificationHandler<ProductCloned>
{
    public async Task Handle(ProductCloned notification, CancellationToken cancellationToken)
    {
        var result = await productService.CreateAsync(
            tenantId.Value,
            new CreateProductCommand()
            {
                ProductId = notification.ClonedProduct.Id,
                LifecycleStage = notification.ClonedProduct.LifecycleStage,
                Representation = notification.ClonedProduct.Representation,
                Fields = notification.ClonedProduct.Fields,
                ScriptIds = notification.ClonedProduct.ScriptIds,
                TermsAndConditionsTemplateId = notification.ClonedProduct.TermsAndConditionsTemplateId,
                TermsAndConditionsJacketId = notification.ClonedProduct.TermsAndConditionsJacketId,
                RatingFactorsTable = notification.ClonedProduct.RatingFactorsTable,
                Segments = notification.ClonedProduct.Segments,
                PolicyIssuanceMethod = notification.ClonedProduct.PolicyIssuanceMethod,
                OfferValidityPeriod = notification.ClonedProduct.OfferValidityPeriod,
                AllowCustomProduct = notification.ClonedProduct.AllowCustomProduct,
                AttachedDocumentsIds = notification.ClonedProduct.AttachedDocumentsIds,
                AutoRenewal = notification.ClonedProduct.AutoRenewal,
                RenewalNotification = notification.ClonedProduct.RenewalNotification,
                TaxConfiguration = notification.ClonedProduct.TaxConfiguration,
            },
            cancellationToken);
        if (!result.IsSuccess)
        {
            logger.LogWarning("Errors occured during creating a product {@Errors}", result.Errors_2);
            throw new Exception("Errors occured during creating a product");
        }
    }
}
