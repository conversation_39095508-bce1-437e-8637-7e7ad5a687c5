#nullable enable

using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.Multitenancy;
using CoverGo.Products.Domain.Products.Ports;
using CoverGo.Products.Domain.Scripts;

using Microsoft.Extensions.Logging;

namespace CoverGo.Products.Infrastructure.Products;

public class ProductScriptsRepository(
    TenantId tenantId,
    ScriptService scriptService,
    ILogger<ProductScriptsRepository> logger) : IProductScriptsRepository
{
    public async Task<List<string>> CloneProductScripts(IReadOnlyCollection<string>? scriptIds, CancellationToken cancellationToken = default)
    {
        if (scriptIds is null || scriptIds.Count == 0)
        {
            return [];
        }

        logger.LogInformation("[CloneAsync] scriptIds to clone:{scriptIdsToClone}", string.Join(',', scriptIds));
        IEnumerable<string> cloneScriptsResult = await scriptService.CloneAsync(tenantId.Value, new CloneScriptCommand() { ScriptIds = scriptIds?.ToList() }, cancellationToken);
        cloneScriptsResult ??= [];
        logger.LogInformation("[CloneAsync] cloned scriptIds:{clonedScriptIds}", string.Join(',', cloneScriptsResult));
        return cloneScriptsResult.ToList();
    }
}
