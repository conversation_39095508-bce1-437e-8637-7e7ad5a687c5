#nullable enable

using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.L10n.Client;
using CoverGo.Multitenancy;
using CoverGo.Products.Domain.Products.Ports;
using CoverGo.Products.Infrastructure.GatewayClient;

using Microsoft.Extensions.Logging;

using StrawberryShake;
using Exception = System.Exception;
using ProductId = CoverGo.Products.Domain.Products.ProductId;

namespace CoverGo.Products.Infrastructure.Products;

public sealed class ProductNameRepository(
    IGatewayStrawberryShakeClient gatewayClient,
    IL10nClient l10NClient,
    TenantId tenantId,
    ILogger<ProductNameRepository> logger) : IProductNameRepository
{
    public async Task<string> Create(ProductId productId, string productName, CancellationToken cancellationToken = default)
    {
        // We have to use Gateway since L10N service doesn't check permissions
        var l10nResult = await gatewayClient.UpsertL10n.ExecuteAsync(
            new()
            {
                Key = $"products-{productId}-name",
                Locale = "en-US",
                Value = productName,
            },
            cancellationToken);
        l10nResult.EnsureNoErrors();
        if (l10nResult.Data!.UpsertL10n!.Errors_2?.Any() == true)
        {
            logger.LogWarning("Errors occured during inserting localization name {@Errors}", l10nResult.Data!.UpsertL10n!.Errors_2);
            throw new Exception("Errors occured during inserting localization name");
        }
        return productName;
    }

    public async Task<string?> Get(ProductId productId, CancellationToken cancellationToken = default)
    {
        // Reuses complex l10n logic from Gateway GetL10nAsync
        var result = await gatewayClient.GetProductsName.ExecuteAsync(new()
        {
            Plan = productId.Plan,
            Type = productId.Type,
            Version = productId.Version,
        });
        result.EnsureNoErrors();
        return result.Data?.Products_2?.List?.FirstOrDefault()?.Name;
    }

    public async Task<List<string?>> GetOrDefaultByIds(IReadOnlyList<ProductId> productIds, CancellationToken cancellationToken = default)
    {
        var l10nIds = productIds
            .SelectMany(productId => new List<string> {
                $"products-{productId}-name",
                $"products-{productId.Plan}-name"
            })
            .ToList();
        var results = await l10NClient.L10nAsync(
            tenantId.Value,
            !string.IsNullOrEmpty(CultureInfo.CurrentCulture.Name) ? CultureInfo.CurrentCulture.Name : "en-US",
            null,
            l10nIds,
            cancellationToken);
        var productNames = productIds
            .Select(productId => new string?[]
            {
                results.Result.TryGetValue($"products-{productId}-name", out var productNameById) ? productNameById : null,
                results.Result.TryGetValue($"products-{productId.Plan}-name", out var productNameByPlan) ? productNameByPlan : null
            }.FirstOrDefault(it => !string.IsNullOrEmpty(it)))
            .ToList();

        return productNames;
    }
}
