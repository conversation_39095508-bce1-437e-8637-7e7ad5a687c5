using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

using CoverGo.Products.Domain.BenefitDefinitions;
using CoverGo.Products.Domain.BenefitDefinitionTypes;
using CoverGo.Products.Domain.Insurers;
using CoverGo.Products.Domain.Jackets.EventStores;
using CoverGo.Products.Domain.Jackets.Repositories;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.Ports;
using CoverGo.Products.Domain.Scripts;
using CoverGo.Products.Domain.UiSchemas;
using CoverGo.Products.Infrastructure.BenefitDefinitions.Adapters.Mongo;
using CoverGo.Products.Infrastructure.BenefitDefinitionTypes.Adapters.Mongo;
using CoverGo.Products.Infrastructure.Insurers.Adapters.Mongo;
using CoverGo.Products.Infrastructure.Jackets.Adapters.Mongo;
using CoverGo.Products.Infrastructure.Products.Adapters.Mongo;
using CoverGo.Products.Infrastructure.Scripts.Adapters.Mongo;
using CoverGo.Products.Infrastructure.UiSchemas.Adapters.Mongo;

using JsonLogic.Net;

using Microsoft.Extensions.DependencyInjection;

namespace CoverGo.Products.Infrastructure.Products;

public static class ProductsInfraDIExtensions
{
    public static IServiceCollection AddProductsInfrastructure(this IServiceCollection services)
    {
        services
            .AddScoped<IProductNameRepository, ProductNameRepository>()
            .AddScoped<IProductVersionRepository, ProductVersionRepository>()
            .AddScoped<IProductScriptsRepository, ProductScriptsRepository>()
            .AddSingleton<IMongoDbContext, MongoDbContext>()
            .AddScoped<IProductRepository, MongoProductRepository>()
            .AddScoped<IReferenceGenerator, MongoDbReferenceGenerator>()
            .AddScoped<IScriptRepository, MongoScriptRepository>()
            .AddScoped<IBenefitDefinitionTypeRepository, MongoBenefitDefinitionTypeRepository>()
            .AddScoped<IBenefitDefinitionRepository, MongoBenefitDefinitionRepository>()
            .AddScoped<IProductEventStore, MongoProductEventStore>()
            .AddScoped<IInsurerRepository, MongoInsurerRepository>()
            .AddScoped<IInsurerEventStore, MongoInsurerEventStore>()
            .AddScoped<IJacketRepository, MongoDbJacketRepository>()
            .AddScoped<IEventStore<JacketEvent, JacketEventType>, MongoDbJacketEventStore>()
            .AddScoped<IUiSchemaRepository, MongoUiSchemaRepository>();

        services
            .AddSingleton(sp =>
            {
                EvaluateOperators operators = EvaluateOperators.Default;
                operators.DeleteOperator("missing");

                #region CustomMissingOperator //fixing library version not working
                operators.AddOperator("missing", (p, args, data) =>
                {
                    IEnumerable<object> names = args.Select(a => p.Apply(a, data));
                    if (names.Count() == 1 && names.First().IsEnumerable()) names = names.First().MakeEnumerable();
                    if (data == null) return names.ToArray();

                    string[] missingVariables = names.Select(n => n.ToString()).Where(n =>
                    {
                        try
                        {
                            return GetValueByName(data, n) == null; //line where the mistake was
                        }
                        catch
                        {
                            return true;
                        }
                    }).ToArray();

                    return missingVariables;
                });

                #endregion

                return new JsonLogicEvaluator(operators);
            });

        return services;
    }

    #region JsonLogicEvaluatorCode //copied from https://github.com/yavuztor/JsonLogic.Net/blob/master/JsonLogic.Net/EvaluateOperators.cs

    private static object GetValueByName(object data, string namePath)
    {
        if (string.IsNullOrEmpty(namePath)) return data;

        if (data == null) throw new ArgumentNullException(nameof(data));

        string[] names = namePath.Split('.');
        object d = data;
        foreach (string name in names)
        {
            if (d == null) return null;
            if (d.GetType().IsArray)
            {
                d = (d as Array).GetValue(int.Parse(name));
            }
            else if (DictionaryType(d) != null)
            {
                var type = DictionaryType(d);
                var prop = type.GetTypeInfo().DeclaredProperties.FirstOrDefault(p => p.Name == "Item");
                d = prop.GetValue(d, new object[] { name });
            }
            else if (d is IEnumerable<object>)
            {
                d = (d as IEnumerable<object>).Skip(int.Parse(name)).First();
            }
            else
            {
                var property = d.GetType().GetTypeInfo().GetDeclaredProperty(name);
                if (property == null) throw new ArgumentNullException(name);
                d = property.GetValue(d);
            }
        }
        return d;
    }

    private static Type DictionaryType(object d)
    {
        return d.GetType().GetTypeInfo().ImplementedInterfaces.FirstOrDefault(t => t.GetTypeInfo().IsGenericType && t.GetGenericTypeDefinition() == typeof(IDictionary<,>));
    }

    #endregion
}
