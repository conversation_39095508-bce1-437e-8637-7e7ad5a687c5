using System;
using CoverGo.Applications.Infrastructure;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.ProductImportHistory;
using MongoDB.Driver;

namespace CoverGo.Products.Infrastructure.ProductImportHistory.Adapters.Mongo;

public class MongoProductImportHistoryRepository : CoverGoGenericMongoRepositoryBase<ProductImportHistoryRecord, ProductImportHistoryRecord, ProductImportHistoryRecordFilter>, IProductImportHistoryRepository
{
    protected override string DbName => "products";

    protected override string CollectionNameGet(string tenantId) => $"{tenantId}-productImportHistory";

    protected override ProductImportHistoryRecord EntityCreate(ProductImportHistoryRecord create)
        => create;

    protected override UpdateDefinition<ProductImportHistoryRecord> EntityUpdate(ProductImportHistoryRecord update)
        => throw new NotSupportedException();

    protected override FilterDefinition<ProductImportHistoryRecord> FilterApplyInternal(FilterDefinition<ProductImportHistoryRecord> filter, Filter<ProductImportHistoryRecordFilter> @where)
    {
        if (where?.Where == null) return filter;
        if (where.Where.ProductId_in != null) return Builders<ProductImportHistoryRecord>.Filter.In(c => c.Package.ProductId, @where.Where.ProductId_in);
        return base.FilterApplyInternal(filter, @where);
    }
}