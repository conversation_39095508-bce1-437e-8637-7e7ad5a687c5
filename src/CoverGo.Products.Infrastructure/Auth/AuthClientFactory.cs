using System;
using System.Net.Http;
using CoverGo.Auth.Client;
using Microsoft.Extensions.Configuration;

namespace CoverGo.Products.Infrastructure.Auth
{
    public interface IAuthClientFactory
    {
        AuthClient CreateClientForTenant(string tenantId);
    }

    public class AuthClientFactory : IAuthClientFactory
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;

        public AuthClientFactory(IHttpClientFactory httpClientFactory, IConfiguration configuration)
        {
            _httpClientFactory = httpClientFactory;
            _configuration = configuration;
        }

        public AuthClient CreateClientForTenant(string tenantId)
        {
            HttpClient httpClient = _httpClientFactory.CreateClient(nameof(AuthClient));
            httpClient.BaseAddress = new Uri($"{_configuration["serviceUrls:auth"]}{tenantId}/");
            return new AuthClient(httpClient);
        }
    }
}