using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Auth.Client;
using CoverGo.Products.Domain.Auth;

namespace CoverGo.Products.Infrastructure.Auth.Adapters
{
    public class AuthAdapter : IAuthAdapter
    {
        private readonly IAuthClientFactory _authClientFactory;

        public AuthAdapter(IAuthClientFactory authClientFactory) => _authClientFactory = authClientFactory;

        public async Task<string[]> GetUsernamesByLoginIds(string tenantId, CancellationToken cancellationToken, params string[] loginIds)
        {
            AuthClient authClient = _authClientFactory.CreateClientForTenant(tenantId);

            Dictionary<string, string> usernames = new();
            foreach (string loginId in loginIds)
            {
                if (string.IsNullOrEmpty(loginId) || usernames.ContainsKey(loginId)) continue;
                Response<Login> response = await authClient.FromLoginIdAsync(loginId, appId: string.Empty, version: null, cancellationToken);
                string username = response?.Result?.Username;
                usernames.Add(loginId, username);
            }

            return loginIds.Select(id => string.IsNullOrEmpty(id) ? null : usernames[id]).ToArray();
        }
    }
}