using CoverGo.HttpUtils;
using CoverGo.Products.Domain.Scripts;
using CoverGo.Scripts.Client;
using Microsoft.Extensions.Logging;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Infrastructure.Scripts.Adapters.Evaluation;

public class ScriptAdapter : IScriptAdapter
{
    private readonly ILogger<ScriptAdapter> _logger;
    private readonly HttpClient _client;

    public ScriptAdapter(ILogger<ScriptAdapter> logger, HttpClient client)
    {
        _client = client;
        _logger = logger;
    }

    public async Task<ScriptResult> Evaluate(string tenantId, EvaluateScriptCommand command, CancellationToken cancellationToken)
    {
        ScriptResult res;
        if (command.Script.Type == ScriptTypeEnum.Pricing_standard)
        {
            _logger.LogInformation("ScriptAdapter - Evaluate V2 - Input: {@Input}", command);
            res = await _client.GenericPostAsync<ScriptResult, EvaluateScriptCommand>($"{tenantId}/api/v2/scripts", command, cancellationToken);
            _logger.LogInformation("ScriptAdapter - Evaluate V2 - Result: {@Result}", res);
        }
        else
        {

            _logger.LogInformation("ScriptAdapter - Evaluate V1 - Input: {@Input}", command);
            res = await _client.GenericPostAsync<ScriptResult, EvaluateScriptCommand>($"{tenantId}/api/v1/scripts", command, cancellationToken);
            _logger.LogInformation("ScriptAdapter - Evaluate V1 - Result: {@Result}", res);
        }
        return res;
    }

    public async Task<PricingResult> EvaluateStandardPricing(string tenantId, StandardPricingCommand command, CancellationToken cancellationToken)
    {
        _logger.LogInformation("ScriptAdapter - EvaluateStandardPricing V2 - Input: {@Input}", command);
        var res = await _client.GenericPostWithPricingResultConvertersAsync<PricingResult, StandardPricingCommand>($"{tenantId}/api/v2/pricing", command, cancellationToken);
        // serialize the PricingResult object to JSON string
        _logger.LogInformation("ScriptAdapter - EvaluateStandardPricing V2 - Result: {@Result}", res);
        try
        {
            var json = System.Text.Json.JsonSerializer.Serialize(res);
            _logger.LogInformation("ScriptAdapter - EvaluateStandardPricing V2 - Json Result: {@json}", json);
        }
        catch (System.Exception e)
        {
            _logger.LogError("ScriptAdapter - EvaluateStandardPricing V2 - Error: {@Error}", e);
        }
        return res;
    }

    public async Task<UnderwritingResult> EvaluateStandardUnderwriting(string tenantId, StandardUnderwritingCommand command, CancellationToken cancellationToken)
    {
        _logger.LogInformation("ScriptAdapter - EvaluateStandardUnderwriting V2 - Input: {@Input}", command);
        var res = await _client.GenericPostAsync<UnderwritingResult, StandardUnderwritingCommand>($"{tenantId}/api/v2/underwriting", command, cancellationToken);
        _logger.LogInformation("ScriptAdapter - EvaluateStandardUnderwriting V2 - Result: {@Result}", res);
        return res;
    }
}
