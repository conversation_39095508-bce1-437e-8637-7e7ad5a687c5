﻿using CoverGo.DomainUtils;
using CoverGo.HttpUtils;
using CoverGo.Products.Domain;
using CoverGo.Scripts.Client;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Serialization;
using System;
using System.Globalization;
using System.IO;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Infrastructure.Scripts.Adapters.Evaluation;

public static class ScriptHttpExtensions
{
    private static readonly JsonSerializer s_serializer;
    private static readonly JsonSerializerSettings s_settings;

    static ScriptHttpExtensions()
    {
        s_settings = new JsonSerializerSettings
        {
            TypeNameHandling = TypeNameHandling.None,
            ContractResolver = new CamelCasePropertyNamesContractResolver(),
            Converters = {
                   new BaseEffectConverter(),
                   new StringEnumConverter(),
                   new IssueConverter(),
                   new ScriptErrorConverter(),
                   new AdjustmentValueConverter()
            }
        };

        s_serializer = JsonSerializer.Create(s_settings);
    }

    public static async Task<T> GenericPostWithPricingResultConvertersAsync<T, U>(this HttpClient client, string uri, U command, CancellationToken cancellationToken = default)
    {
        using HttpResponseMessage response = await client.GenericSendAsync(HttpMethod.Post, uri, command, cancellationToken);
        if (!response.IsSuccessStatusCode)
            throw new ApiException
            {
                StatusCode = (int)response.StatusCode,
                Content = await response.Content.ReadAsStringAsync(cancellationToken)
            };

        return await response.Content.ReadAsJsonAsync<T>(cancellationToken);
    }

    private static async Task<HttpResponseMessage> GenericSendAsync<T>(this HttpClient httpClient, HttpMethod method, string url, T data, CancellationToken cancellationToken = default)
    {
        using var request = new HttpRequestMessage()
        {
            RequestUri = new Uri(httpClient.BaseAddress + url),
            Method = method,
            Content = GetContent(data)
        };
        request.Headers.AcceptLanguage.TryParseAdd(CultureInfo.CurrentCulture.Name);
        HttpResponseMessage response = await httpClient.SendAsync(request, cancellationToken);
        return response;
    }

    private static StringContent GetContent<T>(T data)
    {
        string dataAsString = JsonConvert.SerializeObject(data, s_settings);
        var content = new StringContent(dataAsString);
        content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
        return content;
    }

    private static async Task<T> ReadAsJsonAsync<T>(this HttpContent content, CancellationToken cancellationToken = default)
    {
        using Stream stream = await content.ReadAsStreamAsync(cancellationToken);
        return DeserializeFromStream<T>(stream);
    }

    private static T DeserializeFromStream<T>(Stream stream)
    {
        using var sr = new StreamReader(stream);
        using var jsonTextReader = new JsonTextReader(sr);
        return s_serializer.Deserialize<T>(jsonTextReader);
    }
}
