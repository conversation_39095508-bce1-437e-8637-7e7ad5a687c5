﻿using CoverGo.Configuration;
using CoverGo.DomainUtils;
using CoverGo.MongoUtils;
using CoverGo.Products.Domain.Scripts;
using CoverGo.Scripts.Client;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Infrastructure.Scripts.Adapters.Mongo
{
    public class MongoScriptRepository : IScriptRepository
    {
        const string DbName = "products";

        public string ProviderId { get; } = "mongoDb";

        public async Task<IReadOnlyCollection<Script>> GetAsync(string tenantId, ScriptWhere where, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<Script> collection = db.GetCollection<Script>($"{tenantId}-scripts");

            FilterDefinition<Script> filter = CreateFilter(where);

            return await collection.Find(filter).ToListAsync(cancellationToken);
        }

        public async Task<IEnumerable<string>> CloneAsync(string tenantId, CloneScriptCommand command, CancellationToken cancellationToken)
        {
            List<Script> scriptDao = (await GetAsync(tenantId, new ScriptWhere() { Id_in = command.ScriptIds }, cancellationToken)).ToList();

            if (scriptDao == null || !scriptDao.Any())
                throw new Exception("script not found");


            DateTime currentTime = DateTime.UtcNow;
            for (int index =0; index<scriptDao.Count; index++)
            {
                var script = scriptDao[index];
                script.Id = Guid.NewGuid().ToString();
                script.CreatedAt = currentTime;
                script.LastModifiedById = command.CreatedById;
                script.LastModifiedAt = currentTime;
            }

            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<Script> collection = db.GetCollection<Script>($"{tenantId}-scripts");

            await collection.InsertManyAsync(scriptDao, cancellationToken: cancellationToken);

            return scriptDao.Select(script => script.Id).ToList();
        }

            FilterDefinition<Script> CreateFilter(ScriptWhere where)
        {
            FilterDefinition<Script> result = Builders<Script>.Filter.Empty;
            if (where?.Or != null)
            {
                foreach (ScriptWhere orFilter in where.Or)
                {
                    result = result != FilterDefinition<Script>.Empty
                        ? result | CreateFilter(orFilter)
                        : CreateFilter(orFilter);
                }
            }
            else if (where?.And != null)
            {
                foreach (ScriptWhere andFilter in where.And)
                    result &= CreateFilter(andFilter);
            }
            else
            {
                if (where == null)
                    return result;
                if (where.Id != null)
                    return Builders<Script>.Filter.Eq(x => x.Id, where.Id);
                if (where.Id_in != null)
                    return Builders<Script>.Filter.In(x => x.Id, where.Id_in);
                if (where.Type != null)
                    return Builders<Script>.Filter.Eq(x => x.Type, where.Type);
                if (where.Id == null && where.Id_in == null)
                    return result;
            }

            return result;
        }

        public async Task<Result<CreatedStatus>> CreateAsync(string tenantId, string id, CreateScriptCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<Script> collection = db.GetCollection<Script>($"{tenantId}-scripts");
            await collection.Indexes.CreateOneAsync(new CreateIndexModel<Script>(Builders<Script>.IndexKeys.Ascending("Id")), cancellationToken: cancellationToken);

            var script = new Script
            {
                Id = id,
                Name = command.Name,
                InputSchema = command.InputSchema,
                OutputSchema = command.OutputSchema,
                SourceCode = command.SourceCode,
                ReferenceSourceCodeUrl = command.ReferenceSourceCodeUrl,
                Checksum = command.Checksum,
                ExternalTableDataUrl = command.ExternalTableDataUrl,
                ExternalTableDataUrls = command.ExternalTableDataUrls,
                Type = command.Type,
                LastModifiedById = command.CreatedById,
                LastModifiedAt = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow,
            };

            await collection.InsertOneAsync(script, cancellationToken: cancellationToken);

            return new Result<CreatedStatus> { Status = "success", Value = new CreatedStatus { Id = script.Id } };
        }

        public async Task<Result> UpdateAsync(string tenantId, UpdateScriptCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<Script> collection = db.GetCollection<Script>($"{tenantId}-scripts");

            FilterDefinition<Script> filter = Builders<Script>.Filter.Eq(p => p.Id, command.ScriptId);

            UpdateDefinitionBuilder<Script> update = Builders<Script>.Update;

            var updates = new List<UpdateDefinition<Script>>
            {
                update.Set(a => a.LastModifiedAt, DateTime.UtcNow),
                update.Set(a => a.LastModifiedById, command.ModifiedById)
            };
            if (command.Name != null) updates.Add(update.Set(a => a.Name, command.Name));
            if (command.InputSchema != null) updates.Add(update.Set(a => a.InputSchema, command.InputSchema));
            if (command.OutputSchema != null) updates.Add(update.Set(a => a.OutputSchema, command.OutputSchema));
            if (command.SourceCode != null) updates.Add(update.Set(a => a.SourceCode, command.SourceCode));
            if (command.ReferenceSourceCodeUrl != null) updates.Add(update.Set(a => a.ReferenceSourceCodeUrl, command.ReferenceSourceCodeUrl));
            if (command.ExternalTableDataUrl != null) updates.Add(update.Set(a => a.ExternalTableDataUrl, command.ExternalTableDataUrl));
            if (command.ExternalTableDataUrls != null) updates.Add(update.Set(a => a.ExternalTableDataUrls, command.ExternalTableDataUrls));
            if (command.Type != null) updates.Add(update.Set(a => a.Type, command.Type));
            if (command.Checksum != null) updates.Add(update.Set(x => x.Checksum, command.Checksum));

            UpdateResult result = await collection.UpdateOneAsync(filter, update.Combine(updates), cancellationToken: cancellationToken);

            return new Result { Status = result.MatchedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> DeleteAsync(string tenantId, DeleteCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "products"));
            IMongoDatabase db = client.GetDatabase(DbName);

            DeleteResult result = await db.GetCollection<Script>($"{tenantId}-scripts").DeleteOneAsync(Builders<Script>.Filter.Eq(p => p.Id, command.DeletedById), cancellationToken);

            return result.DeletedCount > 0
                ? new Result { Status = "success" }
                : new Result { Status = "failure", Errors = new List<string> { "Script with such id doesn't exist" } };
        }
    }
}