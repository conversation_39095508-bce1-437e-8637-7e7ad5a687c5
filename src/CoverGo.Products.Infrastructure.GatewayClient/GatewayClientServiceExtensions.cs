using Microsoft.Extensions.DependencyInjection;

namespace CoverGo.Quotation.Infrastructure.ProductsClient;

public static class GatewayClientServiceExtensions
{
    public static IHttpClientBuilder AddGatewayClient(this IServiceCollection services, string gatewayUrl)
    {
        services.AddGatewayStrawberryShakeClient();
        return services
            .AddHttpClient("GatewayStrawberryShakeClient", client =>
            {
                client.BaseAddress = new Uri(gatewayUrl);
            });
    }
}
