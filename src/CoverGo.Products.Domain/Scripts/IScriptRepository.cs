﻿using CoverGo.DomainUtils;
using CoverGo.Scripts.Client;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Domain.Scripts
{
    public interface IScriptRepository
    {
        public string ProviderId { get; }
        Task<IReadOnlyCollection<Script>> GetAsync(string tenantId, ScriptWhere filter, CancellationToken cancellationToken);
        Task<IEnumerable<string>> CloneAsync(string tenantId, CloneScriptCommand command, CancellationToken cancellationToken);
        Task<Result<CreatedStatus>> CreateAsync(string tenantId, string id, CreateScriptCommand command, CancellationToken cancellationToken);
        Task<Result> UpdateAsync(string tenantId, UpdateScriptCommand command, CancellationToken cancellationToken);
        Task<Result> DeleteAsync(string tenantId, DeleteCommand command, CancellationToken cancellationToken);
    }

    public class CreateScriptCommand
    {
        public string Name { get; set; }
        public string InputSchema { get; set; }
        public string OutputSchema { get; set; }
        public string SourceCode { get; set; }
        public string ReferenceSourceCodeUrl { get; set; }
        public string Checksum { get; set; }
        public string ExternalTableDataUrl { get; set; }
        public List<string> ExternalTableDataUrls { get; set; }
        public ScriptTypeEnum? Type { get; set; }
        public string CreatedById { get; set; }
    }

    public class UpdateScriptCommand
    {
        public string ScriptId { get; set; }
        public string Name { get; set; }
        public string InputSchema { get; set; }
        public string OutputSchema { get; set; }
        public string SourceCode { get; set; }
        public string ReferenceSourceCodeUrl { get; set; }
        public string Checksum { get; set; }
        public string ExternalTableDataUrl { get; set; }
        public List<string> ExternalTableDataUrls { get; set; }
        public ScriptTypeEnum? Type { get; set; }
        public string ModifiedById { get; set; }
    }

    public class CloneScriptCommand
    {
        public CoverGo.Products.Domain.Products.ProductId ProductId { get;set; }

        public List<string> ScriptIds { get; set; }
        public string CreatedById { get; set; }
    }
}