﻿using CoverGo.Scripts.Client;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Domain.Scripts;

public interface IScriptAdapter
{
    Task<ScriptResult> Evaluate(string tenantId, EvaluateScriptCommand command, CancellationToken cancellationToken);
    Task<PricingResult> EvaluateStandardPricing(string tenantId, StandardPricingCommand command, CancellationToken cancellationToken);
    Task<UnderwritingResult> EvaluateStandardUnderwriting(string tenantId, StandardUnderwritingCommand command, CancellationToken cancellationToken);
}