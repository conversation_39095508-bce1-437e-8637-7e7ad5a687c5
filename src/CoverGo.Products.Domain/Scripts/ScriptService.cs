using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.DomainUtils;
using CoverGo.FileSystem.Client;
using CoverGo.Scripts.Client;

namespace CoverGo.Products.Domain.Scripts
{
    public class ScriptService(IScriptRepository scriptRepository, IFileSystemClient fileSystemClient)
    {
        public async Task<IEnumerable<Script>> GetAsync(string tenantId, ScriptWhere where, CancellationToken cancellationToken)
        {
            return await scriptRepository.GetAsync(tenantId, where, cancellationToken);
        }

        public async Task<IEnumerable<string>> CloneAsync(string tenantId,CloneScriptCommand command, CancellationToken cancellationToken)
        {
            if (command.ScriptIds == null || !command.ScriptIds.Any())
                return await Task.FromResult(new List<string>());

            return await scriptRepository.CloneAsync(tenantId,command,cancellationToken);

        }

        public async Task<Result<CreatedStatus>> CreateAsync(string tenantId, CreateScriptCommand command, CancellationToken cancellationToken)
        {
            command.Checksum = Md5(await ResolveScriptSourceCode(tenantId, command, cancellationToken));
            return await scriptRepository.CreateAsync(tenantId, Guid.NewGuid().ToString(), command, cancellationToken);
        }


        public async Task<Result> UpdateAsync(string tenantId, UpdateScriptCommand command, CancellationToken cancellationToken)
        {
            command.Checksum = Md5(await ResolveScriptSourceCode(tenantId, command, cancellationToken));
            return await scriptRepository.UpdateAsync(tenantId, command, cancellationToken);
        }
            

        public Task<Result> DeleteAsync(string tenantId, DeleteCommand command, CancellationToken cancellationToken)
        {
            return scriptRepository.DeleteAsync(tenantId, command, cancellationToken);
        }

        private async Task<string> ResolveScriptSourceCode(string tenantId, dynamic script, CancellationToken cancellationToken)
        {
            if (!string.IsNullOrEmpty(script.SourceCode)) return script.SourceCode;
            if (string.IsNullOrEmpty(script.ReferenceSourceCodeUrl)) return string.Empty;
            try
            {
                ResultOfByteOf file = await fileSystemClient.FileSystem_GetAsync(tenantId, null, new GetFileCommand { Key = script.ReferenceSourceCodeUrl }, cancellationToken);
                return Encoding.UTF8.GetString(file.Value);
            }
            catch
            {
                return string.Empty;
            }
        }

        private static string Md5(string str)
        {
            string md5Out = string.Empty;
            using (var md5 = MD5.Create())
            {
                byte[] md5In = Encoding.UTF8.GetBytes(str);
                byte[] bytesMd5Out = md5.ComputeHash(md5In);

                md5Out = BitConverter.ToString(bytesMd5Out);
                md5Out = md5Out.Replace("-", "");
            }
            return md5Out;
        }
    }
}