﻿using CoverGo.Scripts.Client;
using System.Collections.Generic;

namespace CoverGo.Products.Domain.Scripts
{
    public class ScriptWhere
    {
        public IEnumerable<ScriptWhere> Or { get; set; }
        public IEnumerable<ScriptWhere> And { get; set; }
        public string Id { get; set; }
        public List<string> Id_in { get; set; }
        public ScriptTypeEnum? Type { get; set; }
    }
}