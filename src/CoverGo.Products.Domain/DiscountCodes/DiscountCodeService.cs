﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Applications.Domain;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Products;

namespace CoverGo.Products.Domain.DiscountCodes;

public class DiscountCodeService : CoverGoGenericDataServiceBase<DiscountCode, DiscountCodeUpsert, DiscountCodeFilter, IDiscountCodeRepository>, IDiscountCodeService
{
    private readonly IDiscountCodeRepository _repository;
    
    public DiscountCodeService(IDiscountCodeRepository repository) : base(repository)
    {
        _repository = repository;
    }

    public override async Task<Result> BatchAsync(string tenantId, EntityBatch<DiscountCodeUpsert, DiscountCodeUpsert, DiscountCodeUpsert> batch, CancellationToken cancellationToken = default)
    {
        Result validateCreateInputsResult = await ValidateCreateInputs(tenantId, batch.Create, cancellationToken);
        if (!validateCreateInputsResult.IsSuccess)
            return validateCreateInputsResult;
        
        Result validateUpdateInputsResult = await ValidateUpdateInputs(tenantId, batch.Update, cancellationToken);
        if (!validateUpdateInputsResult.IsSuccess)
            return validateUpdateInputsResult;
        
        Result validateDeleteInputsResult = await ValidateDeleteInputs(tenantId, batch.Delete, cancellationToken);
        if (!validateDeleteInputsResult.IsSuccess)
            return validateDeleteInputsResult;
        
        return await base.BatchAsync(tenantId, batch, cancellationToken);
    }

    private async Task<Result> ValidateDeleteInputs(string tenantId, List<DiscountCodeUpsert> delete, CancellationToken cancellation)
    {
        if (delete == null || !delete.Any()) return Result.Success();
        
        var existingCodesMapper = (await _repository.QueryAsync(tenantId,
            new QueryArguments<Filter<DiscountCodeFilter>>
            {
                Where = new Filter<DiscountCodeFilter>
                {
                    Where = new DiscountCodeFilter
                    {
                        Id_in = delete.Select(x => x.Id).ToList()
                        
                    }
                }
            }, cancellation)).ToDictionary(x => x.Id);
            
        var existingCodes = delete.Select(x => existingCodesMapper[x.Id]).ToList();
        var unableToDeleteCodes = existingCodes.Where(x => x.IsActive() || x.IsExpired()).ToList();
        
        if(!unableToDeleteCodes.Any())
            return Result.Success();
       
        IEnumerable<string> errors = unableToDeleteCodes.Select(x =>
            $"The discount code with id {x.Id} is active/expired so it can't be deleted.");
        return Result.Failure(errors);
    }

    private async Task<Result> ValidateUpdateInputs(string tenantId, List<DiscountCodeUpsert> update, CancellationToken cancellation)
    {
        if (update == null || !update.Any()) return Result.Success();
        
        var existingCodesMapper = (await _repository.QueryAsync(tenantId,
            new QueryArguments<Filter<DiscountCodeFilter>>
            {
                Where = new Filter<DiscountCodeFilter>
                {
                    Where = new DiscountCodeFilter
                    {
                        Id_in = update.Select(x => x.Id).ToList()
                    
                    }
                }
            }, cancellation)).ToDictionary(x => x.Id);

        update.RemoveAll(x => !existingCodesMapper.ContainsKey(x.Id));

        var existingCodes = update.Select(x => existingCodesMapper[x.Id]).ToList();
        var unableToUpdateCodes = existingCodes.Where(x => x.IsActive() || x.IsExpired()).ToList();
        if(unableToUpdateCodes.Any())
        {
            IEnumerable<string> errors = unableToUpdateCodes.Select(x =>
                $"The discount code with id {x.Id} is active/expired so it can't be updated.");
            return Result.Failure(errors);
        }
        
        var codes = update.Select(x => x.Name).ToList();
        bool isAllCodeUnique = await CheckCodesUnique(tenantId, codes, cancellation);
        
        return isAllCodeUnique ? Result.Success(): Result.Failure("Duplicating discount code(s).");
    }

    private async Task<Result> ValidateCreateInputs(string tenantId,  List<DiscountCodeUpsert> create, CancellationToken cancellation)
    {
        if (create == null || !create.Any()) return Result.Success();
        
        var codes = create.Select(x => x.Name).ToList();
        bool isAllCodeUnique = await CheckCodesUnique(tenantId, codes, cancellation);
        
        return isAllCodeUnique ? Result.Success(): Result.Failure("Duplicating discount code(s).");
    }

    public async Task<Result> AddEligibleProducts(string tenantId, DiscountCodeProductsUpsert discountCodeProductsUpsert,
        CancellationToken cancellation)
    {
        DiscountCode code = (await _repository.QueryAsync(tenantId,
            new QueryArguments<Filter<DiscountCodeFilter>>
            {
                Where = new Filter<DiscountCodeFilter>
                {
                    Where = new DiscountCodeFilter
                    {
                        Id = discountCodeProductsUpsert.DiscountCodeId
                        
                    }
                }
            }, cancellation)).FirstOrDefault();
        
        if(code == null)
            return Result.Failure("Discount code id not found.");
        
        if(code.IsExpired())
            return Result.Failure("Can't add products into an expired discount code.");

        code.ProductIds ??= new List<ProductId>();
        discountCodeProductsUpsert.ProductIds.RemoveAll(x => code.ProductIds.Contains(x));
        code.ProductIds.AddRange(discountCodeProductsUpsert.ProductIds);

        return await _repository.UpdateAsync(tenantId, new DiscountCodeUpsert { Id = code.Id, ProductIds = code.ProductIds }, cancellation);
    }
    
    public async Task<Result> RemoveEligibleProducts(string tenantId, DiscountCodeProductsUpsert discountCodeProductsUpsert,
        CancellationToken cancellation)
    {
        DiscountCode code = (await _repository.QueryAsync(tenantId,
            new QueryArguments<Filter<DiscountCodeFilter>>
            {
                Where = new Filter<DiscountCodeFilter>
                {
                    Where = new DiscountCodeFilter
                    {
                        Id = discountCodeProductsUpsert.DiscountCodeId
                        
                    }
                }
            }, cancellation)).FirstOrDefault();
        
        if(code == null)
            return Result.Failure("Discount code id not found.");
        
        if(code.IsExpired() || code.IsActive())
            return Result.Failure("Can't remove products from an expired/active discount code.");
        
        code.ProductIds.RemoveAll(x => discountCodeProductsUpsert.ProductIds.Contains(x));

        return await _repository.UpdateAsync(tenantId, new DiscountCodeUpsert {Id = code.Id,  ProductIds = code.ProductIds }, cancellation);
    }

    public override async Task<Result> DeleteAsync(string tenantId, DiscountCodeUpsert delete, CancellationToken cancellation = default)
    {
        DiscountCode code = (await _repository.QueryAsync(tenantId,
            new QueryArguments<Filter<DiscountCodeFilter>>
            {
                Where = new Filter<DiscountCodeFilter>
                {
                    Where = new DiscountCodeFilter
                    {
                        Id = delete.Id
                        
                    }
                }
            }, cancellation)).FirstOrDefault();
        
        if(code == null)
            return Result.Success();

        if (code.IsActive() || code.IsExpired()) return Result.Failure("Can't delete an active/expired discount code.");

        return await base.DeleteAsync(tenantId, delete, cancellation);
    }

    public override async Task<Result> UpdateAsync(string tenantId, DiscountCodeUpsert update, CancellationToken cancellation = default)
    {
        DiscountCode code = (await _repository.QueryAsync(tenantId,
            new QueryArguments<Filter<DiscountCodeFilter>>
            {
                Where = new Filter<DiscountCodeFilter>
                {
                    Where = new DiscountCodeFilter
                    {
                        Id = update.Id
                        
                    }
                }
            }, cancellation)).FirstOrDefault();
        
        if(code == null)
            return Result.Failure("Discount code id not found.");
        
        if(code.IsExpired() || code.IsActive())
            return Result.Failure("An expired/active discount code can't be updated");

        if (!(await CheckCodesUnique(tenantId, new List<string> {update.Name}, cancellation))) 
            return Result.Failure("Duplicating discount code(s)");
        
        return await base.UpdateAsync(tenantId, update, cancellation);
    }

    public async Task<Result> ApplyCodeAsync(string tenantId, string code, CancellationToken cancellationToken)
    {
        IReadOnlyCollection<DiscountCode> discountCodes = await QueryAsync(tenantId,
                new QueryArguments<Filter<DiscountCodeFilter>>()
                {
                    Where = new Filter<DiscountCodeFilter>()
                    {
                        And = new List<Filter<DiscountCodeFilter>>()
                        {
                            new() { Where = new DiscountCodeFilter() { Name = code } }
                        }
                    }
                }, cancellationToken);

        if (!discountCodes.Any())
        {
            return Result.Failure("Invalid discount code");
        }

        var discountCode = discountCodes.FirstOrDefault();

        if (discountCode.IsExpired())
        {
            return Result.Failure("Discount code is expired or reaches redemption limit");
        }

        if (discountCode.RedemptionLimit.HasValue)
        {
            return await UpdateAsync(tenantId, new DiscountCodeUpsert
            {
                Id = discountCode.Id,
                RedemptionLimit = --discountCode.RedemptionLimit
            }, cancellationToken);
        }

        return Result.Success();
    }

    private async Task<bool> CheckCodesUnique(string tenantId, List<string> codes, CancellationToken cancellation)
    {
        if (codes == null || !codes.Any()) return true;

        long count = await _repository.CountAsync(tenantId,
            new QueryArguments<Filter<DiscountCodeFilter>>
            {
                Where = new Filter<DiscountCodeFilter>
                {
                    Where = new DiscountCodeFilter
                    {
                        Name_in = codes
                    }
                }
            }, cancellation);
        
        return count == 0;
    }
}