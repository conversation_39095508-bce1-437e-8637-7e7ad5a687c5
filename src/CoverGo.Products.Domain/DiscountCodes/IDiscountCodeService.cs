﻿using System.Threading;
using System.Threading.Tasks;
using CoverGo.Applications.Domain;
using CoverGo.DomainUtils;

namespace CoverGo.Products.Domain.DiscountCodes;

public interface IDiscountCodeService : ICoverGoGenericDataService<DiscountCode, DiscountCodeUpsert, DiscountCodeFilter>
{
    Task<Result> AddEligibleProducts(string tenantId, DiscountCodeProductsUpsert discountCodeProductsUpsert,
        CancellationToken cancellation);
    Task<Result> RemoveEligibleProducts(string tenantId, DiscountCodeProductsUpsert discountCodeProductsUpsert,
        CancellationToken cancellation);

    Task<Result> ApplyCodeAsync(string tenantId, string code, CancellationToken cancellationToken);
}