﻿using System;
using System.Collections.Generic;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Products;

namespace CoverGo.Products.Domain.DiscountCodes;

public class DiscountCode : SystemObject, IUniqueSystemObject
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public string ProductTypeId { get; set; }
    public DiscountType Type { get; set; }
    public decimal Value { get; set; }
    public DateTime? ValidFrom { get; set; }
    public DateTime? ValidTo { get; set; }
    public int? RedemptionLimit { get; set; }
    public List<ProductId> ProductIds { get; set; }
    
    public bool IsActive()
    {
        DateTime now = DateTime.UtcNow;
        return ValidFrom.HasValue && ValidFrom <= now && (ValidTo.HasValue
            && now <= ValidTo || !ValidTo.HasValue);
    }
    
    public bool IsExpired() => (ValidTo.HasValue && DateTime.UtcNow > ValidTo) || (RedemptionLimit.HasValue && RedemptionLimit <= 0);
}

public class DiscountCodeUpsert : IUniqueSystemObject
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public string ProductTypeId { get; set; }
    public DiscountType? Type { get; set; }
    public decimal? Value { get; set; }
    public DateTime? ValidFrom { get; set; }
    public DateTime? ValidTo { get; set; }
    public int? RedemptionLimit { get; set; }
    public List<ProductId> ProductIds { get; set; }
    public string ById { get; set; }
}

public class DiscountCodeProductsUpsert
{
    public string DiscountCodeId { get; set; }
    public List<ProductId> ProductIds { get; set; }
    public string ById { get; set; }
}

public class DiscountCodeApply
{
    public string DiscountCode { get; set; }
}

public class DiscountCodeFilter
{
    [FilterCondition(FilterCondition.Eq)]
    public string Id { get; set; }
    [FilterCondition(FilterCondition.In, nameof(DiscountCode.Id))]
    public List<string> Id_in { get; set; }
    [FilterCondition(FilterCondition.Eq)]
    public string Name { get; set; }
    [FilterCondition(FilterCondition.Contains, nameof(DiscountCode.Name))]
    public string Name_contains { get; set; }
    [FilterCondition(FilterCondition.In, nameof(DiscountCode.Name))]
    public List<string> Name_in { get; set; }
    [FilterCondition(FilterCondition.Eq)]
    public string ProductTypeId { get; set; }
    [FilterCondition(FilterCondition.In, nameof(DiscountCode.ProductTypeId))]
    public List<string> ProductTypeId_in { get; set; }
    [FilterCondition(FilterCondition.Eq)]
    public DiscountType? Type { get; set; }
    
    [FilterCondition(FilterCondition.Gt, nameof(DiscountCode.ValidFrom))]
    public DateTime? ValidFrom_gt { get; set; }
    [FilterCondition(FilterCondition.Lt, nameof(DiscountCode.ValidFrom))]
    public DateTime? ValidFrom_lt { get; set; }
    [FilterCondition(FilterCondition.Gte, nameof(DiscountCode.ValidFrom))]
    public DateTime? ValidFrom_gte { get; set; }
    [FilterCondition(FilterCondition.Lte, nameof(DiscountCode.ValidFrom))]
    public DateTime? ValidFrom_lte { get; set; }
    
    [FilterCondition(FilterCondition.Gt, nameof(DiscountCode.ValidTo))]
    public DateTime? ValidTo_gt { get; set; }
    [FilterCondition(FilterCondition.Lt, nameof(DiscountCode.ValidTo))]
    public DateTime? ValidTo_lt { get; set; }
    [FilterCondition(FilterCondition.Gte, nameof(DiscountCode.ValidTo))]
    public DateTime? ValidTo_gte { get; set; }
    [FilterCondition(FilterCondition.Lte, nameof(DiscountCode.ValidTo))]
    public DateTime? ValidTo_lte { get; set; }
    [FilterCondition(FilterCondition.Exists, nameof(DiscountCode.ValidTo))]
    public bool? ValidTo_exists { get; set; }
    
    [FilterCondition(FilterCondition.Exists, nameof(DiscountCode.ProductIds))]
    public bool? ProductIds_exists { get; set; }
    
    public List<ProductId> ProductIds_in { get; set; }
    public ProductIdWhere ProductIds_contains { get; set; }
}

public enum DiscountType
{
    AMOUNT,
    PERCENTAGE
}

