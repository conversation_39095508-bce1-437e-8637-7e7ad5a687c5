﻿using System.Collections.Generic;
using CoverGo.DomainUtils;

namespace CoverGo.Products.Domain.UiSchemas
{
    public class UiSchemaWhere : Where
    {
        public IReadOnlyCollection<UiSchemaWhere> Or { get; set; }
        public IReadOnlyCollection<UiSchemaWhere> And { get; set; }
        public string Id { get; set; }
        public string Name { get; set; }
        public IReadOnlyCollection<string> Name_in { get; set; }
        public IReadOnlyCollection<string> Id_in { get; set; }
        public FieldsWhere Schema { get; set; }
    }
}
