﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.DomainUtils;

namespace CoverGo.Products.Domain.UiSchemas
{
    public interface IUiSchemaService
    {
        Task<Result<CreatedStatus>> CreateAsync(string tenantId, CreateUiSchemaCommand command, CancellationToken cancellationToken);

        Task<IEnumerable<UiSchema>> GetAsync(string tenantId, UiSchemaWhere where, CancellationToken cancellationToken);

        Task<Result> UpdateAsync(string tenantId, UpdateUiSchemaCommand command, CancellationToken cancellationToken);

        Task<Result> DeleteAsync(string tenantId, string uiSchemaId, DeleteCommand command, CancellationToken cancellationToken);
    }
}
