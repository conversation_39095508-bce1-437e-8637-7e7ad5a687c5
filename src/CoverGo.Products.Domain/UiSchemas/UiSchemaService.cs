using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.DomainUtils;

namespace CoverGo.Products.Domain.UiSchemas
{
    public class UiSchemaService(IUiSchemaRepository uiSchemaRepository) : IUiSchemaService
    {
        public Task<Result<CreatedStatus>> CreateAsync(string tenantId, CreateUiSchemaCommand command, CancellationToken cancellationToken)
        {
            return uiSchemaRepository.CreateAsync(tenantId, Guid.NewGuid().ToString(), command, cancellationToken);
        }

        public async Task<IEnumerable<UiSchema>> GetAsync(string tenantId, UiSchemaWhere where, CancellationToken cancellationToken)
        {
            return await uiSchemaRepository.GetAsync(tenantId, where, cancellationToken);
        }

        public Task<Result> UpdateAsync(string tenantId, UpdateUiSchemaCommand command, CancellationToken cancellationToken)
        {
            return uiSchemaRepository.UpdateAsync(tenantId, command, cancellationToken);
        }

        public Task<Result> DeleteAsync(string tenantId, string uiSchemaId, DeleteCommand command, CancellationToken cancellationToken)
        {
            return uiSchemaRepository.DeleteAsync(tenantId, uiSchemaId, command, cancellationToken);
        }
    }
}
