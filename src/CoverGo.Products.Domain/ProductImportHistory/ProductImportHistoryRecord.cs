using System;
using System.Collections.Generic;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Products;

namespace CoverGo.Products.Domain.ProductImportHistory;

public class ProductImportHistoryRecord : IUniqueSystemObject
{
    public string Id { get; set; }
    public DateTime ImportedAt { get; set; }
    public string ImportedById { get; set; }
    public string ImportedBy { get; set; }
    public ProductImportHistoryPackage Package { get; set; }
}

public class ProductImportHistoryPackage
{
    public ProductId ProductId { get; set; }
    public string ProductName { get; set; }
    public string ProductLifecycleStage { get; set; }
    public DateTime? ProductLastModifiedAt { get; set; }
    public string ProductLastModifiedBy { get; set; }
    public string Tenant { get; set; }
    public DateTime ExportedAt { get; set; }
    public string ExportedBy { get; set; }
    public IReadOnlyCollection<string> IncludedEntries { get; set; }
    public string RequestId { get; set; }
    public string RelativeFilePath { get; set; }
}

public class ProductImportHistoryRecordFilter
{
    public List<ProductId> ProductId_in { get; set; }
}