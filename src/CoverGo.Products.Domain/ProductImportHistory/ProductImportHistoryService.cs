using System.Threading;
using System.Threading.Tasks;
using CoverGo.Applications.Domain;
using CoverGo.DomainUtils;

namespace CoverGo.Products.Domain.ProductImportHistory;

public class ProductImportHistoryService : CoverGoGenericDataServiceBase<ProductImportHistoryRecord, ProductImportHistoryRecord, ProductImportHistoryRecordFilter, IProductImportHistoryRepository>, IProductImportHistoryService
{
    private readonly IProductImportHistoryRepository _repository;

    public ProductImportHistoryService(IProductImportHistoryRepository repository)
        : base(repository) => _repository = repository;

    public override Task<Result<CreatedStatus>> CreateAsync(string tenantId, ProductImportHistoryRecord create, CancellationToken cancellation = default)
        => base.CreateAsync(tenantId, create, cancellation);
}