using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.DomainUtils;
using CoverGo.Products.Domain.BenefitDefinitions;

namespace CoverGo.Products.Domain.BenefitDefinitionTypes
{
    public class BenefitDefinitionTypeService(IBenefitDefinitionTypeRepository benefitDefinitionTypeRepository)
    {
        public async Task<IEnumerable<BenefitDefinitionType>> GetBenefitDefinitionTypeAsync(string tenantId, BenefitTypeQueryArguments queryArguments, CancellationToken cancellationToken)
        {
            return await benefitDefinitionTypeRepository.GetBenefitDefinitionTypeAsync(tenantId, queryArguments.Where, queryArguments.OrderBy, queryArguments.Skip, queryArguments.First, cancellationToken);
        }

        public async Task<Result<CreatedStatus>> CreateBenefitDefinitionTypeAsync(string tenantId, CreateBenefitDefinitionTypeCommand command, CancellationToken cancellationToken)
        {
            return await IsBusinessIdTaken(tenantId, command.BusinessId, cancellationToken)
                ? Result<CreatedStatus>.Failure("This BusinessId is taken already")
                : await benefitDefinitionTypeRepository.CreateBenefitDefinitionTypeAsync(tenantId, Guid.NewGuid().ToString(), command, cancellationToken);
        }

        public Task<Result> UpdateBenefitDefinitionTypeAsync(string tenantId, UpdateBenefitDefinitionTypeCommand command, CancellationToken cancellationToken)
        {
            return benefitDefinitionTypeRepository.UpdateBenefitDefinitionTypeAsync(tenantId, command, cancellationToken);
        }

        public Task<Result> DeleteBenefitDefinitionTypeAsync(string tenantId, string benefitDefinitionTypeId, DeleteCommand command, CancellationToken cancellationToken)
        {
            return benefitDefinitionTypeRepository.DeleteBenefitDefinitionTypeAsync(tenantId, benefitDefinitionTypeId, command, cancellationToken);
        }

        public async Task<Result> BatchBenefitDefinitionTypeAsync(string tenantId, BatchBenefitDefinitionTypeCommand command, CancellationToken cancellationToken)
        {
            if (await IsBusinessIdTaken(tenantId,
                command.CreateBenefitDefinitionTypeCommands.Select(x => x.BusinessId).ToList(), cancellationToken))
            {
                return Result.Failure("Some of BusinessIds are taken already");
            }

            return await benefitDefinitionTypeRepository.BatchBenefitDefinitionTypeAsync(tenantId, command, cancellationToken);
        }

        async Task<bool> IsBusinessIdTaken(string tenantId, string businessId, CancellationToken cancellationToken)
        {
            return (await benefitDefinitionTypeRepository.GetBenefitDefinitionAsync(tenantId, new BenefitDefinitionWhere { BusinessId_in = new List<string> { businessId } }, cancellationToken)).Any()
            || (await benefitDefinitionTypeRepository.GetBenefitDefinitionTypeAsync(tenantId, new BenefitDefinitionTypeWhere { BusinessId_in = new List<string> { businessId } }, cancellationToken: cancellationToken)).Any();
        }

        async Task<bool> IsBusinessIdTaken(string tenantId, List<string> businessIds,
            CancellationToken cancellationToken)
        {
            if (!businessIds.Any())
            {
                return false;
            }

            return (await benefitDefinitionTypeRepository.GetBenefitDefinitionAsync(tenantId, new BenefitDefinitionWhere { BusinessId_in = businessIds }, cancellationToken: cancellationToken)).Any()
                   || (await benefitDefinitionTypeRepository.GetBenefitDefinitionTypeAsync(tenantId, new BenefitDefinitionTypeWhere { BusinessId_in = businessIds }, cancellationToken: cancellationToken)).Any()
                   || businessIds.Count != businessIds.Distinct().Count();
        }
    }
}
