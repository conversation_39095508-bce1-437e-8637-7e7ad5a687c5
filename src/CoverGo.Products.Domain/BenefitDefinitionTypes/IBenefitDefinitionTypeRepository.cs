﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.BenefitDefinitions;

namespace CoverGo.Products.Domain.BenefitDefinitionTypes
{
    public interface IBenefitDefinitionTypeRepository
    {
        string ProviderId { get; }
        Task<IReadOnlyCollection<BenefitDefinitionType>> GetBenefitDefinitionTypeAsync(string tenantId, BenefitDefinitionTypeWhere where, OrderBy orderBy = null, int? skip = null, int? first = null, CancellationToken cancellationToken = default);
        Task<IReadOnlyCollection<BenefitDefinition>> GetBenefitDefinitionAsync(string tenantId, BenefitDefinitionWhere filter, CancellationToken cancellationToken);
        Task<Result<CreatedStatus>> CreateBenefitDefinitionTypeAsync(string tenantId, string id, CreateBenefitDefinitionTypeCommand command, CancellationToken cancellationToken);
        Task<Result> UpdateBenefitDefinitionTypeAsync(string tenantId, UpdateBenefitDefinitionTypeCommand command, CancellationToken cancellationToken);
        Task<Result> DeleteBenefitDefinitionTypeAsync(string tenantId, string benefitDefinitionTypeId, DeleteCommand command, CancellationToken cancellationToken);
        Task<Result> BatchBenefitDefinitionTypeAsync(string tenantId, BatchBenefitDefinitionTypeCommand command, CancellationToken cancellationToken);
    }
}
