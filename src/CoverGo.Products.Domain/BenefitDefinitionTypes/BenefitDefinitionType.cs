﻿using System.Collections.Generic;
using CoverGo.DomainUtils;
using HotChocolate;
using Newtonsoft.Json.Linq;

namespace CoverGo.Products.Domain.BenefitDefinitionTypes
{
    public class BenefitDefinitionType : SystemObject
    {
        public string Id { get; set; }
        public string BusinessId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        [GraphQLIgnore]
        public JToken Fields { get; set; }
    }

    public class CreateBenefitDefinitionTypeCommand
    {
        public string BusinessId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string CreatedById { get; set; }
        public string Status { get; set; }
        public string Fields { get; set; }
    }

    public class UpdateBenefitDefinitionTypeCommand
    {
        public string BenefitDefinitionTypeId { get; set; }
        public string BusinessId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string ModifiedById { get; set; }
        public string Status { get; set; }
        public string Fields { get; set; }
    }

    public class BatchBenefitDefinitionTypeCommand
    {
        public List<CreateBenefitDefinitionTypeCommand> CreateBenefitDefinitionTypeCommands { get; set; } =
            new List<CreateBenefitDefinitionTypeCommand>();

        public List<UpdateBenefitDefinitionTypeCommand> UpdateBenefitDefinitionTypeCommands { get; set; } =
            new List<UpdateBenefitDefinitionTypeCommand>();
    }

    public class BenefitTypeQueryArguments
    {
        public BenefitDefinitionTypeWhere Where { get; set; }
        public OrderBy OrderBy { get; set; }
        public int? First { get; set; }
        public int? Skip { get; set; }
    }

    public class BenefitDefinitionTypeWhere : Where
    {
        public IEnumerable<BenefitDefinitionTypeWhere> Or { get; set; }
        public IEnumerable<BenefitDefinitionTypeWhere> And { get; set; }
        public List<string> Id_in { get; set; }
        public List<string> BusinessId_in { get; set; }
        public string BusinessId_contains { get; set; }
        public string Name_contains { get; set; }
        public string Status { get; set; }
        public FieldsWhere Fields { get; set; }
    }
}
