﻿using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Products;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Domain.Illustrations
{
    public interface IIllustrationRepository
    {
        string ProviderId { get; }

        Task<Result<IEnumerable<Illustration>>> GetIllustrationsAsync(string tenantId, ProductWhere filter, JToken factors, CancellationToken cancellationToken);
    }
}
