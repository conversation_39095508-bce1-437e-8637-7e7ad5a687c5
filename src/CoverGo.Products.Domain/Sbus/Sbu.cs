using System;
using System.Collections.Generic;
using CoverGo.DomainUtils;

namespace CoverGo.Products.Domain.Sbus
{
    public class Sbu : SystemObject, IUniqueSystemObject
    {
        public string Id { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal Amount { get; set; }
    }

    public class SbuUpsert : IUniqueSystemObject
    {
        public string Id { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public decimal? Amount { get; set; }
        public string ById { get; set; }
    }

    public class SbuFilter
    {
        [FilterCondition(FilterCondition.Eq)]
        public string Id { get; set; }
        [FilterCondition(FilterCondition.In, nameof(Sbu.Id))]
        public List<string> Id_in { get; set; }
    }
}