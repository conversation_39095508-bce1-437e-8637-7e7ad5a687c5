﻿using CoverGo.DomainUtils;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Threading;
using System.Linq;
using CoverGo.Applications.Domain;

namespace CoverGo.Products.Domain.Sbus
{
    public class SbuService : CoverGoGenericDataServiceBase<Sbu, SbuUpsert, SbuFilter, ISbuRepository>, ISbuService
    {
        private readonly ISbuRepository _repository;

        public SbuService(ISbuRepository repository) : base(repository)
        {
            _repository = repository;
        }

        public override async Task<Result<CreatedStatus>> CreateAsync(
            string tenantId,
            SbuUpsert create,
            CancellationToken cancellation = default)
        {
            if (create.StartDate.Value.Date <= DateTime.UtcNow.Date ||
                create.StartDate.Value.Date >= create.EndDate.Value.Date)
            {
                return Result<CreatedStatus>
                    .Failure($"StartDate must be greater than {DateTime.UtcNow.Date} and less than EndDate");
            }

            if (create.Amount.Value <= 0)
            {
                return Result<CreatedStatus>
                    .Failure($"Amount must be greater than zero");
            }

            var sbus = await QueryAsync(tenantId, null, cancellation);

            if (IsOverlapping(sbus, create.StartDate.Value.Date, create.EndDate.Value.Date))
            {
                return Result<CreatedStatus>
                    .Failure($"Period from StartDate to EndDate must not overlap with existing SBUs");
            }

            return await base.CreateAsync(tenantId, create, cancellation);
        }

        private bool IsOverlapping(IEnumerable<Sbu> sbus, DateTime startDate, DateTime endDate)
        {
            return sbus.Any(s =>
                (s.StartDate.Date <= startDate && startDate <= s.EndDate.Date) ||
                (s.StartDate.Date <= endDate && endDate <= s.EndDate.Date) ||
                (startDate <= s.StartDate.Date && s.EndDate.Date <= endDate)
            );
        }

        public override async Task<Result> UpdateAsync(
            string tenantId,
            SbuUpsert update,
            CancellationToken cancellation = default)
        {
            if (update.Amount <= 0)
            {
                return Result
                    .Failure($"Amount must be greater than zero");
            }

            var sbus = await QueryAsync(
                tenantId,
                where: null,
                cancellation: cancellation);

            var sbu = sbus.FirstOrDefault(s => s.Id == update.Id);

            if (sbu == null)
            {
                return Result.Failure($"SBU not found");
            }

            if (sbu.EndDate.Date <= DateTime.UtcNow.Date)
            {
                return Result.Failure($"SBU EndDate is in the past and can not be updated");
            }

            if (update.StartDate?.Date <= DateTime.UtcNow.Date)
            {
                return Result.Failure($"StartDate must be `null` or greater than {DateTime.UtcNow.Date}");
            }

            var newStartDate = update.StartDate?.Date ?? sbu.StartDate.Date;
            var newEndDate = update.EndDate?.Date ?? sbu.EndDate.Date;

            if (newStartDate >= newEndDate)
            {
                return Result.Failure($"StartDate must be less than EndDate");
            }

            if (IsOverlapping(sbus.Where(s => s.Id != update.Id), newStartDate, newEndDate))
            {
                return Result.Failure($"Period from StartDate to EndDate must not overlap with existing SBUs");
            }

            return await base.UpdateAsync(tenantId, update, cancellation);
        }

        public override async Task<Result> DeleteAsync(string tenantId, SbuUpsert delete, CancellationToken cancellation = default)
        {
            if (delete.Id == null)
            {
                return Result.Failure($"Id must not be null");
            }

            var sbus = await QueryAsync(
                tenantId,
                where: new QueryArguments<Filter<SbuFilter>>()
                {
                    Where = new Filter<SbuFilter>()
                    {
                        Where = new SbuFilter
                        {
                            Id_in = new List<string> { delete.Id },
                        }
                    }
                },
                cancellation: cancellation);

            var sbu = sbus.FirstOrDefault();

            if (sbu == null)
            {
                return Result.Failure($"SBU not found");
            }

            if (sbu.StartDate.Date <= DateTime.UtcNow.Date)
            {
                return Result.Failure($"SBU StartDate is in the past and can not be deleted");
            }

            return await base.DeleteAsync(tenantId, delete, cancellation);
        }
    }
}