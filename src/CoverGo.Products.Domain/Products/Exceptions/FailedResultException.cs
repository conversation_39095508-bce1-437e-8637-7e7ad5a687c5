using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;

namespace CoverGo.Products.Domain.Products
{
    [Serializable]
    public class FailedResultException : Exception
    {
        private readonly string[] _errors;

        public FailedResultException(IEnumerable<string> errors) => _errors = errors.ToArray();

        protected FailedResultException(SerializationInfo info, StreamingContext context) : base(info, context) { }

        public string[] Errors => _errors;
    }
}