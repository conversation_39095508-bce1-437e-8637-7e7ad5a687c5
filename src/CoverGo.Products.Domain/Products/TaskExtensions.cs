using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.FileSystem.Client;

namespace CoverGo.Products.Domain.Products
{
    public static class TaskExtensions
    {
        public static async Task<T> EnsureSuccess<T>(this Task<Result<T>> task)
        {
            Result<T> result = await task;
            if (!result.IsSuccess) throw new FailedResultException(result.Errors);
            return result.Value;
        }

        public static async Task EnsureSuccess(this Task<Result> task)
        {
            Result result = await task;
            if (!result.IsSuccess) throw new FailedResultException(result.Errors);
        }
    }
}