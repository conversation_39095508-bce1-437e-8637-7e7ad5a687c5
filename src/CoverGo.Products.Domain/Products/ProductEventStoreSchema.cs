﻿using System;
using System.Collections.Generic;


namespace CoverGo.Products.Domain.Products
{
    public class ProductEventStoreSchema
    {
        public List<string> Children { get; set; }
        public string Ref { get; set; }
        public string Alias { get; set; }
        public bool IsCreated { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsNodeTypeDefinition { get; set; }
        public Fields Fields { get; set; }
        public NodeLockStatus LockStatus { get; set; }
    }
    public class Commissions
    {
        public string Ref { get; set; }
        public string Alias { get; set; }
        public string Type { get; set; }
        public Resolver Resolver { get; set; }
    }

    public class CommissionsSum
    {
        public string Ref { get; set; }
        public string Alias { get; set; }
        public string Type { get; set; }
        public Resolver Resolver { get; set; }
    }

    public class Coverage
    {
        public string Ref { get; set; }
        public string Alias { get; set; }
        public string Type { get; set; }
        public Resolver Resolver { get; set; }
    }

    public class NodeCurrencyCode
    {
        public string Ref { get; set; }
        public string Alias { get; set; }
        public string Type { get; set; }
        public Resolver Resolver { get; set; }
    }

    public class Description
    {
        public string Ref { get; set; }
        public string Alias { get; set; }
        public string Type { get; set; }
        public Resolver Resolver { get; set; }
    }

    public class Discounts
    {
        public string Ref { get; set; }
        public string Alias { get; set; }
        public string Type { get; set; }
        public Resolver Resolver { get; set; }
    }

    public class DiscountsSum
    {
        public string Ref { get; set; }
        public string Alias { get; set; }
        public string Type { get; set; }
        public Resolver Resolver { get; set; }
    }

    public class Excesses
    {
        public string Ref { get; set; }
        public string Alias { get; set; }
        public string Type { get; set; }
        public Resolver Resolver { get; set; }
    }

    public class ExcessesSum
    {
        public string Ref { get; set; }
        public string Alias { get; set; }
        public string Type { get; set; }
        public Resolver Resolver { get; set; }
    }

    public class Fees
    {
        public string Ref { get; set; }
        public string Alias { get; set; }
        public string Type { get; set; }
        public Resolver Resolver { get; set; }
    }

    public class FeesSum
    {
        public string Ref { get; set; }
        public string Alias { get; set; }
        public string Type { get; set; }
        public Resolver Resolver { get; set; }
    }

    public class Fields
    {
        public Description description { get; set; }
        public NodeCurrencyCode currencyCode { get; set; }
        public GrossPrice grossPrice { get; set; }
        public Taxes taxes { get; set; }
        public TaxesSum taxesSum { get; set; }
        public Loadings loadings { get; set; }
        public LoadingsSum loadingsSum { get; set; }
        public Fees fees { get; set; }
        public FeesSum feesSum { get; set; }
        public Commissions commissions { get; set; }
        public CommissionsSum commissionsSum { get; set; }
        public Discounts discounts { get; set; }
        public DiscountsSum discountsSum { get; set; }
        public Excesses excesses { get; set; }
        public ExcessesSum excessesSum { get; set; }
        public NetPrice netPrice { get; set; }
        public ModalNetPrice modalNetPrice { get; set; }
        public Pricing pricing { get; set; }
        public Coverage coverage { get; set; }
        public NodeUnderwriting underwriting { get; set; }
        public ProductMeta meta { get; set; }
    }

    public class GrossPrice
    {
        public string Ref { get; set; }
        public string Alias { get; set; }
        public string Type { get; set; }
        public Resolver Resolver { get; set; }
    }

    public class Loadings
    {
        public string Ref { get; set; }
        public string Alias { get; set; }
        public string Type { get; set; }
        public Resolver Resolver { get; set; }
    }

    public class LoadingsSum
    {
        public string Ref { get; set; }
        public string Alias { get; set; }
        public string Type { get; set; }
        public Resolver Resolver { get; set; }
    }

    public class NodeLockStatus
    {
        public bool IsLocked { get; set; }
        public string lockedById { get; set; }
        public DateTime ExpiresAt { get; set; }
    }

    public class ProductMeta
    {
        public string Ref { get; set; }
        public string Type { get; set; }
        public Resolver Resolver { get; set; }
    }

    public class ModalNetPrice
    {
        public string Ref { get; set; }
        public string Alias { get; set; }
        public string Type { get; set; }
        public Resolver Resolver { get; set; }
    }

    public class NetPrice
    {
        public string Ref { get; set; }
        public string Alias { get; set; }
        public string Type { get; set; }
        public Resolver Resolver { get; set; }
    }

    public class Pricing
    {
        public string Ref { get; set; }
        public string Alias { get; set; }
        public string Type { get; set; }
        public Resolver Resolver { get; set; }
    }

    public class Resolver
    {
        public string Text { get; set; }
        public string Language { get; set; }
    }

    

    public class Taxes
    {
        public string Ref { get; set; }
        public string Alias { get; set; }
        public string Type { get; set; }
        public Resolver Resolver { get; set; }
    }

    public class TaxesSum
    {
        public string Ref { get; set; }
        public string Alias { get; set; }
        public string Type { get; set; }
        public Resolver Resolver { get; set; }
    }

    public class NodeUnderwriting
    {
        public string Ref { get; set; }
        public string Alias { get; set; }
        public string Type { get; set; }
        public Resolver Resolver { get; set; }
    }
}
