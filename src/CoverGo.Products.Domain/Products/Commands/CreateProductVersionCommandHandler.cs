#nullable enable

using System.Threading;
using System.Threading.Tasks;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Multitenancy;

namespace CoverGo.Products.Domain.Products.Commands;

public sealed record CreateProductVersionCommandResult
{
    public required ProductId ClonedProductId { get; init; }

    public required string ClonedProductName { get; init; }
}

public interface ICreateProductVersionCommand
{
    ProductId OriginalProductId { get; }

    bool IsReadonly { get; }
}

public sealed record CreateProductVersionCommand : ICommand<CreateProductVersionCommandResult>, ICreateProductVersionCommand
{
    public required ProductId OriginalProductId { get; set; }

    public required bool IsReadonly { get; set; }
}

public sealed class CreateProductVersionCommandHandler(
    ProductService productService,
    TenantId tenantId) : ICommandHandler<CreateProductVersionCommand, CreateProductVersionCommandResult>
{
    public async Task<CreateProductVersionCommandResult> Handle(CreateProductVersionCommand request, CancellationToken cancellationToken)
    {
       var result = await productService.CloneProduct(request, tenantId:tenantId, cancellationToken:cancellationToken);
       return new CreateProductVersionCommandResult { ClonedProductId = result.ClonedProduct.Id, ClonedProductName = result.ClonedProductName};
    }
}
