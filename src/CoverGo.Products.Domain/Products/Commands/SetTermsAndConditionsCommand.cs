using System;

namespace CoverGo.Products.Domain.Products.Commands;

public class SetTermsAndConditionsCommand
{
    public ProductId ProductId { get; set; }
    [Obsolete("Represents clauses directly added to product. Instead, add clauses to jacket and use `JacketId` property.")]
    public string? TemplateId { get; set; } 
    public string? JacketId { get; set; }
    public string SetById { get; set; }
}