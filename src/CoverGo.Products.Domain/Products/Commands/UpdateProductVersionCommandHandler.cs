#nullable enable

using System;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Products.Domain.Products.Ports;
using CoverGo.SettableValues;

namespace CoverGo.Products.Domain.Products.Commands;

public sealed record UpdateProductVersionCommand : ICommand<Product>
{
    public required ProductId ProductId { get; set; }

    public required Settable<TimeSpan?>? OfferValidityPeriod { get; set; }
}

public sealed class UpdateProductVersionCommandHandler(
    IProductVersionRepository productVersionRepository) : ICommandHandler<UpdateProductVersionCommand, Product>
{
    public async Task<Product> Handle(UpdateProductVersionCommand request, CancellationToken cancellationToken)
    {
        var product = await productVersionRepository.Get(request.ProductId, cancellationToken);
        if (request.OfferValidityPeriod is not null)
        {
            product.OfferValidityPeriod = request.OfferValidityPeriod.Value;
        }
        await productVersionRepository.Update(product, cancellationToken);
        return product;
    }
}
