#nullable enable

using System.Threading;
using System.Threading.Tasks;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;

namespace CoverGo.Products.Domain.Products.Commands;

public sealed record CloneProductVersionCommandResult
{
    public required Product ClonedProduct { get; set; }

    public required string ClonedProductName { get; set; }
}

public sealed record CloneProductVersionCommand : ICommand<CloneProductVersionCommandResult>, ICreateProductVersionCommand
{
    public required ProductId OriginalProductId { get; set; }

    public required string CloneProductVersion { get; set; }

    public required bool IsReadonly { get; set; }
}

public sealed class CloneProductVersionCommandHandler(
    ProductService productService) : ICommandHandler<CloneProductVersionCommand, CloneProductVersionCommandResult>
{
    public Task<CloneProductVersionCommandResult> Handle(CloneProductVersionCommand request, CancellationToken cancellationToken)
    => productService.CloneProduct(request, request.CloneProductVersion, cancellationToken:cancellationToken);
}
