namespace CoverGo.Products.Domain.Products
{
    public static class ProductExchangeValidationMessages
    {
        public static ProductImportValidationMessage NoProduct() => new ProductImportValidationMessage
        {
            Level = ProductImportValidationMessageLevel.Error,
            Code = "product_not_found"
        };

        public static ProductImportValidationMessage DifferentLifecycleStages() => new ProductImportValidationMessage
        {
            Level = ProductImportValidationMessageLevel.Warning,
            Code = "different_lifecycle_stages"
        };

        public static ProductImportValidationMessage ProductTreeExists() => new ProductImportValidationMessage
        {
            Level = ProductImportValidationMessageLevel.Warning,
            Entry = EntryNames.ProductTreeEntryName,
            Code = "product_tree_exists"
        };

        public static ProductImportValidationMessage DataSchemaExists() => new ProductImportValidationMessage
        {
            Level = ProductImportValidationMessageLevel.Warning,
            Entry = EntryNames.DataSchemaEntryName,
            Code = "data_schema_exists"
        };

        public static ProductImportValidationMessage UISchemaExists() => new ProductImportValidationMessage
        {
            Level = ProductImportValidationMessageLevel.Warning,
            Entry = EntryNames.UISchemaEntryName,
            Code = "ui_schema_exists"
        };

        public static ProductImportValidationMessage ValidationScriptExists() => new ProductImportValidationMessage
        {
            Level = ProductImportValidationMessageLevel.Warning,
            Entry = EntryNames.ValidationScriptEntryName,
            Code = "validation_script_exists"
        };

        public static ProductImportValidationMessage LockedProduct(string lockedBy) => new ProductImportValidationMessage
        {
            Level = ProductImportValidationMessageLevel.Error,
            Code = "locked_product",
            Meta = new ProductImportValidationMessageMeta
            {
                LockedBy = lockedBy
            }
        };

        public static ProductImportValidationMessage ExternalTableExists(string externalTable) => new ProductImportValidationMessage
        {
            Level = ProductImportValidationMessageLevel.Warning,
            Entry = externalTable,
            Code = "external_table_exists"
        };
    }
}