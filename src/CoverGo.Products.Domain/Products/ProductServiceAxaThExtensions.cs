using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using Newtonsoft.Json;

namespace CoverGo.Products.Domain.Products;

public static class ProductServiceAxaThExtensions
{
    public static async Task<Result> ValidateAxaThPdtIdOnCreate(this ProductService _, string tenantId, CreateProductCommand command,
        IProductRepository repository, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(command.Fields) || command.ProductId.Version != "1.0") return Result.Success();
        string pdtId = JsonConvert.DeserializeObject<AxaThProductFields>(command.Fields).PdtId?.Trim();

        if (string.IsNullOrWhiteSpace(pdtId))
            return Result.Success();

        ProductConfig config = await repository.GetProductConfigAsync(tenantId, null, cancellationToken);
        bool isPdtIdUnique = (await repository.GetTotalCountAsync(tenantId, GetAxaThPdtIdFilter(pdtId), config, cancellationToken)) == 0;

        if (!isPdtIdUnique)
            return PdtIdAlreadyExistsResult();
        return Result.Success();
    }

    public static async Task<Result> ValidateAxaThPdtIdOnUpdate(this ProductService _, string tenantId, UpdateProductCommand command,
        IProductRepository repository, CancellationToken cancellationToken)
    {
        if ((string.IsNullOrWhiteSpace(command.Fields) && string.IsNullOrWhiteSpace(command.FieldsPatch)) || command.ProductId.Version != "1.0")
            return Result.Success();

        string pdtId = string.IsNullOrWhiteSpace(command.Fields)
            ? string.Empty
            : JsonConvert.DeserializeObject<AxaThProductFields>(command.Fields).PdtId?.Trim();

        if (string.IsNullOrWhiteSpace(pdtId))
        {
            var productFieldsPatch = JsonConvert.DeserializeObject<FieldsPatchObject[]>(command.FieldsPatch);
            pdtId = Array.Find(productFieldsPatch, f => f.Path.Equals("/pdtID", StringComparison.OrdinalIgnoreCase))?
                .Value?.ToString()?.Trim();
        }

        if (string.IsNullOrWhiteSpace(pdtId))
            return Result.Success();

        ProductConfig config = await repository.GetProductConfigAsync(tenantId, null, cancellationToken);
        bool isPdtIdUpdated = await repository.GetTotalCountAsync(tenantId, new ProductWhere
        {
            And = new List<ProductWhere>
            {
                GetAxaThPdtIdFilter(pdtId),
                new ProductWhere
                {
                    ProductId = new ProductIdWhere
                    {
                        Plan = command.ProductId.Plan,
                        Type = command.ProductId.Type,
                        Version = command.ProductId.Version
                    }
                }
            }
        }, config, cancellationToken) == 0;

        if (!isPdtIdUpdated) return Result.Success();

        bool isPdtIdUnique = await repository.GetTotalCountAsync(tenantId, GetAxaThPdtIdFilter(pdtId), config, cancellationToken) == 0;

        if (!isPdtIdUnique)
            return PdtIdAlreadyExistsResult();

        return Result.Success();
    }

    private static ProductWhere GetAxaThPdtIdFilter(string pdtId) =>
        new ProductWhere
        {
            Fields = new FieldsWhere
            {
                Condition = FieldsWhereCondition.Equals,
                Path = "fields.pdtID",
                Value = new ScalarValue() { StringValue = pdtId }
            }
        };

    private static Result PdtIdAlreadyExistsResult() => Result.Failure("This pdtID already exists");
}

public class AxaThProductFields
{
    [JsonProperty("pdtID")]
    public string? PdtId { get; set; }
}

public class FieldsPatchObject
{
    public string? Op { get; set; }
    public string? Path { get; set; }
    public object? Value { get; set; }
}