using System.Collections.Generic;

namespace CoverGo.Products.Domain.Products.ProductTreeParsing;

public class ProductRepresentationNodeProps
{
    public string? Theme { get; set; }
    public string? Id { get; set; }
    public string? Name { get; set; }
    public string? FormulaName { get; set; }
    public string? Path { get; set; }
    public object? Value { get; set; }
    public string? TableName { get; set; }

    public List<ProductRepresentationNodePropsOption>? Options { get; set; }
}

public class ProductRepresentationNodePropsOption
{
    public string? Name { get; set; }
    public string? Value { get; set; }
}
