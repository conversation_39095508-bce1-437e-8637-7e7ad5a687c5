using System.Collections.Generic;
using System.Linq;

using Newtonsoft.Json.Linq;

namespace CoverGo.Products.Domain.Products.ProductTreeParsing;

public static class NodeLibrary
{
    public static List<ProductRepresentationNode> GetNodesLibrary(
        string productType,
        string productCurrency,
        string productPremiumBaseFrequency
    )
    {
        List<ProductRepresentationNode> overwriteNodesLibrary = GenerateOverwriteNodesLibrary(
            productCurrency
        );

        List<ProductRepresentationNode> nodesLibrary = MergeNodes(
            overwriteNodesLibrary,
            GenerateBaseNodesLibrary(productCurrency, productPremiumBaseFrequency)
        );

        // Ex: generate nodesLibrary of tenant and product type "gm"
        var gmOverwriteNodesLibrary = new List<ProductRepresentationNode>();
        List<ProductRepresentationNode> gmNodesLibrary = MergeNodes(
            gmOverwriteNodesLibrary,
            nodesLibrary
        );

        if (productType == "gm")
            return gmNodesLibrary;
        return gmNodesLibrary;
    }

    public static List<ProductRepresentationNode> MergeNodes(
        List<ProductRepresentationNode> overwriteNodes = null,
        List<ProductRepresentationNode> baseNodes = null
    )
    {
        overwriteNodes = overwriteNodes ?? [];
        baseNodes = baseNodes ?? [];

        var mergedNodes = new List<ProductRepresentationNode>(baseNodes);
        foreach (ProductRepresentationNode overwriteNode in overwriteNodes)
        {
            int foundIndex = mergedNodes.FindIndex(node => node.Name == overwriteNode.Name);
            if (foundIndex != -1)
            {
                mergedNodes[foundIndex] = overwriteNode;
            }
            else
            {
                if (overwriteNode.Index.HasValue)
                {
                    mergedNodes.Insert(overwriteNode.Index.Value, overwriteNode);
                }
                else
                {
                    mergedNodes.Add(overwriteNode);
                }
            }
        }
        return mergedNodes;
    }

    private static readonly List<ProductRepresentationNodeParameterPropsOption> LimitTypes =
    [
        new() { Name = "Amount Limit", Value = "amountLimit" },
        new() { Name = "Number Limit", Value = "numberLimit" },
        new() { Name = "Formula", Value = "formula" },
    ];

    private static readonly List<ProductRepresentationNodeParameterPropsOption> WaitingPeriodTypes =

        [
            new() { Name = "Amount", Value = "amount" },
            new() { Name = "Formula", Value = "formula" },
        ];

    private static readonly List<ProductRepresentationNodeParameterPropsOption> NumberLimitUnitOptions =
        [
            new() { Name = "Visit", Value = "visit" },
            new() { Name = "Day", Value = "day" },
            new() { Name = "Per Pair", Value = "perPair" },
            new() { Name = "Per Limb", Value = "perLimb" },
            new() { Name = "Per Item", Value = "perItem" },
            new() { Name = "Per Family", Value = "perFamily" },
            new() { Name = "Per Exam", Value = "perExam" },
            new() { Name = "Units", Value = "units" },
            new() { Name = "Tooth", Value = "tooth" },
            new() { Name = "Per Arch", Value = "perArch" },
        ];

    private static readonly List<ProductRepresentationNodeParameterPropsOption> PerTimeUnitOptions =

        [
            new() { Name = "Per Visit", Value = "perVisit" },
            new() { Name = "Per Day", Value = "perDay" },
            new() { Name = "Per Night", Value = "perNight" },
            new() { Name = "Per Year", Value = "perYear" },
            new() { Name = "Per Attempt", Value = "perAttempt" },
            new() { Name = "Per Lifetime", Value = "perLifetime" },
            new() { Name = "Per Period of Cover", Value = "perPeriodOfCover" },
            new() { Name = "Per 2 Years", Value = "per2Years" },
            new() { Name = "Per 3 Years", Value = "per3Years" },
            new() { Name = "Per 4 Years", Value = "per4Years" },
            new() { Name = "Per 5 Years", Value = "per5Years" },
            new() { Name = "12 rolling months", Value = "12rollingMonths" },
        ];

    private static readonly List<ProductRepresentationNodeParameterPropsOption> VariableUnits =
    [
        new() { Name = "Per Disability", Value = "perDisability" },
        new() { Name = "Per Surgery", Value = "perSurgery" },
        new() { Name = "Per Pregnancy", Value = "perPregnancy" },
        new() { Name = "Per Delivery", Value = "perDelivery" },
        new() { Name = "Per Accident", Value = "perAccident" },
        new() { Name = "Per Medical Condition", Value = "perMedicalCondition" },
    ];

    private static readonly List<ProductRepresentationNodeParameterPropsOption> NetworkTypes =
    [
        new() { Name = "In-Network", Value = "inNetwork" },
        new() { Name = "Out-of-Network", Value = "outOfNetwork" },
        new() { Name = "In-Network and Out-of-Network", Value = "inNetworkAndOutOfNetwork" },
    ];

    private static readonly List<ProductRepresentationNodeParameterPropsOption> CoPaymentsTypes =
    [
        new() { Name = "Amount", Value = "coPaymentAmount" },
        new() { Name = "Formula", Value = "coPaymentFormula" },
    ];

    private static readonly List<ProductRepresentationNodeParameterPropsOption> UnitPricesTypes =
    [
        new() { Name = "Amount", Value = "unitPriceAmount" },
        new() { Name = "Formula", Value = "unitPriceFormula" },
    ];

    private static readonly List<ProductRepresentationNodeParameterPropsOption> LimitPerVariableOptions =
        VariableUnits
            .Concat(
                new List<ProductRepresentationNodeParameterPropsOption>
                {
                    new() { Name = "Per Life", Value = "perLife" },
                }
            )
            .ToList();

    private static readonly List<ProductRepresentationNodeParameterPropsOption> GroupedBenefitLimitTypes =

        [
            new() { Name = "Amount Limit", Value = "amountLimit" },
            new() { Name = "Number Limit", Value = "numberLimit" },
            new() { Name = "Formula", Value = "formula" },
        ];

    private static readonly List<ProductRepresentationNodeParameterPropsOption> PremiumBaseFrequencyOptions =

        [
            new() { Name = "Monthly", Value = "MONTHLY" },
            new() { Name = "Quarterly", Value = "QUARTERLY" },
            new() { Name = "Annual", Value = "ANNUALLY" },
            new() { Name = "Semi-Annual", Value = "SEMI_ANNUALLY" },
            new() { Name = "Single Payment", Value = "SINGLE_PAYMENT" },
        ];

    private static readonly List<ProductRepresentationNodeParameterPropsOption> DeductibleTimeUnits =

        [
            new() { Name = "Per lifetime", Value = "perLifetime" },
            new() { Name = "Per year", Value = "perYear" },
        ];

    private static readonly List<ProductRepresentationNodeParameterPropsOption> DeductibleTypes =
    [
        new() { Name = "Amount", Value = "amount" },
        new() { Name = "Formula", Value = "formula" },
    ];

    private static List<ProductRepresentationNode> GenerateOverwriteNodesLibrary(
        string productCurrency
    )
    {
        return
        [
            new("#A40F0F")
            {
                Name = "limit",
                Label = "Limit",
                Editor = new ProductRepresentationNodeEditor { ComponentName = "LimitNodeEditor" },
                Props = new ProductRepresentationNodeProps
                {
                    Theme = "#A40F0F",
                    Id = "",
                    Name = "",
                },
                Parameters =
                [
                    new()
                    {
                        Name = "limitType",
                        Label = "",
                        Component = "Radios",
                        Value = "amountLimit",
                        DataType = "string",
                        Display = "radio",
                        Props = new ProductRepresentationNodeParameterProps
                        {
                            Options = LimitTypes,
                        },
                    },
                    new()
                    {
                        Name = "formula",
                        Label = "Formula",
                        Component = "Formula",
                        Value = new List<object>(),
                        DataType = "formula",
                        Display = "formula",
                    },
                    new()
                    {
                        Name = "amount",
                        Label = "Amount",
                        Component = "Number",
                        Value = null,
                        DataType = "number",
                        Display = "amountLimit",
                        Props = new ProductRepresentationNodeParameterProps
                        {
                            Theme = "Enter amount",
                        },
                    },
                    new()
                    {
                        Name = "currency",
                        Label = "Currency",
                        Component = "Select",
                        Value = productCurrency,
                        DataType = "string",
                        Display = "amountLimit",
                        Props = GenerateCurrencyNodeProps("limit"),
                    },
                    new()
                    {
                        Name = "number",
                        Label = "Number",
                        Component = "Number",
                        Value = null,
                        DataType = "number",
                        Display = "numberLimit",
                        Props = new ProductRepresentationNodeParameterProps
                        {
                            Theme = "Enter a number",
                        },
                    },
                    new()
                    {
                        Name = "unit",
                        Label = "Unit",
                        Component = "Select",
                        Value = null,
                        DataType = "string",
                        Display = "numberLimit",
                        Props = new ProductRepresentationNodeParameterProps
                        {
                            Theme = "Please Select",
                            Options = NumberLimitUnitOptions,
                        },
                    },
                    new()
                    {
                        Name = "limitPerTime",
                        Label = "Per time unit",
                        Component = "Select",
                        Value = null,
                        DataType = "string",
                        Props = new ProductRepresentationNodeParameterProps
                        {
                            Theme = "Please Select",
                            Options = PerTimeUnitOptions,
                        },
                    },
                    new()
                    {
                        Name = "limitPerVariable",
                        Label = "Per variable unit",
                        Component = "Select",
                        Value = null,
                        DataType = "string",
                        Props = new ProductRepresentationNodeParameterProps
                        {
                            Theme = "---",
                            Options = LimitPerVariableOptions,
                        },
                    },
                    new()
                    {
                        Name = "description",
                        Label = "Description",
                        Component = "TextArea",
                        Value = null,
                        DataType = "string",
                        Display = "formula",
                    },
                    new()
                    {
                        Name = "networkType",
                        Label = "Network Type",
                        Component = "Radios",
                        Value = "inNetworkAndOutOfNetwork",
                        DataType = "string",
                        Display = "radio",
                        Props = new ProductRepresentationNodeParameterProps
                        {
                            Options = NetworkTypes,
                        },
                    },
                    new()
                    {
                        Name = "networks",
                        Label = "Networks",
                        Component = "Text",
                        Value = null,
                        DataType = "string",
                        Display = "none",
                        Props = new ProductRepresentationNodeParameterProps(),
                    },
                ],
            },
            new("#A40F0F")
            {
                Name = "networkSelection",
                Label = "Network Selection",
                Parameters =
                [
                    new()
                    {
                        Name = "type",
                        Label = "Type",
                        Component = "Select",
                        Value = "",
                        DataType = "string",
                        Props = new ProductRepresentationNodeParameterProps
                        {
                            Theme = "Please Select",
                            Options =
                            [
                                new() { Name = "In-network", Value = "inNetwork" },
                                new() { Name = "Out-of-network", Value = "outOfNetwork" },
                            ],
                        },
                    },
                ],
            },
            new("#A40F0F")
            {
                Name = "geographicalLimit",
                Label = "Geographical Limit",
                Parameters =
                [
                    new()
                    {
                        Name = "region",
                        Label = "Region",
                        Component = "Select",
                        Value = "worldwide",
                        DataType = "string",
                        Props = new ProductRepresentationNodeParameterProps
                        {
                            Theme = "Please Select",
                            Options =
                            [
                                new() { Name = "Europe", Value = "europe" },
                                new() { Name = "Europe + Israel", Value = "europeIsrael" },
                                new()
                                {
                                    Name = "Worldwide excluding U.S.A.",
                                    Value = "worldwideExcludingUSA",
                                },
                                new() { Name = "Worldwide", Value = "worldwide" },
                            ],
                        },
                    },
                ],
            },
            new("#A40F0F")
            {
                Name = "preApproval",
                Label = "Pre-Approval",
                Parameters = [],
            },
        ];
    }

    public static List<ProductRepresentationNode> GenerateBaseNodesLibrary(
        string productCurrency = "HKD",
        string productPremiumBaseFrequency = "ANNUALLY"
    )
    {
        return
        [
            new ProductRepresentationNode("#091B94")
            {
                Name = "plan",
                Label = "Plan",
                Props = new()
                {
                    Theme = "#091B94",
                    Id = "",
                    Name = "",
                },
                Parameters =
                [
                    new()
                    {
                        Name = "Id",
                        Label = "ID",
                        Component = "Text",
                        Value = null,
                        DataType = "string",
                    },
                    new()
                    {
                        Name = "Name",
                        Label = "Name",
                        Component = "Text",
                        Value = null,
                        DataType = "string",
                    },
                ],
                Readonly = false,
            },
            new ProductRepresentationNode("#17B669")
            {
                Name = "benefitType",
                Label = "Benefit Type",
                Editor = new() { ComponentName = "BenefitTypeNodeEditor" },
                Props = new()
                {
                    Theme = "#17B669",
                    Id = "",
                    Name = "",
                },
                Parameters =
                [
                    new()
                    {
                        Name = "Name",
                        Label = "Name",
                        Component = "Select",
                        Value = null,
                        DataType = "string",
                        Props = new() { Placeholder = "Please select", Options = [] },
                    },
                ],
            },
            new ProductRepresentationNode("#17B669")
            {
                Name = "benefit",
                Label = "Benefit",
                Editor = new() { ComponentName = "BenefitNodeEditor" },
                Props = new()
                {
                    Theme = "#17B669",
                    Id = "",
                    Name = "",
                },
                Parameters =
                [
                    new()
                    {
                        Name = "type",
                        Label = "",
                        Component = "Radios",
                        Value = null,
                        DataType = "string",
                        Props = new() { Placeholder = "Please select", Options = [] },
                    },
                    new()
                    {
                        Name = "Name",
                        Label = "Name",
                        Component = "Select",
                        Value = null,
                        DataType = "string",
                        Props = new() { Placeholder = "Please select", Options = [] },
                    },
                ],
            },
            new ProductRepresentationNode("#A40F0F")
            {
                Name = "limit",
                Label = "Limit",
                Editor = new() { ComponentName = "LimitNodeEditor" },
                Props = new()
                {
                    Theme = "#A40F0F",
                    Id = "",
                    Name = "",
                },
                Parameters =
                [
                    new()
                    {
                        Name = "limitType",
                        Label = "",
                        Component = "Radios",
                        Value = "amountLimit",
                        DataType = "string",
                        Display = "radio",
                        Props = new() { Options = LimitTypes },
                    },
                    new()
                    {
                        Name = "formula",
                        Label = "Formula",
                        Component = "Formula",
                        Value = new JArray(),
                        DataType = "formula",
                        Display = "formula",
                        IsUsingFormula = false,
                        UnitType = null,
                        Unit = "",
                    },
                    new()
                    {
                        Name = "amount",
                        Label = "Amount",
                        Component = "Number",
                        Value = null,
                        DataType = "number",
                        Display = "amountLimit",
                        Props = new() { Placeholder = "Enter amount" },
                    },
                    new()
                    {
                        Name = "currency",
                        Label = "Currency",
                        Component = "Select",
                        Value = productCurrency,
                        DataType = "string",
                        Display = "amountLimit",
                        Props = GenerateCurrencyNodeProps("limit"),
                    },
                    new()
                    {
                        Name = "number",
                        Label = "Number",
                        Component = "Number",
                        Value = null,
                        DataType = "number",
                        Display = "numberLimit",
                        Props = new() { Placeholder = "Enter a number" },
                    },
                    new()
                    {
                        Name = "Unit",
                        Label = "Unit",
                        Component = "Select",
                        Value = null,
                        DataType = "string",
                        Display = "numberLimit",
                        Props = new()
                        {
                            Placeholder = "Please Select",
                            Options = NumberLimitUnitOptions,
                        },
                    },
                    new()
                    {
                        Name = "limitPerTime",
                        Label = "Per time Unit",
                        Component = "Select",
                        Value = null,
                        DataType = "string",
                        Props = new()
                        {
                            Placeholder = "Please Select",
                            Options = PerTimeUnitOptions,
                        },
                    },
                    new()
                    {
                        Name = "limitPerVariable",
                        Label = "Per variable Unit",
                        Component = "Select",
                        Value = null,
                        DataType = "string",
                        Props = new() { Placeholder = "---", Options = LimitPerVariableOptions },
                    },
                    new()
                    {
                        Name = "Description",
                        Label = "Description",
                        Component = "TextArea",
                        Value = null,
                        DataType = "string",
                        Display = "formula",
                    },
                ],
            },
            new ProductRepresentationNode("#A40F0F")
            {
                Name = "overseasLimit",
                Label = "Overseas AccIdental Limit",
                Editor = new() { ComponentName = "OverseasLimitNodeEditor" },
                Props = new()
                {
                    Theme = "#A40F0F",
                    Id = "",
                    Name = "",
                },
                Parameters =
                [
                    new()
                    {
                        Name = "percentage",
                        Label = "Percentage",
                        Component = "Number",
                        Value = null,
                        DataType = "number",
                    },
                ],
            },
            new ProductRepresentationNode("#A40F0F")
            {
                Name = "groupedBenefitLimit",
                Label = "Grouped Benefit Limit",
                Editor = new() { ComponentName = "GroupedBenefitLimitNodeEditor" },
                Props = new()
                {
                    Theme = "#A40F0F",
                    Id = "",
                    Name = "",
                },
                Parameters =
                [
                    new()
                    {
                        Name = "limitType",
                        Label = "",
                        Component = "Radio",
                        Value = "amountLimit",
                        DataType = "string",
                        Display = "radio",
                        Props = new() { Options = GroupedBenefitLimitTypes },
                    },
                    new()
                    {
                        Name = "formula",
                        Label = "Formula",
                        Component = "Formula",
                        Value = new JArray(),
                        DataType = "formula",
                        Display = "formula",
                        IsUsingFormula = false,
                        UnitType = null,
                        Unit = "",
                    },
                    new()
                    {
                        Name = "amount",
                        Label = "Amount",
                        Component = "Number",
                        Value = null,
                        DataType = "number",
                        Display = "amountLimit",
                        Props = new() { Placeholder = "Enter amount" },
                    },
                    new()
                    {
                        Name = "currency",
                        Label = "Currency",
                        Component = "Select",
                        Value = productCurrency,
                        DataType = "string",
                        Display = "amountLimit",
                        Props = GenerateCurrencyNodeProps("groupedBenefitLimit"),
                    },
                    new()
                    {
                        Name = "number",
                        Label = "Number",
                        Component = "Number",
                        Value = null,
                        DataType = "number",
                        Display = "numberLimit",
                        Props = new() { Placeholder = "Enter a number" },
                    },
                    new()
                    {
                        Name = "Unit",
                        Label = "Unit",
                        Component = "Select",
                        Value = null,
                        DataType = "string",
                        Display = "numberLimit",
                        Props = new()
                        {
                            Placeholder = "Please Select",
                            Options = NumberLimitUnitOptions,
                        },
                    },
                    new()
                    {
                        Name = "limitPerTime",
                        Label = "Per time Unit",
                        Component = "Select",
                        Value = null,
                        DataType = "string",
                        Props = new()
                        {
                            Placeholder = "Please Select",
                            Options = PerTimeUnitOptions,
                        },
                    },
                    new()
                    {
                        Name = "limitPerVariable",
                        Label = "Per variable Unit",
                        Component = "Select",
                        Value = null,
                        DataType = "string",
                        Props = new() { Placeholder = "---", Options = VariableUnits },
                    },
                    new()
                    {
                        Name = "Description",
                        Label = "Description",
                        Component = "TextArea",
                        Value = null,
                        DataType = "string",
                        Display = "formula",
                    },
                    new() { Name = "groupedBenefits", Value = new JArray() },
                ],
            },
            new ProductRepresentationNode("#A40F0F")
            {
                Name = "deductible",
                Label = "Deductible",
                Editor = new() { ComponentName = "DeductibleNodeEditor" },
                Parameters =
                [
                    new()
                    {
                        Name = "deductibleType",
                        Label = "",
                        Component = "Radios",
                        Value = "amount",
                        DataType = "string",
                        Display = "radio",
                        Props = new() { Options = DeductibleTypes },
                    },
                    new()
                    {
                        Name = "formula",
                        Label = "Formula",
                        Component = "Formula",
                        Value = new JArray(),
                        DataType = "formula",
                        Display = "formula",
                        IsUsingFormula = false,
                        UnitType = null,
                        Unit = "",
                    },
                    new()
                    {
                        Name = "currency",
                        Label = "Currency",
                        Component = "Select",
                        Value = productCurrency,
                        DataType = "string",
                        Props = GenerateCurrencyNodeProps("deductible"),
                    },
                    new()
                    {
                        Name = "amount",
                        Label = "Amount",
                        Component = "Number",
                        Value = null,
                        DataType = "number",
                        Display = "amount",
                        Props = new() { Placeholder = "Enter amount" },
                    },
                    new()
                    {
                        Name = "perTime",
                        Label = "Per time Unit",
                        Component = "Select",
                        Value = "perYear",
                        DataType = "string",
                        Props = new()
                        {
                            Placeholder = "Please Select",
                            Options = DeductibleTimeUnits,
                        },
                    },
                    new()
                    {
                        Name = "perVariable",
                        Label = "Per variable Unit",
                        Component = "Select",
                        Value = "",
                        DataType = "string",
                        Props = new() { Placeholder = "---", Options = VariableUnits },
                    },
                    new()
                    {
                        Name = "Description",
                        Label = "Description",
                        Component = "TextArea",
                        Value = null,
                        DataType = "string",
                        Display = "formula",
                    },
                ],
            },
            new ProductRepresentationNode("#A40F0F")
            {
                Name = "coPayment",
                Label = "Co-payment",
                Editor = new() { ComponentName = "CoPaymentNodeEditor" },
                Parameters =
                [
                    new()
                    {
                        Name = "type",
                        Label = "",
                        Component = "Radio",
                        Value = "coPaymentAmount",
                        DataType = "string",
                        Display = "radio",
                        Props = new() { Options = CoPaymentsTypes },
                    },
                    new()
                    {
                        Name = "amount",
                        Label = "Amount",
                        Component = "Number",
                        Value = null,
                        DataType = "number",
                        Display = "none",
                    },
                    new()
                    {
                        Name = "coPaymentAmount",
                        Label = "Amount",
                        Component = "Number",
                        Value = null,
                        DataType = "number",
                        Display = "coPaymentAmount",
                    },
                    new()
                    {
                        Name = "coPaymentFormula",
                        Component = "Formula",
                        DataType = "formula",
                        Display = "coPaymentFormula",
                    },
                    new()
                    {
                        Name = "currency",
                        Label = "Currency",
                        Component = "Select",
                        Value = productCurrency,
                        DataType = "string",
                        Props = GenerateCurrencyNodeProps("coPayment"),
                    },
                    new()
                    {
                        Name = "Description",
                        Label = "Description",
                        Description = "What is the formula about",
                        Component = "LongText",
                        Value = "",
                        DataType = "string",
                        Display = "coPaymentFormula",
                    },
                ],
            },
            new ProductRepresentationNode("#A40F0F")
            {
                Name = "panelCoPayment",
                Label = "Panel Co-payment",
                Editor = new() { ComponentName = "PanelCoPaymentNodeEditor" },
                Parameters =
                [
                    new()
                    {
                        Name = "amount",
                        Label = "Amount",
                        Component = "Number",
                        Value = null,
                        DataType = "number",
                    },
                    new()
                    {
                        Name = "currency",
                        Label = "Currency",
                        Component = "Select",
                        Value = productCurrency,
                        DataType = "string",
                        Props = GenerateCurrencyNodeProps("panelCoPayment"),
                    },
                    new()
                    {
                        Name = "limitPerTime",
                        Label = "Per time Unit",
                        Component = "Select",
                        Value = null,
                        DataType = "string",
                        Props = new()
                        {
                            Placeholder = "Please Select",
                            Options = PerTimeUnitOptions,
                        },
                    },
                    new()
                    {
                        Name = "limitPerVariable",
                        Label = "Per variable Unit",
                        Component = "Select",
                        Value = "",
                        DataType = "string",
                        Props = new() { Placeholder = "---", Options = VariableUnits },
                    },
                ],
            },
            new ProductRepresentationNode("#A40F0F")
            {
                Name = "reimbursement",
                Label = "Reimbursement",
                Editor = new() { ComponentName = "ReimbursementEditor" },
                Parameters =
                [
                    new()
                    {
                        Name = "type",
                        Label = "",
                        Component = "Radio",
                        Value = "reImbursement",
                        DataType = "string",
                        Display = "radio",
                        Props = new()
                        {
                            Options =
                            [
                                new() { Name = "Co-insurance", Value = "coInsurance" },
                                new() { Name = "Reimbursement", Value = "reImbursement" },
                            ],
                        },
                    },
                    new()
                    {
                        Name = "inputType",
                        Label = "Input Type",
                        Component = "Select",
                        Value = "reImbursementFormula",
                        DataType = "string",
                        Props = new()
                        {
                            Options =
                            [
                                new() { Name = "Input (%)", Value = "reImbursementAmount" },
                                new() { Name = "Formula (%)", Value = "reImbursementFormula" },
                            ],
                            AllowSelectNothing = false,
                        },
                    },
                    new()
                    {
                        Name = "percentage",
                        Label = "Percentage",
                        Component = "Number",
                        Value = null,
                        DataType = "number",
                        Display = "none",
                    },
                    new()
                    {
                        Name = "reImbursementAmount",
                        Label = "Percentage",
                        Component = "Number",
                        Value = null,
                        DataType = "number",
                        Display = "reImbursementAmount",
                    },
                    new()
                    {
                        Name = "reImbursementFormula",
                        Component = "Formula",
                        DataType = "formula",
                        Display = "reImbursementFormula",
                    },
                    new()
                    {
                        Name = "Description",
                        Label = "Description",
                        Description = "What is the formula about",
                        Component = "LongText",
                        Value = "",
                        DataType = "string",
                        Display = "reImbursementFormula",
                    },
                ],
            },
            new ProductRepresentationNode("#A40F0F")
            {
                Name = "referralLetter",
                Label = "Referral Letter",
                Parameters =
                [
                    new()
                    {
                        Name = "number",
                        Label = "Number of referral letter",
                        Component = "Number",
                        Value = null,
                        DataType = "number",
                    },
                ],
            },
            new ProductRepresentationNode("#A40F0F")
            {
                Name = "network",
                Label = "In-Network / Out-of-Network Limit",
                Editor = new() { ComponentName = "NetworkNodeEditor" },
                Parameters =
                [
                    new()
                    {
                        Name = "type",
                        Label = "",
                        Component = "Radio",
                        Value = "inNetwork",
                        DataType = "string",
                        Display = "radio",
                        Props = new()
                        {
                            Options =
                            [
                                new() { Name = "In-Network", Value = "inNetwork" },
                                new() { Name = "Out-of-Network", Value = "outOfNetwork" },
                            ],
                        },
                    },
                    new()
                    {
                        Name = "amount",
                        Label = "Amount",
                        Component = "Number",
                        Value = null,
                        DataType = "number",
                    },
                    new()
                    {
                        Name = "currency",
                        Label = "Currency",
                        Component = "Select",
                        Value = productCurrency,
                        DataType = "string",
                        Props = GenerateCurrencyNodeProps("network"),
                    },
                ],
            },
            new ProductRepresentationNode("#A40F0F")
            {
                Name = "waitingPeriod",
                Label = "Waiting Period",
                Index = 10,
                Editor = new() { ComponentName = "WaitingPeriodNodeEditor" },
                Props = new()
                {
                    Theme = "#A40F0F",
                    Id = "",
                    Name = "",
                },
                Parameters =
                [
                    new()
                    {
                        Name = "waitingPeriodType",
                        Label = "",
                        Component = "Radios",
                        Value = "amount",
                        DataType = "string",
                        Display = "radio",
                        Props = new() { Options = WaitingPeriodTypes },
                    },
                    new()
                    {
                        Name = "formula",
                        Label = "Formula",
                        Component = "Formula",
                        Value = new JArray(),
                        DataType = "formula",
                        Display = "formula",
                        IsUsingFormula = false,
                        UnitType = null,
                        Unit = "",
                    },
                    new()
                    {
                        Name = "amount",
                        Label = "Amount",
                        Component = "Number",
                        Value = null,
                        DataType = "number",
                        Display = "amount",
                        Props = new() { Placeholder = "Enter amount" },
                    },
                    new()
                    {
                        Name = "Unit",
                        Label = "Unit",
                        Component = "Select",
                        Value = "hour(s)",
                        DataType = "string",
                        Props = new()
                        {
                            Placeholder = "Please Select",
                            Options =
                            [
                                new() { Name = "Hour(s)", Value = "hour(s)" },
                                new() { Name = "Day(s)", Value = "day(s)" },
                                new() { Name = "Month(s)", Value = "month(s)" },
                            ],
                        },
                    },
                    new()
                    {
                        Name = "amountDescription",
                        Label = "Amount Description",
                        Component = "TextArea",
                        Value = null,
                        DataType = "string",
                        Display = "amount",
                        Props = new()
                        {
                            Placeholder = "",
                            Maxlength = 100,
                            Rows = 3,
                        },
                    },
                    new()
                    {
                        Name = "Description",
                        Label = "Description",
                        Component = "TextArea",
                        Value = null,
                        DataType = "string",
                        Display = "formula",
                    },
                ],
            },
            new ProductRepresentationNode("#815FB9")
            {
                Name = "UnitPrice",
                Label = "Unit price",
                Editor = new() { ComponentName = "UnitPriceNodeEditor" },
                Parameters =
                [
                    new()
                    {
                        Name = "type",
                        Label = "",
                        Component = "Radio",
                        Value = "UnitPriceAmount",
                        DataType = "string",
                        Display = "radio",
                        Props = new() { Options = UnitPricesTypes },
                    },
                    new()
                    {
                        Name = "amount",
                        Label = "Amount",
                        Component = "Number",
                        Value = null,
                        DataType = "number",
                        Display = "none",
                    },
                    new()
                    {
                        Name = "UnitPriceAmount",
                        Label = "Amount",
                        Component = "Number",
                        Value = null,
                        DataType = "number",
                        Display = "UnitPriceAmount",
                    },
                    new()
                    {
                        Name = "UnitPriceFormula",
                        Component = "Formula",
                        DataType = "formula",
                        Display = "UnitPriceFormula",
                    },
                    new()
                    {
                        Name = "currency",
                        Label = "Currency",
                        Component = "Select",
                        Value = productCurrency,
                        DataType = "string",
                        Props = GenerateCurrencyNodeProps("UnitPrice"),
                    },
                    new()
                    {
                        Name = "Unit",
                        Label = "Unit",
                        Component = "Select",
                        Value = "",
                        DataType = "string",
                        Props = new()
                        {
                            Placeholder = "Please Select",
                            Options =
                            [
                                new() { Name = "Per insured", Value = "perInsured" },
                                new() { Name = "Per employee", Value = "perEmployee" },
                                new() { Name = "Per dependent", Value = "perDependent" },
                                new() { Name = "Per adult", Value = "perAdult" },
                                new() { Name = "Per child", Value = "perChild" },
                                new() { Name = "Per policy", Value = "perPolicy" },
                            ],
                        },
                    },
                    new()
                    {
                        Name = "limitPerTime",
                        Label = "Modality",
                        Component = "Select",
                        Value = productPremiumBaseFrequency,
                        DataType = "string",
                        Props = new() { Disabled = true, Options = PremiumBaseFrequencyOptions },
                    },
                    new()
                    {
                        Name = "Description",
                        Label = "Description",
                        Description = "What is the formula about",
                        Component = "LongText",
                        Value = "",
                        DataType = "string",
                        Display = "UnitPriceFormula",
                    },
                ],
            },
            new ProductRepresentationNode("#815FB9")
            {
                Name = "bandedPremium",
                Label = "Banded Premium",
                Editor = new() { ComponentName = "BandedPremiumEditor" },
                Parameters =
                [
                    new()
                    {
                        Name = "bandedPremium",
                        Segments = [],
                        Values = [],
                        Value = "",
                    },
                    new()
                    {
                        Name = "currency",
                        Label = "Currency",
                        Component = "Select",
                        Value = productCurrency,
                        DataType = "string",
                        Props = GenerateCurrencyNodeProps("bandedPremium"),
                    },
                    new()
                    {
                        Name = "Unit",
                        Label = "Unit",
                        Component = "Select",
                        Value = "perInsured",
                        DataType = "string",
                        Props = new()
                        {
                            Placeholder = "Please Select",
                            Options =
                            [
                                new() { Name = "Per insured", Value = "perInsured" },
                                new() { Name = "Per employee", Value = "perEmployee" },
                                new() { Name = "Per dependent", Value = "perDependent" },
                                new() { Name = "Per adult", Value = "perAdult" },
                                new() { Name = "Per child", Value = "perChild" },
                                new() { Name = "Per policy", Value = "perPolicy" },
                            ],
                        },
                    },
                    new()
                    {
                        Name = "limitPerTime",
                        Label = "Per Time Unit",
                        Component = "Select",
                        Value = productPremiumBaseFrequency,
                        DataType = "string",
                        Props = new() { Disabled = true, Options = PremiumBaseFrequencyOptions },
                    },
                ],
            },
            new ProductRepresentationNode("#FF00E5")
            {
                Name = "ifCondition",
                Label = "IF condition",

                Parameters =
                [
                    new()
                    {
                        Name = "Description",
                        Label = "Description",
                        Description = "What is the condition about",
                        Component = "Text",
                        Value = "",
                        DataType = "text",
                    },
                    new()
                    {
                        Name = "expression",
                        Label = "Expression",
                        Description = "expression to evaluate the if condition",
                        Component = "Formula",
                        Value = null,
                        DataType = "formula",
                    },
                ],
            },
            new ProductRepresentationNode("#FF00E5")
            {
                Name = "exclusiveOptions",
                Label = "Exclusive Options",
            },
            new ProductRepresentationNode("#00CAE5")
            {
                Name = "manualApproval",
                Label = "Manual Approval",
            },
            new ProductRepresentationNode("#00CAE5") { Name = "rejection", Label = "Rejection" },
        ];
    }

    private static readonly Dictionary<string, string> PRICING_NODES =
        new() { { "unitPrice", "unitPrice" }, { "bandedPremium", "bandedPremium" } };

    public static ProductRepresentationNodeParameterProps GenerateCurrencyNodeProps(
        string nodeNameKey
    )
    {
        string placeholder = "Please Select";
        var currencyOptions = new List<ProductRepresentationNodeParameterPropsOption>
        {
            new() { Name = "HKD", Value = "HKD" },
            new() { Name = "USD", Value = "USD" },
            new() { Name = "THB", Value = "THB" },
            new() { Name = "SAU", Value = "SAU" },
            new() { Name = "GBP", Value = "GBP" },
            new() { Name = "VND", Value = "VND" },
            new() { Name = "EUR", Value = "EUR" },
            new() { Name = "MXN", Value = "MXN" },
            new() { Name = "BRL", Value = "BRL" },
        };

        if (currencyOptions == null || currencyOptions.Count == 0)
        {
            placeholder = "Currency options is not configured for this tenant";
            currencyOptions = [];
        }

        bool Disabled = false;
        if (PRICING_NODES.ContainsKey(nodeNameKey))
        {
            Disabled = true;
        }

        return new ProductRepresentationNodeParameterProps
        {
            Theme = placeholder,
            Options = currencyOptions,
            Disabled = Disabled,
        };
    }
}