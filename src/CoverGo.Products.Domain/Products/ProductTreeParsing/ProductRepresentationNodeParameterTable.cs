using System.Collections.Generic;

namespace CoverGo.Products.Domain.Products.ProductTreeParsing;

public class ProductRepresentationNodeParameterTable
{
    public List<ProductRepresentationNodeParameterTableColumn>? Columns { get; set; }
    public List<ProductRepresentationNodeParameterTableRow>? Rows { get; set; }
}

public class ProductRepresentationNodeParameterTableColumn
{
    public List<string>? Value { get; set; }
}

public class ProductRepresentationNodeParameterTableRow
{
    public List<string>? Value { get; set; }
}
