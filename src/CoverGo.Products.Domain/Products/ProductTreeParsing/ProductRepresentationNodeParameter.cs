
using System.Collections.Generic;

namespace CoverGo.Products.Domain.Products.ProductTreeParsing;

public class ProductRepresentationNodeParameter
{
    public string? Component { get; set; }
    public string? DataType { get; set; }
    public string? Description { get; set; }
    public string? Display { get; set; }
    public string? DisplayValue { get; set; }
    public string? Label { get; set; }
    public string? Name { get; set; }
    public ProductRepresentationNodeParameterProps? Props { get; set; }
    public List<ProductRepresentationNodeParameterSegment>? Segments { get; set; }
    public ProductRepresentationNodeParameterTable? Table { get; set; }
    public object? Value { get; set; }
    public List<ProductRepresentationNodeParameterValue>? Values { get; set; }
    public bool? IsUsingFormula { get; set; }
    public string? Unit { get; set; }
    public string? UnitType { get; set; }
}

public class ProductRepresentationNodeParameterValue
{
    public List<string>? Condition { get; set; }
    public int? Value { get; set; }
}
