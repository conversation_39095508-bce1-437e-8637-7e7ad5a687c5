using System.Collections.Generic;

namespace CoverGo.Products.Domain.Products.ProductTreeParsing;

public class ProductRepresentationNodeParameterProps
{
    public bool? Disabled { get; set; }
    public string? Placeholder { get; set; }
    public int? Maxlength { get; set; }
    public int? Rows { get; set; }
    public string? Theme { get; set; }
    public bool? AllowSelectNothing { get; set; }
    public List<ProductRepresentationNodeParameterPropsOption>? Options { get; set; }
}
