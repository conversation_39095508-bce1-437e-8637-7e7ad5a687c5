using System.Collections.Generic;

namespace CoverGo.Products.Domain.Products.ProductTreeParsing;

public class ProductRepresentationNodeParameterSegment
{
    public string? Name { get; set; }
    public List<ProductRepresentationNodeParameterSegmentOption>? Options { get; set; }
    public string? Path { get; set; }
    public string? SegId { get; set; }
}

public class ProductRepresentationNodeParameterSegmentOption
{
    public bool? IsToggle { get; set; }
    public string? Value { get; set; }
}
