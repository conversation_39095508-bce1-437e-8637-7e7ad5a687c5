using System;
using System.Collections.Generic;
using System.Linq;

namespace CoverGo.Products.Domain.Products.ProductTreeParsing;

public class ProductRepresentationNode
{
    public List<ProductRepresentationNode>? Children { get; set; }
    public string? ComponentName { get; set; }
    public ProductRepresentationNodeEditor? Editor { get; set; }
    public string? Id { get; set; }
    public bool? Minified { get; set; }
    public string? Name { get; set; }
    public string? NodeName { get; set; }
    public List<ProductRepresentationNodeParameter>? Parameters { get; set; } //parameters //9913933985174248
    public string? ParentId { get; set; }
    public List<string>? ParentIds { get; set; }
    public string? PremiumBaseFrequency { get; set; }
    public string? ProductCurrency { get; set; }
    public string? ProductType { get; set; }
    public ProductRepresentationNodeProps? Props { get; set; }
    public bool? Readonly { get; set; }
    public int? ResolvedValue { get; set; }
    public string? Label { get; set; }
    public int? Index { get; set; }
    public int? Indent { get; set; }
    public int? OriginalAmount { get; set; }
    public object? Percentage { get; set; }
    public object? Resolved { get; set; }
    public string? Path { get; set; }
    public string? PlanId { get; set; }
    public bool? IsExpanded { get; set; }
    public string? SortedPath { get; set; }
    public string? DirectParent { get; set; }
    public object? Order { get; set; }
    public object? Summary { get; set; }
    public object? ExtraSummary { get; set; }
    public object? MasterPlan { get; set; }
    public string? Identify { get; set; }
    public string? SortedIdentify { get; set; }
    public string? Code { get; set; }
    public string? Desc { get; set; }
    public int? DescIdx { get; set; }
    public string? LimitType { get; set; }
    public string? LimitPerTime { get; set; }
    public string? LimitPerVariable { get; set; }
    public int? Amount { get; set; }
    public int? Number { get; set; }
    public string? Value { get; set; }
    public string? HtmlLabel { get; set; }
    public ProductRepresentationNode? Parent { get; set; }

    public override string ToString() => $"{Id} - {Label} - {Order}";

    public ProductRepresentationNode() { }

    public ProductRepresentationNode(string? style)
    {
        style ??= "darkBlue";
        Id = GenerateId();
        Name = "plan";
        Label = "Plan";
        Props = new() { Theme = style };
        ComponentName = "";
        Editor = new() { ComponentName = "DefaultNodeEditor" };
    }

    private static string GenerateId()
    {
        return Guid.NewGuid().ToString("N").Substring(0, 20);
    }

    public string[]? ChildrenIds => Children?.Select(c => c.Id).ToArray();
}
