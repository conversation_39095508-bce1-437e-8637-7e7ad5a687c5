#nullable enable

using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.Multitenancy;
using CoverGo.Products.Domain.BenefitDefinitions;
using CoverGo.Products.Domain.BenefitDefinitionTypes;
using CoverGo.Products.Domain.Templates;

using MediatR;

namespace CoverGo.Products.Domain.Products.ProductTreeParsing;

public record PlanDetailsQuery : IRequest<ProductPlanDetails?>
{
    public required ProductId ProductId { get; init; }
}

public class PlanDetailsQueryHandler(
    TenantId tenantId,
    IProductRepository productRepository,
    BenefitDefinitionService benefitDefinitionService,
    BenefitDefinitionTypeService benefitDefinitionTypeService,
    ITemplatesAdapter templatesAdapter) : IRequestHandler<PlanDetailsQuery, ProductPlanDetails?>
{
    public async Task<ProductPlanDetails?> Handle(PlanDetailsQuery notification, CancellationToken cancellationToken)
    {
        Product product = (await productRepository.GetAsync(tenantId.Value, new ProductWhere { Id_in = [notification.ProductId] }, null, new QueryParameters(), true, cancellationToken: cancellationToken)).FirstOrDefault();
        if (product == null)
            return null;

        var benefitDefinitions = await benefitDefinitionService.GetBenefitDefinitionAsync(tenantId.Value, new(), cancellationToken);
        var benefitTypeDefinitions = await benefitDefinitionTypeService.GetBenefitDefinitionTypeAsync(tenantId.Value, new(), cancellationToken);

        var attachedDocumentsIds = product.AttachedDocumentsIds;
        var templates = await templatesAdapter.GetTemplates(tenantId.Value, attachedDocumentsIds);

        return PlanDetailBuilder.GetProductPlanDetails(
            notification.ProductId,
            product.Representation,
            benefitDefinitions.ToList(),
            benefitTypeDefinitions.ToList(),
            attachedDocumentsIds,
            templates
            );
    }
}
