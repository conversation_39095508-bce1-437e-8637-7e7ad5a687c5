#nullable enable

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.Json;
using System.Text.RegularExpressions;
using CoverGo.Products.Domain.BenefitDefinitions;
using CoverGo.Products.Domain.BenefitDefinitionTypes;
using CoverGo.Products.Domain.Products.Limits;
using CoverGo.Templates.Client;

using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace CoverGo.Products.Domain.Products.ProductTreeParsing;

public static class PlanDetailBuilder
{
    private static JsonSerializerOptions _jsonOptions = new JsonSerializerOptions()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
    };

    private static readonly Dictionary<string, string> _keyMapping =
        new()
        {
            { "ch", "children" },
            { "n", "name" },
            { "l", "label" },
            { "p", "parameters" },
            { "c", "component" },
            { "d", "dataType" },
            { "dv", "displayValue" },
            { "de", "description" },
            { "v", "value" },
            { "pc", "productCurrency" },
            { "pbf", "premiumBaseFrequency" },
        };

    private static readonly List<string> _commonFormulas =
    [
        "AND",
        "OR",
        "SUM",
        "MULTIPLY",
        "DIVIDE",
        "EQUAL",
        "DOB_TO_AGE",
        "DOB_TO_DAY",
        "GREATER_THAN",
        "GREATER_THAN_OR_EQUAL",
        "LESS_THAN",
        "LESS_THAN_OR_EQUAL",
        "DAYS",
        "JOIN",
        "IF",
        "COUNT_INSURED",
        "MAX",
        "MIN",
    ];

    private static readonly Regex RepresentationUnminifyRegex =
        new(
            @"""(\w+)""(?=\s*:)",
            RegexOptions.Compiled | RegexOptions.Multiline,
            TimeSpan.FromMinutes(1)
        );

    public static ProductPlanDetails GetProductPlanDetails(
        ProductId productId,
        string representation,
        IList<BenefitDefinition> benefitDefinitions,
        IList<BenefitDefinitionType> benefitTypeDefinitions,
        List<AttachedDocumentId> attachedDocumentIds,
        List<Template> templates
        )
    {
        List<ProductRepresentationNode> representationNodes = ReadRepresentation(representation);
        if (representationNodes == null || representationNodes.Count == 0)
            return new ProductPlanDetails() {
                ProductId = productId,
                MasterPlan = [],
                AllPlans = [],
                AttachedDocuments = MapAttachedDocuments(attachedDocumentIds, templates)
            };

        List<ProductRepresentationNode> masterPlan = GetMasterPlan(representationNodes) ?? [];
        List<ProductRepresentationNode> allPlans = GetAllPlans(representationNodes) ?? [];

        ProductRepresentationNode representationObject = representationNodes[0];

        var productPlanDetails = new ProductPlanDetails()
        {
            ProductId = productId,
            MasterPlan = masterPlan,
            AllPlans = allPlans,
            AttachedDocuments = MapAttachedDocuments(attachedDocumentIds, templates)
        };

        IEnumerable<ProductRepresentationNode> planNodes = GetChildArrayNodeByName(
            representationObject,
            "plan");
        foreach (ProductRepresentationNode planNode in planNodes ?? [])
        {
            var planDetail = new ProductPlanDetail
            {
                Id = GetNodePropertyValue(planNode, "id"),
                ProductId = productId,
                Name = GetNodePropertyValue(planNode, "name"),
                Benefits = BuildBenefits(planNode, benefitDefinitions, benefitTypeDefinitions),
                BenefitTypes = BuildBenefitTypes(planNode, benefitDefinitions, benefitTypeDefinitions),
                AddOns = BuildAddOns(GetChildArrayNodeByName(planNode, "addOn"), benefitDefinitions, benefitTypeDefinitions),
            };
            productPlanDetails.PlanDetails.Add(planDetail);
        }

        IEnumerable<ProductRepresentationNode> addOnNodes = GetChildArrayNodeByName(
            representationObject,
            "addOn");
        List<PlanAddOn> addOns = BuildAddOns(addOnNodes, benefitDefinitions, benefitTypeDefinitions);
        productPlanDetails.AddOns = addOns;
        return productPlanDetails;
    }

    static List<PlanBenefitType> BuildBenefitTypes(
        ProductRepresentationNode planNode,
        IList<BenefitDefinition> benefitDefinitions,
        IList<BenefitDefinitionType> benefitTypeDefinitions)
    {
        var benefitTypes = new List<PlanBenefitType>();
        IEnumerable<ProductRepresentationNode> benefitTypeNodes = GetChildArrayNodeByName(
            planNode,
            "benefitType");
        foreach (ProductRepresentationNode benefitTypeNode in benefitTypeNodes ?? [])
        {
            var benefitType = new PlanBenefitType
            {
                Name = GetNodePropertyValue(benefitTypeNode, "name", true),
                Code = GetNodePropertyValue(benefitTypeNode, "name"),
                Benefits = BuildBenefits(benefitTypeNode, benefitDefinitions, benefitTypeDefinitions),
                BenefitTypes = BuildBenefitTypes(benefitTypeNode, benefitDefinitions, benefitTypeDefinitions),
            };
            FillLimitNodes(benefitType, benefitTypeNode, benefitDefinitions, benefitTypeDefinitions);
            benefitTypes.Add(benefitType);
        }
        return benefitTypes;
    }

    static List<PlanBenefit> BuildBenefits(
        ProductRepresentationNode node,
        IList<BenefitDefinition> benefitDefinitions,
        IList<BenefitDefinitionType> benefitTypeDefinitions)
    {
        var benefits = new List<PlanBenefit>();
        IEnumerable<ProductRepresentationNode> benefitNodes = GetChildArrayNodeByName(
            node,
            "benefit");
        foreach (ProductRepresentationNode benefitNode in benefitNodes ?? [])
        {
            var benefit = new PlanBenefit
            {
                Name = GetNodePropertyValue(benefitNode, "name", true) ?? GetNodePropertyValue(benefitNode, "type"),
                Benefits = BuildBenefits(benefitNode, benefitDefinitions, benefitTypeDefinitions),
                BenefitTypes = BuildBenefitTypes(benefitNode, benefitDefinitions, benefitTypeDefinitions),
            };
            FillLimitNodes(benefit, benefitNode, benefitDefinitions, benefitTypeDefinitions);
            benefits.Add(benefit);
        }
        return benefits;
    }

    static T FillLimitNodes<T>(T limitHolder, ProductRepresentationNode node, IList<BenefitDefinition> benefitDefinitions, IList<BenefitDefinitionType> benefitTypeDefinitions)
        where T : ILimitHolder
    {
        limitHolder.Limits = BuildLimits(node);
        limitHolder.OverseasAccidentalLimits = BuildOverseasAccidentalLimits(node);
        limitHolder.GroupedBenefitLimits = BuildGroupedBenefitLimits(node, benefitDefinitions, benefitTypeDefinitions);
        limitHolder.Deductibles = BuildDeductibles(node);
        limitHolder.Copayments = BuildCopayments(node);
        limitHolder.PanelCopayments = BuildPanelCopayments(node);
        limitHolder.Coinsurances = BuildCoinsurances(node);
        limitHolder.Reimbursements = BuildReimbursements(node);
        limitHolder.GeographicalLimits = BuildGeographicalLimits(node);
        limitHolder.WaitingPeriods = BuildWaitingPeriods(node);
        limitHolder.DeferredPeriods = BuildDeferredPeriods(node);
        limitHolder.QualificationPeriods = BuildQualifitcationPeriods(node);
        return limitHolder;
    }

    static List<PlanBenefitLimit> BuildLimits(ProductRepresentationNode node)
    {
        var limits = new List<PlanBenefitLimit>();
        limits.AddRange(GetChildArrayNodeByName(node, "limit")
            ?.Select(n => new PlanBenefitLimit
            {
                Network = n.Parameters.SingleOrDefault(it => it.Name == "networkType")?.Value switch
                {
                    "inNetworkAndOutOfNetwork" => new InNetworkAndOutOfNetwork(),
                    "outOfNetwork" => new OutOfNetwork(),
                    "inNetwork" => new InNetwork()
                    {
                        Organizations = n.Parameters.SingleOrDefault(it => it.Name == "networks")?.Value is string json
                            ? System.Text.Json.JsonSerializer.Deserialize<List<InNetworkOrganization>>(json, _jsonOptions) ?? []
                            : []
                    },
                    _ => null,
                },
                LimitValue = BuildLimitValue(n),
                Description = n.Parameters.SingleOrDefault(it => it.Name == "description")?.Value as string,
            })
            ?.ToList() ?? []);
        return limits;
    }

    static ILimitValue BuildLimitValue(ProductRepresentationNode n)
    {
        return n.Parameters.SingleOrDefault(it => it.Name == "limitType")?.Value switch
        {
            "amountLimit" => n.Parameters.SingleOrDefault(it => it.Name == "formula")?.IsUsingFormula == false
                ? new AmountLimitValue()
                {
                    Amount = n.Parameters.SingleOrDefault(it => it.Name == "amount")?.Value?.ToString() is string amountStr && decimal.TryParse(amountStr, out var amount) ? amount : null,
                    Currency = n.Parameters.SingleOrDefault(it => it.Name == "currency")?.Value as string,
                    PerTimeUnit = n.Parameters.SingleOrDefault(it => it.Name == "limitPerTime")?.Value as string,
                    PerVariableUnit = n.Parameters.SingleOrDefault(it => it.Name == "limitPerVariable")?.Value as string,
                }
                : new AmountFormulaLimitValue()
                {
                    Currency = n.Parameters.SingleOrDefault(it => it.Name == "currency")?.Value as string,
                    PerTimeUnit = n.Parameters.SingleOrDefault(it => it.Name == "limitPerTime")?.Value as string,
                    PerVariableUnit = n.Parameters.SingleOrDefault(it => it.Name == "limitPerVariable")?.Value as string,
                },
            "numberLimit" => new NumberLimitValue()
            {
                Amount = n.Parameters.SingleOrDefault(it => it.Name == "number")?.Value as long?,
                Unit = n.Parameters.SingleOrDefault(it => it.Name == "unit")?.Value as string,
                PerTimeUnit = n.Parameters.SingleOrDefault(it => it.Name == "limitPerTime")?.Value as string,
                PerVariableUnit = n.Parameters.SingleOrDefault(it => it.Name == "limitPerVariable")?.Value as string,
            },
            _ => null,
        };
    }

    static List<PlanBenefitOverseasAccidentalLimit> BuildOverseasAccidentalLimits(ProductRepresentationNode node)
    {
        var limits = new List<PlanBenefitOverseasAccidentalLimit>();
        limits.AddRange(GetChildArrayNodeByName(node, "overseasLimit")
            ?.Select(n => new PlanBenefitOverseasAccidentalLimit
            {
                Percentage = n.Parameters.SingleOrDefault(it => it.Name == "percentage")?.Value is string percentageStr
                    ? int.TryParse(percentageStr.Split("%")[0], CultureInfo.InvariantCulture, out var percentageInt)
                        ? percentageInt
                        : null
                    : null
            })
            ?.ToList() ?? []);
        return limits;
    }

    static List<PlanBenefitGroupedBenefitLimit> BuildGroupedBenefitLimits(
        ProductRepresentationNode node,
        IList<BenefitDefinition> benefitDefinitions,
        IList<BenefitDefinitionType> benefitTypeDefinitions)
    {
        var limits = new List<PlanBenefitGroupedBenefitLimit>();
        limits.AddRange(GetChildArrayNodeByName(node, "groupedBenefitLimit")
            ?.Select(n => new PlanBenefitGroupedBenefitLimit
            {
                LimitValue = BuildLimitValue(n),
                GroupedBenefits = n.Parameters.SingleOrDefault(it => it.Name == "groupedBenefits")?.Value is JArray groupedBenefitStrings
                    ? groupedBenefitStrings
                        .Select(groupedBenefitJToken =>
                        {
                            var groupBenefitString = groupedBenefitJToken.Value<string>();
                            if (benefitDefinitions.SingleOrDefault(it => $"benefit.{it.BusinessId}.{it.Name}" == groupBenefitString) is BenefitDefinition benefitDef)
                            {
                                return new GroupedBenefit() { Value = benefitDef };
                            }
                            else if (benefitTypeDefinitions.SingleOrDefault(it => $"benefitType.{it.BusinessId}.{it.Name}" == groupBenefitString) is BenefitDefinitionType benefitTypeDef)
                            {
                                return new GroupedBenefitType() { Value = benefitTypeDef };
                            }
                            return (IGroupedBenefitItem?)null;
                        })
                        .Where(groupedBenefitItem => groupedBenefitItem != null)
                        .Cast<IGroupedBenefitItem>()
                        .ToList()
                    : [],
            })
            ?.ToList() ?? []);
        return limits;
    }

    private static List<PlanBenefitDeductible> BuildDeductibles(
        ProductRepresentationNode node)
    {
        var limits = new List<PlanBenefitDeductible>();
        limits.AddRange(GetChildArrayNodeByName(node, "deductible")
            ?.Select(n => new PlanBenefitDeductible
            {
                DeductibleValue = n.Parameters.SingleOrDefault(it => it.Name == "formula")?.IsUsingFormula == false
                    ? new AmountDeductibleValue()
                    {
                        Amount = decimal.TryParse(n.Parameters.SingleOrDefault(it => it.Name == "amount")?.Value?.ToString(), out var amount) ? amount : null,
                        Currency = n.Parameters.SingleOrDefault(it => it.Name == "currency")?.Value as string,
                        PerTimeUnit = n.Parameters.SingleOrDefault(it => it.Name == "perTime")?.Value as string,
                        PerVariableUnit = n.Parameters.SingleOrDefault(it => it.Name == "perVariable")?.Value as string,
                    }
                    : new AmountFormulaDeductibleValue()
                    {
                        Currency = n.Parameters.SingleOrDefault(it => it.Name == "currency")?.Value as string,
                        PerTimeUnit = n.Parameters.SingleOrDefault(it => it.Name == "perTime")?.Value as string,
                        PerVariableUnit = n.Parameters.SingleOrDefault(it => it.Name == "perVariable")?.Value as string,
                    },
                Description = n.Parameters.SingleOrDefault(it => it.Name == "description")?.Value as string,
            })
            ?.ToList() ?? []);
        return limits;
    }

    private static List<PlanBenefitCopayment> BuildCopayments(ProductRepresentationNode node)
    {
        var limits = new List<PlanBenefitCopayment>();
        limits.AddRange(GetChildArrayNodeByName(node, "coPayment")
            ?.Select(n => new PlanBenefitCopayment
            {
                CopaymentValue = (n.Parameters.SingleOrDefault(it => it.Name == "type")?.Value as string) switch
                {
                    "coPaymentAmount" => new AmountCopaymentValue()
                    {
                        Amount = decimal.TryParse(n.Parameters.SingleOrDefault(it => it.Name == "amount")?.Value?.ToString(), out var amount) ? amount : null,
                        Currency = n.Parameters.SingleOrDefault(it => it.Name == "currency")?.Value as string,
                    },
                    "coPaymentFormula" => new AmountFormulaCopaymentValue()
                    {
                        Currency = n.Parameters.SingleOrDefault(it => it.Name == "currency")?.Value as string,
                    },
                    _ => null
                },
                Description = n.Parameters.SingleOrDefault(it => it.Name == "description")?.Value as string,
            })
            ?.ToList() ?? []);
        return limits;
    }

    private static List<PlanBenefitPanelCopayment> BuildPanelCopayments(ProductRepresentationNode node)
    {
        var limits = new List<PlanBenefitPanelCopayment>();
        limits.AddRange(GetChildArrayNodeByName(node, "panelCoPayment")
            ?.Select(n => new PlanBenefitPanelCopayment
            {
                PanelCopaymentValue = new AmountPanelCopaymentValue()
                {
                    Amount = decimal.TryParse(n.Parameters.SingleOrDefault(it => it.Name == "amount")?.Value?.ToString(), out var amount) ? amount : null,
                    Currency = n.Parameters.SingleOrDefault(it => it.Name == "currency")?.Value as string,
                    PerTimeUnit = n.Parameters.SingleOrDefault(it => it.Name == "limitPerTime")?.Value as string,
                    PerVariableUnit = n.Parameters.SingleOrDefault(it => it.Name == "limitPerVariable")?.Value as string,
                }
            })
            ?.ToList() ?? []);
        return limits;
    }

    private static List<PlanBenefitCoinsurance> BuildCoinsurances(ProductRepresentationNode node)
    {
        var limits = new List<PlanBenefitCoinsurance>();
        limits.AddRange(GetChildArrayNodeByName(node, "reimbursement")
            ?.Where(n => n.Parameters.SingleOrDefault(it => it.Name == "type")?.Value is "coInsurance")
            ?.Select(n => new PlanBenefitCoinsurance
            {
                CoinsuranceValue = n.Parameters.SingleOrDefault(it => it.Name == "inputType")?.Value switch
                {
                    "reImbursementAmount" => new PercentageCoinsuranceValue
                    {
                        Amount = n.Parameters.SingleOrDefault(it => it.Name == "percentage")?.Value is string percentageStr
                            ? decimal.TryParse(percentageStr.Split("%")[0], CultureInfo.InvariantCulture, out var percentage)
                                ? percentage
                                : null
                            : null,
                    },
                    "reImbursementFormula" => new PercentageFormulaCoinsuranceValue { },
                    _ => null,
                },
                MaxLimit = n.Parameters.SingleOrDefault(it => it.Name == "maxLimitType")?.Value switch
                {
                    "amount" => new AmountCoinsuranceMaxLimitValue
                    {
                        Amount = decimal.TryParse(n.Parameters.SingleOrDefault(it => it.Name == "maxLimit")?.Value?.ToString(), out var amount) ? amount : null,
                        Currency = n.Parameters.SingleOrDefault(it => it.Name == "maxLimitCurrency")?.Value as string,
                    },
                    "formula" => new AmountFormulaCoinsuranceMaxLimitValue
                    {
                        Currency = n.Parameters.SingleOrDefault(it => it.Name == "maxLimitCurrency")?.Value as string,
                    },
                    _ => null,
                },
                Description = n.Parameters.SingleOrDefault(it => it.Name == "description")?.Value as string,
            })
            ?.ToList() ?? []);
        return limits;
    }

    private static List<PlanBenefitReimbursement> BuildReimbursements(ProductRepresentationNode node)
    {
        var limits = new List<PlanBenefitReimbursement>();
        limits.AddRange(GetChildArrayNodeByName(node, "reimbursement")
            ?.Where(n => n.Parameters.SingleOrDefault(it => it.Name == "type")?.Value is "reImbursement")
            ?.Select(n => new PlanBenefitReimbursement
            {
                ReimbursementValue = n.Parameters.SingleOrDefault(it => it.Name == "inputType")?.Value switch
                {
                    "reImbursementAmount" => new PercentageReimbursementValue
                    {
                        Amount = n.Parameters.SingleOrDefault(it => it.Name == "percentage")?.Value is string percentageStr
                            ? decimal.TryParse(percentageStr.Split("%")[0], CultureInfo.InvariantCulture, out var percentage)
                                ? percentage
                                : null
                            : null,
                    },
                    "reImbursementFormula" => new PercentageFormulaReimbursementValue { },
                    _ => null,
                },
                Description = n.Parameters.SingleOrDefault(it => it.Name == "description")?.Value as string,
            })
            ?.ToList() ?? []);
        return limits;
    }

    private static List<PlanBenefitGeographicalLimit> BuildGeographicalLimits(ProductRepresentationNode node)
    {
        var limits = new List<PlanBenefitGeographicalLimit>();
        limits.AddRange(GetChildArrayNodeByName(node, "geographicalLimit")
            ?.Select(n => new PlanBenefitGeographicalLimit
            {
                Region = n.Parameters.SingleOrDefault(it => it.Name == "region")?.Value as string,
            })
            ?.ToList() ?? []);
        return limits;
    }

    private static List<PlanBenefitWaitingPeriod> BuildWaitingPeriods(ProductRepresentationNode node)
    {
        var limits = new List<PlanBenefitWaitingPeriod>();
        limits.AddRange(GetChildArrayNodeByName(node, "waitingPeriod")
            ?.Select(n => new PlanBenefitWaitingPeriod
            {
                WaitingPeriodValue = n.Parameters.SingleOrDefault(it => it.Name == "waitingPeriodType")?.Value switch
                {
                    "amount" => n.Parameters.SingleOrDefault(it => it.Name == "formula")?.IsUsingFormula == false
                        ? new AmountWaitingPeriodValue
                        {
                            Amount = decimal.TryParse(n.Parameters.SingleOrDefault(it => it.Name == "amount")?.Value?.ToString(), out var amount) ? amount : null,
                            Unit = n.Parameters.SingleOrDefault(it => it.Name == "unit")?.Value as string,
                        }
                        : new AmountFormulaWaitingPeriodValue
                        {
                            Unit = n.Parameters.SingleOrDefault(it => it.Name == "unit")?.Value as string,
                        },
                    _ => null,
                },
                Description = n.Parameters.SingleOrDefault(it => it.Name == "description")?.Value as string ??
                    n.Parameters.SingleOrDefault(it => it.Name == "amountDescription")?.Value as string,
            })
            ?.ToList() ?? []);
        return limits;
    }

    private static List<PlanBenefitDeferredPeriod> BuildDeferredPeriods(ProductRepresentationNode node)
    {
        var limits = new List<PlanBenefitDeferredPeriod>();
        limits.AddRange(GetChildArrayNodeByName(node, "deferredPeriod")
            ?.Select(n => new PlanBenefitDeferredPeriod
            {
                DeferredPeriodValue = n.Parameters.SingleOrDefault(it => it.Name == "type")?.Value switch
                {
                    "number" => new NumberDeferredPeriodValue
                    {
                        Length = long.TryParse(n.Parameters.SingleOrDefault(it => it.Name == "length")?.Value?.ToString(), out var amount) ? amount : null,
                        Period = n.Parameters.SingleOrDefault(it => it.Name == "periodUnit")?.Value as string,
                    },
                    "formula" => new NumberFormulaDeferredPeriodValue
                    {
                        Period = n.Parameters.SingleOrDefault(it => it.Name == "periodUnit")?.Value as string,
                    },
                    _ => null,
                },
            })
            ?.ToList() ?? []);
        return limits;
    }

    private static List<PlanBenefitQualificationPeriod> BuildQualifitcationPeriods(ProductRepresentationNode node)
    {
        var limits = new List<PlanBenefitQualificationPeriod>();
        limits.AddRange(GetChildArrayNodeByName(node, "qualificationPeriod")
            ?.Select(n => new PlanBenefitQualificationPeriod
            {
                QualificationPeriodValue = n.Parameters.SingleOrDefault(it => it.Name == "type")?.Value switch
                {
                    "number" => new NumberQualificationPeriodValue
                    {
                        Length = long.TryParse(n.Parameters.SingleOrDefault(it => it.Name == "length")?.Value?.ToString(), out var amount) ? amount : null,
                        Period = n.Parameters.SingleOrDefault(it => it.Name == "periodUnit")?.Value as string,
                    },
                    "formula" => new NumberFormulaQualificationPeriodValue
                    {
                        Period = n.Parameters.SingleOrDefault(it => it.Name == "periodUnit")?.Value as string,
                    },
                    _ => null,
                },
            })
            ?.ToList() ?? []);
        return limits;
    }

    static string GetNodePropertyValue(
        ProductRepresentationNode node,
        string propertyName,
        bool displayValue = false
    ) =>
        displayValue
            ? node?.Parameters?.FirstOrDefault(p => p.Name == propertyName)?.DisplayValue
            : StringValue(node?.Parameters?.FirstOrDefault(p => p.Name == propertyName)?.Value);

    static IEnumerable<ProductRepresentationNode> GetChildArrayNodeByName(
        ProductRepresentationNode planNode,
        string name
    ) => planNode?.Children?.Where(p => p.Name == name);

    static IEnumerable<ProductRepresentationNode> GetChildArrayNodeByNames(
        ProductRepresentationNode planNode,
        params string[] names
    ) => planNode?.Children?.Where(p => names.Contains(p.Name));

    static List<PlanAddOn> BuildAddOns(
        IEnumerable<ProductRepresentationNode> addOnNodes,
        IList<BenefitDefinition> benefitDefinitions,
        IList<BenefitDefinitionType> benefitTypeDefinitions)
    {
        var addOnList = new List<PlanAddOn>();
        foreach (ProductRepresentationNode addOnNode in addOnNodes ?? [])
        {
            var addOn = new PlanAddOn
            {
                Id = GetNodePropertyValue(addOnNode, "id"),
                Name = GetNodePropertyValue(addOnNode, "name"),
                Benefits = BuildBenefits(addOnNode, benefitDefinitions, benefitTypeDefinitions),
                BenefitTypes = BuildBenefitTypes(addOnNode, benefitDefinitions, benefitTypeDefinitions),
            };
            addOnList.Add(addOn);
        }
        return addOnList;
    }

    static List<ProductRepresentationNode> ReadRepresentation(string representation)
    {
        if (string.IsNullOrEmpty(representation))
        {
            return new List<ProductRepresentationNode>();
        }

        string unminified = RepresentationUnminifyRegex.Replace(
            representation,
            match =>
            {
                string key = match.Groups[1].Value;
                return _keyMapping.TryGetValue(key, out string mappedKey)
                    ? $"\"{mappedKey}\""
                    : $"\"{key}\"";
            }
        );

        List<ProductRepresentationNode> result = JsonConvert.DeserializeObject<
            List<ProductRepresentationNode>
        >(unminified);

        if (!result.Any())
            return result;

        string productType = result[0].ProductType ?? "gm";
        string productCurrency = result[0].ProductCurrency ?? "USD";
        string productPremiumBaseFrequency = result[0].PremiumBaseFrequency;

        List<ProductRepresentationNode> nodesLibrary = NodeLibrary.GetNodesLibrary(
            productType,
            productCurrency,
            productPremiumBaseFrequency
        );

        void Walk(List<ProductRepresentationNode>? nodes)
        {
            if (nodes == null)
                return;

            foreach (ProductRepresentationNode node in nodes)
            {
                AddUIProps(nodesLibrary, node);
                AddOptions(nodesLibrary, node);

                Walk(node.Children);
            }
        }

        Walk(result);

        return result;
    }

    static void GenerateParentId(
        ProductRepresentationNode node,
        ProductRepresentationNode? parentNode)
    {
        if (node != null)
        {
            node.ParentId = parentNode?.Id;
        }

        if (node?.Children != null && node.Children.Count > 0)
        {
            foreach (ProductRepresentationNode childNode in node.Children)
            {
                GenerateParentId(childNode, node);
            }
        }
    }

    static string StringValue(dynamic? dynamicObject)
    {
        if (dynamicObject is null)
            return null;

        if (dynamicObject is JArray jArray)
        {
            return string.Join(", ", jArray.Select(x => x?.ToString()));
        }

        return dynamicObject?.ToString();
    }

    static List<ProductRepresentationNode> FlattenPlan(ProductRepresentationNode planNode)
    {
        GenerateParentId(planNode, null);

        var flattenTree = new List<ProductRepresentationNode>();
        var stack = new Stack<ProductRepresentationNode>();
        stack.Push(CloneNode(planNode));

        while (stack.Count > 0)
        {
            ProductRepresentationNode temp = stack.Pop();
            temp.PlanId = planNode.Id;
            flattenTree.Add(temp);

            if (temp.Children != null && temp.Children.Count > 0)
            {
                foreach (ProductRepresentationNode child in temp.Children.AsEnumerable().Reverse())
                {
                    stack.Push(child);
                }
            }
        }

        GenerateNodeParentId(flattenTree);
        AddIdentify(flattenTree);
        AddPath(flattenTree);

        return flattenTree;
    }

    private static List<ProductRepresentationNode> FlattenNodes(
        List<ProductRepresentationNode> representation
    )
    {
        var flattenTree = new List<ProductRepresentationNode>();

        List<ProductRepresentationNode> stack = JsonConvert.DeserializeObject<
            List<ProductRepresentationNode>
        >(JsonConvert.SerializeObject(representation));

        while (stack.Count > 0)
        {
            ProductRepresentationNode temp = stack[stack.Count - 1];
            stack.RemoveAt(stack.Count - 1);
            flattenTree.Add(temp);

            if (temp?.Children?.Count > 0)
            {
                stack.AddRange(temp.Children.AsEnumerable().Reverse());
            }
        }

        return flattenTree;
    }

    static ProductRepresentationNode CloneNode(ProductRepresentationNode node) =>
        JsonConvert.DeserializeObject<ProductRepresentationNode>(JsonConvert.SerializeObject(node));

    static List<ProductRepresentationNode> CloneNode(List<ProductRepresentationNode> nodes) =>
        JsonConvert.DeserializeObject<List<ProductRepresentationNode>>(
            JsonConvert.SerializeObject(nodes)
        );

    static void GenerateNodeParentId(List<ProductRepresentationNode> flattenRepresentation)
    {
        foreach (ProductRepresentationNode node in flattenRepresentation)
        {
            // Generate all parent ids for node
            // Plan: 0
            //   BenefitType id:1 parentIds [0]
            //     benefit id:2 parentIds: [0, 1]
            //       benefit id:3 parentIds: [0, 1, 2]
            node.ParentIds = new List<string>();
            string parentId = node.ParentId;
            while (parentId != null)
            {
                node.ParentIds.Add(parentId);
                ProductRepresentationNode parentNode = flattenRepresentation.FirstOrDefault(n =>
                    n.Id == parentId
                );
                parentId = flattenRepresentation
                    .FirstOrDefault(n => n.Id == parentNode?.ParentId)
                    ?.Id;
            }
        }
    }

    private static void AddIdentify(List<ProductRepresentationNode> flattenRepresentation)
    {
        Func<ProductRepresentationNode, string> GetIdentifyHandler(string nodeName)
        {
            var identifyHandlerMapping = new Dictionary<
                string,
                Func<ProductRepresentationNode, string>
            >
            {
                { "plan", node => GetPath(node, new[] { "id" }, false, false) },
                { "addOn", node => GetPath(node, new[] { "id" }) },
                { "benefitType", node => GetPath(node, new[] { "name" }, false, false) },
                { "benefit", node => GetPath(node, new[] { "name" }, false, false) },
                {
                    "limit",
                    node =>
                        GetPath(
                            node,
                            new[] { "limitType", "currency", "limitPerTime", "limitPerVariable" },
                            true,
                            true
                        )
                },
                {
                    "groupedBenefitLimit",
                    node =>
                        GetPath(
                            node,
                            new[]
                            {
                                "limitType",
                                "currency",
                                "limitPerTime",
                                "limitPerVariable",
                                "groupedBenefits",
                            },
                            true,
                            true
                        )
                },
                { "overseasLimit", node => GetPath(node, new[] { "percentage" }, true, true) },
                {
                    "deductible",
                    node => GetPath(node, new[] { "currency", "perVariable" }, true, true)
                },
                { "coPayment", node => GetPath(node, new[] { "currency" }, true, true) },
                { "panelCoPayment", node => GetPath(node, new[] { "currency" }, true, true) },
                { "reimbursement", node => GetPath(node, new[] { "type" }, true, true) },
                { "referralLetter", node => GetPath(node, new string[] { }, true, true) },
                { "network", node => GetPath(node, new[] { "type", "currency" }) },
                { "ifCondition", node => GetPath(node, new[] { "description" }) },
                { "unitPrice", node => GetPath(node, new[] { "currency", "unit" }) },
                { "exclusiveOptions", node => GetPath(node, new string[] { }) },
                { "manualApproval", node => GetPath(node, new string[] { }) },
                { "rejection", node => GetPath(node, new string[] { }) },
                { "waitingPeriod", node => GetPath(node, new[] { "unit" }, true, true) },
                { "bandedPremium", node => GetPath(node, new[] { "bandedPremium" }) },
                { "networkSelection", node => GetPath(node, new string[] { }) },
                { "geographicalLimit", node => GetPath(node, new string[] { }) },
                { "preApproval", node => GetPath(node, new[] { "name" }) },
            };

            return nodeName is not null && identifyHandlerMapping.ContainsKey(nodeName)
                ? identifyHandlerMapping[nodeName]
                : node => $"{nodeName} --- {node.Id} -- autogenerated";
        }

        var trackMap = new Dictionary<string, List<string>>();

        foreach (ProductRepresentationNode node in flattenRepresentation)
        {
            Func<ProductRepresentationNode, string> identifyHandler = GetIdentifyHandler(node.Name);
            if (identifyHandler == null)
            {
                //Console.Error.WriteLine($"Missing identify handler for node type \"{node.Name}\"");
            }

            if (node.Name == "unitPrice")
            {
                node.SortedIdentify = "1";
            }
            else if (
                node.Name == "ifCondition"
                && node.Children?.Count == 1
                && new[] { "rejection", "manualApproval" }.Contains(node.Children[0].Name)
            )
            {
                node.SortedIdentify = "2";
            }
            else
            {
                node.SortedIdentify = "3";
            }

            if (identifyHandler != null)
                node.Identify = identifyHandler(node);

            if ((node.Name == "benefitType" || node.Name == "benefit") && node.ParentId != null)
            {
                if (!trackMap.ContainsKey(node.ParentId))
                {
                    trackMap[node.ParentId] = new List<string>();
                }

                trackMap[node.ParentId].Add(node.Identify);
                var sameIdentities = trackMap[node.ParentId]
                    .Where(identity => identity == node.Identify)
                    .ToList();
                node.Identify =
                    node.Identify + "%" + sameIdentities.Count.ToString().PadLeft(2, '0');
            }
        }
    }

    private static string GetPath(
        ProductRepresentationNode node,
        string[] parametersList,
        bool shouldAddNodeName = true,
        bool isAddIdentifyNetworkType = false,
        bool isAddIdentifyNetworks = false
    )
    {
        string path = shouldAddNodeName ? node.Name + (parametersList.Length > 0 ? "_" : "") : "";
        string parameterString = "";
        string identifyNetworkTypeString = isAddIdentifyNetworkType
            ? AddIdentifyNetworkType(node)
            : "";
        string identifyNetworksString = isAddIdentifyNetworks ? AddIdentifyNetworks(node) : "";

        if (parametersList.Length > 0)
        {
            parameterString = parametersList.Aggregate(
                "",
                (result, parameter) =>
                {
                    if (node.Parameters == null)
                        return result;
                    string parameterValue = GetParameterValue(node.Parameters, parameter);
                    if (parameter == "perVariable")
                    {
                        parameterValue =
                            GetParameterValue(node.Parameters, parameter)
                            ?? GetParameterValue(node.Parameters, "limitPerVariable");
                    }
                    return result != "" ? result + "_" + parameterValue : result + parameterValue;
                }
            );
        }

        path = path + parameterString + identifyNetworkTypeString + identifyNetworksString;
        return path;
    }

    private static string GetParameterValue(
        List<ProductRepresentationNodeParameter> parameters,
        string property
    )
    {
        dynamic parameterValue =
            parameters?.FirstOrDefault(item => item.Name == property)?.Value ?? "";
        if (property == "groupedBenefits")
        {
            return string.Join(
                ".",
                ((JArray)parameterValue)?.Select(i => i?.ToString().Split('.')[1].Replace("-", "_"))
            );
        }
        if (property == "bandedPremium")
        {
            List<ProductRepresentationNodeParameterSegment> segments =
                parameters?.FirstOrDefault()?.Segments
                ?? new List<ProductRepresentationNodeParameterSegment>();
            var optionSegments = segments.Select(segment =>
                segment.Name?.Replace(" ", "+")
            );
            return string.Join("_", optionSegments);
        }
        if (parameterValue is string[] arrayValue)
        {
            parameterValue = arrayValue[0].Replace("-", "_");
            parameterValue = new string[] { parameterValue };
        }
        else if (!string.IsNullOrEmpty(parameterValue))
        {
            parameterValue = parameterValue.Replace("-", "_");
        }
        return parameterValue;
    }

    private static string AddIdentifyNetworks(ProductRepresentationNode node)
    {
        // CH isApplyNetworkType = store.state.tenantSettings?.productBenefits?.isApplyNetworkType || false
        bool isApplyNetworkType = false;
        var DEFAULT_NODE_APPLY_NETWORK = new List<string> { "limit" };
        string defaultNetworkType = DEFAULT_NODE_APPLY_NETWORK.Contains(node.Name)
            ? "inNetworkAndOutOfNetwork"
            : "";
        string networksString =
            node.Parameters != null ? GetParameterValue(node.Parameters, "networks") : "";
        var networks = JArray
            .Parse(networksString ?? "[]")
            .OrderBy(network => network["id"])
            .ToList();
        string networkIdsString = networks.Aggregate("", (a, network) => $"{a}_{network["id"]}");
        string networkType =
            node.Parameters != null
                ? GetParameterValue(node.Parameters, "networkType")
                : defaultNetworkType;

        if (isApplyNetworkType && networkType == "inNetwork")
        {
            return networkIdsString;
        }
        else
        {
            return "";
        }
    }

    private static string AddIdentifyNetworkType(ProductRepresentationNode node)
    {
        // CH isApplyNetworkType = store.state.tenantSettings?.productBenefits?.isApplyNetworkType || false
        bool isApplyNetworkType = false;
        var DEFAULT_NODE_APPLY_NETWORK = new List<string> { "limit" };
        string defaultNetworkType = DEFAULT_NODE_APPLY_NETWORK.Contains(node.Name)
            ? "inNetworkAndOutOfNetwork"
            : "";

        if (isApplyNetworkType && node.Parameters != null)
        {
            string networkType = GetParameterValue(node.Parameters, "networkType");
            return !string.IsNullOrEmpty(networkType)
                ? $"_{networkType}"
                : $"_{defaultNetworkType}";
        }
        else
        {
            return "";
        }
    }

    private static void AddPath(List<ProductRepresentationNode> flattenRepresentation)
    {
        flattenRepresentation.ForEach(node =>
        {
            node.Path = node.ParentIds?.Aggregate(
                node.Identify,
                (finalPath, parentId) =>
                {
                    ProductRepresentationNode parent = flattenRepresentation.Find(n =>
                        n.Id == parentId
                    );
                    if (parent?.Name == "plan" || parent?.Name == "root")
                        return finalPath;
                    return parent?.Identify + "-" + finalPath;
                }
            );

            node.SortedPath = node.ParentIds?.Aggregate(
                node.SortedIdentify,
                (finalPath, parentId) =>
                {
                    ProductRepresentationNode parent = flattenRepresentation.Find(n =>
                        n.Id == parentId
                    );
                    if (parent?.Name == "plan" || parent?.Name == "root")
                        return finalPath;
                    return parent?.SortedIdentify + "." + finalPath;
                }
            );
        });
    }

    private static void AddIndentation(List<ProductRepresentationNode> flattenRepresentation)
    {
        var alteredNode = new List<string>();
        flattenRepresentation.ForEach(node =>
        {
            node.Indent = node?.ParentIds?.Count ?? 0;
            if ((node?.ParentIds?.Count ?? 0) < 2 && node?.Name != "plan" && node?.Name != "root")
            {
                node.Indent = 2;
                alteredNode.Add(node?.Id);
            }
            else if (node?.ParentIds?.Any(element => alteredNode.Contains(element)) == true)
            {
                node.Indent = (node?.ParentIds?.Count ?? 0) + 1;
            }
        });
    }

    private static List<ProductRepresentationNode> GetAllPlans(
        List<ProductRepresentationNode> representation
    )
    {
        foreach (var node in representation)
        {
            GenerateParentId(node, null);
        }

        List<ProductRepresentationNode> plansNode =
            representation
                ?[0]?.Children?.Where(node => node?.Name == "plan")
                .Cast<ProductRepresentationNode>()
                .ToList() ?? new List<ProductRepresentationNode>();
        return plansNode?.Select(FlattenNode).ToList() ?? new List<ProductRepresentationNode>();
    }

    public static ProductRepresentationNode FlattenNode(ProductRepresentationNode planNode)
    {
        var _flattenTree = new List<ProductRepresentationNode>();
        var stack = new Stack<ProductRepresentationNode>();
        stack.Push(CloneNode(planNode));
        while (stack.Count > 0)
        {
            ProductRepresentationNode temp = stack.Pop();
            temp.PlanId = planNode.Id;
            _flattenTree.Add(temp);

            if (temp?.Children?.Count > 0)
            {
                foreach (ProductRepresentationNode child in temp.Children.AsEnumerable().Reverse())
                {
                    stack.Push(child);
                }
            }
        }
        // #endregion

        GenerateNodeParentId(_flattenTree);
        AddIdentify(_flattenTree);
        AddPath(_flattenTree);

        return new ProductRepresentationNode
        {
            Id = planNode.Id,
            PlanId = GetParameterValue(planNode.Parameters, "id"),
            Name = planNode.Parameters.FirstOrDefault(i => i.Name == "name")?.Value?.ToString(),
            Children = _flattenTree,
        };
    }

    private static List<ProductRepresentationNode> GetMasterPlan(
        List<ProductRepresentationNode> representation
    )
    {
        representation = CloneNode(representation);
        representation.ForEach(node => GenerateParentId(node, null));
        var flattenRepresentation = FlattenNodes(representation)
            .Where(node => node?.Name != null)
            .ToList();
        GenerateNodeParentId(flattenRepresentation);
        AddIndentation(flattenRepresentation);
        AddIdentify(flattenRepresentation);
        AddPath(flattenRepresentation);

        // We need to add the root node here, so we can order the whole tree at first
        // And it's always expanded so we will first show all children right under it
        var masterPlan = new List<ProductRepresentationNode>
        {
            new()
            {
                Id = "master-plan-id",
                Path = "masterPlan",
                Label = "Master Plan",
                Name = "plan",
                IsExpanded = true,
                Indent = 1,
                SortedPath = "0masterPlan",
                Props = new() { Theme = "#e4b917" },
            },
        };

        masterPlan.AddRange(
            flattenRepresentation
                .GroupBy(node => node.Path)
                .Select(group => group.First())
                .Where(node => node.Name != "plan" && node.Name != "root")
                .OrderBy(node => node.SortedPath)
        );

        masterPlan.ForEach(node =>
        {
            // Put every node under masterPlan node, except the master node
            if (node.Id == "master-plan-id")
                return;
            node.Path = "masterPlan-" + node.Path;
        });

        masterPlan
            .Where(item => item.Path != "masterPlan")
            .ToList()
            .ForEach(node =>
            {
                // All nodes will collapse by default
                node.IsExpanded = false;
                // Get all children and nested children of this node
                node.Children = GetChildren(node, masterPlan);
                // Get direct parent for this node
                node.DirectParent = GetDirectParent(node, masterPlan);
                // Reset order
                node.Order = "0";
                // Set display value
                // node.Label = GetLabel(node);
                node.Summary = GetNodeSummary(node);
                node.ExtraSummary = GetExtraSummary(node);
                node.ParentId = null;
                node.ParentIds = null;
                node.ComponentName = null;
                node.Editor = null;
            });

        OrderingNode(masterPlan, masterPlan[0], "1");

        return masterPlan.OrderBy(x => x, new AscByOrderComparer()).ToList();
    }

    private static List<ProductRepresentationNode> GetChildren(
        ProductRepresentationNode node,
        List<ProductRepresentationNode> tree
    )
    {
        return tree.Where(_node => _node.Path?.StartsWith(node.Path + "-") == true).ToList();
    }

    private static string GetParentPath(ProductRepresentationNode node)
    {
        if (node == null)
            return string.Empty;
        string path =
            (node.Identify != null && node.Identify.Contains("-"))
                ? node.Path.Replace(node.Identify, "")
                : node.Path ?? string.Empty;
        return string.Join("-", path.Split('-').SkipLast(1));
    }

    private static string GetDirectParent(
        ProductRepresentationNode node,
        List<ProductRepresentationNode> tree
    )
    {
        string parentPath = GetParentPath(node);
        ProductRepresentationNode directParent = tree.FirstOrDefault(item =>
            item.Path == parentPath
        );
        return directParent?.Id;
    }

    private static string GetNodeSummary(ProductRepresentationNode node)
    {
        var editor = new
        {
            AllowedImportData = false,
            FormulaNodeLibraries = new List<string>(_commonFormulas),
            FilterSummary = new Func<ProductRepresentationNodeParameter, bool>(item =>
            {
                var PARAMETERS_NOT_INCLUDED_IN_SUMMARY = new List<string>
                {
                    "limitType",
                    "theme",
                    "id",
                    "unitPriceFormula",
                    "unitPriceAmount",
                    "inputType",
                    "reImbursementAmount",
                    "reImbursementFormula",
                    "coPaymentAmount",
                    "coPaymentFormula",
                };
                return !PARAMETERS_NOT_INCLUDED_IN_SUMMARY.Contains(item.Name);
            }),
        };

        var parametersToShow = (
            node?.Parameters ?? new List<ProductRepresentationNodeParameter>()
        )
            .Where(item => editor.FilterSummary(item))
            .Select(item =>
            {
                if (item.Name == "amount" && item.Value != null)
                    return "---";
                if (item.Name == "number" && item.Value != null)
                    return "---";
                if (item.Name == "networks" && item.Value != null)
                {
                    var itemValueString = item.Value?.ToString();
                    var networks = JToken.Parse(string.IsNullOrEmpty(itemValueString) ? "[]" : itemValueString);
                    string list = networks.Aggregate(
                        string.Empty,
                        (acc, network) =>
                            !string.IsNullOrEmpty(acc)
                                ? $"{acc}, {network["name"]}"
                                : network["name"]?.ToString()
                    );
                    return list;
                }
                return item.DisplayValue ?? item.Value;
            })
            .Where(item => item != null && !(item is IEnumerable<object>));

        return !parametersToShow.Any() ? "---" : string.Join(" / ", parametersToShow);
    }

    private static string GetExtraSummary(ProductRepresentationNode node)
    {
        var summaryBuilderMapping = new Dictionary<string, Func<ProductRepresentationNode, string>>
        {
            {
                "groupedBenefitLimit",
                node =>
                {
                    List<string> groupedBenefits =
                        (
                            node?.Parameters?.FirstOrDefault(p =>
                                p.Name == "groupedBenefits"
                            )?.Value as List<string>
                        ) ?? new List<string>();
                    string displayValue = string.Join(
                        ", ",
                        groupedBenefits.Select(item => item.Split('.')[2])
                    );
                    return !string.IsNullOrEmpty(displayValue) ? $"({displayValue})" : string.Empty;
                }
            },
        };

        string nodeName = node?.Name;
        return summaryBuilderMapping.ContainsKey(nodeName)
            ? summaryBuilderMapping[nodeName](node)
            : null;
    }

    private static void OrderingNode(
        List<ProductRepresentationNode> tree,
        ProductRepresentationNode node,
        string initialOrder
    )
    {
        try
        {
            int nodeIndex = tree.FindIndex(item => item.Path == node.Path);
            tree[nodeIndex].Order = initialOrder;
            var allChildren = tree.Where(item =>
                    item.Path?.StartsWith(node.Path + "-") == true
                    && item.Indent != null
                    && node.Indent != null
                    && item.Indent >= node.Indent
                )
                .ToList();

            allChildren.ForEach(ReOrder);

            void ReOrder(ProductRepresentationNode child)
            {
                ProductRepresentationNode directParent = tree.Find(item =>
                    item.Id == child.DirectParent
                );
                if (directParent == null)
                    return;
                var sameLevelChildren = allChildren
                    .Where(item =>
                        item.Indent == child.Indent
                        && item.Path?.StartsWith(directParent.Path + "-") == true
                        && item.Id != child.Id
                    )
                    .ToList();
                string orderCap =
                    sameLevelChildren
                        .OrderBy(x => x, new DescByOrderComparer())
                        .FirstOrDefault()
                        ?.Order?.ToString() ?? "0";
                child.Order =
                    orderCap == "0"
                        ? (directParent.Order ?? initialOrder) + ".1"
                        : IncreaseOrder(orderCap);
            }
        }
        catch (Exception e)
        {
            Console.Error.WriteLine(e);
        }
    }

    private static int DescByOrder(ProductRepresentationNode a, ProductRepresentationNode b)
    {
        int[] aOrder = a.Order?.ToString().Split('.').Select(int.Parse).ToArray();
        int[] bOrder = b.Order?.ToString().Split('.').Select(int.Parse).ToArray();
        for (int i = 0; i < Math.Max(aOrder.Length, bOrder.Length); i++)
        {
            if (i >= aOrder.Length)
                return 1;
            if (i >= bOrder.Length)
                return -1;
            if (bOrder[i] - aOrder[i] != 0)
                return bOrder[i] - aOrder[i];
        }
        return 0;
    }

    private static string IncreaseOrder(string currentOrder)
    {
        string[] reversedArrOrder = currentOrder.Split('.').Reverse().ToArray();
        reversedArrOrder[0] = (int.Parse(reversedArrOrder[0]) + 1).ToString();
        string result = string.Join(".", reversedArrOrder.Reverse());
        return result;
    }

    private static readonly Dictionary<string, string> PRICING_NODES = new Dictionary<
        string,
        string
    >
    {
        { "unitPrice", "unitPrice" },
        { "bandedPremium", "bandedPremium" },
    };

    public static void AddOptions(
        List<ProductRepresentationNode> nodesLibrary,
        ProductRepresentationNode node
    )
    {
        ProductRepresentationNode nodeConfig = nodesLibrary.FirstOrDefault(item =>
            item.Name == node.Name
        );
        if (nodeConfig == null)
            return;

        var configParamsWithPropsOptions = nodeConfig
            .Parameters?.Where(item => item.Props?.Options != null)
            .ToList();
        var nodeParamsWithPropsOptions = node
            .Parameters?.Where(item => item.Props?.Options != null)
            .ToList();

        if (
            (nodeParamsWithPropsOptions == null || !nodeParamsWithPropsOptions.Any())
            && configParamsWithPropsOptions != null
            && configParamsWithPropsOptions.Any()
        )
        {
            node.Parameters?.ForEach(param =>
            {
                param.Props =
                    configParamsWithPropsOptions.FirstOrDefault(i => i.Name == param.Name)?.Props
                    ?? param.Props;
            });
        }

        if (PRICING_NODES.ContainsKey(node.Name))
        {
            ProductRepresentationNodeParameter perTimeUnit = node.Parameters?.FirstOrDefault(item =>
                item.Name == "limitPerTime"
            );
            if (perTimeUnit == null)
            {
                ProductRepresentationNodeParameter defaultPerTimeUnit =
                    configParamsWithPropsOptions?.FirstOrDefault(item =>
                        item.Name == "limitPerTime"
                    );
                if (defaultPerTimeUnit != null)
                {
                    if (node.Parameters != null)
                    {
                        node.Parameters.Add(defaultPerTimeUnit);
                    }
                    else
                    {
                        node.Parameters = new List<ProductRepresentationNodeParameter>
                        {
                            defaultPerTimeUnit,
                        };
                    }
                }
            }
        }
    }

    public static void AddUIProps(
        List<ProductRepresentationNode> nodesLibrary,
        ProductRepresentationNode node
    )
    {
        ProductRepresentationNode nodeConfig = nodesLibrary.FirstOrDefault(item =>
            item.Name == node.Name
        );
        if (nodeConfig == null)
            return;

        if (string.IsNullOrEmpty(node.ComponentName))
            node.ComponentName = nodeConfig.ComponentName;
        if (node.Editor == null)
            node.Editor = nodeConfig.Editor;
        if (node.Props == null)
            node.Props = nodeConfig.Props;
        if (string.IsNullOrEmpty(node.Label))
            node.Label = nodeConfig.Label;
    }

    class DescByOrderComparer : IComparer<ProductRepresentationNode>
    {
        public int Compare(ProductRepresentationNode? a, ProductRepresentationNode? b)
        {
            int[] aOrder = a.Order?.ToString().Split('.').Select(int.Parse).ToArray();
            int[] bOrder = b.Order?.ToString().Split('.').Select(int.Parse).ToArray();
            for (int i = 0; i < Math.Max(aOrder.Length, bOrder.Length); i++)
            {
                if (i >= aOrder.Length)
                    return 1;
                if (i >= bOrder.Length)
                    return -1;
                if (bOrder[i] - aOrder[i] != 0)
                    return bOrder[i] - aOrder[i];
            }
            return 0;
        }
    }

    class AscByOrderComparer : IComparer<ProductRepresentationNode>
    {
        public int Compare(ProductRepresentationNode? a, ProductRepresentationNode? b)
        {
            int[] aOrder = a.Order?.ToString().Split('.').Select(int.Parse).ToArray();
            int[] bOrder = b.Order?.ToString().Split('.').Select(int.Parse).ToArray();
            for (int i = 0; i < Math.Max(aOrder.Length, bOrder.Length); i++)
            {
                if (i >= aOrder.Length)
                    return -1;
                if (i >= bOrder.Length)
                    return 1;
                if (aOrder[i] - bOrder[i] != 0)
                    return aOrder[i] - bOrder[i];
            }
            return 0;
        }
    }

    private static List<AttachedDocument> MapAttachedDocuments(List<AttachedDocumentId> attachedDocumentIds, List<Template> templates)
    {
        return templates.Select(t => new AttachedDocument
        {
            Id = t.Id,
            LogicalId = t.LogicalId,
            Name = t.Name,
            Type = t.Type,
            Category = attachedDocumentIds.Where(d => d.TemplateId == t.Id).Select(d => d.Category).FirstOrDefault()
        }).ToList();
    }
}