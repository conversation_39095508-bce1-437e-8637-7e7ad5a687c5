#nullable enable

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Dynamic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.Configuration;
using CoverGo.DomainUtils;
using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.Products.Domain.Facts;
using CoverGo.Products.Domain.Illustrations;
using CoverGo.Products.Domain.Products.Commands;
using CoverGo.Products.Domain.Products.Events;
using CoverGo.Products.Domain.Products.Ports;
using CoverGo.Products.Domain.Underwriting;
using CoverGo.Threading.Tasks;

using JsonLogic.Net;
using MediatR;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;

namespace CoverGo.Products.Domain.Products
{
    public class ProductService
    {
        private readonly IEnumerable<IIllustrationRepository> _illustrationRepositories;
        private readonly IEnumerable<IUnderwritingEngine> _underwritingRepositories;
        private readonly ProductEventService _productEventService;
        private readonly JsonSerializer _jsonSerializer;
        private readonly JsonLogicEvaluator _evaluator;
        private readonly IMultiTenantFeatureManager _multiTenantFeatureManager;
        private readonly IProductScriptsRepository _productScriptsRepository;
        private readonly IProductVersionRepository _productVersionRepository;
        private readonly IProductRepository _productRepository;
        private readonly IReferenceGenerator _refGen;
        private readonly IProductNameRepository _productNameRepository;
        private readonly IMediator _mediator;

        private static readonly List<ProductEventType> _addEvents = new List<ProductEventType>
        {
            ProductEventType.addBenefit,
            ProductEventType.addUnderwritingVariable,
            ProductEventType.updateUnderwritingVariable,
            ProductEventType.addTag,
            ProductEventType.addFact,
            ProductEventType.addInternalReview,
        };

        private static readonly List<ProductEventType> _removeEvents = new List<ProductEventType>
        {
            ProductEventType.removeBenefit,
            ProductEventType.delete,
            ProductEventType.removeUnderwritingVariable,
            ProductEventType.removeTag,
            ProductEventType.removeFact,
            ProductEventType.removeInternalReview
        };

        public ProductService(
            ProductEventService productEventService,
            IEnumerable<IIllustrationRepository> illustrationRepositories,
            IEnumerable<IUnderwritingEngine> underwritingRepositories,
            IMultiTenantFeatureManager multiTenantFeatureManager,
            IProductScriptsRepository productScriptsRepository,
            IProductVersionRepository productVersionRepository,
            IProductRepository productRepository,
            IReferenceGenerator referenceGenerator,
            JsonLogicEvaluator evaluator,
            IProductNameRepository productNameRepository,
            IMediator mediator)
        {
            _productEventService = productEventService;

            _illustrationRepositories = illustrationRepositories;
            _underwritingRepositories = underwritingRepositories;

            var settings = new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
                TypeNameHandling = TypeNameHandling.Auto,
                NullValueHandling = NullValueHandling.Ignore
            };
            settings.Converters.Add(new StringEnumConverter());

            _jsonSerializer = JsonSerializer.Create(settings);
            _multiTenantFeatureManager = multiTenantFeatureManager;
            _productScriptsRepository = productScriptsRepository;
            _productVersionRepository = productVersionRepository;
            _productRepository = productRepository;
            _refGen = referenceGenerator;
            _evaluator = evaluator;
            _productNameRepository = productNameRepository;
            _mediator = mediator;
        }

        public async Task<Result> MigrateProductsAsync(string tenantId, MigrateProductsCommand command, CancellationToken cancellationToken)
        {
            Result result = await _productRepository.MigrateProductsAsync(tenantId, command, cancellationToken);

            if (result.Status != "success")
                return result;

            await command.ProductInputs?.ParallelForEachAsync(async (p, ct) =>
                await _productEventService.AddEventAsync(tenantId, new ProductEvent(p.ProductId, ProductEventType.creation, DateTime.UtcNow)
                {
                    Values = JObject.FromObject(p, _jsonSerializer)
                }, ct), new ParallelRunOptions() { MaxDegreeOfParallelism = 10, CancellationToken = cancellationToken });

            return result;
        }

        public async Task<Result> CleanProductTest(string tenantId, string typeId, CancellationToken cancellationToken)
        {
            return await _productRepository.CleanProductTest(tenantId, typeId, cancellationToken);
        }

        public virtual Task<IEnumerable<Product>> GetAsync(string tenantId, string? clientId, ProductWhere where, QueryParameters queryParameters, bool loadRepresentation = false, CancellationToken cancellationToken = default)
        {
            return GetAsync(tenantId, clientId, where, null!, queryParameters, loadRepresentation, cancellationToken);
        }
        public virtual async Task<IEnumerable<Product>> GetAsync(string tenantId, string? clientId, ProductWhere where, JToken factors, QueryParameters queryParameters, bool loadRepresentation = false, CancellationToken cancellationToken = default)
        {
            ProductConfig config = await _productRepository.GetProductConfigAsync(tenantId, clientId, cancellationToken);

            IEnumerable<Product> products = await _productRepository.GetAsync(tenantId, where, config, queryParameters, loadRepresentation, cancellationToken);

            if (factors == null)
                return products;

            ExpandoObject dataToken = JsonConvert.DeserializeObject<ExpandoObject>(JsonConvert.SerializeObject(factors), new ExpandoObjectConverter());

            var validProducts = new ConcurrentBag<Product>();

            var underwrittenProducts = new ConcurrentBag<Product>();
            IEnumerable<IGrouping<string, Product>> productsByUnderwritingEngineIds = products.GroupBy(p => p.Underwriting.SourceType);

            await productsByUnderwritingEngineIds.ParallelForEachAsync(async (g, ct) =>
            {
                IUnderwritingEngine underwritingEngine = _underwritingRepositories.FirstOrDefault(r => r.ProviderId == g.Key);
                if (_productRepository != null)
                {
                    IEnumerable<(ProductId id, Result result)> evaluationResults = await underwritingEngine.EvaluateUnderwritingsAsync(tenantId, g.Select(u => u.Id), factors, ct);
                    IEnumerable<ProductId> validIds = evaluationResults.Where(e => e.result?.Status == "success").Select(p => p.id);

                    foreach (ProductId validId in validIds)
                        underwrittenProducts.Add(products.FirstOrDefault(p => p.Id == validId));
                }
                ;
            }, new ParallelRunOptions() { MaxDegreeOfParallelism = 10, CancellationToken = cancellationToken });

            Parallel.ForEach(underwrittenProducts, p =>
            {
                if (factors?.Value<DateTime?>("startDate") != null) //NOTE: Used only by apex
                {
                    int? effectiveDayLimit = config.Meta?.EffectiveDayLimits?.FirstOrDefault(edl => edl.ProductId == p.Id)?.Limit;

                    if (effectiveDayLimit != null && effectiveDayLimit <= (factors.Value<DateTime?>("startDate") - DateTime.UtcNow.Date)?.Days)
                        return;
                }

                foreach (Benefit benefit in p.Benefits ?? new List<Benefit>())
                {
                    if (benefit.Condition == null)
                        continue;

                    //change evaluator here based on type
                    if (benefit.Condition.JsonLogicRule?.HasValues ?? false)
                    {
                        try
                        {
                            object eval = _evaluator.Apply(benefit.Condition.JsonLogicRule, dataToken);
                            benefit.Value = JToken.FromObject(eval);
                        }
                        catch
                        {
                            continue;
                        }
                    }
                }

                validProducts.Add(p);
            });

            return validProducts.OrderBy(p => p.Id?.Plan);
        }

        public async Task<long> GetTotalCountAsync(string tenantId, string? clientId, ProductWhere where, CancellationToken cancellationToken = default)
        {
            ProductConfig config = await _productRepository.GetProductConfigAsync(tenantId, clientId, cancellationToken);
            long productsCount = await _productRepository.GetTotalCountAsync(tenantId, where, config, cancellationToken);
            return productsCount;
        }

        public async virtual Task<Result> CloneAsync(string tenantId, CloneProductCommand command, CancellationToken cancellationToken)
        {
            command.IssuerProductId ??= await _refGen.GenerateAsync(_productRepository, tenantId, "addProduct", cancellationToken: cancellationToken);

            var product = await _productVersionRepository.Get(command.ProductId, cancellationToken);
            command.ScriptIds = await _productScriptsRepository.CloneProductScripts(product.ScriptIds, cancellationToken);

            Result result = await _productRepository.CloneAsync(tenantId, command, cancellationToken);

            return result.Status != "success"
                ? result
                : await _productEventService.AddEventAsync(tenantId, new ProductEvent(command.CloneProductId, ProductEventType.creation, DateTime.UtcNow)
                {
                    Values = JObject.FromObject(command, _jsonSerializer)
                }, cancellationToken);
        }

        public async Task<IEnumerable<ProductUnderwritingJsonLogicRules>> GetUnderwritingJsonLogicRulesAsync(string tenantId, ProductWhere where, CancellationToken cancellationToken)
        {
            return await _productRepository.GetUnderwritingJsonLogicRulesAsync(tenantId, where, cancellationToken);
        }

        public async Task<IEnumerable<ProductUnderwritingJsonSchema>> GetUnderwritingJsonSchemasAsync(string tenantId, ProductUnderwritingJsonSchemaQuery query, CancellationToken cancellationToken)
        {
            IEnumerable<IGrouping<string, ProductUnderwritingJsonSchemaKey>> productsByUnderwritingEngineIds = query.Keys.GroupBy(p => p.UnderwritingEngineId);

            var productUnderwritingJsonSchemas = new ConcurrentBag<ProductUnderwritingJsonSchema>();

            await productsByUnderwritingEngineIds.ParallelForEachAsync(async (g, ct) =>
            {
                IUnderwritingEngine underwritingEngine = _underwritingRepositories.FirstOrDefault(r => r.ProviderId == g.Key);
                if (underwritingEngine != null)
                {
                    IEnumerable<(ProductId, string)> results = await underwritingEngine.GetUnderwritingJsonSchema(tenantId, g.Select(u => u.ProductId), query.Factors, ct);
                    foreach ((ProductId id, string schema) in results)
                        productUnderwritingJsonSchemas.Add(new ProductUnderwritingJsonSchema { ProductId = id, JsonSchema = schema, UnderwritingEngineId = g.Key });
                }
            }, new ParallelRunOptions() { MaxDegreeOfParallelism = 10, CancellationToken = cancellationToken });

            return productUnderwritingJsonSchemas;
        }

        public async Task<Result> CreateAsync(string tenantId, CreateProductCommand command, CancellationToken cancellationToken)
        {
            if (command.Tags?.Any() ?? false)
                foreach (Tag tag in command.Tags)
                    tag.Id = Guid.NewGuid().ToString();

            if (command.Facts?.Any() ?? false)
                foreach (AddFactCommand fact in command.Facts)
                    fact.Id = Guid.NewGuid().ToString();

            command.IssuerProductId ??= await _refGen.GenerateAsync(_productRepository, tenantId, "addProduct", command, cancellationToken);

            if (await _multiTenantFeatureManager.IsEnabled("ValidatePdtId", tenantId))
            {
                Result pdtIdValidationResult = await this.ValidateAxaThPdtIdOnCreate(tenantId, command, _productRepository, cancellationToken);
                if (!pdtIdValidationResult.IsSuccess) return pdtIdValidationResult;
            }

            Result result = await _productRepository.CreateAsync(tenantId, command, cancellationToken);

            return result.Status != "success"
              ? result
              : await _productEventService.AddEventAsync(tenantId, new ProductEvent(command.ProductId, ProductEventType.creation, DateTime.UtcNow)
              {
                  Values = JObject.FromObject(command, _jsonSerializer)
              }, cancellationToken);
        }

        public async virtual Task<Result> UpdateAsync(string tenantId, UpdateProductCommand command, CancellationToken cancellationToken)
        {
            if (await _multiTenantFeatureManager.IsEnabled("ValidatePdtId", tenantId))
            {
                Result pdtIdValidationResult = await this.ValidateAxaThPdtIdOnUpdate(tenantId, command, _productRepository, cancellationToken);
                if (!pdtIdValidationResult.IsSuccess) return pdtIdValidationResult;
            }

            IEnumerable<Product> products = await _productRepository.GetAsync(tenantId, new ProductWhere { Id_in = new List<ProductId> { command.ProductId } }, null!, new QueryParameters(), cancellationToken: cancellationToken);
            Product product = products.FirstOrDefault();
            if (product == null)
                return new Result { Status = "failure", Errors = new List<string> { $"The product {command.ProductId.ToString()} was not found." } };

            var useLegacyLifecycle = await _multiTenantFeatureManager.IsEnabled("UseLegacyLifecycle", tenantId);
            bool isCustomVersion = product?.Id?.Version?.Contains("custom") ?? false;
            if (!useLegacyLifecycle && !isCustomVersion)
            {
                command = RestrictVersioningUpdate(product, command);
            }

            Result result = await _productRepository.UpdateAsync(tenantId, command, cancellationToken);

            return result.Status != "success"
              ? result
              : await _productEventService.AddEventAsync(tenantId, new ProductEvent(command.ProductId, ProductEventType.update, DateTime.UtcNow)
              {
                  Values = JObject.FromObject(command, _jsonSerializer)
              }, cancellationToken);
        }

        private readonly static IReadOnlyList<string> NEW_VERSION_LOCKED_FIELDS = [
                    "productName",
                    "productType",
                    "productCurrency",
                    "premiumBaseFrequency",
                    "prorateMethod",
                    "paymentProvider"
                ];
        private readonly static IReadOnlyList<string> NEW_ALPHA_VERSION_ALLOWED_FIELDS = [
                    "pricingModel",
                    "pricingFactors",
                    "offerValidityPeriod",
                    "policyIssuanceMethod",
                    "allowCustomProduct"
                ];
        private UpdateProductCommand RestrictVersioningUpdate(Product product, UpdateProductCommand command)
        {
            var restrictedCommand = command;
            if(double.TryParse(product.Id.Version, CultureInfo.InvariantCulture, out double version) && version > 1)
            {
                var updatingFields = JObject.Parse(command.Fields ?? "{}");
                var existingFields = JToken.Parse(product.Fields ?? "{}");
                var restrictedFields = updatingFields.Children().OfType<JProperty>().Aggregate(new JObject(), (JObject obj, JProperty next) => {
                    var name = next.Name;
                    if (NEW_VERSION_LOCKED_FIELDS.Contains(name))
                    {
                        obj[name] = existingFields[name];
                    }
                    else
                    {
                        obj[name] = next.Value;
                    }
                    return obj;
                });
                restrictedCommand = restrictedCommand with {
                    Fields = restrictedFields?.ToString()
                };

                if (product.LifecycleStage != "alpha")
                {
                    restrictedFields = restrictedFields?.Children().OfType<JProperty>().Aggregate(new JObject(), (JObject obj, JProperty next) => {
                        var name = next.Name;
                        if (NEW_ALPHA_VERSION_ALLOWED_FIELDS.Contains(name))
                        {
                            obj[name] = existingFields[name];
                        }
                        else
                        {
                            obj[name] = next.Value;
                        }
                        return obj;
                    });
                    restrictedCommand = restrictedCommand with {
                        IsOfferValidityPeriodChanged = false,
                        IsPolicyIssuanceMethodChanged = false,
                        IsAllowCustomProductChanged = false,
                        Fields = restrictedFields?.ToString()
                    };
                }
            }

            return restrictedCommand;
        }

        public async Task<IEnumerable<Proxies.Product.EventLog>> GetEventLogsAsync(string tenantId, Proxies.Product.EventQuery query, CancellationToken cancellationToken)
        {
            IEnumerable<ProductEvent> events = await _productEventService.GetEventsAsync(tenantId, query.ProductIds.Select(id => (ProductId)id), cancellationToken);

            IEnumerable<Proxies.Product.EventLog> eventLogs = HandleLogs(events);
            return eventLogs;
        }

        public async Task<Result<IEnumerable<Illustration>>> GetIllustrationsAsync(string tenantId, ProductWhere where, JToken factors, CancellationToken cancellationToken)
        {
            var dbconfig = DbConfig.GetConfig(tenantId, "illustrations");
            IIllustrationRepository repository = _illustrationRepositories.First(r => r.ProviderId == dbconfig.ProviderId);

            Result<IEnumerable<Illustration>> illustrations = await repository.GetIllustrationsAsync(tenantId, where, factors, cancellationToken);
            return illustrations;
        }

        public async Task<Result> AddBenefitAsync(string tenantId, ProductId productId, AddBenefitCommand command, CancellationToken cancellationToken)
        {
            Result result = await _productRepository.AddBenefitAsync(tenantId, productId, command, cancellationToken);

            return result.Status != "success"
             ? result
             : await _productEventService.AddEventAsync(tenantId, new ProductEvent(productId, ProductEventType.addBenefit, DateTime.UtcNow)
             {
                 Values = JObject.FromObject(command, _jsonSerializer)
             }, cancellationToken);
        }

        public async Task<Result> UpdateBenefitAsync(string tenantId, ProductId productId, UpdateBenefitCommand command, CancellationToken cancellationToken)
        {
            Result result = await _productRepository.UpdateBenefitAsync(tenantId, productId, command, cancellationToken);

            return result.Status != "success"
             ? result
             : await _productEventService.AddEventAsync(tenantId, new ProductEvent(productId, ProductEventType.updateBenefit, DateTime.UtcNow)
             {
                 Values = JObject.FromObject(command, _jsonSerializer)
             }, cancellationToken);
        }

        public async Task<Result> BenefitBatchAsync(string tenantId, ProductId productId, BenefitCommandBatch batch, CancellationToken cancellationToken)
        {
            IEnumerable<Product> products = await _productRepository.GetAsync(tenantId, new ProductWhere { Id_in = new List<ProductId> { productId } }, null, new QueryParameters(), cancellationToken: cancellationToken);
            Product product = products.FirstOrDefault();
            if (product == null)
                return new Result { Status = "failure", Errors = new List<string> { $"The product {productId.ToString()} was not found." } };

            var errors = new List<string> { };

            if (batch.AddBenefitCommands?.Any() == true)
            {
                Result result = await _productRepository.AddManyBenefitsAsync(tenantId, productId, batch.AddBenefitCommands, cancellationToken);

                if (result.Status != "success")
                    errors.AddRange(result.Errors);
                else
                {
                    // create add fact events
                    var addBenefitEventResults = batch.AddBenefitCommands.Select(
                            async c => await _productEventService.AddEventAsync(tenantId,
                                new ProductEvent(productId, ProductEventType.addBenefit, DateTime.UtcNow)
                                {
                                    Values = JObject.FromObject(c, _jsonSerializer)
                                }, cancellationToken))
                        .Select(t => t.Result)
                        .Where(i => i != null)
                        .ToList();

                    IEnumerable<Result> failedAddBenefitEventResults =
                        addBenefitEventResults.Where(r => r.Status != "success");
                    if (failedAddBenefitEventResults?.Any() == true)
                        errors.AddRange(failedAddBenefitEventResults.FirstOrDefault().Errors);
                }
            }

            if (batch.UpdateBenefitCommands?.Any() == true)
            {
                IEnumerable<(string, string)> benefitIds = batch.UpdateBenefitCommands?.Select(b => (b.TypeId, b.OptionKey));
                IEnumerable<(string, string)> unMatchedBenefitIds = benefitIds?.Where(id => !product.Benefits.ToList().Exists(b => b.TypeId == id.Item1 && b.OptionKey == id.Item2));
                if (unMatchedBenefitIds?.Count() > 0)
                    errors.Add(
                            $"Benefits to update of " +
                            $"'{String.Join(", ", unMatchedBenefitIds.Select(b => "TypeIds: " + b.Item1 + "; OptionKey: " + b.Item2))}' " +
                            $"were not found for this product."
                    );

                Result result = await _productRepository.UpdateManyBenefitsAsync(tenantId, productId, batch.UpdateBenefitCommands, cancellationToken);

                if (result.Status != "success")
                    errors.AddRange(result.Errors);
                else
                {
                    var updateBenefitEventResults = batch.UpdateBenefitCommands.Select(
                            async c => await _productEventService.AddEventAsync(tenantId, new ProductEvent(productId, ProductEventType.updateBenefit, DateTime.UtcNow)
                            {
                                Values = JObject.FromObject(c, _jsonSerializer)
                            }, cancellationToken))
                        .Select(t => t.Result)
                        .Where(i => i != null)
                        .ToList();

                    IEnumerable<Result> failedUpdateBenefitEventResults = updateBenefitEventResults.Where(r => r.Status != "success");
                    if (failedUpdateBenefitEventResults?.Any() == true)
                        errors.AddRange(failedUpdateBenefitEventResults.FirstOrDefault().Errors);
                }
            }

            if (batch.RemoveBenefitCommands?.Any() == true)
            {
                IEnumerable<(string, string)> benefitIds = batch.RemoveBenefitCommands?.Select(b => (b.TypeId, b.OptionKey));
                IEnumerable<(string, string)> unMatchedBenefitIds = benefitIds?.Where(id => !product.Benefits.ToList().Exists(b => b.TypeId == id.Item1 && b.OptionKey == id.Item2));
                if (unMatchedBenefitIds?.Count() > 0)
                    errors.Add(
                        $"Benefits to remove of " +
                            $"'{String.Join(", ", unMatchedBenefitIds.Select(b => "TypeIds: " + b.Item1 + "; OptionKey: " + b.Item2))}' " +
                            $"were not found for this product."
                    );

                Result result = await _productRepository.RemoveManyBenefitsAsync(tenantId, productId, batch.RemoveBenefitCommands, cancellationToken);

                List<Result> removeBenefitEventResults = batch.RemoveBenefitCommands.Select(
                        async c => await _productEventService.AddEventAsync(tenantId, new ProductEvent(productId, ProductEventType.removeBenefit, DateTime.UtcNow)
                        {
                            Values = JObject.FromObject(c, _jsonSerializer)
                        }, cancellationToken))
                    .Select(t => t.Result)
                    .Where(i => i != null)
                    .ToList();

                var failedRemoveBenefitEventResults = removeBenefitEventResults.Where(r => r.Status != "success");
                if (failedRemoveBenefitEventResults?.Any() == true)
                    errors.AddRange(failedRemoveBenefitEventResults.FirstOrDefault().Errors);
            }

            return errors.Any()
                ? Result.Failure(errors)
                : Result.Success();
        }

        public async Task<Result> RemoveBenefitAsync(string tenantId, ProductId productId, RemoveBenefitCommand command, CancellationToken cancellationToken)
        {
            Result result = await _productRepository.RemoveBenefitAsync(tenantId, productId, command, cancellationToken);

            return result.Status != "success"
             ? result
             : await _productEventService.AddEventAsync(tenantId, new ProductEvent(productId, ProductEventType.removeBenefit, DateTime.UtcNow)
             {
                 Values = JObject.FromObject(command, _jsonSerializer)
             }, cancellationToken);
        }

        public async Task<Result<CreatedStatus>> AddTagAsync(string tenantId, ProductId productId, AddTagCommand command, CancellationToken cancellationToken)
        {
            command.Id = Guid.NewGuid().ToString();
            Result result = await _productRepository.AddTagAsync(tenantId, productId, command, cancellationToken);

            if (result.Status != "success")
                return new Result<CreatedStatus> { Status = result.Status, Errors = result.Errors };

            Result addEventResult = await _productEventService.AddEventAsync(tenantId, new ProductEvent(productId, ProductEventType.addTag, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return new Result<CreatedStatus> { Status = addEventResult.Status, Errors = addEventResult.Errors, Value = new CreatedStatus { Id = command.Id } };
        }

        public async Task<Result> RemoveTagAsync(string tenantId, ProductId productId, RemoveCommand command, CancellationToken cancellationToken)
        {
            Result result = await _productRepository.RemoveTagAsync(tenantId, productId, command, cancellationToken);

            return result.Status != "success"
             ? result
             : await _productEventService.AddEventAsync(tenantId, new ProductEvent(productId, ProductEventType.removeTag, DateTime.UtcNow)
             {
                 Values = JObject.FromObject(command, _jsonSerializer)
             }, cancellationToken);
        }

        public async Task<Result<CreatedStatus>> AddFactAsync(string tenantId, ProductId productId, AddFactCommand command, CancellationToken cancellationToken)
        {
            command.Id = Guid.NewGuid().ToString();
            Result result = await _productRepository.AddFactAsync(tenantId, productId, command, cancellationToken);

            if (result.Status != "success")
                return new Result<CreatedStatus> { Status = result.Status, Errors = result.Errors };

            Result addEventResult = await _productEventService.AddEventAsync(tenantId, new ProductEvent(productId, ProductEventType.addFact, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return new Result<CreatedStatus> { Status = addEventResult.Status, Errors = addEventResult.Errors, Value = new CreatedStatus { Id = command.Id } };
        }

        public async Task<Result> UpdateFactAsync(string tenantId, ProductId productId, UpdateFactCommand command, CancellationToken cancellationToken)
        {
            IEnumerable<Product> products = await _productRepository.GetAsync(tenantId, new ProductWhere { Id_in = new List<ProductId> { productId } }, null, new QueryParameters(), cancellationToken: cancellationToken);
            Product product = products.FirstOrDefault();
            if (product == null)
                return new Result { Status = "failure", Errors = new List<string> { $"The product {productId.ToString()} was not found." } };
            else if (!product.Facts?.Any(i => i.Id == command.Id) ?? true)
                return new Result { Status = "failure", Errors = new List<string> { $"The fact with Id '{command.Id}' was not found for this product." } };

            Result result = await _productRepository.UpdateFactAsync(tenantId, productId, command, cancellationToken);

            if (result.Status != "success")
                return new Result { Status = result.Status, Errors = result.Errors };

            Result addEventResult = await _productEventService.AddEventAsync(tenantId, new ProductEvent(productId, ProductEventType.updateFact, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return new Result { Status = addEventResult.Status, Errors = addEventResult.Errors };
        }

        public async Task<Result> FactBatchAsync(string tenantId, ProductId productId, FactCommandBatch command, CancellationToken cancellationToken)
        {
            IEnumerable<Product> products = await _productRepository.GetAsync(tenantId, new ProductWhere { Id_in = new List<ProductId> { productId } }, null, new QueryParameters(), cancellationToken: cancellationToken);
            Product product = products.FirstOrDefault();
            if (product == null)
                return new Result { Status = "failure", Errors = new List<string> { $"The product {productId.ToString()} was not found." } };

            IEnumerable<string> factIds = command.UpdateFactCommands?.Select(c => c.Id);
            IEnumerable<string> unMatchedFactIds = factIds?.Where(id => !product.Facts.ToList().Exists(f => f.Id == id));
            if (unMatchedFactIds?.Count() > 0)
                return new Result { Status = "failure", Errors = new List<string> { $"Facts of Ids '{String.Join(", ", unMatchedFactIds)}' were not found for this product." } };

            // Add facts to product if command.AddFactCommands is not empty
            if (command.AddFactCommands?.Any() == true)
            {
                command.AddFactCommands?.ForEach(c => c.Id = Guid.NewGuid().ToString());
                Result result = await _productRepository.AddManyFactsAsync(tenantId, productId, command.AddFactCommands, cancellationToken);

                if (result.Status != "success")
                    return new Result { Status = result.Status, Errors = result.Errors };

                // create add fact events
                List<Result> addFactEventResults = command.AddFactCommands.Select(
                    async c => await _productEventService.AddEventAsync(tenantId, new ProductEvent(productId, ProductEventType.addFact, DateTime.UtcNow)
                    {
                        Values = JObject.FromObject(c, _jsonSerializer)
                    }, cancellationToken))
                    .Select(t => t.Result)
                    .Where(i => i != null)
                    .ToList();

                var failedAddFactEventResults = addFactEventResults.Where(r => r.Status != "success");
                if (failedAddFactEventResults?.Any() == true)
                    return new Result { Status = failedAddFactEventResults.FirstOrDefault().Status, Errors = failedAddFactEventResults.FirstOrDefault().Errors };
            }

            // Update facts of product if command.UpdateFactCommands is not empty
            if (command.UpdateFactCommands?.Any() == true)
            {
                Result result = await _productRepository.UpdateManyFactsAsync(tenantId, productId, command.UpdateFactCommands, cancellationToken);

                if (result.Status != "success")
                    return new Result { Status = result.Status, Errors = result.Errors };

                // create update facts events
                List<Result> updateFactEventResults = command.UpdateFactCommands.Select(
                    async c => await _productEventService.AddEventAsync(tenantId, new ProductEvent(productId, ProductEventType.updateFact, DateTime.UtcNow)
                    {
                        Values = JObject.FromObject(c, _jsonSerializer)
                    }, cancellationToken))
                    .Select(t => t.Result)
                    .Where(i => i != null)
                    .ToList();

                var failedUpdateFactEventResults = updateFactEventResults.Where(r => r.Status != "success");
                if (failedUpdateFactEventResults?.Any() == true)
                    return new Result { Status = failedUpdateFactEventResults.FirstOrDefault().Status, Errors = failedUpdateFactEventResults.FirstOrDefault().Errors };
            }

            return new Result { Status = "success" };
        }

        public async Task<Result> RemoveFactAsync(string tenantId, ProductId productId, RemoveCommand command, CancellationToken cancellationToken)
        {
            Result result = await _productRepository.RemoveFactAsync(tenantId, productId, command, cancellationToken);

            return result.Status != "success"
             ? result
             : await _productEventService.AddEventAsync(tenantId, new ProductEvent(productId, ProductEventType.removeFact, DateTime.UtcNow)
             {
                 Values = JObject.FromObject(command, _jsonSerializer)
             }, cancellationToken);
        }

        public async Task<Result<CreatedStatus>> AddInternalReviewAsync(string tenantId, ProductId productId, AddInternalReviewCommand command, CancellationToken cancellationToken)
        {
            command.Id = Guid.NewGuid().ToString();
            IEnumerable<Product> products = await _productRepository.GetAsync(tenantId, new ProductWhere { Id_in = new List<ProductId> { productId } }, null, new QueryParameters(), cancellationToken: cancellationToken);
            Product product = products.FirstOrDefault();
            if (product == null)
                return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The product {productId.ToString()} was not found." } };

            Result result = await _productRepository.AddInternalReviewAsync(tenantId, productId, command, cancellationToken);

            if (result.Status != "success")
                return new Result<CreatedStatus> { Status = result.Status, Errors = result.Errors };

            Result addEventResult = await _productEventService.AddEventAsync(tenantId, new ProductEvent(productId, ProductEventType.addInternalReview, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return new Result<CreatedStatus> { Status = addEventResult.Status, Errors = addEventResult.Errors, Value = new CreatedStatus { Id = command.Id } };
        }

        public async Task<Result> UpdateInternalReviewAsync(string tenantId, ProductId productId, UpdateInternalReviewCommand command, CancellationToken cancellationToken)
        {
            IEnumerable<Product> products = await _productRepository.GetAsync(tenantId, new ProductWhere { Id_in = new List<ProductId> { productId } }, null, new QueryParameters(), cancellationToken: cancellationToken);
            Product product = products.FirstOrDefault();
            if (product == null)
                return new Result { Status = "failure", Errors = new List<string> { $"The product {productId.ToString()} was not found." } };
            else if (!product.InternalReviews?.Any(i => i.Id == command.Id) ?? true)
                return new Result { Status = "failure", Errors = new List<string> { $"The internal review with Id '{command.Id}' was not found for this product." } };

            Result result = await _productRepository.UpdateInternalReviewAsync(tenantId, productId, command, cancellationToken);

            if (result.Status != "success")
                return new Result { Status = result.Status, Errors = result.Errors };

            Result updateEventResult = await _productEventService.AddEventAsync(tenantId, new ProductEvent(productId, ProductEventType.updateInternalReview, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return new Result { Status = updateEventResult.Status, Errors = updateEventResult.Errors };
        }

        public async Task<Result> RemoveInternalReviewAsync(string tenantId, ProductId productId, RemoveCommand command, CancellationToken cancellationToken)
        {
            IEnumerable<Product> products = await _productRepository.GetAsync(tenantId, new ProductWhere { Id_in = new List<ProductId> { productId } }, null, new QueryParameters(), cancellationToken: cancellationToken);
            Product product = products.FirstOrDefault();
            if (product == null)
                return new Result { Status = "failure", Errors = new List<string> { $"The product {productId.ToString()} was not found." } };
            else if (!product.InternalReviews?.Any(i => i.Id == command.Id) ?? true)
                return new Result { Status = "failure", Errors = new List<string> { $"The internal review with Id '{command.Id}' was not found for this product." } };

            Result result = await _productRepository.RemoveInternalReviewAsync(tenantId, productId, command, cancellationToken);

            return result.Status != "success"
             ? result
             : await _productEventService.AddEventAsync(tenantId, new ProductEvent(productId, ProductEventType.removeInternalReview, DateTime.UtcNow)
             {
                 Values = JObject.FromObject(command, _jsonSerializer)
             }, cancellationToken);
        }

        public async Task<Result> DeleteAsync(string tenantId, DeleteProductCommand command, CancellationToken cancellationToken)
        {
            Result result = await _productRepository.DeleteAsync(tenantId, command, cancellationToken);

            return result.Status != "success"
            ? result
            : await _productEventService.AddEventAsync(tenantId, new ProductEvent(command.ProductId, ProductEventType.delete, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);
        }

        public async Task<Dictionary<string, Dictionary<string, List<string>>>> GetBenefitInfosAsync(string tenantId, string clientId, CancellationToken cancellationToken)
        {
            return await _productRepository.GetBenefitInfosAsync(tenantId, clientId, cancellationToken);
        }

        public async Task<Dictionary<string, List<string>>> GetBenefitCategoriesAsync(string tenantId, string clientId, CancellationToken cancellationToken)
        {
            return await _productRepository.GetBenefitCategoriesAsync(tenantId, clientId, cancellationToken);
        }

        public async Task<IEnumerable<ProductConfig>> GetProductConfigsAsync(string tenantId, ProductConfigWhere where, OrderBy orderBy, int? skip, int? first, CancellationToken cancellationToken)
        {
            return await _productRepository.GetProductConfigsAsync(tenantId, where, orderBy, skip, first, cancellationToken);
        }

        public async Task<Result> CreateProductConfig(string tenantId, ProductConfig productConfig, CancellationToken cancellationToken)
        {
            ProductConfig exisingConfig = (await _productRepository.GetProductConfigsAsync(tenantId, new ProductConfigWhere { ClientId = productConfig.ClientId }, cancellationToken: cancellationToken)).FirstOrDefault();
            if (exisingConfig != null)
                return new Result { Status = "failure", Errors = new List<string> { $"A config already exists for clientId `{productConfig.ClientId}`" } };

            Result<CreatedStatus> result = await _productRepository.CreateConfigAsync(tenantId, productConfig, cancellationToken);
            return new Result { Status = result.Status, Errors = result.Errors?.ToList() };
        }

        public async Task<Result<CreatedStatus>> CreateProductConfig(string tenantId, CreateProductConfigCommand command, CancellationToken cancellationToken)
        {
            ProductConfig exisingConfig = (await _productRepository.GetProductConfigsAsync(tenantId, new ProductConfigWhere { ClientId = command.ClientId }, cancellationToken: cancellationToken)).FirstOrDefault();
            if (exisingConfig != null)
                return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"A config already exists for clientId `{command.ClientId}`" } };

            var productConfig = new ProductConfig
            {
                ClientId = command.ClientId,
                DisplayedBenefits = command.DisplayedBenefits,
                DisplayedInsurers = command.DisplayedInsurers,
                DisplayedProducts = command.DisplayedProducts,
                DisplayedTypes = command.DisplayedTypes,
                Meta = command.Meta
            };

            return await _productRepository.CreateConfigAsync(tenantId, productConfig, cancellationToken);
        }

        public async Task<Result> UpdateProductConfig(string tenantId, string id, UpdateProductConfigCommand command, CancellationToken cancellationToken)
        {
            ProductConfig config = (await _productRepository.GetProductConfigsAsync(tenantId, new ProductConfigWhere { Id = id }, cancellationToken: cancellationToken)).FirstOrDefault();
            if (config == null)
                return new Result { Status = "failure", Errors = new List<string> { $"No existing config found for id `{id}`." } };

            if (command.IsClientIdChanged)
            {
                ProductConfig existingconfig = (await _productRepository.GetProductConfigsAsync(tenantId, new ProductConfigWhere { ClientId = command.ClientId }, cancellationToken: cancellationToken)).FirstOrDefault();
                if (existingconfig != null)
                    return new Result { Status = "failure", Errors = new List<string> { $"Existing config found for clientId `{command.ClientId}`." } };
                config.ClientId = command.ClientId;
            }
            if (command.IsDisplayedBenefitsChanged)
                config.DisplayedBenefits = command.DisplayedBenefits;
            if (command.IsDisplayedInsurersChanged)
                config.DisplayedInsurers = command.DisplayedInsurers;
            if (command.IsDisplayedProductsChanged)
                config.DisplayedProducts = command.DisplayedProducts;
            if (command.IsDisplayedTypesChanged)
                config.DisplayedTypes = command.DisplayedTypes;
            if (command.IsMetaChanged)
            {
                if (command.Meta == null)
                    config.Meta = null;
                else if (config.Meta == null)
                    config.Meta = new Meta { };

                if (config.Meta.EffectiveDayLimits != null)
                    config.Meta.EffectiveDayLimits = command.Meta.EffectiveDayLimits;
            }

            return await _productRepository.UpdateConfigAsync(tenantId, config, cancellationToken);
        }

        public async Task<Result> DeleteProductConfig(string tenantId, string id, CancellationToken cancellationToken)
        {
            return await _productRepository.DeleteConfigAsync(tenantId, id, cancellationToken);
        }

        public async Task<Result> AddUnderwritingVariableAsync(string tenantId, ProductId productId, AddUnderwritingVariableCommand command, CancellationToken cancellationToken)
        {
            Result result = await _productRepository.AddUnderwritingVariableAsync(tenantId, productId, command, cancellationToken);

            return result.Status != "success"
               ? result
               : await _productEventService.AddEventAsync(tenantId, new ProductEvent(productId, ProductEventType.addUnderwritingVariable, DateTime.UtcNow)
               {
                   Values = JObject.FromObject(command, _jsonSerializer)
               }, cancellationToken);
        }

        public async Task<Result> UpdateUnderwritingVariableAsync(string tenantId, ProductId productId, UpdateUnderwritingVariableCommand command, CancellationToken cancellationToken)
        {
            Result result = await _productRepository.UpdateUnderwritingVariableAsync(tenantId, productId, command, cancellationToken);

            return result.Status != "success"
               ? result
               : await _productEventService.AddEventAsync(tenantId, new ProductEvent(productId, ProductEventType.updateUnderwritingVariable, DateTime.UtcNow)
               {
                   Values = JObject.FromObject(command, _jsonSerializer)
               }, cancellationToken);
        }

        public async Task<Result> RemoveUnderwritingVariableAsync(string tenantId, ProductId productId, RemoveCommand command, CancellationToken cancellationToken)
        {
            Result result = await _productRepository.RemoveUnderwritingVariableAsync(tenantId, productId, command, cancellationToken);

            return result.Status != "success"
               ? result
               : await _productEventService.AddEventAsync(tenantId, new ProductEvent(productId, ProductEventType.removeUnderwritingVariable, DateTime.UtcNow)
               {
                   Values = JObject.FromObject(command, _jsonSerializer)
               }, cancellationToken);
        }

        public Task<IEnumerable<ValidateResult>> ValidateProductsAsync(string tenantId, string clientId, ProductWhere where, JToken factors, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();

        }

        public async Task<IEnumerable<ProductType>> GetTypesAsync(string tenantId, string? clientId, ProductTypeWhere where, CancellationToken cancellationToken)
        {
            return await _productRepository.GetTypesAsync(tenantId, clientId, where, cancellationToken);
        }

        public async Task<Result> CreateTypeAsync(string tenantId, CreateProductTypeCommand command, CancellationToken cancellationToken)
        {
            Result result = await _productRepository.CreateTypeAsync(tenantId, command, cancellationToken);

            return result.Status != "success"
                ? result
                : await _productEventService.AddTypeEventAsync(tenantId, new ProductTypeEvent(command.TypeId, ProductTypeEventType.creation, DateTime.UtcNow)
                {
                    Values = JObject.FromObject(command, _jsonSerializer)
                }, cancellationToken);
        }

        public async Task<Result> DeleteTypeAsync(string tenantId, string typeId, string deletedById, CancellationToken cancellationToken)
        {
            Result result = await _productRepository.DeleteTypeAsync(tenantId, typeId, deletedById, cancellationToken);

            return result.Status != "success"
               ? result
               : await _productEventService.AddTypeEventAsync(tenantId, new ProductTypeEvent(typeId, ProductTypeEventType.delete, DateTime.UtcNow)
               {
                   Values = JObject.FromObject(new DeleteCommand
                   {
                       DeletedById = deletedById
                   }, _jsonSerializer)
               }, cancellationToken);
        }

        public Task<Result> AddScriptToProduct(string tenantId, AddScriptToProductCommand command, CancellationToken cancellationToken) =>
            _productRepository.AddScriptToProduct(tenantId, command, cancellationToken);

        public Task<Result> RemoveScriptFromProduct(string tenantId, RemoveScriptFromProductCommand command, CancellationToken cancellationToken) =>
            _productRepository.RemoveScriptFromProduct(tenantId, command, cancellationToken);

        public Task<Result> AddTemplateRelationshipToProduct(string tenantId, AddTemplateRelationshipToProductCommand command, CancellationToken cancellationToken) =>
            _productRepository.AddTemplateRelationshipToProduct(tenantId, command, cancellationToken);

        public Task<Result> RemoveTemplateRelationshipFromProduct(string tenantId, RemoveTemplateRelationshipFromProductCommand command, CancellationToken cancellationToken) =>
            _productRepository.RemoveTemplateRelationshipFromProduct(tenantId, command, cancellationToken);

        public Task<Result> AddDataSchemaToProductTypeToProduct(string tenantId, AddDataSchemaToProductTypeCommand command, CancellationToken cancellationToken) =>
            _productRepository.AddDataSchemaToProductTypeToProduct(tenantId, command, cancellationToken);

        public Task<Result> RemoveDataSchemaToProductTypeFromProduct(string tenantId, RemoveDataSchemaFromProductTypeCommand command, CancellationToken cancellationToken) =>
            _productRepository.RemoveDataSchemaToProductTypeFromProduct(tenantId, command, cancellationToken);

        public async Task<Result> AttachDocumentsToProduct(string tenantId, AttachDocumentsCommand input, CancellationToken cancellationToken)
        {
            Result result = await _productRepository.AttachDocumentsToProduct(tenantId, input, cancellationToken);

            return result.Status != "success"
                ? result
                : await _productEventService.AddEventAsync(tenantId, new ProductEvent(input.ProductId, ProductEventType.attachDocuments, DateTime.UtcNow)
                {
                    Values = JObject.FromObject(input, _jsonSerializer)
                }, cancellationToken);
        }

        public async Task<Result> RemoveAttachedDocument(string tenantId, RemoveAttachedDocumentCommand input, CancellationToken cancellationToken)
        {
            Result result = await _productRepository.RemoveAttachedDocumentFromProduct(tenantId, input, cancellationToken);

            return result.Status != "success"
                ? result
                : await _productEventService.AddEventAsync(tenantId, new ProductEvent(input.ProductId, ProductEventType.removeAttachedDocument, DateTime.UtcNow)
                {
                    Values = JObject.FromObject(input, _jsonSerializer)
                }, cancellationToken);
        }

        private static IEnumerable<Proxies.Product.EventLog> HandleLogs(IEnumerable<ProductEvent> productEvents) => //Reminder: add new command to here
           productEvents.Select(e =>
               new Proxies.Product.EventLog
               {
                   RelatedId = e.ProductId.ToString(),
                   Type = e.Type.ToString(),

                   Timestamp = e.Timestamp,
                   Value = e.Values,
                   ById = GetById(e)
               })?.ToList();

        private static string GetById(ProductEvent @event) //Reminder: check if new eventTypes need to be added here
        {
            if (@event.Type == ProductEventType.creation) return @event.Values.Value<string>("createdById");
            else if (_addEvents.Contains(@event.Type)) return @event.Values.Value<string>("addedById");
            else if (_removeEvents.Contains(@event.Type)) return @event.Values.Value<string>("removedById");
            else return @event.Values.Value<string>("modifiedById");
        }

        public async Task<Result<string>> GetNextIssuerProductId(string tenantId, CancellationToken cancellationToken)
        {
            string issuerProductId = await _refGen.GenerateAsync(_productRepository, tenantId, "addProduct", cancellationToken: cancellationToken);

            return Result<string>.Success(issuerProductId);
        }

        public async Task<Result> SetTermsAndConditionsTemplateToProduct(string tenantId, SetTermsAndConditionsCommand input, CancellationToken cancellationToken)
        {
            Result result = await _productRepository.SetTermsAndConditionsTemplateToProduct(tenantId, input, cancellationToken);

            return result.Status != "success"
                ? result
                : await _productEventService.AddEventAsync(tenantId, new ProductEvent(input.ProductId, ProductEventType.setTermsAndConditions, DateTime.UtcNow)
                {
                    Values = JObject.FromObject(input, _jsonSerializer)
                }, cancellationToken);
        }

        public async Task<Result> RemoveTermsAndConditionsTemplateFromProduct(string tenantId, RemoveTermsAndConditionsCommand input, CancellationToken cancellationToken)
        {
            Result result = await _productRepository.RemoveTermsAndConditionsTemplateFromProduct(tenantId, input, cancellationToken);

            return result.Status != "success"
                ? result
                : await _productEventService.AddEventAsync(tenantId, new ProductEvent(input.ProductId, ProductEventType.removeTermsAndConditions, DateTime.UtcNow)
                {
                    Values = JObject.FromObject(input, _jsonSerializer)
                }, cancellationToken);
        }

        public async Task<Result> SetRatingFactorsTableToProduct(string tenantId, SetRatingFactorsTableCommand input, CancellationToken cancellationToken)
        {
            Result result = await _productRepository.SetRatingFactorsTableToProduct(tenantId, input, cancellationToken);

            return result.Status != "success"
                ? result
                : await _productEventService.AddEventAsync(tenantId, new ProductEvent(input.ProductId, ProductEventType.setTermsAndConditions, DateTime.UtcNow)
                {
                    Values = JObject.FromObject(input, _jsonSerializer)
                }, cancellationToken);
        }
        public async Task<Result> RemoveRatingFactorsTableFromProduct(string tenantId, RemoveRatingFactorsTableCommand input, CancellationToken cancellationToken)
        {
            Result result = await _productRepository.RemoveRatingFactorsTableFromProduct(tenantId, input, cancellationToken);

            return result.Status != "success"
                ? result
                : await _productEventService.AddEventAsync(tenantId, new ProductEvent(input.ProductId, ProductEventType.removeRatingFactorsTable, DateTime.UtcNow)
                {
                    Values = JObject.FromObject(input, _jsonSerializer)
                }, cancellationToken);
        }
        public async Task<Result> SetSegmentsToProduct(string tenantId, SetSegmentsCommand input, CancellationToken cancellationToken)
        {
            Result result = await _productRepository.SetSegmentsToProduct(tenantId, input, cancellationToken);

            return result.Status != "success"
                ? result
                : await _productEventService.AddEventAsync(tenantId, new ProductEvent(input.ProductId, ProductEventType.setSegments, DateTime.UtcNow)
                {
                    Values = JObject.FromObject(input, _jsonSerializer)
                }, cancellationToken);
        }

        public async Task<Result> AddEventAsync(string tenantId, ProductEvent productEvent,
             CancellationToken cancellationToken)
        {

            Result eventResult = await _productEventService.AddEventAsync(tenantId, productEvent, cancellationToken);

            return eventResult;
        }

        public async Task<CloneProductVersionCommandResult> CloneProduct(
            ICreateProductVersionCommand request,
            string? cloneProductVersion = default,
            TenantId? tenantId = default,
            CancellationToken cancellationToken = default)
        {
            var originalProductId = request.OriginalProductId;
            var product = await _productVersionRepository.Get(originalProductId, cancellationToken);
            const string alpha = "alpha";
            var isCreating = cloneProductVersion is null;
            if (isCreating)
            {
                if (product.LifecycleStage == alpha)
                {
                    throw new ProductVersioningException("New versions of a product can only be created when the product is not in Alpha stage");
                }

                IEnumerable<Product> products = await _productRepository.GetAsync(tenantId?.Value, new ProductWhere
                {
                    ProductId = new ProductIdWhere
                    {
                        Plan = request.OriginalProductId.Plan,
                        Type = request.OriginalProductId.Type
                    }
                }, null!, new QueryParameters(), cancellationToken: cancellationToken);
                var baseVersion = products
                    .Select(p => p.Id.Version)
                    .Aggregate(new List<double>(), (acc, v) =>
                    {
                        if (double.TryParse(v, CultureInfo.InvariantCulture, out double version))
                        {
                            acc.Add(version);
                        }
                        return acc;
                    })
                    .Order()
                    .LastOrDefault();
                if (baseVersion > default(double))
                {
                    cloneProductVersion = IncreaseVersion(baseVersion);
                }
                else
                {
                    throw new ProductVersioningException("Invalid base version");
                }
            }
            var productName = await _productNameRepository.Get(request.OriginalProductId, cancellationToken);

            var newScriptIds = await _productScriptsRepository.CloneProductScripts(product.ScriptIds, cancellationToken);

            var clonedProduct = product.CloneProduct(cloneProductVersion, request.IsReadonly, newScriptIds, isCreating ? alpha : default);
            var clonedProductName = productName ?? product.Id.Plan;
            clonedProductName = await _productNameRepository.Create(clonedProduct.Id, clonedProductName, cancellationToken);

            await _mediator.Publish(new ProductCloned
            {
                OriginalProduct = product,
                ClonedProduct = clonedProduct,
                ClonedProductName = clonedProductName,
            }, cancellationToken);
            return new()
            {
                ClonedProduct = clonedProduct,
                ClonedProductName = clonedProductName,
            };
        }

        private static string IncreaseVersion(double version)
        {
            const double increment = 1.0d;
            return $"{version + increment}.0";
        }
    }
}
