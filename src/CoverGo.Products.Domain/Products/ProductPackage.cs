using System;
using System.Collections.Generic;

namespace CoverGo.Products.Domain.Products
{
    public class ProductPackage
    {
        public ProductMetadata Metadata { get; set; }
        public string ProductTreeContent { get; set; }
        public string DataSchemaContent { get; set; }
        public string UISchemaContent { get; set; }
        public string ValidationScriptContent { get; set; }
        public IReadOnlyDictionary<string, string> ExternalTablesContent { get; set; }
    }

    public class ProductMetadata
    {
        public ProductId ProductId { get; set; }
        public string ProductName { get; set; }
        public string ProductLifecycleStage { get; set; }
        public DateTime? ProductLastModifiedAt { get; set; }
        public string ProductLastModifiedBy { get; set; }
        public string Tenant { get; set; }
        public DateTime ExportedAt { get; set; }
        public string ExportedBy { get; set; }
        public string[] IncludedEntries { get; set; }
        public string RequestId { get; set; }
    }
}