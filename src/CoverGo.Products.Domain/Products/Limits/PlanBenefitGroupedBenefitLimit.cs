#nullable enable

using System;
using System.Collections.Generic;
using System.Linq;

using CoverGo.Products.Domain.BenefitDefinitions;
using CoverGo.Products.Domain.BenefitDefinitionTypes;
using HotChocolate.Types;

namespace CoverGo.Products.Domain.Products.Limits;

public record PlanBenefitGroupedBenefitLimit
{
    public ILimitValue? LimitValue { get; set; }
    public List<IGroupedBenefitItem> GroupedBenefits { get; set; } = [];
    public string? Description { get; set; }

    public virtual bool Equals(PlanBenefitGroupedBenefitLimit? other)
    {
        return other is PlanBenefitGroupedBenefitLimit planBenefitGroupedBenefitLimit
            && LimitValue.Equals(planBenefitGroupedBenefitLimit.LimitValue)
            && Description == planBenefitGroupedBenefitLimit.Description
            && GroupedBenefits.Count == planBenefitGroupedBenefitLimit.GroupedBenefits.Count
            && GroupedBenefits
                .Select((it, index) => (Value: it, Index: index))
                .All(it => it.Value.Equals(planBenefitGroupedBenefitLimit.GroupedBenefits[it.Index]));
    }

    public override int GetHashCode() => HashCode.Combine(LimitValue, Description, GroupedBenefits.Count, GroupedBenefits.Select(it => it));
}

[UnionType]
public interface IGroupedBenefitItem
{
}

public record GroupedBenefit : IGroupedBenefitItem
{
    public required BenefitDefinition Value { get; init; }
}

public record GroupedBenefitType : IGroupedBenefitItem
{
    public required BenefitDefinitionType Value { get; init; }
}
