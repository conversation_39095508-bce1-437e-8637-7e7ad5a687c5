#nullable enable

using HotChocolate.Types;

namespace CoverGo.Products.Domain.Products.Limits;

public record PlanBenefitQualificationPeriod
{
    public IQualificationPeriodValue? QualificationPeriodValue { get; set; }
}

[UnionType]
public interface IQualificationPeriodValue
{
}

public record NumberQualificationPeriodValue : IQualificationPeriodValue
{
    public long? Length { get; set; }

    public string? Period { get; set; }
}

public record NumberFormulaQualificationPeriodValue : IQualificationPeriodValue
{
    public string? Period { get; set; }

    // public Formula? Formula { get; set; } Add this later if needed. Don't do it as JSON string - not usable by any customer.
}
