#nullable enable

using HotChocolate.Types;

namespace CoverGo.Products.Domain.Products.Limits;

public record PlanBenefitReimbursement
{
    public IReimbursementValue? ReimbursementValue { get; set; }

    public string? Description { get; set; }
}

[UnionType]
public interface IReimbursementValue
{
}

public record PercentageReimbursementValue : IReimbursementValue
{
    public decimal? Amount { get; set; }
}

public record PercentageFormulaReimbursementValue : IReimbursementValue
{
    public string? Description { get; set; }
    // public Formula? Formula { get; set; } Add this later if needed. Don't do it as JSON string - not usable by any customer.
}
