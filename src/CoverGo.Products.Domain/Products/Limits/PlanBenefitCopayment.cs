#nullable enable

using HotChocolate.Types;

namespace CoverGo.Products.Domain.Products.Limits;

public record PlanBenefitCopayment
{
    public ICopaymentValue? CopaymentValue { get; set; }

    public string? Description { get; set; }
}

[UnionType]
public interface ICopaymentValue
{
}

public record AmountCopaymentValue : ICopaymentValue
{
    public decimal? Amount { get; set; }

    public string? Currency { get; set; }
}

public record AmountFormulaCopaymentValue : ICopaymentValue
{
    public string? Currency { get; set; }

    // public Formula? Formula { get; set; } Add this later if needed. Don't do it as JSON string - not usable by any customer.
}
