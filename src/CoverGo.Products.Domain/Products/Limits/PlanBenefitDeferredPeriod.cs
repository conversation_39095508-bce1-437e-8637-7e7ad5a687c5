#nullable enable

using HotChocolate.Types;

namespace CoverGo.Products.Domain.Products.Limits;

public record PlanBenefitDeferredPeriod
{
    public IDeferredPeriodValue? DeferredPeriodValue { get; set; }
}

[UnionType]
public interface IDeferredPeriodValue
{
}

public record NumberDeferredPeriodValue : IDeferredPeriodValue
{
    public long? Length { get; set; }

    public string? Period { get; set; }
}

public record NumberFormulaDeferredPeriodValue : IDeferredPeriodValue
{
    public string? Period { get; set; }

    // public Formula? Formula { get; set; } Add this later if needed. Don't do it as JSON string - not usable by any customer.
}
