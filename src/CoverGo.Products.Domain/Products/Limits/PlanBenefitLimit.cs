#nullable enable

using System;
using System.Collections.Generic;
using System.Linq;
using HotChocolate.Types;

namespace CoverGo.Products.Domain.Products.Limits;

public record PlanBenefitLimit
{
    public ILimitValue? LimitValue { get; set; }

    public INetworkType? Network { get; set; }

    public string? Description { get; set; }
}

[UnionType]
public interface ILimitValue
{
}

public record AmountLimitValue : ILimitValue
{
    public decimal? Amount { get; set; }

    public string? Currency { get; set; }

    /// <summary>
    /// Limit per time. Not a display value.
    /// Display value is hard coded in FE.
    /// </summary>
    public string? PerTimeUnit { get; set; }

    /// <summary>
    /// Limit per variable. Not a display value.
    /// Display value is hard coded in FE.
    /// </summary>
    public string? PerVariableUnit { get; set; }
}

public record NumberLimitValue : ILimitValue
{
    public long? Amount { get; set; }

    /// <summary>
    /// Unit. Not a display value.
    /// Display value is hard coded in FE.
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    /// Limit per time. Not a display value.
    /// Display value is hard coded in FE.
    /// </summary>
    public string? PerTimeUnit { get; set; }

    /// <summary>
    /// Limit per variable. Not a display value.
    /// Display value is hard coded in FE.
    /// </summary>
    public string? PerVariableUnit { get; set; }
}

public record AmountFormulaLimitValue : ILimitValue
{
    public string? Currency { get; set; }

    /// <summary>
    /// Limit per time. Not a display value.
    /// Display value is hard coded in FE.
    /// </summary>
    public string? PerTimeUnit { get; set; }

    /// <summary>
    /// Limit per variable. Not a display value.
    /// Display value is hard coded in FE.
    /// </summary>
    public string? PerVariableUnit { get; set; }

    // public Formula? Formula { get; set; } Add this later if needed. Don't do it as JSON string - not usable by any customer.
}

[UnionType]
public interface INetworkType
{
}

public record InNetworkAndOutOfNetwork : INetworkType
{
    public string? Description { get; set; }
}

public record OutOfNetwork : INetworkType
{
    public string? Description { get; set; }
}

public record InNetwork : INetworkType
{
    public List<InNetworkOrganization> Organizations { get; set; } = [];

    public virtual bool Equals(InNetwork? other)
    {
        return
            other is InNetwork inNetwork &&
            Organizations.Count == inNetwork.Organizations.Count &&
            Organizations
                .Select((it, index) => (Value: it, Index: index))
                .All(it => it.Value == inNetwork.Organizations[it.Index]);
    }

    public override int GetHashCode() => HashCode.Combine(Organizations.Select(it => it));
}

public record InNetworkOrganization
{
    /// <summary>
    /// Taken from organizations query.
    /// </summary>
    public string? Id { get; set; }

    /// <summary>
    /// Name persisted to PB Node from organizations query.
    /// </summary>
    public string? Name { get; set; }
}
