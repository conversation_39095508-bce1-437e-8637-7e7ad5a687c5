#nullable enable

using HotChocolate.Types;

namespace CoverGo.Products.Domain.Products.Limits;

public record PlanBenefitPanelCopayment
{
    public IPanelCopaymentValue? PanelCopaymentValue { get; set; }
}

[UnionType]
public interface IPanelCopaymentValue
{
}

public record AmountPanelCopaymentValue : IPanelCopaymentValue
{
    public decimal? Amount { get; set; }

    public string? Currency { get; set; }

    /// <summary>
    /// Limit per time. Not a display value.
    /// Display value is hard coded in FE.
    /// </summary>
    public string? PerTimeUnit { get; set; }

    /// <summary>
    /// Limit per variable. Not a display value.
    /// Display value is hard coded in FE.
    /// </summary>
    public string? PerVariableUnit { get; set; }
}
