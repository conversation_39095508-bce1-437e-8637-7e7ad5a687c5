#nullable enable

using HotChocolate.Types;

namespace CoverGo.Products.Domain.Products.Limits;

public record PlanBenefitWaitingPeriod
{
    public IWaitingPeriodValue? WaitingPeriodValue { get; set; }

    public string? Description { get; set; }
}

[UnionType]
public interface IWaitingPeriodValue
{
}

public record AmountWaitingPeriodValue : IWaitingPeriodValue
{
    public decimal? Amount { get; set; }

    public string? Unit { get; set; }
}

public record AmountFormulaWaitingPeriodValue : IWaitingPeriodValue
{
    public string? Unit { get; set; }
}
