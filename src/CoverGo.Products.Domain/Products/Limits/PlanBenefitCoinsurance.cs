#nullable enable

using HotChocolate.Types;

namespace CoverGo.Products.Domain.Products.Limits;

public record PlanBenefitCoinsurance
{
    public ICoinsuranceValue? CoinsuranceValue { get; set; }

    public ICoinsuranceMaxLimitValue? MaxLimit { get; set; }

    public string? Description { get; set; }
}

[UnionType]
public interface ICoinsuranceValue
{
}

public record PercentageCoinsuranceValue : ICoinsuranceValue
{
    public decimal? Amount { get; set; }
}

public record PercentageFormulaCoinsuranceValue : ICoinsuranceValue
{
    public string? Description { get; set; }
    // public Formula? Formula { get; set; } Add this later if needed. Don't do it as JSON string - not usable by any customer.
}

[UnionType]
public interface ICoinsuranceMaxLimitValue
{
}

public record AmountCoinsuranceMaxLimitValue : ICoinsuranceMaxLimitValue
{
    public decimal? Amount { get; set; }

    public string? Currency { get; set; }
}

public record AmountFormulaCoinsuranceMaxLimitValue : ICoinsuranceMaxLimitValue
{
    public string? Currency { get; set; }

    // public Formula? Formula { get; set; } Add this later if needed. Don't do it as JSON string - not usable by any customer.
}
