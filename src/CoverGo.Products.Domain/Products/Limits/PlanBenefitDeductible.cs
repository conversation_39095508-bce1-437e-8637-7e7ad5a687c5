#nullable enable

using HotChocolate.Types;

namespace CoverGo.Products.Domain.Products.Limits;

public record PlanBenefitDeductible
{
    public IDeductibleValue? DeductibleValue { get; set; }

    public string? Description { get; set; }
}

[UnionType]
public interface IDeductibleValue
{
}

public record AmountDeductibleValue : IDeductibleValue
{
    public decimal? Amount { get; set; }

    public string? Currency { get; set; }

    /// <summary>
    /// Limit per time. Not a display value.
    /// Display value is hard coded in FE.
    /// </summary>
    public string? PerTimeUnit { get; set; }

    /// <summary>
    /// Limit per variable. Not a display value.
    /// Display value is hard coded in FE.
    /// </summary>
    public string? PerVariableUnit { get; set; }
}

public record AmountFormulaDeductibleValue : IDeductibleValue
{
    public string? Currency { get; set; }

    /// <summary>
    /// Limit per time. Not a display value.
    /// Display value is hard coded in FE.
    /// </summary>
    public string? PerTimeUnit { get; set; }

    /// <summary>
    /// Limit per variable. Not a display value.
    /// Display value is hard coded in FE.
    /// </summary>
    public string? PerVariableUnit { get; set; }

    // public Formula? Formula { get; set; } Add this later if needed. Don't do it as JSON string - not usable by any customer.
}
