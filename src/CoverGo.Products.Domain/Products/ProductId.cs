﻿using System;

namespace CoverGo.Products.Domain.Products
{
    public class ProductId
    {
        public string Plan { get; set; }
        public string Version { get; set; }
        public string Type { get; set; }

        public override bool Equals(object obj) => obj is ProductId id &&
            Plan == id.Plan &&
            Version == id.Version &&
            Type == id.Type;

        public override int GetHashCode() => HashCode.Combine(Plan, Version, Type);

        public override string ToString() => $"{Plan}|{Version}|{Type}";

        public static bool operator ==(ProductId left, ProductId right) =>
            left?.Plan == right?.Plan &&
            left?.Type == right?.Type &&
            left?.Version == right?.Version;

        public static bool operator !=(ProductId left, ProductId right) => !(left == right);

        public static ProductId FromString(string idString)
        {
            try
            {
                string[] split = idString.Split('|');
                return new ProductId
                {
                    Plan = split[0],
                    Version = string.IsNullOrEmpty(split[1]) ? null : split[1],
                    Type = split[2]
                };
            }
            catch
            {
                return null;
            }
        }

        public static explicit operator ProductId(Proxies.Product.ProductId v)
        {
            return new ProductId
            {
                Plan = v.Plan,
                Type = v.Type,
                Version = v.Version
            };
        }
    }
}
