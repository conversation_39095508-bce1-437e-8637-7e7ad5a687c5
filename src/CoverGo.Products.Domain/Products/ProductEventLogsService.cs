using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Products.Ports;
using CoverGo.Proxies.Auth;
using CoverGo.Reference.Client;
using CoverGo.Users.Client;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Domain.Products
{
    public class ProductEventLogsService
    {
        private readonly IProductEventStore _productEventStore;
        private readonly IProductBuilderEventStore _productBuilderEventStore;
        private readonly ProductService _productService;
        private readonly IAuthService _authService;
        private readonly IUsersClient _usersClient;
        private readonly ILogger<ProductEventLogsService> _logger;

        public ProductEventLogsService(IProductEventStore productEventStore,
            IProductBuilderEventStore productBuilderEventStore,
            ProductService productService,
            IAuthService authService,
            IUsersClient usersClient,
            ILogger<ProductEventLogsService> logger)
        {
            _productEventStore = productEventStore;
            _productBuilderEventStore = productBuilderEventStore;
            _productService = productService;
            _authService = authService;
            _usersClient = usersClient;
            _logger = logger;
        }
        public async Task<List<ProductEventLog>> GetProductEventLogsAsync(string tenantId, string productType, ProductEventWhere where, OrderBy? orderBy, int skip, int first, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Getting product events from MONGO");
            var productEvents = (await _productEventStore.GetEventsAsync(tenantId, productType, where, orderBy, skip, first, cancellationToken)).ToList();
            _logger.LogInformation("Finished Getting product events from MONGO with count:{count}",productEvents.Count);


            var userIds = GetUserIds();
            var logins = await GetLogins();

            List<Entity> entities = await GetEntitiesMapping();

            var logUserLogInMapping = userIds.ToDictionary(id => id, id => logins?.FirstOrDefault(x => x.Id == id));
            var logUserIdMapping = userIds.ToDictionary(id => id, id => entities?.FirstOrDefault(user => user.EntityId == (logins?.FirstOrDefault(login => login.Id == id)?.EntityId)));

            var productEventLogs = MapProductEventLogs();

            if (orderBy != null && 
                string.Equals(orderBy.FieldName, "timestamp", System.StringComparison.OrdinalIgnoreCase) && 
                orderBy.Type == OrderByType.ASC)
                productEventLogs = productEventLogs.OrderBy(e => e.Timestamp);
            else
                productEventLogs = productEventLogs.OrderByDescending(e => e.Timestamp);

            return productEventLogs.ToList();


            IEnumerable<ProductEventLog> MapProductEventLogs()
            {
                return productEvents.Select(e => new ProductEventLog
                {
                    Id = e.Id,
                    ProductId = e.ProductId,
                    Ref = e.Ref,
                    Alias = e.Alias,
                    Type = e.Type == ProductEventType.nodeEvent ? e.NodeEventType : e.Type.ToString(),
                    NodeId = e.NodeId,
                    Values = e.Values.ToString(),
                    Timestamp = e.Timestamp,
                    UpdatedBy = new User()
                    {
                        Email = GetProductUpdatedByUser(e)?.Email ?? e.UpdatedById,
                        Username = GetProductUpdatedByUser(e)?.Username ?? e.UpdatedById,
                        Name = GetProductUpdatedByUser(e)?.Name
                    }
                });
            }


            async Task<IEnumerable<Login>> GetLogins()
            {
                var logins = await _authService.GetLoginsAsync(tenantId, new LoginWhere() { Ids = userIds }, cancellationToken);
                return logins;
            }

            async Task<List<Entity>> GetEntitiesMapping()
            {
                var entityIds = logins?.Select(x => x.EntityId).ToList();
                var entityIdsWithType = await _usersClient.Entities_QueryIdsAndTypesAsync(tenantId, new EntityWhere { Id_in = entityIds }, cancellationToken);


                Task<List<Individual>> individualsTask = _usersClient.Individuals_QueryAsync(tenantId, new QueryArgumentsOfIndividualWhere { Where = new Users.Client.IndividualWhere { Id_in = entityIdsWithType.Where(x => x.Type == "individual").Select(x => x.Id).ToList() } }, cancellationToken);
                Task<List<Internal>> internalsTask = _usersClient.Internals_QueryAsync(tenantId, new QueryArgumentsOfInternalWhere { Where = new Users.Client.InternalWhere { Id_in = entityIdsWithType.Where(x => x.Type == "internal").Select(x => x.Id).ToList() } }, cancellationToken);
                await Task.WhenAll(individualsTask, internalsTask);

                List<Entity> entities = new List<Entity>();

                var individuals = individualsTask.Result?.Select(i => new Entity { EntityId = i.EntityId, Name = i.Name, Email = i.Email });
                if (individuals != null && individuals.Any())
                    entities.AddRange(individuals);

                var internals = internalsTask.Result?.Select(i => new Entity { EntityId = i.EntityId, Name = i.Name, Email = i.Email });
                if (internals != null && internals.Any())
                    entities.AddRange(internals);

                return entities;
            }

            User GetNodeUpdatedByUser(NodeEvent nodeEvent)
            {
                if(nodeEvent.UpdatedById == null)
                    return new User();

                if (!logUserIdMapping.ContainsKey(nodeEvent.UpdatedById))
                    return new User()
                    {
                        Email = logUserLogInMapping[nodeEvent.UpdatedById]?.Email,
                        Username = logUserLogInMapping[nodeEvent.UpdatedById]?.Username
                    };

                return new User()
                {
                    Email = logUserLogInMapping[nodeEvent.UpdatedById]?.Email,
                    Username = logUserLogInMapping[nodeEvent.UpdatedById]?.Username,
                    Name = logUserIdMapping[nodeEvent.UpdatedById]?.Name
                };
            }

            User GetProductUpdatedByUser(ProductEvent productEvent)
            {
                string updatedById = GetUpdatedByValue(productEvent);

                if (updatedById == null)
                    return new User();

                if (!logUserIdMapping.ContainsKey(updatedById))
                    return new User()
                    {
                        Email = logUserLogInMapping[updatedById]?.Email,
                        Username = logUserLogInMapping[updatedById]?.Username
                    };

                return new User()
                {
                    Email = logUserLogInMapping[updatedById]?.Email,
                    Username = logUserLogInMapping[updatedById]?.Username,
                    Name = logUserIdMapping[updatedById]?.Name
                };
            }

            string GetUpdatedByValue(ProductEvent productEvent)
            {
                if (productEvent.Type == ProductEventType.creation)
                    return productEvent.CreatedById;
                if (productEvent.Type == ProductEventType.update)
                    return productEvent.ModifiedById;
                if (productEvent.Type == ProductEventType.delete)
                    return productEvent.DeletedById;

                return productEvent.ModifiedById ?? "";
            }

            IEnumerable<string> GetUserIds()
            {
                var createdByIds = productEvents?.Select(x => x.CreatedById)?.Where(x => x != null) ?? Enumerable.Empty<string>();
                var updatedByIds = productEvents?.Select(x => x.ModifiedById)?.Where(x => x != null) ?? Enumerable.Empty<string>();
                var deletedByIds = productEvents?.Select(x => x.DeletedById)?.Where(x => x != null) ?? Enumerable.Empty<string>();
                var nodeUpdateByIds = productEvents?.Select(x => x.UpdatedById)?.Where(x => x != null) ?? Enumerable.Empty<string>();
                return createdByIds.Concat(updatedByIds.Concat(deletedByIds)).Concat(nodeUpdateByIds).Distinct();
            }
        }

        public virtual async Task<List<ProductEvent>> GetProductEventsAsync(string tenantId, string productType, ProductEventWhere where, OrderBy? orderBy, int? skip, int? first, CancellationToken cancellationToken)
        {
            List<NodeEvent> nodeEventLogs = new List<NodeEvent>();
            _logger.LogInformation("Getting product events from MONGO");
            var productEvents = (await _productEventStore.GetEventsAsync(tenantId, productType, where, orderBy, skip: skip, first: first, cancellationToken)).ToList();

            return productEvents;
        }

        public virtual async Task<List<ProductEvent>> GetProductEventStoreLogsAsync(
            string tenantId,
            string productType,
            ProductEventWhere where,
            CancellationToken cancellationToken)
        {
            var nodeEventLogs = new List<ProductEvent>();
            var productIds = where.ProductIds_in;

            _logger.LogInformation("Getting Product Information");
            var productsInfo = await GetProductsInfo(tenantId, productIds, cancellationToken);

            await ProcessProductNodes(tenantId, productsInfo, nodeEventLogs, cancellationToken);

            await EnrichWithUserData(tenantId, nodeEventLogs, cancellationToken);

            return FilterEventsByTimestamp(nodeEventLogs, where.Timestamp);
        }

        private async Task ProcessProductNodes(
            string tenantId,
            IEnumerable<ProductInfo> productsInfo,
            List<ProductEvent> nodeEventLogs,
            CancellationToken cancellationToken)
        {
            foreach (var product in productsInfo)
            {
                _logger.LogInformation("Getting Product Nodes for:{product}", product);
                var productNodes = await _productBuilderEventStore.GetProductNodes(
                    tenantId, product.ProductTreeId, cancellationToken);

                _logger.LogInformation("Finished Getting Product Nodes for:{product} with count:{count}",
                    product, productNodes.Count);

                await ProcessSingleProductNodes(
                    tenantId, product, productNodes, nodeEventLogs, cancellationToken);
            }
        }

        private async Task ProcessSingleProductNodes(
            string tenantId,
            ProductInfo product,
            List<string> productNodes,
            List<ProductEvent> nodeEventLogs,
            CancellationToken cancellationToken)
        {
            foreach (var node in productNodes)
            {
                _logger.LogInformation("Current nodeEventLogs count:{count}", nodeEventLogs.Count);
                _logger.LogInformation("Getting event for Node:{node} for:{product}", node, product);

                var eventStoreNodeEvents = await _productBuilderEventStore.GetNodeEvents(
                    tenantId, node, product, cancellationToken);

                _logger.LogInformation("Finished Getting event for Node:{node} for:{product} with count:{count}",
                    node, product, eventStoreNodeEvents.Count());

                var nodeEvents = MapProductEvents(eventStoreNodeEvents, product);
                nodeEventLogs.AddRange(nodeEvents);
            }
        }

        private async Task EnrichWithUserData(
            string tenantId,
            List<ProductEvent> nodeEventLogs,
            CancellationToken cancellationToken)
        {
            var userIds = GetDistinctUserIds(nodeEventLogs);
            var logins = await GetLogins(tenantId, userIds, cancellationToken);
            var entities = await GetEntitiesMapping(tenantId, logins, cancellationToken);

            // These mappings could be used later if needed
            var logUserLogInMapping = CreateLoginMapping(userIds, logins);
            var logUserIdMapping = CreateEntityMapping(userIds, logins, entities);
        }

        private Dictionary<string, Login> CreateLoginMapping(
            IEnumerable<string> userIds,
            IEnumerable<Login> logins)
        {
            return userIds.ToDictionary(
                id => id,
                id => logins?.FirstOrDefault(x => x.Id == id));
        }

        private Dictionary<string, Entity> CreateEntityMapping(
            IEnumerable<string> userIds,
            IEnumerable<Login> logins,
            List<Entity> entities)
        {
            return userIds.ToDictionary(
                id => id,
                id => entities?.FirstOrDefault(user =>
                    user.EntityId == (logins?.FirstOrDefault(login => login.Id == id)?.EntityId)));
        }

        private IEnumerable<ProductEvent> MapProductEvents(
            IEnumerable<NodeEvent> nodeEvents,
            ProductInfo product)
        {
            return nodeEvents.Select(x => new ProductEvent(product.Id, ProductEventType.nodeEvent, x.Timestamp)
            {
                NodeEventType = x.Type,
                Alias = x.Alias,
                NodeId = x.NodeId,
                ProductId = x.ProductId,
                Ref = x.Ref,
                UpdatedById = x.UpdatedById,
                Values = x.Values,
            });
        }

        private async Task<IEnumerable<ProductInfo>> GetProductsInfo(
            string tenantId,
            IEnumerable<ProductId> productIds,
            CancellationToken cancellationToken)
        {
            var products = await _productService.GetAsync(
                tenantId,
                null,
                new ProductWhere() { Id_in = productIds.ToList() },
                null,
                null,
                false,
                cancellationToken);

            return products.Select(x => new ProductInfo()
            {
                Id = x.Id,
                ProductTreeId = x.ProductTreeId
            });
        }

        private async Task<IEnumerable<Login>> GetLogins(
            string tenantId,
            IEnumerable<string> userIds,
            CancellationToken cancellationToken)
        {
            return await _authService.GetLoginsAsync(
                tenantId,
                new LoginWhere() { Ids = userIds },
                cancellationToken);
        }

        private async Task<List<Entity>> GetEntitiesMapping(
            string tenantId,
            IEnumerable<Login> logins,
            CancellationToken cancellationToken)
        {
            var entityIds = logins?.Select(x => x.EntityId).ToList();
            if (entityIds == null || !entityIds.Any())
                return new List<Entity>();

            var entityIdsWithType = await _usersClient.Entities_QueryIdsAndTypesAsync(
                tenantId,
                new EntityWhere { Id_in = entityIds },
                cancellationToken);

            var individualsTask = GetIndividuals(tenantId, entityIdsWithType, cancellationToken);
            var internalsTask = GetInternals(tenantId, entityIdsWithType, cancellationToken);

            await Task.WhenAll(individualsTask, internalsTask);

            var entities = new List<Entity>();
            entities.AddRange(individualsTask.Result ?? Enumerable.Empty<Entity>());
            entities.AddRange(internalsTask.Result ?? Enumerable.Empty<Entity>());

            return entities;
        }

        private async Task<IEnumerable<Entity>> GetIndividuals(
            string tenantId,
            IEnumerable<EntityId> entityIdsWithType,
            CancellationToken cancellationToken)
        {
            var individualIds = entityIdsWithType
                .Where(x => x.Type == "individual")
                .Select(x => x.Id)
                .ToList();

            if (!individualIds.Any())
                return Enumerable.Empty<Entity>();

            var individuals = await _usersClient.Individuals_QueryAsync(
                tenantId,
                new QueryArgumentsOfIndividualWhere
                {
                    Where = new Users.Client.IndividualWhere { Id_in = individualIds }
                },
                cancellationToken);

            return individuals?.Select(i => new Entity
            {
                EntityId = i.EntityId,
                Name = i.Name,
                Email = i.Email
            }) ?? Enumerable.Empty<Entity>();
        }

        private async Task<IEnumerable<Entity>> GetInternals(
            string tenantId,
            IEnumerable<EntityId> entityIdsWithType,
            CancellationToken cancellationToken)
        {
            var internalIds = entityIdsWithType
                .Where(x => x.Type == "internal")
                .Select(x => x.Id)
                .ToList();

            if (!internalIds.Any())
                return Enumerable.Empty<Entity>();

            var internals = await _usersClient.Internals_QueryAsync(
                tenantId,
                new QueryArgumentsOfInternalWhere
                {
                    Where = new Users.Client.InternalWhere { Id_in = internalIds }
                },
                cancellationToken);

            return internals?.Select(i => new Entity
            {
                EntityId = i.EntityId,
                Name = i.Name,
                Email = i.Email
            }) ?? Enumerable.Empty<Entity>();
        }

        private IEnumerable<string> GetDistinctUserIds(List<ProductEvent> nodeEventLogs)
        {
            return nodeEventLogs
                .Select(x => x.UpdatedById)
                .Distinct();
        }

        private List<ProductEvent> FilterEventsByTimestamp(
            List<ProductEvent> events,
            DateTime? timestamp)
        {
            return timestamp.HasValue
                ? events.Where(x => x.Timestamp > timestamp).ToList()
                : events;
        }

        //public virtual async Task<List<ProductEvent>> GetProductEventStoreLogsAsync(string tenantId, string productType, ProductEventWhere where, CancellationToken cancellationToken)
        //{
        //    List<ProductEvent> nodeEventLogs = new List<ProductEvent>();
        //    var productIds = where.ProductIds_in;

        //    _logger.LogInformation("Getting Product Information");
        //    IEnumerable<ProductInfo> productsInfo = await GetProductsInfo();

        //    foreach (var product in productsInfo)
        //    {
        //        _logger.LogInformation("Getting Product Nodes for:{product}", product);
        //        var productNodes = await _productBuilderEventStore.GetProductNodes(tenantId, product.ProductTreeId, cancellationToken);
        //        _logger.LogInformation("Finished Getting Product Nodes for:{product} with count:{count}", product, productNodes.Count);
        //        await PopulateNodeEvents(productNodes, product);
        //    }


        //    var userIds = GetUserIds();
        //    var logins = await GetLogins();

        //    List<Entity> entities = await GetEntitiesMapping();

        //    var logUserLogInMapping = userIds.ToDictionary(id => id, id => logins?.FirstOrDefault(x => x.Id == id));
        //    var logUserIdMapping = userIds.ToDictionary(id => id, id => entities?.FirstOrDefault(user => user.EntityId == (logins?.FirstOrDefault(login => login.Id == id)?.EntityId)));

        //    return nodeEventLogs.Where(x => x.Timestamp > where.Timestamp).ToList();


        //    async Task PopulateNodeEvents(List<string> productNodes, ProductInfo product)
        //    {
        //        foreach (var node in productNodes)
        //        {
        //            _logger.LogInformation("Current nodeEventLogs count:{count}", nodeEventLogs.Count);
        //            _logger.LogInformation("Getting event for Node:{node} for:{product}", node, product);
        //            var eventStoreNodeEvents = await _productBuilderEventStore.GetNodeEvents(tenantId, node, product, cancellationToken);
        //            _logger.LogInformation("Finished Getting event for Node:{node} for:{product} with count:{count}", node, product, eventStoreNodeEvents.Count());
        //            var nodeEvents = MapProductEvents(eventStoreNodeEvents, product);
        //            nodeEventLogs.AddRange(nodeEvents);
        //        }
        //    }

        //    IEnumerable<ProductEvent> MapProductEvents(IEnumerable<NodeEvent> nodeEvents, ProductInfo product)
        //    {
        //        return nodeEvents.Select(x => new ProductEvent(product.Id, ProductEventType.nodeEvent, x.Timestamp)
        //        {
        //            NodeEventType = x.Type,
        //            Alias = x.Alias,
        //            NodeId = x.NodeId,
        //            ProductId = x.ProductId,
        //            Ref = x.Ref,
        //            UpdatedById = x.UpdatedById,
        //            Values = x.Values,
        //        });
        //    }


        //    async Task<IEnumerable<ProductInfo>> GetProductsInfo()
        //    {
        //        IEnumerable<ProductInfo> productsInfo = (await _productService.GetAsync(tenantId, null, new ProductWhere() { Id_in = productIds }, null, null, false, cancellationToken))
        //        .Select(x => new ProductInfo()
        //        {
        //            Id = x.Id,
        //            ProductTreeId = x.ProductTreeId
        //        });

        //        return productsInfo;
        //    }

        //    async Task<IEnumerable<Login>> GetLogins()
        //    {
        //        var logins = await _authService.GetLoginsAsync(tenantId, new LoginWhere() { Ids = userIds }, cancellationToken);
        //        return logins;
        //    }

        //    async Task<List<Entity>> GetEntitiesMapping()
        //    {
        //        var entityIds = logins?.Select(x => x.EntityId).ToList();
        //        var entityIdsWithType = await _usersClient.Entities_QueryIdsAndTypesAsync(tenantId, new EntityWhere { Id_in = entityIds }, cancellationToken);


        //        Task<List<Individual>> individualsTask = _usersClient.Individuals_QueryAsync(tenantId, new QueryArgumentsOfIndividualWhere { Where = new Users.Client.IndividualWhere { Id_in = entityIdsWithType.Where(x => x.Type == "individual").Select(x => x.Id).ToList() } }, cancellationToken);
        //        Task<List<Internal>> internalsTask = _usersClient.Internals_QueryAsync(tenantId, new QueryArgumentsOfInternalWhere { Where = new Users.Client.InternalWhere { Id_in = entityIdsWithType.Where(x => x.Type == "internal").Select(x => x.Id).ToList() } }, cancellationToken);
        //        await Task.WhenAll(individualsTask, internalsTask);

        //        List<Entity> entities = new List<Entity>();

        //        var individuals = individualsTask.Result?.Select(i => new Entity { EntityId = i.EntityId, Name = i.Name, Email = i.Email });
        //        if (individuals != null && individuals.Any())
        //            entities.AddRange(individuals);

        //        var internals = internalsTask.Result?.Select(i => new Entity { EntityId = i.EntityId, Name = i.Name, Email = i.Email });
        //        if (internals != null && internals.Any())
        //            entities.AddRange(internals);

        //        return entities;
        //    }

        //    IEnumerable<string> GetUserIds()
        //    {
        //        var nodeUpdateByIds = nodeEventLogs.Select(x => x.UpdatedById);
        //        return nodeUpdateByIds.Distinct();
        //    }
        //}
    }
}