#nullable enable

using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.Multitenancy;

using MediatR;

namespace CoverGo.Products.Domain.Products.Queries;

public sealed record ProductQuery : IRequest<Product?>
{
    public required ProductId ProductId { get; init; }
}

public sealed class ProductQueryHandler(
    TenantId tenantId,
    IProductRepository productRepository) : IRequestHandler<ProductQuery, Product?>
{
    public async Task<Product?> Handle(ProductQuery request, CancellationToken cancellationToken)
    {
        var product = (await productRepository.GetAsync(
            tenantId.Value,
            new ProductWhere
            {
                ProductId = new() { Plan = request.ProductId.Plan, Type = request.ProductId.Type, Version = request.ProductId.Version }
            },
            null,
            new QueryParameters(),
            true,
            cancellationToken: cancellationToken)).FirstOrDefault();
        return product;
    }
}
