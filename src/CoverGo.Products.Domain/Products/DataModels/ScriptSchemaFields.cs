#nullable enable

using System.Collections.Generic;

namespace CoverGo.Products.Domain.Products.DataModels;

public class ScriptSchemaPlanFieldOption
{
    public required string? Key { get; set; }

    public required string Name { get; set; }

    public required string? Value { get; set; }
}

public class ScriptSchemaPlanFieldMeta
{
    public bool Required { get; set; }

    /// <summary>
    /// Which UI component to display.
    /// </summary>
    public string? Component { get; set; }

    /// <summary>
    /// text | boolean | description | number | date
    /// </summary>
    public string? FieldType { get; set; }

    public string? Label { get; set; }

    /// <summary>
    /// | separated list of validations.
    /// samples:
    /// length:10,200
    /// required
    /// email|required
    /// number
    /// </summary>
    public string? Validations { get; set; }

    public int? MinLength { get; set; }

    public int? MaxLength { get; set; }

    public bool? Numeric { get; set; }

    public List<ScriptSchemaPlanFieldOption>? Options { get; set; } = null;

    public string? Condition { get; set; }
}

public class ScriptSchemaPlanField
{
    /// <summary>
    /// string | boolean | number
    /// </summary>
    public required string Type { get; set; }

    public required ScriptSchemaPlanFieldMeta Meta { get; set; }
}
