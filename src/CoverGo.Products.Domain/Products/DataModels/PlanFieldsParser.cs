#nullable enable

using System;
using System.Collections.Generic;
using System.Linq;

using Newtonsoft.Json.Linq;

namespace CoverGo.Products.Domain.Products.DataModels;

public class PlanFields
{
    public required string PlanId { get; set; }

    public required Dictionary<string, ScriptSchemaPlanField> Fields { get; set; }
}

public class PlanFieldsParser
{
    public static IEnumerable<PlanFields> Parse(string pricingScriptInputSchema)
    {
        var jsonObject = JObject.Parse(pricingScriptInputSchema);

        var planFieldsProperties = jsonObject.Properties().Where(x => x.Name.StartsWith("planFields_", StringComparison.OrdinalIgnoreCase));

        foreach (var planFieldsProperty in planFieldsProperties)
        {
            var planId = GetPlanId(planFieldsProperty.Name);
            var fields = ProductDataModelParser.Parse(planFieldsProperty);

            yield return new PlanFields
            {
                PlanId = planId,
                Fields = fields,
            };
        }
    }
    private static string GetPlanId(string propertyName)
    {
        return propertyName.Substring("planFields_".Length);
    }
}
