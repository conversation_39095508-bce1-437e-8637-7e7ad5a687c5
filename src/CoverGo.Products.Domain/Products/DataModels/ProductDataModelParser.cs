#nullable enable

using System.Collections.Generic;

using Newtonsoft.Json.Linq;

namespace CoverGo.Products.Domain.Products.DataModels;

public static class ProductDataModelParser
{
    public static Dictionary<string, ScriptSchemaPlanField> Parse(JObject pricingScriptInputSchema, string property)
    {
        var scriptFieldProperty = pricingScriptInputSchema.Property(property);
        return Parse(scriptFieldProperty);
    }

    public static Dictionary<string, ScriptSchemaPlanField> Parse(JProperty? scriptFieldProperty)
    {
        if (scriptFieldProperty is null)
        {
            return [];
        }

        var fieldsJsonObject = scriptFieldProperty.Value["properties"]?.ToObject<Dictionary<string, ScriptSchemaPlanField>>()
            ?? new();

        var fields = new Dictionary<string, ScriptSchemaPlanField>();

        foreach (var fieldProperty in fieldsJsonObject)
        {
            var fieldName = fieldProperty.Key;
            var fieldType = fieldProperty.Value.Type;
            var fieldMeta = fieldProperty.Value.Meta;

            AddMissingFieldTypes(fieldType, fieldMeta);

            fields.Add(fieldName, new ScriptSchemaPlanField
            {
                Type = fieldType,
                Meta = fieldMeta
            });
        }

        return fields;
    }

    private static void AddMissingFieldTypes(string fieldType, ScriptSchemaPlanFieldMeta fieldMeta)
    {
        if (fieldType == "string" && fieldMeta.FieldType == null)
        {
            if (fieldMeta.Component == "CDatePicker")
            {
                fieldMeta.FieldType = "date";
            }
            else if (fieldMeta.Component == "JSelect")
            {
                fieldMeta.FieldType = "text";
            }
        }
    }
}
