using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Auth;
using CoverGo.Products.Domain.ProductImportHistory;
using CoverGo.Products.Domain.UiSchemas;
using CoverGo.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace CoverGo.Products.Domain.Products
{
    public enum ProductImportValidationMessageLevel
    {
        Error = 1,
        Warning = 2
    }

    public static class EntryNames
    {
        public const string MetadataEntryName = "metadata.json";
        public const string ProductTreeEntryName = "productTree.json";
        public const string DataSchemaEntryName = "dataSchema.json";
        public const string UISchemaEntryName = "uiSchema.json";
        public const string ValidationScriptEntryName = "validationScript.js";
        public const string ExternalTableEntryNamePrefix = "externalTables/";
        public const string ExternalTableEntryNameExtension = ".json";
        public static string GetExternalTableEntryName(string externalTableName) => $"{ExternalTableEntryNamePrefix}{externalTableName}{ExternalTableEntryNameExtension}";
        public static string GetExternalTableName(string externalTableEntryName) => externalTableEntryName.Substring(ExternalTableEntryNamePrefix.Length, externalTableEntryName.Length - ExternalTableEntryNamePrefix.Length - ExternalTableEntryNameExtension.Length);
    }

    public class ProductImportValidationMessageMeta
    {
        public string LockedBy { get; set; }
    }

    public class ProductImportValidationMessage
    {
        public ProductImportValidationMessageLevel Level { get; set; }
        public string Entry { get; set; }
        public string Code { get; set; }
        public ProductImportValidationMessageMeta Meta { get; set; }
    }

    public class ProductImportInfo
    {
        public ProductId ProductId { get; set; }
        public string ProductName { get; set; }
        public string ProductLifecycleStage { get; set; }
        public DateTime? ProductLastModifiedAt { get; set; }
        public string ProductLastModifiedBy { get; set; }
        public string Tenant { get; set; }
        public DateTime ExportedAt { get; set; }
        public string ExportedBy { get; set; }
        public IReadOnlyCollection<string> IncludedEntries { get; set; }
        public IReadOnlyCollection<ProductImportValidationMessage> ValidationMessages { get; set; }
    }

    public class ProductExchangeService
    {
        const int LockExpirationSeconds = 30;

        readonly ProductService _productService;
        readonly IProductL10NAdapter _productL10NAdapter;
        readonly IAuthAdapter _authAdapter;
        readonly IProductBuilderAdapter _productBuilderAdapter;
        readonly IProductExchangeAdapter _productExchangeAdapter;
        readonly IExternalTablesAdapter _externalTablesAdapter;
        readonly IProductImportHistoryService _productImportHistoryService;
        readonly ILogger<ProductExchangeService> _logger;

        public ProductExchangeService(
            ProductService productService,
            IProductL10NAdapter productL10NAdapter,
            IAuthAdapter authAdapter,
            IProductBuilderAdapter productBuilderAdapter,
            IProductExchangeAdapter productExchangeAdapter,
            IExternalTablesAdapter externalTablesAdapter,
            IProductImportHistoryService productImportHistoryService,
            ILogger<ProductExchangeService> logger)
        {
            _productService = productService;
            _productL10NAdapter = productL10NAdapter;
            _authAdapter = authAdapter;
            _productBuilderAdapter = productBuilderAdapter;
            _productExchangeAdapter = productExchangeAdapter;
            _externalTablesAdapter = externalTablesAdapter;
            _productImportHistoryService = productImportHistoryService;
            _logger = logger;
        }

        public async Task<Result<string>> ExportAsync(string tenantId, string clientId, string loginId, ProductId productId,bool? skipValidateProductTreeLock, CancellationToken cancellationToken)
        {
            Product product = await GetProductAsync(tenantId, productId, cancellationToken);
            if (product == null) return Result<string>.Failure(NoProduct());
            if (string.IsNullOrEmpty(product.ProductTreeId)) return Result<string>.Failure(NoProductTree());

            ProductPackage productPackage = new() { Metadata = ToProductMetadata(product, tenantId) };

            try
            {
                ExportedTree exportedTree = await _productBuilderAdapter.ExportProductTree(product.ProductTreeId, cancellationToken).EnsureSuccess();
                if (exportedTree.LockStatus?.IsLocked == true) return Result<string>.Failure(await LockedProductAsync(tenantId, exportedTree.LockStatus.LockedById, cancellationToken));
                productPackage.ProductTreeContent = exportedTree.Raw;

                if (!skipValidateProductTreeLock.GetValueOrDefault())
                { 
                    LockStatus lockStatus = await _productBuilderAdapter.LockProductTree(product.ProductTreeId, LockExpirationSeconds, cancellationToken).EnsureSuccess();
                    if (lockStatus.LockedById != null) return Result<string>.Failure(await LockedProductAsync(tenantId, lockStatus.LockedById, cancellationToken));
                }

                List<Task> tasks = new()
                {
                    LoadProductNameAsync(tenantId, clientId, productId, productPackage.Metadata, cancellationToken),
                    LoadLastModifiedByAndExportedByAsync(tenantId, product.LastModifiedById, loginId, productPackage.Metadata, cancellationToken),
                    LoadProductSchemaAsync(product.ProductTreeId, productPackage, cancellationToken),
                    LoadValidationScriptAsync(product.ProductTreeId, productPackage, cancellationToken)
                };

                ConcurrentDictionary<string, string> externalTables = new();
                tasks.Add(exportedTree.ReferencedExternalTables.ParallelForEachAsync((t, ct) => LoadExternalTableAsync(tenantId, t, externalTables, ct),
                    new ParallelRunOptions() { MaxDegreeOfParallelism = 10, CancellationToken = cancellationToken }));

                await Task.WhenAll(tasks);

                productPackage.ExternalTablesContent = externalTables.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

                productPackage.Metadata.ExportedAt = DateTime.UtcNow;

                string filePath = await _productExchangeAdapter.Upload(tenantId, productPackage, cancellationToken).EnsureSuccess();

                await _productBuilderAdapter.UnlockProductTree(product.ProductTreeId, cancellationToken); // Failed results are ignored.

                return Result<string>.Success(filePath);
            }
            catch (FailedResultException ex)
            {
                return Result<string>.Failure(ex.Errors);
            }
        }

        public async virtual Task<Result> CloneProductTreeAsync(string tenantId, string clientId, string loginId, ProductId productId, ProductName name, ProductId cloneProductId, string lifeCycleStage, CancellationToken cancellationToken)
        {
            var product = await GetProductAsync(tenantId, productId, cancellationToken);
            
            if (product == null)
                return Result.Failure(NoProduct());

            if(string.IsNullOrEmpty(product.ProductTreeId))
                return Result.Failure(NoProductTree());

            _logger.LogInformation("Started getting Product Schema for product {ProductId} with ProductTreeId: {productTreeId}", productId, product.ProductTreeId);
            Result<ProductSchema> productSchema = await _productBuilderAdapter.GetProductSchema(product.ProductTreeId, cancellationToken);
            _logger.LogInformation("Finished getting Product Schema for ProductTreeId: {productTreeId} with Schema:{schemaJson}", product.ProductTreeId, JsonConvert.SerializeObject(productSchema?.Value));
            _logger.LogInformation("Started cloning Product ProductTreeId: {productTreeId}", product.ProductTreeId);
            Result<string> cloneProductTreeId = await _productBuilderAdapter.CloneProductTree(product.ProductTreeId, cancellationToken);
            _logger.LogInformation("Finished cloning Product ProductTreeId: {productTreeId}", product.ProductTreeId);

            if (!cloneProductTreeId.IsSuccess)
                return Result.Failure(cloneProductTreeId.Errors);

            _logger.LogInformation("Started cloning Product for ProductId: {ProductId}", productId);
            Result cloneProductResult = await _productService.CloneAsync(tenantId, new CloneProductCommand()
            {
                ProductId = productId,
                CloneProductId = cloneProductId,
            }, cancellationToken);
            _logger.LogInformation("Finished cloning Product for ProductId: {ProductId}", productId);
            if (!cloneProductResult.IsSuccess)
            {
                _logger.LogError("Failed to clone product {ProductId} to {CloneProductId} with error:{error}", productId, cloneProductId, cloneProductResult.Errors.FirstOrDefault());
                return cloneProductResult;
            }

            Result updateProductNameResult = await _productL10NAdapter.UpsertAsync(tenantId, name.Locale, $"products-{cloneProductId.Plan}|{cloneProductId.Version}|{cloneProductId.Type}-name", name.Value, cancellationToken);

            if (!updateProductNameResult.IsSuccess)
                return updateProductNameResult;

            if (productSchema.IsSuccess && productSchema.Value != null)
            {
                _logger.LogInformation("Started creating Product Schema for cloned ProductTreeId: {productTreeId}", cloneProductTreeId.Value);
                Result<CreateProductSchema> createProductSchemaResult = await _productBuilderAdapter.CreateProductSchema(cloneProductTreeId.Value, productSchema.Value, cancellationToken);
                _logger.LogInformation("Finished creating Product Schema for cloned ProductTreeId: {productTreeId}", cloneProductTreeId.Value);
                if (!createProductSchemaResult.IsSuccess)
                    return Result.Failure(createProductSchemaResult.Errors);

                if (!string.IsNullOrEmpty(createProductSchemaResult.Value.Value))
                {
                    var clonedProduct = await GetProductAsync(tenantId, cloneProductId, cancellationToken);
                    _logger.LogInformation("Checking [GetProductAsync] cloneProduct ProductTreeId:{treeId}", clonedProduct.ProductTreeId);
                    _logger.LogInformation("Checking cloneProductTreeId value:{productTreeIdValue}", cloneProductTreeId.Value);
                    _logger.LogInformation("Started adding UI to Product Schema for cloned ProductTreeId: {productTreeId} with UI Schema: {uiSchema}", cloneProductTreeId.Value, JsonConvert.SerializeObject(productSchema?.Value?.UISchemas?.FirstOrDefault()));
                    
                    var originalProductUiSchema = productSchema.Value.UISchemas?.FirstOrDefault();
                    if (originalProductUiSchema != null)
                    {
                        UISchema clonedProductUiSchema = new UISchema()
                        {
                            Name = cloneProductTreeId.Value,
                            Schema = originalProductUiSchema.Schema
                        };

                        Result addUiToProductSchemaResult = await _productBuilderAdapter.AddUiToProductSchema(createProductSchemaResult.Value.Value, clonedProductUiSchema, cancellationToken);
                        _logger.LogInformation("Finished adding UI to Product Schema for cloned ProductTreeId: {productTreeId} haiving ProductSchemaId:{productSchemaId} with UI Schema: {uiSchema}", clonedProduct.ProductTreeId, createProductSchemaResult.Value.Value, JsonConvert.SerializeObject(clonedProductUiSchema));

                        if (!addUiToProductSchemaResult.IsSuccess)
                            return Result.Failure(addUiToProductSchemaResult.Errors);
                    }
                }
            }

            _logger.LogInformation("Started Updating Product with cloned ProductTreeId: {productTreeId}", cloneProductTreeId.Value);
            Result updateProductResult = await _productService.UpdateAsync(tenantId, new UpdateProductCommand()
            {
                ProductId = cloneProductId,
                ProductTreeId = cloneProductTreeId.Value,
                IsProductTreeIdChanged = true,
                ModifiedById = loginId
            }, cancellationToken);

            _logger.LogInformation("Finished Updating Product with cloned ProductTreeId: {productTreeId}", cloneProductTreeId.Value);

            if (!updateProductResult.IsSuccess)
                return Result.Failure(updateProductResult.Errors);

            if (!string.IsNullOrEmpty(lifeCycleStage))
            {
                Result updateProductLifeCycleStageResult = await _productService.UpdateAsync(tenantId, new UpdateProductCommand()
                {
                    ProductId = cloneProductId,
                    LifecycleStage = lifeCycleStage,
                    IsLifecycleStageChanged = true,
                    ModifiedById = loginId
                }, cancellationToken);
                if (!updateProductLifeCycleStageResult.IsSuccess)
                    return Result.Failure(updateProductLifeCycleStageResult.Errors);
            }

            return Result.Success();
        }

        async Task<Product> GetProductAsync(string tenantId, ProductId productId, CancellationToken cancellationToken)
        {
            string clientId = null;
            ProductWhere filter = new() { Id_in = new List<ProductId> { productId } };
            IEnumerable<Product> products = await _productService.GetAsync(tenantId, clientId, filter, null, new QueryParameters() { Limit = 1 }, true, cancellationToken: cancellationToken);
            return products.FirstOrDefault();
        }

        static ProductMetadata ToProductMetadata(Product product, string tenantId) => new ProductMetadata
        {
            ProductId = product.Id,
            ProductLifecycleStage = product.LifecycleStage,
            ProductLastModifiedAt = product.LastModifiedAt,
            Tenant = tenantId
        };

        async Task LoadProductNameAsync(string tenantId, string clientId, ProductId productId, ProductMetadata productMetadata, CancellationToken cancellationToken)
        {
            productMetadata.ProductName = await _productL10NAdapter.GetProductName(tenantId, clientId, productId, cancellationToken).EnsureSuccess();
        }

        async Task LoadLastModifiedByAndExportedByAsync(string tenantId, string lastModifiedById, string exportedById, ProductMetadata productMetadata, CancellationToken cancellationToken)
        {
            string[] usernames = await _authAdapter.GetUsernamesByLoginIds(tenantId, cancellationToken, lastModifiedById, exportedById);
            productMetadata.ProductLastModifiedBy = usernames[0];
            productMetadata.ExportedBy = usernames[1];
        }

        async Task LoadProductSchemaAsync(string productTreeId, ProductPackage productPackage, CancellationToken cancellationToken)
        {
            ProductSchema productSchema = await _productBuilderAdapter.GetProductSchema(productTreeId, cancellationToken).EnsureSuccess();
            productPackage.DataSchemaContent = productSchema?.DataSchema;
            productPackage.UISchemaContent = GetDefaultUISchema(productSchema?.UISchemas, productTreeId);
        }

        async Task LoadValidationScriptAsync(string productTreeId, ProductPackage productPackage, CancellationToken cancellationToken)
        {
            productPackage.ValidationScriptContent = await _productBuilderAdapter.GetValidationScript(productTreeId, cancellationToken).EnsureSuccess();
        }

        async Task LoadExternalTableAsync(string tenantId, string externalTableName, ConcurrentDictionary<string, string> externalTables, CancellationToken cancellationToken)
        {
            string content = await _externalTablesAdapter.DownloadExternalTable(tenantId, externalTableName, cancellationToken).EnsureSuccess();
            externalTables.TryAdd(externalTableName, content);
        }

        static string GetDefaultUISchema(UISchema[] uiSchemas, string productTreeId) => uiSchemas?.Where(s => s.Name == productTreeId).Select(s => s.Schema).FirstOrDefault();

        static Error NoProduct() => new Error { Code = "product_not_found", Message = "Product does not exist" };

        static Error NoProductTree() => new Error { Code = "product_tree_not_found", Message = "There is no tree associated with the product" };

        static Error LockedProduct(string lockedBy) => new Error { Code = "locked_product", Message = $"The product is locked by {lockedBy}" };

        static Error TamperedFile() => new Error { Code = "tampered_file", Message = "The file is tampered" };

        static Error InvalidFile() => new Error { Code = "invalid_file", Message = "The file is invalid" };

        async Task<Error> LockedProductAsync(string tenantId, string lockedById, CancellationToken cancellationToken)
        {
            string[] usernames = await _authAdapter.GetUsernamesByLoginIds(tenantId, cancellationToken, lockedById);
            string lockedBy = usernames[0];
            return LockedProduct(lockedBy);
        }

        public async virtual Task<Result> ImportAsync(string tenantId, string loginId, string filePath, bool? skipValidateProductTreeLock, CancellationToken cancellationToken, bool validateFileCheckSum = true)
        {
            try
            {
                ProductPackage productPackage = await _productExchangeAdapter.Download(tenantId, filePath, cancellationToken, validateFileCheckSum).EnsureSuccess();

                Product product = await GetProductAsync(tenantId, productPackage.Metadata.ProductId, cancellationToken);
                if (product == null) return Result.Failure(NoProduct());
                if (!string.IsNullOrEmpty(product.ProductTreeId) && !skipValidateProductTreeLock.GetValueOrDefault())
                {
                    LockStatus lockStatus = await _productBuilderAdapter.LockProductTree(product.ProductTreeId, expiresInSeconds: LockExpirationSeconds, cancellationToken).EnsureSuccess();
                    if (lockStatus.LockedById != null) return Result.Failure(await LockedProductAsync(tenantId, lockStatus.LockedById, cancellationToken));
                }

                string productTreeId = await _productBuilderAdapter.ImportProductTree(productPackage.ProductTreeContent, cancellationToken).EnsureSuccess();

                ProductImportHistoryRecord productImportHistoryRecord = CreateProductImportHistoryRecord(loginId, productPackage.Metadata, filePath);

                List<Task> tasks = new()
                {
                    LoadImportedByAsync(tenantId, loginId, productImportHistoryRecord, cancellationToken)
                };

                ProductSchema productSchema = ToProductSchema(productPackage, productTreeId);
                if (productSchema != null)
                {
                    tasks.Add(_productBuilderAdapter.ImportProductSchema(productTreeId, productSchema, cancellationToken).EnsureSuccess());
                }

                if (!string.IsNullOrEmpty(productPackage.ValidationScriptContent))
                {
                    tasks.Add(_productBuilderAdapter.ImportValidationScript(productTreeId, productPackage.ValidationScriptContent, cancellationToken).EnsureSuccess());
                }

                tasks.Add(productPackage.ExternalTablesContent.ParallelForEachAsync((kvp, ct) =>
                    _externalTablesAdapter.UploadExternalTable(tenantId, kvp.Key, kvp.Value, ct).EnsureSuccess(),
                    new ParallelRunOptions() { MaxDegreeOfParallelism = 10, CancellationToken = cancellationToken }));

                await Task.WhenAll(tasks);

                await UpdateProductTreeIdAsync(tenantId, productPackage.Metadata.ProductId, productTreeId, loginId, cancellationToken).EnsureSuccess();

                productImportHistoryRecord.ImportedAt = DateTime.UtcNow;
                await LogImportAsync(tenantId, productImportHistoryRecord, cancellationToken).EnsureSuccess();

                return Result.Success();
            }
            catch (FailedResultException ex)
            {
                Error error = HandleFailedResultErrors(ex.Errors);
                if (error != null) return Result.Failure(error);

                return Result.Failure(ex.Errors);
            }
        }

        static Error HandleFailedResultErrors(IEnumerable<string> errors) => errors?.FirstOrDefault() switch
        {
            "tampered_file" => TamperedFile(),
            "invalid_file" => InvalidFile(),
            _ => null
        };

        static ProductImportHistoryRecord CreateProductImportHistoryRecord(string importedById, ProductMetadata productMetadata, string filePath) => new ProductImportHistoryRecord
        {
            ImportedById = importedById,
            Package = new ProductImportHistoryPackage
            {
                ProductId = productMetadata.ProductId,
                ProductName = productMetadata.ProductName,
                ProductLifecycleStage = productMetadata.ProductLifecycleStage,
                ProductLastModifiedAt = productMetadata.ProductLastModifiedAt,
                ProductLastModifiedBy = productMetadata.ProductLastModifiedBy,
                Tenant = productMetadata.Tenant,
                ExportedAt = productMetadata.ExportedAt,
                ExportedBy = productMetadata.ExportedBy,
                IncludedEntries = productMetadata.IncludedEntries,
                RequestId = productMetadata.RequestId,
                RelativeFilePath = filePath
            }
        };

        async Task LoadImportedByAsync(string tenantId, string importedById, ProductImportHistoryRecord record, CancellationToken cancellationToken)
        {
            string[] usernames = await _authAdapter.GetUsernamesByLoginIds(tenantId, cancellationToken, importedById);
            record.ImportedBy = usernames[0];
        }

        async Task<Result> LogImportAsync(string tenantId, ProductImportHistoryRecord record, CancellationToken cancellationToken)
        {
            Result<CreatedStatus> result = await _productImportHistoryService.CreateAsync(tenantId, record, cancellationToken);
            if (!result.IsSuccess) return Result.Failure(result.Errors);
            return Result.Success();
        }

        static ProductSchema ToProductSchema(ProductPackage productPackage, string productTreeId)
        {
            if (string.IsNullOrEmpty(productPackage.DataSchemaContent)) return null;

            ProductSchema productSchema = new() { DataSchema = productPackage.DataSchemaContent };

            if (!string.IsNullOrEmpty(productPackage.UISchemaContent))
            {
                productSchema.UISchemas = new[]
                {
                    new UISchema { Name = productTreeId, Schema = productPackage.UISchemaContent }
                };
            }

            return productSchema;
        }

        async Task<Result> UpdateProductTreeIdAsync(string tenantId, ProductId productId, string productTreeId, string modifiedById, CancellationToken cancellationToken)
        {
            UpdateProductCommand updateProductCommand = new()
            {
                ProductId = productId,
                ProductTreeId = productTreeId,
                IsProductTreeIdChanged = true,
                ModifiedById = modifiedById
            };

            return await _productService.UpdateAsync(tenantId, updateProductCommand, cancellationToken);
        }

        public async virtual Task<Result<ProductImportInfo>> GetImportInfoAsync(string tenantId, string filePath, CancellationToken cancellationToken, bool validateFileCheckSum = true)
        {
            Result<ProductPackage> downloadResult = await _productExchangeAdapter.Download(tenantId, filePath, cancellationToken, validateFileCheckSum);
            if (!downloadResult.IsSuccess)
            {
                Error error = HandleFailedResultErrors(downloadResult.Errors);
                if (error != null) return Result<ProductImportInfo>.Failure(error);

                return Result<ProductImportInfo>.Failure(downloadResult.Errors);
            }

            ProductPackage productPackage = downloadResult.Value;

            Result<List<ProductImportValidationMessage>> validationResult = await ValidateProductImportAsync(tenantId, productPackage.Metadata, cancellationToken);
            if (!validationResult.IsSuccess) return Result<ProductImportInfo>.Failure(validationResult.Errors);

            return Result<ProductImportInfo>.Success(ToProductImportInfo(productPackage.Metadata, validationResult.Value));
        }

        static ProductImportInfo ToProductImportInfo(ProductMetadata metadata, IReadOnlyCollection<ProductImportValidationMessage> validationMessages) => new ProductImportInfo
        {
            ProductId = metadata.ProductId,
            ProductName = metadata.ProductName,
            ProductLifecycleStage = metadata.ProductLifecycleStage,
            ProductLastModifiedAt = metadata.ProductLastModifiedAt,
            ProductLastModifiedBy = metadata.ProductLastModifiedBy,
            Tenant = metadata.Tenant,
            ExportedAt = metadata.ExportedAt,
            ExportedBy = metadata.ExportedBy,
            IncludedEntries = metadata.IncludedEntries,
            ValidationMessages = validationMessages.Count == 0 ? null : validationMessages
        };

        async Task<Result<List<ProductImportValidationMessage>>> ValidateProductImportAsync(string tenantId, ProductMetadata productMetadata, CancellationToken cancellationToken)
        {
            try
            {
                List<ProductImportValidationMessage> validationMessages = new();

                await foreach (ProductImportValidationMessage validationMessage in ValidateProductAsync(tenantId, productMetadata, cancellationToken))
                {
                    validationMessages.Add(validationMessage);
                }

                return Result<List<ProductImportValidationMessage>>.Success(validationMessages);
            }
            catch (FailedResultException ex)
            {
                return Result<List<ProductImportValidationMessage>>.Failure(ex.Errors);
            }
        }

        async IAsyncEnumerable<ProductImportValidationMessage> ValidateProductAsync(string tenantId, ProductMetadata productMetadata, [EnumeratorCancellation] CancellationToken cancellationToken)
        {
            Product product = await GetProductAsync(tenantId, productMetadata.ProductId, cancellationToken);
            if (product == null)
            {
                yield return ProductExchangeValidationMessages.NoProduct();
            }
            else
            {
                if (product.LifecycleStage != productMetadata.ProductLifecycleStage) yield return ProductExchangeValidationMessages.DifferentLifecycleStages();

                await foreach (ProductImportValidationMessage validationMessage in ValidateProductTreeAsync(product.ProductTreeId, tenantId, cancellationToken))
                {
                    yield return validationMessage;
                }
            }
        }

        async IAsyncEnumerable<ProductImportValidationMessage> ValidateProductTreeAsync(string productTreeId, string tenantId, [EnumeratorCancellation] CancellationToken cancellationToken)
        {
            if (!string.IsNullOrEmpty(productTreeId))
            {
                yield return ProductExchangeValidationMessages.ProductTreeExists();

                ExportedTree exportedTree = await _productBuilderAdapter.ExportProductTree(productTreeId, cancellationToken).EnsureSuccess();
                if (exportedTree.LockStatus?.IsLocked == true)
                {
                    string[] usernames = await _authAdapter.GetUsernamesByLoginIds(tenantId, cancellationToken, exportedTree.LockStatus.LockedById);
                    string lockedBy = usernames[0];
                    yield return ProductExchangeValidationMessages.LockedProduct(lockedBy);
                }

                ProductSchema productSchema = await _productBuilderAdapter.GetProductSchema(productTreeId, cancellationToken).EnsureSuccess();
                if (!string.IsNullOrEmpty(productSchema?.DataSchema)) yield return ProductExchangeValidationMessages.DataSchemaExists();
                if (!string.IsNullOrEmpty(GetDefaultUISchema(productSchema?.UISchemas, productTreeId))) yield return ProductExchangeValidationMessages.UISchemaExists();

                string validationScript = await _productBuilderAdapter.GetValidationScript(productTreeId, cancellationToken).EnsureSuccess();
                if (!string.IsNullOrEmpty(validationScript)) yield return ProductExchangeValidationMessages.ValidationScriptExists();

                IReadOnlyCollection<string> externalTables = await _externalTablesAdapter.ListExternalTables(tenantId, cancellationToken).EnsureSuccess();
                foreach (string externalTable in externalTables.Intersect(exportedTree.ReferencedExternalTables))
                {
                    yield return ProductExchangeValidationMessages.ExternalTableExists(EntryNames.GetExternalTableEntryName(externalTable));
                }
            }
        }
    }
}