﻿using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;

using System;
using System.Collections.Generic;

namespace CoverGo.Products.Domain.Products
{
    public class ProductEvent
    {
        public ProductEvent(ProductId productId, ProductEventType type, DateTime timestamp)
        {
            ProductId = productId;
            Type = type;
            Timestamp = timestamp;
        }
        
        public ProductEvent(ProductId productId, string EventType, DateTime timestamp) : this(productId, ProductEventType.nodeEvent, timestamp)
        {
            NodeEventType = EventType;
        }

        public ProductEvent()
        {

        }

        public string Id { get; set; }
        public ProductId ProductId { get; set; }
        public string NodeId { get; set; }
        public string Ref { get; set; }
        public string Alias { get; set; }
        public ProductEventType Type { get; set; }
        public string NodeEventType { get; set; }
        public JToken Values { get; set; }
        public DateTime Timestamp { get; set; }
        public string UpdatedById { get; set; }
        public string ModifiedById { get { return Type == ProductEventType.nodeEvent ? UpdatedById : Values?.Value<string>("modifiedById"); } }
        public string CreatedById { get { return Values?.Value<string>("createdById"); } }
        public string DeletedById { get { return Values?.Value<string>("deletedById"); } }
    }

    public class NodeEvent
    {
        public string Id { get; set; }
        public ProductId ProductId { get; set; }
        public string NodeId { get; set; }
        public string Ref { get; set; }
        public string Alias { get; set; }
        public string Type { get; set; }
        public JToken Values { get; set; }
        public DateTime Timestamp { get; set; }
        public string UpdatedById { get; set; }
    }

    public class ProductEventWhere : Where
    {
        public List<ProductEventWhere> Or { get; set; }
        public List<ProductEventWhere> And { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DateTime? Timestamp { get; set; }
        public List<ProductId> ProductIds_in { get; set; }
        public IEnumerable<ProductEventType> Types_in { get; set; }
    }

    public enum ProductEventType
    {
        creation,
        update,
        addBenefit,
        updateBenefit,
        removeBenefit,
        delete,
        addUnderwritingVariable,
        updateUnderwritingVariable,
        removeUnderwritingVariable,
        addTag,
        removeTag,
        addFact,
        updateFact,
        removeFact,
        addInternalReview,
        updateInternalReview,
        removeInternalReview,
        setTermsAndConditions,
        removeTermsAndConditions,
        setRatingFactorsTable,
        removeRatingFactorsTable,
        setSegments,
        attachDocuments,
        removeAttachedDocument,
        nodeEvent,

    }

    public class ProductTypeEvent
    {
        public ProductTypeEvent(string typeId, ProductTypeEventType type, DateTime timestamp)
        {
            TypeId = typeId;
            Type = type;
            Timestamp = timestamp;
        }

        public string Id { get; set; }
        public string TypeId { get; set; }
        public ProductTypeEventType Type { get; set; }
        public JToken Values { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public enum ProductTypeEventType
    {
        creation,
        delete
    }

    public class ProductNode
    {
        public string NodeId { get; set; }
    }
}
