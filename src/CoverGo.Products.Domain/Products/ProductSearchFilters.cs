﻿using System;
using System.Collections.Generic;
using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;

namespace CoverGo.Products.Domain.Products
{
    public class ProductWhere : Where
    {
        public IEnumerable<ProductWhere> Or { get; set; }
        public IEnumerable<ProductWhere> And { get; set; }

        public List<ProductId> Id_in { get; set; }
        public ProductIdWhere ProductId { get; set; }
        public string IssuerProductId { get; set; }
        public string TenantId { get; set; }
        public List<string> TenantId_in { get; set; }
        public InsurerWhere Insurer { get; set; }
        public BenefitWhere Benefits_some { get; set; }
        public TagWhere Tags_some { get; set; }
        public DateTime? LaunchPeriodStartDate_lt { get; set; }
        public DateTime? LaunchPeriodStartDate_gt { get; set; }
        public DateTime? LaunchPeriodEndDate_lt { get; set; }
        public DateTime? LaunchPeriodEndDate_gt { get; set; }
        public DateTime? ChangeEffectiveDate_lt { get; set; }
        public DateTime? ChangeEffectiveDate_gt { get; set; }
        public string Status { get; set; }
        public List<string> Status_in { get; set; }
        public List<string> Status_not_in { get; set; }
        public string LifecycleStage { get; set; }
        public InternalReviewWhere InternalReviews_some { get; set; }
        public FieldsWhere Representation { get; set; }

        public FieldsWhere Fields { get; set; }
        public bool? ProductTreeId_exists { get; set; }
    }

    public class TagWhere
    {
        public string Id { get; set; }
        public List<string> Id_in { get; set; }
        public string Type { get; set; }
        public List<string> Type_in { get; set; }
    }

    public class BenefitWhere
    {
        public IEnumerable<BenefitWhere> Or { get; set; }
        public IEnumerable<BenefitWhere> And { get; set; }

        public string TypeId { get; set; }
        public List<string> TypeId_in { get; set; }
        public string RawData_gt { get; set; }
        public string RawData_lt { get; set; }
    }

    public class ProductQuery
    {
        public ProductWhere Where { get; set; }
        public JToken Factors { get; set; }
        public OrderBy? OrderBy { get; set; }
        public int? Skip { get; set; }
        public int? Limit { get; set; }
        public bool LoadRepresentation { get; set; }
    }

    public class ProductEventQuery
    {
        public ProductEventWhere Where { get; set; }
        public OrderBy? OrderBy { get; set; }
        public int? Skip { get; set; }
        public int? Limit { get; set; }

    }

    public class ProductIdWhere
    {
        public string Type { get; set; }
        public IEnumerable<string> Type_in { get; set; }

        public string Plan { get; set; }
        public IEnumerable<string> Plan_in { get; set; }
        public string Plan_contains { get; set; }

        public string Version { get; set; }
        public IEnumerable<string> Version_in { get; set; }
        public string Version_contains { get; set; }

        public string Version_ncontains { get; set; }

        public static implicit operator ProductIdWhere(ProductId productId)
        {
            return new ProductIdWhere
            {
                Plan = productId.Plan,
                Type = productId.Type,
                Version = productId.Version
            };
        }
    }

    public class InsurerWhere
    {
        public string Id { get; set; }
        public IEnumerable<string> Id_in { get; set; }
        public string Id_contains { get; set; }
    }

    public class ProductUnderwritingJsonSchemaQuery
    {
        public IEnumerable<ProductUnderwritingJsonSchemaKey> Keys { get; set; }
        public JToken Factors { get; set; }
    }

    public class ProductUnderwritingJsonSchemaKey
    {
        public string UnderwritingEngineId { get; set; }
        public ProductId ProductId { get; set; }
    }

    public class InternalReviewWhere
    {
        public string Status { get; set; }
        public List<string> Status_in { get; set; }
    }
}
