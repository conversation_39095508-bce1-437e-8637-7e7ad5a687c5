﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Domain.Products
{
    public interface IReferenceGenerator
    {
        string ProviderId { get; }

        Task<string> GenerateAsync(IProductRepository repo, string tenantId, string type, CreateProductCommand command = null, CancellationToken cancellationToken = default);
    }

    public class ReferenceGeneratorConfig
    {
        public string Type { get; set; }
        public string Format { get; set; }
        public List<FormatArgument> Arguments { get; set; } = new List<FormatArgument>();
    }

    public class FormatArgument
    {
        public int Order { get; set; }
        public string Format { get; set; } = string.Empty;
        public string Type { get; set; }
        public string AsAppId { get; set; } // only used to intra-sevice get products call
        public int CurrentIncrement { get; set; } // only used for incrementor
    }
}
