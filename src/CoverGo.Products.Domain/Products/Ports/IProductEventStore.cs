﻿using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Threading;


namespace CoverGo.Products.Domain.Products
{
    public interface IProductEventStore
    {
        string ProviderId { get; }
        Task<Result> AddEventAsync(string tenantId, ProductEvent productEvent, CancellationToken cancellationToken);
        Task<IEnumerable<ProductEvent>> GetEventsAsync(string tenantId, string productType, ProductEventWhere where, OrderBy? orderBy = null, int? skip = null, int? first = null, CancellationToken cancellationToken = default);
        Task<Result> AddTypeEventAsync(string tenantId, ProductTypeEvent productTypeEvent, CancellationToken cancellationToken);
    }
}
