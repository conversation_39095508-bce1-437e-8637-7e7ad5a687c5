using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.DomainUtils;

namespace CoverGo.Products.Domain.Products
{
    public class ExportedTree
    {
        public string Raw { get; set; }
        public LockStatus LockStatus { get; set; }
        public IReadOnlyCollection<string> ReferencedExternalTables { get; set; }
    }

    public class LockStatus
    {
        public bool IsLocked { get; set; }
        public string LockedById { get; set; }
    }

    public class ProductSchema
    {
        public string DataSchema { get; set; }
        public UISchema[] UISchemas { get; set; }
    }

    public class UISchema
    {
        public string Name { get; set; }
        public string Schema { get; set; }
    }

    public class ProductName
    {
        public string Locale { get; set; }
        public string Value { get; set; }
    }

    public class CreateProductSchema
    {
        public string Value { get; set; }
        public string Status { get; set; }
    }

    public class CloneProduct
    {
        public string Status { get; set; }
    }

    public interface IProductBuilderAdapter
    {
        Task<Result<string>> CloneProductTree(string productTreeId, CancellationToken cancellationToken);
        Task<Result> CloneProduct(ProductId productId, ProductName productName, ProductId cloneProductId, CancellationToken cancellationToken);
        Task<Result<CreateProductSchema>> CreateProductSchema(string nodeId, ProductSchema productSchema, CancellationToken cancellationToken);
        Task<Result> AddUiToProductSchema(string productSchemaId, UISchema uiSchema, CancellationToken cancellationToken);
        Task<Result> UpdateProductTreeId(ProductId productId, string productTreeId, CancellationToken cancellationToken);
        Task<Result> UpdateProductLifeCycleStage(ProductId productId, string lifeCycleStage, CancellationToken cancellationToken);
        Task<Result<ExportedTree>> ExportProductTree(string productTreeId, CancellationToken cancellationToken);
        Task<Result<string>> ImportProductTree(string productTreeContent, CancellationToken cancellationToken);
        Task<Result<LockStatus>> LockProductTree(string productTreeId, int expiresInSeconds, CancellationToken cancellationToken);
        Task<Result> UnlockProductTree(string productTreeId, CancellationToken cancellationToken);
        Task<Result<ProductSchema>> GetProductSchema(string productTreeId, CancellationToken cancellationToken);
        Task<Result> ImportProductSchema(string productTreeId, ProductSchema productSchema, CancellationToken cancellationToken);
        Task<Result<string>> GetValidationScript(string productTreeId, CancellationToken cancellationToken);
        Task<Result> ImportValidationScript(string productTreeId, string validationScriptContent, CancellationToken cancellationToken);
    }
}