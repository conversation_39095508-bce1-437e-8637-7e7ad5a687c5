#nullable enable

using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Domain.Products.Ports;

public interface IProductNameRepository
{
    public Task<string?> Get(ProductId productId, CancellationToken cancellationToken = default);

    public Task<List<string?>> GetOrDefaultByIds(IReadOnlyList<ProductId> productId, CancellationToken cancellationToken = default);

    public Task<string> Create(ProductId productId, string productName, CancellationToken cancellationToken = default);
}
