﻿using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Domain.Products.Ports
{
    public interface IProductBuilderEventStore
    {
        Task<List<string>> GetProductNodes(string tenantId, string productTreeId, CancellationToken cancellationToken);

        Task<IEnumerable<NodeEvent>> GetNodeEvents(string tenantId, string nodeId, ProductInfo product, CancellationToken cancellationToken);
    }
}
