using System.Threading;
using System.Threading.Tasks;
using CoverGo.DomainUtils;

namespace CoverGo.Products.Domain.Products
{
    public interface IProductL10NAdapter
    {
        Task<Result<string>> GetProductName(string tenantId, string clientId, ProductId productId, CancellationToken cancellationToken);
        Task<DomainUtils.Result> UpsertAsync(string tenantId, string locale, string key, string value, CancellationToken cancellationToken);
    }
}