using System.Threading;
using System.Threading.Tasks;
using CoverGo.DomainUtils;

namespace CoverGo.Products.Domain.Products
{
    public interface IProductExchangeAdapter
    {
        Task<Result<string>> Upload(string tenantId, ProductPackage productPackage, CancellationToken cancellationToken);
        Task<Result<ProductPackage>> Download(string tenantId, string filePath, CancellationToken cancellationToken, bool validateFileCheckSum = true);
    }
}