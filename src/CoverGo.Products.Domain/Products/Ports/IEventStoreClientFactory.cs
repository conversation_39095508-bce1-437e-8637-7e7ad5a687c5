﻿using EventStore.Client;
using System.Threading;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CoverGo.Products.Domain.Products.Ports
{
    public interface IEventStoreClientFactory
    {
        IEventStoreClientAdapter GetEventStoreClient(EventStoreSettings eventStoreSettings);
    }

    public interface IEventStoreClientAdapter
    {
        Task<List<ResolvedEvent>> ReadStreamAsync(Direction direction, string streamName, StreamPosition revision, long maxCount = long.MaxValue, bool resolveLinkTos = false, TimeSpan? deadline = null, UserCredentials? userCredentials = null, CancellationToken cancellationToken = default(CancellationToken));
    }
}
