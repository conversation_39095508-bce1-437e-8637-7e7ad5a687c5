#nullable enable

using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Domain.Products.Ports;

/// <summary>
/// TenantId breaks Clean Architecture and mustn't be in any layer apart from Infra.
/// </summary>
public interface IProductVersionRepository
{
    public Task<Product> Get(ProductId id, CancellationToken cancellationToken = default);
    public Task<Product> Update(Product product, CancellationToken cancellationToken = default);
}
