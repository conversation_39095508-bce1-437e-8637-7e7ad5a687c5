using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.DomainUtils;

namespace CoverGo.Products.Domain.Products
{
    public interface IExternalTablesAdapter
    {
        Task<Result<string>> DownloadExternalTable(string tenantId, string externalTableName, CancellationToken cancellationToken);
        Task<Result<IReadOnlyCollection<string>>> ListExternalTables(string tenantId, CancellationToken cancellationToken);
        Task<Result> UploadExternalTable(string tenantId, string externalTableName, string content, CancellationToken cancellationToken);
    }
}