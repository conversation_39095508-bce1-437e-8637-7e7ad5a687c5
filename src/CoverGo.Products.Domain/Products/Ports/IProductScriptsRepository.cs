#nullable enable

using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Domain.Products.Ports;

/// <summary>
/// Abstracts away scripts service.
/// Allows to reuse in several endpoints.
/// </summary>
public interface IProductScriptsRepository
{
    public Task<List<string>> CloneProductScripts(IReadOnlyCollection<string>? scriptIds, CancellationToken cancellationToken = default);
}
