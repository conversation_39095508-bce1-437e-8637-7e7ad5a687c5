#nullable enable

using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Facts;
using CoverGo.Products.Domain.Products.Commands;
using Newtonsoft.Json.Linq;

namespace CoverGo.Products.Domain.Products
{
    public interface IProductRepository
    {
        string ProviderId { get; }

        Task<Result> MigrateProductsAsync(string tenantId, MigrateProductsCommand command, CancellationToken cancellationToken);
        Task<Result> CleanProductTest(string tenantId, string typeId, CancellationToken cancellationToken);
        Task<ProductConfig?> GetProductConfigAsync(string tenantId, string? clientId, CancellationToken cancellationToken);
        Task<IEnumerable<ProductConfig>> GetProductConfigsAsync(string tenantId, ProductConfigWhere where, OrderBy? orderBy = null, int? skip = null, int? first = null, CancellationToken cancellationToken = default);
        Task<Result<CreatedStatus>> CreateConfigAsync(string tenantId, ProductConfig productConfig, CancellationToken cancellationToken);
        Task<Result> UpdateConfigAsync(string tenantId, ProductConfig config, CancellationToken cancellationToken);
        Task<Result> DeleteConfigAsync(string tenantId, string id, CancellationToken cancellationToken);
        Task<IEnumerable<Product>> GetAsync(string tenantId, ProductWhere where, ProductConfig config, QueryParameters queryParameters, bool loadRepresentation = false, CancellationToken cancellationToken = default);
        Task<long> GetTotalCountAsync(string tenantId, ProductWhere where, ProductConfig config, CancellationToken cancellationToken = default);
        Task<Result> CreateAsync(string tenantId, CreateProductCommand command, CancellationToken cancellationToken);
        Task<Result> CloneAsync(string tenantId, CloneProductCommand command, CancellationToken cancellationToken);
        Task<Result> UpdateAsync(string tenantId, UpdateProductCommand command, CancellationToken cancellationToken);
        Task<Result> AddBenefitAsync(string tenantId, ProductId productId, AddBenefitCommand command, CancellationToken cancellationToken);
        Task<Result> AddManyBenefitsAsync(string tenantId, ProductId productId, List<AddBenefitCommand> commands, CancellationToken cancellationToken);
        Task<Result> UpdateBenefitAsync(string tenantId, ProductId productId, UpdateBenefitCommand command, CancellationToken cancellationToken);
        Task<Result> UpdateManyBenefitsAsync(string tenantId, ProductId productId, List<UpdateBenefitCommand> commands, CancellationToken cancellationToken);
        Task<Result> RemoveBenefitAsync(string tenantId, ProductId productId, RemoveBenefitCommand command, CancellationToken cancellationToken);
        Task<Result> RemoveManyBenefitsAsync(string tenantId, ProductId productId, List<RemoveBenefitCommand> commands, CancellationToken cancellationToken);
        Task<Result> DeleteAsync(string tenantId, DeleteProductCommand command, CancellationToken cancellationToken);
        Task<Dictionary<string, Dictionary<string, List<string>>>> GetBenefitInfosAsync(string tenantId, string clientId, CancellationToken cancellationToken);
        Task<Dictionary<string, List<string>>> GetBenefitCategoriesAsync(string tenantId, string clientId, CancellationToken cancellationToken);
        Task<IEnumerable<ProductType>> GetTypesAsync(string tenantId, string? clientId, ProductTypeWhere filter, CancellationToken cancellationToken);
        Task<Result> CreateTypeAsync(string tenantId, CreateProductTypeCommand command, CancellationToken cancellationToken);
        Task<Result> DeleteTypeAsync(string tenantId, string typeId, string deletedById, CancellationToken cancellationToken);
        Task<Result> AddUnderwritingVariableAsync(string tenantId, ProductId productId, AddUnderwritingVariableCommand command, CancellationToken cancellationToken);
        Task<Result> UpdateUnderwritingVariableAsync(string tenantId, ProductId productId, UpdateUnderwritingVariableCommand command, CancellationToken cancellationToken);
        Task<Result> RemoveUnderwritingVariableAsync(string tenantId, ProductId productId, RemoveCommand command, CancellationToken cancellationToken);
        Task<Result> AddTagAsync(string tenantId, ProductId productId, AddTagCommand command, CancellationToken cancellationToken);
        Task<Result> RemoveTagAsync(string tenantId, ProductId productId, RemoveCommand command, CancellationToken cancellationToken);
        Task<Result> AddFactAsync(string tenantId, ProductId productId, AddFactCommand command, CancellationToken cancellationToken);
        Task<Result> AddManyFactsAsync(string tenantId, ProductId productId, List<AddFactCommand> commands, CancellationToken cancellationToken);
        Task<Result> UpdateFactAsync(string tenantId, ProductId productId, UpdateFactCommand command, CancellationToken cancellationToken);
        Task<Result> UpdateManyFactsAsync(string tenantId, ProductId productId, List<UpdateFactCommand> commands, CancellationToken cancellationToken);
        Task<Result> RemoveFactAsync(string tenantId, ProductId productId, RemoveCommand command, CancellationToken cancellationToken);
        Task<Result> AddScriptToProduct(string tenantId, AddScriptToProductCommand command, CancellationToken cancellationToken);
        Task<Result> RemoveScriptFromProduct(string tenantId, RemoveScriptFromProductCommand command, CancellationToken cancellationToken);
        Task<Result> AddTemplateRelationshipToProduct(string tenantId, AddTemplateRelationshipToProductCommand command, CancellationToken cancellationToken);
        Task<Result> RemoveTemplateRelationshipFromProduct(string tenantId, RemoveTemplateRelationshipFromProductCommand command, CancellationToken cancellationToken);
        Task<Result> AddInternalReviewAsync(string tenantId, ProductId productId, AddInternalReviewCommand command, CancellationToken cancellationToken);
        Task<Result> UpdateInternalReviewAsync(string tenantId, ProductId productId, UpdateInternalReviewCommand command, CancellationToken cancellationToken);
        Task<Result> RemoveInternalReviewAsync(string tenantId, ProductId productId, RemoveCommand command, CancellationToken cancellationToken);
        Task<Result> AddDataSchemaToProductTypeToProduct(string tenantId, AddDataSchemaToProductTypeCommand command, CancellationToken cancellationToken);
        Task<Result> RemoveDataSchemaToProductTypeFromProduct(string tenantId, RemoveDataSchemaFromProductTypeCommand command, CancellationToken cancellationToken);

        Task<IEnumerable<ProductUnderwritingJsonLogicRules>> GetUnderwritingJsonLogicRulesAsync(string tenantId, ProductWhere where, CancellationToken cancellationToken);
        Task<IReadOnlyCollection<string>> GetProductScriptIds(string tenantId, ProductId productId, CancellationToken cancellationToken);
        Task<Result> SetTermsAndConditionsTemplateToProduct(string tenantId, SetTermsAndConditionsCommand command, CancellationToken cancellationToken);
        Task<Result> RemoveTermsAndConditionsTemplateFromProduct(string tenantId, RemoveTermsAndConditionsCommand command, CancellationToken cancellationToken);
        Task<Result> SetRatingFactorsTableToProduct(string tenantId, SetRatingFactorsTableCommand command, CancellationToken cancellationToken);
        Task<Result> RemoveRatingFactorsTableFromProduct(string tenantId, RemoveRatingFactorsTableCommand incommandput, CancellationToken cancellationToken);
        Task<Result> SetSegmentsToProduct(string tenantId, SetSegmentsCommand command, CancellationToken cancellationToken);
        Task<Result> AttachDocumentsToProduct(string tenantId, AttachDocumentsCommand command, CancellationToken cancellationToken);
        Task<Result> RemoveAttachedDocumentFromProduct(string tenantId, RemoveAttachedDocumentCommand command, CancellationToken cancellationToken);
    }

#nullable disable

    public class CreateProductCommand
    {
        public ProductId ProductId { get; set; }
        public string InsurerId { get; set; }
        public string IssuerProductId { get; set; } //ToDo: used for chubb, maybe need to prevent others from using it
        public string CreatedById { get; set; }
        public IEnumerable<AddBenefitCommand> BenefitInputs { get; set; }
        public ProductSettings RejectionSettings { get; set; }
        public LoadingSettings LoadingSettings { get; set; }
        public ProductSettings ExclusionSettings { get; set; }
        public ClaimSettings ClaimSettings { get; set; }
        public Underwriting Underwriting { get; set; }
        public IEnumerable<Tag> Tags { get; set; }
        public IEnumerable<AddFactCommand> Facts { get; set; }
        public DateTime? LaunchPeriodStartDate { get; set; }
        public DateTime? LaunchPeriodEndDate { get; set; }
        public DateTime? ChangeEffectiveDate { get; set; }
        public string Status { get; set; }
        public string LifecycleStage { get; set; }
        public string Representation { get; set; }
        public string TermsAndConditionsTemplateId { get; set; }
        public string TermsAndConditionsJacketId { get; set; }
        public string RatingFactorsTable { get; set; }
        public string Segments { get; set; }

        public string Fields { get; set; }
        public string ProductTreeId { get; set; }
        public IReadOnlyCollection<string>? ScriptIds { get; set; }
        public PolicyIssuanceMethod? PolicyIssuanceMethod { get; set; }
        public TimeSpan? OfferValidityPeriod { get; set; }
        public bool? AllowCustomProduct { get; set; }
        public List<AttachedDocumentId>? AttachedDocumentsIds { get; set; } = [];
        public bool? AutoRenewal { get; set; }
        public bool? RenewalNotification { get; set; }
        public TaxConfiguration? TaxConfiguration { get; set; }
    }

    public class AddBenefitCommand
    {
        public string TypeId { get; set; }
        public string ParentTypeId { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public List<string> ParentOptionKeys { get; set; }
        public string OptionKey { get; set; }
        public JToken Value { get; set; }
        public Condition Condition { get; set; }
        public bool IsValueInput { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdateBenefitCommand
    {
        public string TypeId { get; set; }
        public string OptionKey { get; set; }
        public string ParentTypeId { get; set; }
        public bool IsParentTypeIdChanged { get; set; }
        public List<string> ParentOptionKeys { get; set; }
        public bool IsParentOptionKeysChanged { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public bool IsCurrencyCodeChanged { get; set; }
        public JToken Value { get; set; }
        public bool IsValueChanged { get; set; }
        public Condition Condition { get; set; }
        public bool IsConditionChanged { get; set; }
        public string ModifiedById { get; set; }
        public bool IsValueInput { get; set; }
        public bool IsIsValueInputChanged { get; set; }
    }

    public class BenefitCommandBatch
    {
        public List<AddBenefitCommand> AddBenefitCommands { get; set; }
        public List<UpdateBenefitCommand> UpdateBenefitCommands { get; set; }
        public List<RemoveBenefitCommand> RemoveBenefitCommands { get; set; }
    }

    public class RemoveBenefitCommand
    {
        public string TypeId { get; set; }
        public string OptionKey { get; set; }
        public string RemovedById { get; set; }
    }

    public record UpdateProductCommand
    {
        public ProductId ProductId { get; set; }
        public string InsurerId { get; set; }
        public bool IsInsurerIdChanged { get; set; }
        public ProductSettingsToUpdate RejectionSettings { get; set; }
        public bool IsRejectionSettingsChanged { get; set; }
        public LoadingSettingsToUpdate LoadingSettings { get; set; }
        public bool IsLoadingSettingsChanged { get; set; }
        public ProductSettingsToUpdate ExclusionSettings { get; set; }
        public bool IsExclusionSettingsChanged { get; set; }
        public ClaimSettingsToUpdate ClaimSettings { get; set; }
        public bool IsClaimSettingsChanged { get; set; }
        public bool IsUnderwritingChanged { get; set; }
        public UnderwritingToUpdate Underwriting { get; set; }
        public string ModifiedById { get; set; }
        public string LifecycleStage { get; set; }
        public bool IsLifecycleStageChanged { get; set; }
        public string Representation { get; set; }
        public bool IsRepresentationChanged { get; set; }
        public string RepresentationPatch { get; set; }

        public string Fields { get; set; }
        public bool IsFieldsChanged { get; set; }
        public string FieldsPatch { get; set; }

        public DateTime? LaunchPeriodStartDate { get; set; }
        public bool IsLaunchPeriodStartDateChanged { get; set; }
        public DateTime? LaunchPeriodEndDate { get; set; }
        public bool IsLaunchPeriodEndDateChanged { get; set; }
        public DateTime? ChangeEffectiveDate { get; set; }
        public bool IsChangeEffectiveDateChanged { get; set; }
        public string Status { get; set; }
        public bool IsStatusChanged { get; set; }
        public string ProductTreeId { get; set; }
        public bool IsProductTreeIdChanged { get; set; }
        public PolicyIssuanceMethod? PolicyIssuanceMethod { get; set; }
        public bool IsPolicyIssuanceMethodChanged { get; set; }
        public bool IsOfferValidityPeriodChanged { get; set; }
        public TimeSpan? OfferValidityPeriod { get; set; }
        public bool? AllowCustomProduct { get; set; }
        public bool IsAllowCustomProductChanged { get; set; }
        public ProductUpdateTypes UpdateTypes { get; set; }
        public bool IsUpdateTypesChanged { get; set; }
        public bool? AutoRenewal { get; set; }
        public bool? RenewalNotification { get; set; }
        public TaxConfiguration? TaxConfiguration { get; set; }
    }

    public class CloneProductCommand
    {
        public ProductId ProductId { get; set; }
        public ProductId CloneProductId { get; set; }
        public List<string> ScriptIds { get; set; }
        public string IssuerProductId { get; set; }
        public string CreatedById { get; set; }
    }

    public class UnderwritingToUpdate
    {
        public bool IsSourceTypeChanged { get; set; }
        public string SourceType { get; set; }

        public bool IsJsonLogicRulesChanged { get; set; } // only for `jsonLogic`
        public JToken JsonLogicRules { get; set; } // only for `jsonLogic`

        public bool IsExcelPathChanged { get; set; } // only for `excel`
        public string ExcelPath { get; set; } // only for `excel`

        public bool IsExcelRulesChanged { get; set; } // only for `excel`
        public ExcelRules ExcelRules { get; set; } // only for `excel`
    }

    public class DeleteProductCommand
    {
        public ProductId ProductId { get; set; }
        public string DeletedById { get; set; }
    }
}
