using System;

namespace CoverGo.Products.Domain.Products
{
    public class ProductEventLog
    {
        public string Id { get; set; }
        public string NodeId { get; set; }
        public ProductId ProductId { get; set; }
        public string Ref { get; set; }
        public string Alias { get; set; }
        public string Type { get; set; }
        public string Values { get; set; }
        public DateTime Timestamp { get; set; }
        public User UpdatedBy { get; set; }
    }
    public class User
    {
        public string Email { get; set; }
        public string Username { get; set; }
        public string Name { get; set; }
    }

    public class ProductInfo
    {
        public ProductId Id { get; set; }
        public string ProductTreeId { get; set; }
    }

}