using System;
using System.Collections.Generic;

using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Facts;

using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

using NPOI.SS.UserModel;

namespace CoverGo.Products.Domain.Products
{
    public class Product : SystemObject
    {
        public ProductId Id { get; set; }
        public string TenantId { get; set; }
        public string InsurerId { get; set; }
        public string IssuerProductId { get; set; }
        public string ClaimFactTemplateId { get; set; }

        public IEnumerable<Benefit> Benefits { get; set; }

        public Underwriting Underwriting { get; set; }

        public ProductSettings RejectionSettings { get; set; }
        public ClaimSettings ClaimSettings { get; set; }
        public LoadingSettings LoadingSettings { get; set; }
        public ProductSettings ExclusionSettings { get; set; }

        public IEnumerable<Tag> Tags { get; set; }
        public IEnumerable<Fact> Facts { get; set; }

        public DateTime? LaunchPeriodStartDate { get; set; }
        public DateTime? LaunchPeriodEndDate { get; set; }
        public DateTime? ChangeEffectiveDate { get; set; }
        public string Status { get; set; }

        public IEnumerable<InternalReview> InternalReviews { get; set; }

        public string LifecycleStage { get; set; }

        /// <summary>
        /// Visual tree or any other JSON-as-set-of-rules product representation
        /// </summary>
        public string Representation { get; set; }

        /// <summary>
        /// Is used for Data Extraction.
        /// Currently, No FE logic related to this
        /// </summary>
        public IReadOnlyCollection<Plan> Plans { get; set; }

        public string Fields { get; set; }
        public IReadOnlyCollection<string> ScriptIds { get; set; }

        public IReadOnlyCollection<string> TemplateRelationshipIds { get; set; }
        public string ProductTreeId { get; set; }

        public string TermsAndConditionsTemplateId { get; set; }
        public string TermsAndConditionsJacketId { get; set; }
        public string RatingFactorsTable { get; set; }
        public string Segments { get; set; }

        // TODO: Ideally it should live in quotation service.
        public PolicyIssuanceMethod? PolicyIssuanceMethod { get; set; }
        public TimeSpan? OfferValidityPeriod { get; set; }
        public bool? AllowCustomProduct { get; set; }
        public ProductUpdateTypes UpdateTypes { get; set; }
        public List<AttachedDocumentId>? AttachedDocumentsIds { get; set; } = [];
        public bool AutoRenewal { get; set; }
        public bool RenewalNotification { get; set; }
        public TaxConfiguration TaxConfiguration { get; set; }

        public Product CloneProduct(
            string cloneProductVersion,
            bool isReadonly,
            List<string> scriptIds,
            string? lifecycleStage = default)
        {
            var newFieldsObj = JObject.Parse(Fields ?? "{}");
            newFieldsObj["readonly"] = false;

            var newRepresentationObj = JArray.Parse(Representation ?? "[]");
            if (isReadonly &&
                newRepresentationObj.Count > 0 &&
                newRepresentationObj[0] is JObject root &&
                root.ContainsKey("ch") &&
                root["ch"] is JArray children)
            {
                foreach (var child in children)
                {
                    if (child is JObject childObj &&
                       childObj["n"]?.ToString() == "plan")
                    {
                        childObj["readonly"] = false;
                    }
                }
            }

            if (lifecycleStage is not null)
            {
                newFieldsObj[nameof(lifecycleStage)] = lifecycleStage;
            }

            var clonedProduct = new Product()
            {
                Id = new()
                {
                    Plan = Id.Plan,
                    Type = Id.Type,
                    Version = cloneProductVersion,
                },
                TenantId = TenantId,
                Benefits = Benefits,
                ClaimFactTemplateId = ClaimFactTemplateId,
                ClaimSettings = ClaimSettings,
                ExclusionSettings = ExclusionSettings,
                Facts = Facts,
                Fields = JsonConvert.SerializeObject(newFieldsObj),
                InsurerId = InsurerId,
                InternalReviews = InternalReviews,
                Underwriting = Underwriting,
                TermsAndConditionsTemplateId = TermsAndConditionsTemplateId,
                TermsAndConditionsJacketId = TermsAndConditionsJacketId,
                RatingFactorsTable = RatingFactorsTable,
                TemplateRelationshipIds = TemplateRelationshipIds,
                Tags = Tags,
                Status = Status,
                ScriptIds = scriptIds,
                Representation = JsonConvert.SerializeObject(newRepresentationObj),
                RejectionSettings = RejectionSettings,
                ProductTreeId = ProductTreeId,
                LoadingSettings = LoadingSettings,
                LifecycleStage = lifecycleStage ?? LifecycleStage,
                LaunchPeriodStartDate = LaunchPeriodStartDate,
                LaunchPeriodEndDate = LaunchPeriodEndDate,
                IssuerProductId = IssuerProductId,
                ChangeEffectiveDate = ChangeEffectiveDate,
                Segments = Segments,
                PolicyIssuanceMethod = PolicyIssuanceMethod,
                OfferValidityPeriod = OfferValidityPeriod,
                AllowCustomProduct = AllowCustomProduct,
                AttachedDocumentsIds = AttachedDocumentsIds,
                AutoRenewal = AutoRenewal,
                RenewalNotification = RenewalNotification,
                TaxConfiguration = TaxConfiguration,
            };
            return clonedProduct;
        }
    }

    public class ProductUnderwritingJsonLogicRules
    {
        public ProductId Id { get; set; }
        public JToken JsonLogicRules { get; set; }
    }

    public class ProductUnderwritingJsonSchema
    {
        public string UnderwritingEngineId { get; set; }
        public ProductId ProductId { get; set; }
        public string JsonSchema { get; set; }
    }

    public class Tag
    {
        public string Id { get; set; }
        public string Type { get; set; }
    }

    public class AddTagCommand
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public string AddedById { get; set; }
    }

    public class Underwriting
    {
        public string SourceType { get; set; }
        public List<UnderwritingVariable> Variables { get; set; }

        public JToken JsonLogicRules { get; set; } // only for `jsonLogic`

        public string ExcelPath { get; set; } // only for `excel`
        public ExcelRules ExcelRules { get; set; } // only for `excel`
    }

    public class ExcelRules
    {
        public ExcelCell ResultCell { get; set; }
    }

    public class ExcelCell
    {
        public int RowIndex { get; set; }
        public int ColumnIndex { get; set; }
        public CellType Type { get; set; }
    }

    public class ValidateResult
    {
        public ProductId ProductId { get; set; }
        public string Status { get; set; }
        public IEnumerable<Error> Errors { get; set; }
    }

    public class UnderwritingVariable
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string JsonSchemaValidation { get; set; }
    }

    public class ProductSettings //add inheritance to this if there are more props later
    {
        public List<string> Codes { get; set; }
    }

    public class ProductSettingsToUpdate //add inheritance to this if there are more props later
    {
        public List<string> Codes { get; set; }
        public bool IsCodesChanged { get; set; }
    }

    public class LoadingSettings : ProductSettings
    {
        public decimal? MaxLoadingMultiplier { get; set; }
    }

    public class LoadingSettingsToUpdate
    {
        public List<string> Codes { get; set; }
        public bool IsCodesChanged { get; set; }
        public decimal? MaxLoadingMultiplier { get; set; }
        public bool IsMaxLoadingMultiplierChanged { get; set; }
    }

    public class ClaimSettings
    {
        public ProductSettings RejectionSettings { get; set; }
        public ProductSettings DiagnosisSettings { get; set; }
        public ProductSettings OperationSettings { get; set; }
        public ProviderSettings ProviderSettings { get; set; }
    }

    public class ClaimSettingsToUpdate
    {
        public ProductSettingsToUpdate RejectionSettings { get; set; }
        public bool IsRejectionSettingsChanged { get; set; }
        public ProductSettingsToUpdate DiagnosisSettings { get; set; }
        public bool IsDiagnosisSettingsChanged { get; set; }
        public ProductSettingsToUpdate OperationSettings { get; set; }
        public bool IsOperationSettingsChanged { get; set; }
        public ProviderSettingsToUpdate ProviderSettings { get; set; }
        public bool IsProviderSettingsChanged { get; set; }
    }

    public class ProviderSettings
    {
        public List<string> EntityIds { get; set; }
    }

    public class ProviderSettingsToUpdate
    {
        public List<string> EntityIds { get; set; }
        public bool IsEntityIdsChanged { get; set; }
    }

    public class Plan
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public List<string> BenefitCodes { get; set; }
    }

    #region Product Tree
    public class Representation
    {
        public Ch[] Ch { get; set; }
    }

    public class Ch
    {
        public string? N { get; set; }
        public P[] P { get; set; }
    }

    public class P
    {
        public string N { get; set; }
        public string V { get; set; }
    }
    #endregion

    public class Benefit
    {
        public string TypeId { get; set; }
        public JToken Value { get; set; }
        public bool IsValueInput { get; set; }
        public string ParentTypeId { get; set; }
        public string ParentOptionKey { get; set; }
        public List<string> ParentOptionKeys { get; set; }
        public string OptionKey { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public bool IsOptional { get; set; }
        public IEnumerable<BenefitOption> Options { get; set; }

        public Condition Condition { get; set; }
    }

    public class Condition
    {
        public string Type { get; set; } // jsonLogic or excelFormula
        public JObject JsonLogicRule { get; set; }
    }

    public class BenefitOption
    {
        public string Key { get; set; }
        public object Value { get; set; }
    }

    public class InternalReview : SystemObject
    {
        public string Id { get; set; }
        public string Status { get; set; }
        public string Comment { get; set; }
    }

    public class AddInternalReviewCommand
    {
        public string Id { get; set; }
        public string Status { get; set; }
        public string Comment { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdateInternalReviewCommand
    {
        public string Id { get; set; }
        public string Status { get; set; }
        public bool IsStatusChanged { get; set; }
        public string Comment { get; set; }
        public bool IsCommentChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class BenefitTypes
    {
        public IEnumerable<string> PrimaryBenefitTypes { get; set; }
        public IEnumerable<string> SecondaryBenefitTypes { get; set; }
    }

    public class ProductType : SystemObject
    {
        public string TypeId { get; set; }
        public string Code { get; set; }
        public string LogoUrl { get; set; }
        public string TenantId { get; set; }

        public IReadOnlyCollection<string> DataSchemaIds { get; set; }
    }

    public class ProductTypeWhere
    {
        public IEnumerable<ProductTypeWhere> Or { get; set; }
        public IEnumerable<ProductTypeWhere> And { get; set; }

        public string TypeId { get; set; }
        public IEnumerable<string> TypeId_in { get; set; }
        public string TenantId { get; set; }
        public IEnumerable<string> TenantId_in { get; set; }
    }

    public class CreateProductTypeCommand
    {
        public string TypeId { get; set; }
        public string LogoUrl { get; set; }
        public string CreatedById { get; set; }
    }

    public class InitializeTenantProductsCommand
    {
        public IEnumerable<ProductConfig> ProductConfigs { get; set; }
    }

    public class AddUnderwritingVariableCommand
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string JsonSchemaValidation { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdateUnderwritingVariableCommand
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public bool IsNameChanged { get; set; }
        public string Description { get; set; }
        public bool IsDescriptionChanged { get; set; }
        public string JsonSchemaValidation { get; set; }
        public bool IsJsonSchemaValidationChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class AddScriptToProductCommand
    {
        public ProductId ProductId { get; set; }

        public string ScriptId { get; set; }

        public string AddedById { get; set; }
    }

    public class RemoveScriptFromProductCommand
    {
        public ProductId ProductId { get; set; }

        public string ScriptId { get; set; }
    }

    public class AddTemplateRelationshipToProductCommand
    {
        public ProductId ProductId { get; set; }

        public string TemplateRelationshipId { get; set; }

        public string AddedById { get; set; }
    }

    public class RemoveTemplateRelationshipFromProductCommand
    {
        public ProductId ProductId { get; set; }

        public string TemplateRelationshipId { get; set; }

        public string RemovedById { get; set; }
    }

    public class AddDataSchemaToProductTypeCommand
    {
        public string ProductTypeId { get; set; }

        public string DataSchemaId { get; set; }

        public string AddedById { get; set; }
    }

    public class RemoveDataSchemaFromProductTypeCommand
    {
        public string ProductTypeId { get; set; }

        public string DataSchemaId { get; set; }

        public string RemovedById { get; set; }
    }

    public enum PolicyIssuanceMethod
    {
        ManualNoPremiumDependency,
        ManualAfterPremiumReceipt,
        AutomaticAfterPremiumReceipt
    }

    public class ProductUpdateTypes
    {
        public bool? Plans { get; set; }
        public bool? BenefitsOrLimits { get; set; }
        public bool? Pricing { get; set; }
        public bool? OtherMinorChanges { get; set; }
    }

    public enum AttachedDocumentCategory
    {
        Offer,
        Proposal,
        Policy,
        PolicyCancellation,
        PolicyEndorsement
    }

    public class AttachedDocumentId
    {
        public string TemplateId { get; set; }
        public AttachedDocumentCategory Category { get; set; }
    }

#nullable enable
    public class TaxConfiguration
    {
        public string ReferenceMnemonic { get; set; } = string.Empty;
        public string MnemonicCode { get; set; } = string.Empty;
        public bool AllowOverrideAtOfferStage { get; set; } = false;
        public bool AllowOverrideAtProposalStage { get; set; } = false;
    }
}
