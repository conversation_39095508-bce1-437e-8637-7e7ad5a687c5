#nullable enable

using System.Collections.Generic;
using CoverGo.Products.Domain.Products.Limits;
using CoverGo.Products.Domain.Products.ProductTreeParsing;

namespace CoverGo.Products.Domain.Products;

public class ProductPlanDetails
{
    public required ProductId ProductId { get; set; }
    public List<ProductPlanDetail> PlanDetails { get; set; } = [];
    public List<PlanAddOn> AddOns { get; set; } = [];
    public List<ProductRepresentationNode> MasterPlan { get; set; } = [];
    public List<ProductRepresentationNode> AllPlans { get; set; } = [];
    public List<AttachedDocument>? AttachedDocuments { get; set; } = [];

}

public class ProductPlanDetail
{
    public required ProductId ProductId { get; set; }
    public string? Id { get; set; }
    public string? Name { get; set; }
    public List<PlanBenefitType> BenefitTypes { get; set; } = [];
    public List<PlanBenefit> Benefits { get; set; } = [];
    public List<PlanAddOn> AddOns { get; set; } = [];
}

public interface ILimitHolder
{
    public List<PlanBenefitLimit> Limits { get; set; }
    public List<PlanBenefitOverseasAccidentalLimit> OverseasAccidentalLimits { get; set; }
    public List<PlanBenefitGroupedBenefitLimit> GroupedBenefitLimits { get; set; }
    public List<PlanBenefitDeductible> Deductibles { get; set; }
    public List<PlanBenefitCopayment> Copayments { get; set; }
    public List<PlanBenefitPanelCopayment> PanelCopayments { get; set; }
    public List<PlanBenefitCoinsurance> Coinsurances { get; set; }
    public List<PlanBenefitReimbursement> Reimbursements { get; set; }
    public List<PlanBenefitGeographicalLimit> GeographicalLimits { get; set; }
    public List<PlanBenefitWaitingPeriod> WaitingPeriods { get; set; }
    public List<PlanBenefitDeferredPeriod> DeferredPeriods { get; set; }
    public List<PlanBenefitQualificationPeriod> QualificationPeriods { get; set; }
}

public record PlanBenefitType : ILimitHolder
{
    public string? Name { get; set; }
    public List<PlanBenefitType> BenefitTypes { get; set; } = [];
    public List<PlanBenefit> Benefits { get; set; } = [];
    public string? Code { get; set; }
    public List<PlanBenefitLimit> Limits { get; set; } = [];
    public List<PlanBenefitOverseasAccidentalLimit> OverseasAccidentalLimits { get; set; } = [];
    public List<PlanBenefitGroupedBenefitLimit> GroupedBenefitLimits { get; set; } = [];
    public List<PlanBenefitDeductible> Deductibles { get; set; } = [];
    public List<PlanBenefitCopayment> Copayments { get; set; } = [];
    public List<PlanBenefitPanelCopayment> PanelCopayments { get; set; } = [];
    public List<PlanBenefitCoinsurance> Coinsurances { get; set; } = [];
    public List<PlanBenefitReimbursement> Reimbursements { get; set; } = [];
    public List<PlanBenefitGeographicalLimit> GeographicalLimits { get; set; } = [];
    public List<PlanBenefitWaitingPeriod> WaitingPeriods { get; set; } = [];
    public List<PlanBenefitDeferredPeriod> DeferredPeriods { get; set; } = [];
    public List<PlanBenefitQualificationPeriod> QualificationPeriods { get; set; } = [];
}

public record PlanBenefit : ILimitHolder
{
    public string? Name { get; set; }
    public List<PlanBenefitType> BenefitTypes { get; set; } = [];
    public List<PlanBenefit> Benefits { get; set; } = [];
    public List<PlanBenefitLimit> Limits { get; set; } = [];
    public List<PlanBenefitOverseasAccidentalLimit> OverseasAccidentalLimits { get; set; } = [];
    public List<PlanBenefitGroupedBenefitLimit> GroupedBenefitLimits { get; set; } = [];
    public List<PlanBenefitDeductible> Deductibles { get; set; } = [];
    public List<PlanBenefitCopayment> Copayments { get; set; } = [];
    public List<PlanBenefitPanelCopayment> PanelCopayments { get; set; } = [];
    public List<PlanBenefitCoinsurance> Coinsurances { get; set; } = [];
    public List<PlanBenefitReimbursement> Reimbursements { get; set; } = [];
    public List<PlanBenefitGeographicalLimit> GeographicalLimits { get; set; } = [];
    public List<PlanBenefitWaitingPeriod> WaitingPeriods { get; set; } = [];
    public List<PlanBenefitDeferredPeriod> DeferredPeriods { get; set; } = [];
    public List<PlanBenefitQualificationPeriod> QualificationPeriods { get; set; } = [];
}

public class PlanAddOn
{
    public string? Id { get; set; }
    public string? Name { get; set; }
    public List<PlanBenefitType> BenefitTypes { get; set; } = [];
    public List<PlanBenefit> Benefits { get; set; } = [];
}

public class AttachedDocument
{
    public string? Id { get; set; }
    public string? LogicalId { get; set; }
    public string? Name { get; set; }
    public string? Type { get; set; }
    public AttachedDocumentCategory? Category { get; set; }
}
