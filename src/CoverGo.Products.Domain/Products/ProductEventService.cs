using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.DomainUtils;
using CoverGo.Threading.Tasks;

namespace CoverGo.Products.Domain.Products
{
    public class ProductEventService(IProductEventStore productEventStore)
    {
        public async Task<Result> AddEventAsync(string tenantId, ProductEvent productEvent, CancellationToken cancellationToken)
        {
            Result result = await productEventStore.AddEventAsync(tenantId, productEvent, cancellationToken);

            return result;
        }

        public async Task<IEnumerable<ProductEvent>> GetEventsAsync(string tenantId, IEnumerable<ProductId> productIds, CancellationToken cancellationToken)
        {
            List<ProductEvent> allEvents = new List<ProductEvent>();
            IEnumerable<string> productTypes = productIds.Select(x => x.Type).Distinct();
            IEnumerable<ProductEvent>[] events = await productTypes.ParallelSelectAsync((productType,  ct) =>
                                                                productEventStore.GetEventsAsync(tenantId, productType, new ProductEventWhere
                                                                {
                                                                    ProductIds_in = productIds.ToList()
                                                                }, null, null, null, ct),
                new ParallelRunOptions(){MaxDegreeOfParallelism = 10, CancellationToken = cancellationToken});
            allEvents.AddRange(events.SelectMany(@event => @event));
            return allEvents;
        }

        public async Task<Result> AddTypeEventAsync(string tenantId, ProductTypeEvent productTypeEvent, CancellationToken cancellationToken)
        {
            Result result = await productEventStore.AddTypeEventAsync(tenantId, productTypeEvent, cancellationToken);

            return result;
        }
    }
}
