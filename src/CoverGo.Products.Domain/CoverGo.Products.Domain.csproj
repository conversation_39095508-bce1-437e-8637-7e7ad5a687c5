<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <NoWarn>8600,8602,8603,8604,8632</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CoverGo.Users.Client" />
    <PackageReference Include="DotNetCore.NPOI" />
    <PackageReference Include="EventStore.Client.Grpc.Streams" />
    <PackageReference Include="JsonLogic.Net" />
    <PackageReference Include="HotChocolate.Types" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CoverGo.FileSystem.Client" />
    <PackageReference Include="CoverGo.Templates.Client.Rest" />
    <PackageReference Include="CoverGo.Reference.Client" />
    <PackageReference Include="CoverGo.ChannelManagement.Client" />
    <PackageReference Include="CoverGo.Scripts.Client" />
    <PackageReference Include="HotChocolate.Data.MongoDb" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CoverGo.Applications.Domain" />
    <PackageReference Include="CoverGo.BuildingBlocks.Application.Core" />
    <PackageReference Include="CoverGo.Configuration" />
    <PackageReference Include="CoverGo.FeatureManagement" />
    <PackageReference Include="CoverGo.Proxies.Product" />
    <PackageReference Include="CoverGo.SettableValues" />
    <PackageReference Include="CoverGo.Proxies.Auth" />
    <PackageReference Include="CoverGo.Threading.Tasks" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Jackets" />
  </ItemGroup>

</Project>
