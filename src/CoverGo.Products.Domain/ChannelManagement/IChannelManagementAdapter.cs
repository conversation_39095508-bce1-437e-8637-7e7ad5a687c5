﻿using CoverGo.ChannelManagement.Client;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Domain.Scripts;

public interface IChannelManagementAdapter
{
    public Task<IReadOnlyList<ICommissionCandidates_CommissionCandidates>> GetCommissionCandidates(CommissionCandidatesInput input, CancellationToken cancellationToken);
    Task<IReadOnlyList<ICampaigns_Campaigns_Items>> GetCampaigns(CampaignFilterInput where, int take, CancellationToken cancellationToken);
}