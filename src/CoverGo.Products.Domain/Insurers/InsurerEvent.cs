﻿using Newtonsoft.Json.Linq;

using System;

namespace CoverGo.Products.Domain.Insurers
{
    public class InsurerEvent
    {
        public InsurerEvent(string insurerId, InsurerEventType type, DateTime timestamp)
        {
            InsurerId = insurerId;
            Type = type;
            Timestamp = timestamp;
        }

        public string Id { get; set; }
        public string InsurerId { get; set; }
        public InsurerEventType Type { get; set; }
        public JToken Values { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public enum InsurerEventType
    {
        creation,
        delete
    }
}
