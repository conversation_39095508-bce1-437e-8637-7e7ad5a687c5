﻿using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Domain.Insurers
{
    public interface IInsurerRepository
    {
        Task<IEnumerable<Insurer>> GetInsurersAsync(string tenantId, string clientId, InsurerFilter filter, CancellationToken cancellationToken);
        Task<Result> CreateInsurerAsync(string tenantId, CreateInsurerCommand command, CancellationToken cancellationToken);
        Task<Result> DeleteInsurerAsync(string tenantId, string id, string deletedById, CancellationToken cancellationToken);
        Task<Result> MigrateInsurersAsync(string tenantId, MigrateInsurersCommand command, CancellationToken cancellationToken);
    }
}
