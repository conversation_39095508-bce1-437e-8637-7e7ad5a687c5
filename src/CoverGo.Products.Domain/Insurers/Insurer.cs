﻿using CoverGo.DomainUtils;
using System.Collections.Generic;

namespace CoverGo.Products.Domain.Insurers
{
    public class Insurer : SystemObject
    {
        public string Id { get; set; }
        public InsurerLogoUrls LogoUrls { get; set; }
        public string TenantId { get; set; }
    }

    public class InsurerLogoUrls
    {
        public string TypeA { get; set; }
        public string TypeB { get; set; }
        public string TypeC { get; set; }
        public string TypeD { get; set; }
        public string TypeE { get; set; }
    }

    public class InsurerFilter
    {
        public IEnumerable<string> Ids { get; set; }
        public IEnumerable<string> ProductTypes { get; set; }
    }

    public class CreateInsurerCommand
    {
        public string Id { get; set; }
        public InsurerLogoUrls LogoUrls { get; set; }
        public string CreatedById { get; set; }
    }

    public class MigrateInsurersCommand
    {
        public IEnumerable<CreateInsurerCommand> InsurerInputs { get; set; }
    }
}
