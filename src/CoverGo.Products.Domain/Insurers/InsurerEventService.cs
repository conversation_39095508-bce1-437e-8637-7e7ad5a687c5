using System.Threading.Tasks;

using CoverGo.DomainUtils;

namespace CoverGo.Products.Domain.Insurers
{
    public class InsurerEventService(IInsurerEventStore insurerEventStore)
    {
        public async Task<Result> AddEventAsync(string tenantId, InsurerEvent insurerEvent)
        {
            Result result = await insurerEventStore.AddEventAsync(tenantId, insurerEvent);

            return result;
        }
    }
}
