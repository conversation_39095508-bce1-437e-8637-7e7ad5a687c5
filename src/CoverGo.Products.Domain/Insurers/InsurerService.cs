using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.DomainUtils;
using CoverGo.Threading.Tasks;

using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;

namespace CoverGo.Products.Domain.Insurers
{
    public class InsurerService
    {
        private readonly IInsurerRepository _insurerRepository;
        private readonly InsurerEventService _insurerEventService;
        private readonly JsonSerializer _jsonSerializer;

        public InsurerService(IInsurerRepository insurerRepository, InsurerEventService insurerEventService)
        {
            _insurerRepository = insurerRepository;
            _insurerEventService = insurerEventService;

            var settings = new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
                TypeNameHandling = TypeNameHandling.Auto,
                NullValueHandling = NullValueHandling.Ignore
            };
            settings.Converters.Add(new StringEnumConverter());

            _jsonSerializer = JsonSerializer.Create(settings);
        }

        public async Task<Result> MigrateInsurersAsync(string tenantId, MigrateInsurersCommand command, CancellationToken cancellationToken)
        {
            Result result = await _insurerRepository.MigrateInsurersAsync(tenantId, command, cancellationToken);

            if (result.Status != "success")
                return result;

            await command.InsurerInputs?.ParallelForEachAsync(async i =>
                await _insurerEventService.AddEventAsync(tenantId, new InsurerEvent(i.Id, InsurerEventType.creation, DateTime.UtcNow)
                {
                    Values = JObject.FromObject(i, _jsonSerializer)
                }), new ParallelRunOptions() { MaxDegreeOfParallelism = 10 });

            return result;
        }

        public async Task<IEnumerable<Insurer>> GetInsurersAsync(string tenantId, string clientId, InsurerFilter filter, CancellationToken cancellationToken)
        {
            return await _insurerRepository.GetInsurersAsync(tenantId, clientId, filter, cancellationToken);
        }

        public async Task<Result> CreateInsurerAsync(string tenantId, CreateInsurerCommand command, CancellationToken cancellationToken)
        {
            Result result = await _insurerRepository.CreateInsurerAsync(tenantId, command, cancellationToken);

            return result.Status != "success"
                ? result
                : await _insurerEventService.AddEventAsync(tenantId, new InsurerEvent(command.Id, InsurerEventType.creation, DateTime.UtcNow)
                {
                    Values = JObject.FromObject(command, _jsonSerializer)
                });
        }

        public async Task<Result> DeleteInsurerAsync(string tenantId, string id, string deletedById, CancellationToken cancellationToken)
        {
            Result result = await _insurerRepository.DeleteInsurerAsync(tenantId, id, deletedById, cancellationToken);

            return result.Status != "success"
                ? result
                : await _insurerEventService.AddEventAsync(tenantId, new InsurerEvent(id, InsurerEventType.delete, DateTime.UtcNow)
                {
                    Values = JObject.FromObject(new DeleteCommand { DeletedById = deletedById }, _jsonSerializer)
                });
        }
    }
}
