using CoverGo.Scripts.Client;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;

namespace CoverGo.Products.Domain;

public class AdjustmentValueConverter : JsonConverter
{
    public override bool CanConvert(Type objectType) => objectType == typeof(AdjustmentValue);

    public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
    {
        if (reader.TokenType != JsonToken.StartObject)
            return null;
        var item = JObject.Load(reader);

        string type = item["type"].Value<string>();
        bool parseSuccessful = Enum.TryParse(type, true, out AdjustmentValueType enumValue);
        if (!parseSuccessful)
            throw new InvalidOperationException($"No AdjustmentValueType value for type {type} is defined.");
        return enumValue switch
        {
            AdjustmentValueType.Flat => item.ToObject<AdjustmentValueFlat>(serializer),
            AdjustmentValueType.Factor => item.ToObject<AdjustmentValueFactor>(serializer),
            AdjustmentValueType.Round => item.ToObject<AdjustmentValueRound>(serializer),
            AdjustmentValueType.Premium => item.ToObject<AdjustmentValuePremium>(serializer),
            AdjustmentValueType.PrimaryCommission => item.ToObject<AdjustmentValuePrimaryCommission>(serializer),
            AdjustmentValueType.SecondaryCommission => item.ToObject<AdjustmentValueSecondaryCommission>(serializer),
            AdjustmentValueType.Discount => item.ToObject<AdjustmentValueDiscount>(serializer),
            AdjustmentValueType.Percentage => item.ToObject<AdjustmentValuePercentage>(serializer),
            _ => throw new InvalidCastException($"No AdjustmentValue class for type {type} is defined.")
        }; ;
    }

    public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer) => throw new NotImplementedException();
}

public class BaseEffectConverter : JsonConverter
{
    public override bool CanConvert(Type objectType) => objectType == typeof(PricingEffect);

    public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
    {
        if (reader.TokenType != JsonToken.StartObject)
            return null;
        var item = JObject.Load(reader);

        string type = item["type"].Value<string>();
        bool parseSuccessful = Enum.TryParse(type, true, out PricingEffectType enumValue);
        return !parseSuccessful
            ? throw new InvalidOperationException($"No PricingEffectType value for type {type} is defined.")
            : enumValue switch
            {
                PricingEffectType.Loading => item.ToObject<LoadingEffect>(serializer),
                PricingEffectType.Factor => item.ToObject<FactorEffect>(serializer),
                PricingEffectType.Discount => item.ToObject<DiscountEffect>(serializer),
                PricingEffectType.Fee => item.ToObject<FeeEffect>(serializer),
                PricingEffectType.Round => item.ToObject<RoundEffect>(serializer),
                PricingEffectType.Premium => item.ToObject<PremiumEffect>(serializer),
                PricingEffectType.PricingFactors => item.ToObject<PricingFactorsEffect>(serializer),
                _ => throw new InvalidCastException($"No BaseEffect class for type {type} is defined.")
            };
    }

    public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer) => throw new NotImplementedException();
}

public class IssueConverter : JsonConverter
{
    public override bool CanConvert(Type objectType) => objectType == typeof(Issue);

    public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
    {
        if (reader.TokenType != JsonToken.StartObject)
            return null;
        var item = JObject.Load(reader);

        string code = item["code"].Value<string>();
        bool parseSuccessful = Enum.TryParse(code, true, out IssueCode enumValue);
        return !parseSuccessful
            ? throw new InvalidOperationException($"No IssueCode value for code {code} is defined.")
            : enumValue switch
            {
                IssueCode.Invalid_type => item.ToObject<InvalidTypeIssue>(serializer),
                IssueCode.Invalid_literal => item.ToObject<InvalidLiteralIssue>(serializer),
                IssueCode.Custom => item.ToObject<CustomIssue>(serializer),
                IssueCode.Invalid_union => item.ToObject<Issue>(serializer),
                IssueCode.Invalid_union_discriminator => item.ToObject<InvalidUnionDiscriminatorIssue>(serializer),
                IssueCode.Invalid_enum_value => item.ToObject<InvalidEnumValueIssue>(serializer),
                IssueCode.Unrecognized_keys => item.ToObject<UnrecognizedKeysIssue>(serializer),
                IssueCode.Invalid_arguments => item.ToObject<Issue>(serializer),
                IssueCode.Invalid_return_type => item.ToObject<Issue>(serializer),
                IssueCode.Invalid_date => item.ToObject<Issue>(serializer),
                IssueCode.Invalid_string => item.ToObject<InvalidStringIssue>(serializer),
                IssueCode.Too_big => item.ToObject<TooBigIssue>(serializer),
                IssueCode.Too_small => item.ToObject<TooSmallIssue>(serializer),
                IssueCode.Invalid_intersection_types => item.ToObject<Issue>(serializer),
                IssueCode.Not_multiple_of => item.ToObject<NotMultipleOfIssue>(serializer),
                IssueCode.Not_finite => item.ToObject<Issue>(serializer),

                _ => throw new InvalidCastException($"No Issue class for code {code} is defined.")
            };
    }

    public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer) => throw new NotImplementedException();
}

public class ScriptErrorConverter : JsonConverter
{
    public override bool CanConvert(Type objectType) => objectType == typeof(ScriptEvaluationError);

    public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
    {
        if (reader.TokenType != JsonToken.StartObject)
            return null;
        var item = JObject.Load(reader);

        string type = item["type"].Value<string>();
        bool parseSuccessful = Enum.TryParse(type, true, out ScriptEvaluationErrorType enumValue);
        return !parseSuccessful
            ? throw new InvalidOperationException($"No ScriptEvaluationError value for type {type} is defined.")
            : enumValue switch
            {
                ScriptEvaluationErrorType.SCRIPT_ERROR => item.ToObject<ScriptExecutionError>(serializer),
                ScriptEvaluationErrorType.INPUT_DATA_VALIDATION_ERROR => item.ToObject<InputDataValidationError>(serializer),

                _ => throw new InvalidCastException($"No ScriptEvaluationError class for type {type} is defined.")
            };
    }

    public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer) => throw new NotImplementedException();
}