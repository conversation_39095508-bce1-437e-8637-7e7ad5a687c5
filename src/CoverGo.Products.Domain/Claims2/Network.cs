﻿using System;
using System.Collections.Generic;

namespace CoverGo.Products.Domain.Claims2;

public class NetworksResult
{
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalElements { get; set; }
    public int TotalPages { get; set; }
    public List<Network> Values { get; set; }
}

public class Network
{
    public Guid Uuid { get; set; }
    public string Id { get; set; }
    public string Name { get; set; }
    public NetworkStatus Status { get; set; }
}

public enum NetworkStatus
{
    PENDING_APPROVAL,
    SUBMITED_FOR_APPROVAL,
    APPROVED,
    REJECTED
}