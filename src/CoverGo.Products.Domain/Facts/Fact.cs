﻿using System.Collections.Generic;
using Newtonsoft.Json.Linq;

namespace CoverGo.Products.Domain.Facts
{
    public class Fact
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public JToken Value { get; set; }
    }

    public class AddFactCommand
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public JToken Value { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdateFactCommand
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public bool IsTypeChanged { get; set; }
        public JToken Value { get; set; }
        public bool IsValueChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class FactCommandBatch
    {
        public List<AddFactCommand> AddFactCommands { get; set; }
        public List<UpdateFactCommand> UpdateFactCommands { get; set; }
    }
}
