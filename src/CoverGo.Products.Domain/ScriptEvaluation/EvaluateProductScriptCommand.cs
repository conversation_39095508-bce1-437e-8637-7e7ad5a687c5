using System;
using System.Collections.Generic;
using CoverGo.Scripts.Client;
using ProductId = CoverGo.Products.Domain.Products.ProductId;

namespace CoverGo.Products.Domain.ScriptEvaluation;

public class EvaluateProductScriptCommand
{
    public ProductId ProductId { get; set; }
    public ScriptTypeEnum ScriptType { get; set; }
    public string DataInput { get; set; }
    public string? DistributorID { get; set; }
    public List<string>? CampaignCodes { get; set; }
    public DateTimeOffset? StartDate { get; set; }
    public bool IsRenewal { get; set; }
}

public class EvaluateStandardPricingCommand
{
    public ProductId ProductId { get; set; }
    public ScriptTypeEnum ScriptType { get; set; }
    public string DataInput { get; set; }
    public List<ModeProperties> Modes { get; set; }
    public string DistributorID { get; set; }
    public string[]? AgentIds { get; set; }
    public string[]? CampaignCodes { get; set; }
    public DateTimeOffset? StartDate { get; set; }
    public bool IsRenewal { get; set; }
    public UwLoadingInput UwLoadings { get; set; }
    public TaxInput[]? Taxes { get; set; }
    public PricingFactorsInput? PricingFactors { get; set; }
}

public class EvaluateStandardUnderwritingCommand
{
    public ProductId ProductId { get; set; }
    public ScriptTypeEnum ScriptType { get; set; }
    public string DataInput { get; set; }
    public List<InsuredUnderwritingInput> InsuredUnderwritingInputs { get; set; }
    public UnderwritingModes Modes { get; set; }
}
