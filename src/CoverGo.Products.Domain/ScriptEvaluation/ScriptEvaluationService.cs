#nullable enable

using CoverGo.ChannelManagement.Client;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.DiscountCodes;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Scripts;
using CoverGo.Reference.Client;
using CoverGo.Scripts.Client;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

using DiscountCode = CoverGo.Scripts.Client.DiscountCode;
using System.Text.Json;
using IdentityModel.Client;

namespace CoverGo.Products.Domain.ScriptEvaluation;

public class ScriptEvaluationService
{
    public ScriptEvaluationService(
        ScriptService scriptService,
        ProductService productService,
        IScriptAdapter scriptAdapter,
        IDiscountCodeService discountCodeService,
        IReferenceGraphQlClient referenceGraphQlClient,
        IChannelManagementAdapter channelManagementAdapter,
        ILogger<ScriptEvaluationService> logger)
    {
        _scriptService = scriptService;
        _productService = productService;
        _scriptAdapter = scriptAdapter;
        _discountCodeService = discountCodeService;
        _referenceGraphQlClient = referenceGraphQlClient;
        _channelManagementAdapter = channelManagementAdapter;
        _logger = logger;
    }

    readonly ILogger<ScriptEvaluationService> _logger;
    readonly ScriptService _scriptService;
    readonly ProductService _productService;
    readonly IScriptAdapter _scriptAdapter;
    readonly IDiscountCodeService _discountCodeService;
    readonly IReferenceGraphQlClient _referenceGraphQlClient;
    readonly IChannelManagementAdapter _channelManagementAdapter;

    public Task<ScriptResult> Evaluate(string tenantId, EvaluateScriptCommand command, CancellationToken cancellationToken) =>
        _scriptAdapter.Evaluate(tenantId, command, cancellationToken);
    public async Task<ScriptResult> Evaluate(string tenantId, string? clientId, EvaluateProductScriptCommand command, CancellationToken cancellationToken)
    {
        Product? product = (await _productService.GetAsync(tenantId, clientId,
            new() { Id_in = [command.ProductId] },
            new() { Limit = 1 }, loadRepresentation: true, cancellationToken)).FirstOrDefault();
        if (product == null) return Failure("Product with this id does not exist");

        Script? script = (await _scriptService.GetAsync(
            tenantId,
            new ScriptWhere
            {
                And = [
                    new ScriptWhere {Id_in = product.ScriptIds.ToList()},
                    new ScriptWhere {Type = command.ScriptType}
                ]
            }, cancellationToken)).FirstOrDefault();
        if (script == null) return Failure("Product script of this type does not exist");

        var evaluateScriptCommand = new EvaluateScriptCommand
        {
            DataInput = command.DataInput,
            Representation = product.Representation,
            Script = script,
            Fields = product.Fields,
            RatingFactorsTable = product.RatingFactorsTable,
            Segments = product.Segments
        };

        #region Pricing
        if (script.Type == ScriptTypeEnum.Pricing || script.Type == ScriptTypeEnum.Pricing_standard)
        {
            List<Task> tasks = [((Func<Task>)(async () => evaluateScriptCommand.DiscountCodes = await GetActiveDiscountCodesOfProduct(tenantId, command.ProductId, cancellationToken)))()];
            if (!string.IsNullOrWhiteSpace(command.DistributorID))
            {
                var (startDate, endDate) = ParsePolicyDates(command.DataInput);
                if (command.StartDate.HasValue) startDate = command.StartDate;
                _logger.LogInformation("startDate: {startDate}, endDate: {endDate}", startDate, endDate);

                if (startDate.HasValue)
                {
                    tasks.Add(((Func<Task>)(async () =>
                    {
                        try
                        {
                            evaluateScriptCommand.Commissions = await FetchCommissions(command.ProductId, command.DistributorID, startDate.Value, command.IsRenewal, cancellationToken);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error while fetching commissions for product {productId} and distributor {distributorId}", command.ProductId, command.DistributorID);
                        }
                    }))());
                }

                var campaignCodes = command.CampaignCodes?.Where(c => !string.IsNullOrEmpty(c)).ToList() ?? [];
                if (campaignCodes.Count > 0 && startDate.HasValue && endDate.HasValue)
                {
                    tasks.Add(((Func<Task>)(async () =>
                    {
                        try
                        {
                            evaluateScriptCommand.Campaigns = await FetchCampaigns(command.ProductId, command.DistributorID, campaignCodes, startDate.Value, endDate.Value, cancellationToken);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error while fetching campaign for product {productId} and distributor {distributorId}", command.ProductId, command.DistributorID);
                        }
                    }))());
                }
            }

            tasks.Add(((Func<Task>)(async () => evaluateScriptCommand.Vat = await FetchVat(product, cancellationToken)))());

            await Task.WhenAll(tasks);
        }
        #endregion

        // Temporary logging for Shikhar. To be removed later.
        if (tenantId == "coverHealth_dev")
        {
            _logger.LogInformation("{scriptType} script input: {input}", script.Type, evaluateScriptCommand.DataInput);
        }

        var result = await Evaluate(tenantId, evaluateScriptCommand, cancellationToken);

        // Temporary logging for Shikhar. To be removed later.
        if (tenantId == "coverHealth_dev")
        {
            _logger.LogInformation("{scriptType} script output: {output}", script.Type, result?.Value);
        }

        // For debugging, will remove later
        if (tenantId == "coverHealth_dev")
        {
            _logger.LogInformation("{scriptType} script ratingFactorsTable: {ratingFactorsTable}", script.Type, product.RatingFactorsTable);
        }

        return result;
    }

    public async Task<PricingResult> EvaluateStandardPricing(string tenantId, string clientId, EvaluateStandardPricingCommand command, CancellationToken cancellationToken)
    {
        Product? product = (await _productService.GetAsync(
            tenantId,
            clientId,
            new ProductWhere { Id_in = [command.ProductId] },
            new QueryParameters() { Limit = 1 }, loadRepresentation: true, cancellationToken)).FirstOrDefault();
        if (product == null)
            return new PricingResult { Errors = new InputDataValidationError { Message = $"Product with this id `{command.ProductId}` does not exist." } };

        Script? script = (await _scriptService.GetAsync(
            tenantId,
            new ScriptWhere
            {
                And = [
                    new ScriptWhere {
                        Id_in = product.ScriptIds.ToList()
                    },
                    new ScriptWhere {
                        Type = command.ScriptType
                    }
                ]
            }, cancellationToken)).FirstOrDefault();
        if (script == null)
            return new PricingResult { Errors = new InputDataValidationError { Message = $"Product script of type `{command.ScriptType}` does not exist for product `{command.ProductId}`." } };

        // For each mode input, check if there is a modalFactorProcessingRule from reference data
        command.Modes = await ProcessModeProperties(command.Modes, cancellationToken);

        var (startDate, endDate) = ParsePolicyDates(command.DataInput);
        if (command.StartDate.HasValue) startDate = command.StartDate.Value;

        List<Commission> commissions = [];
        if (startDate.HasValue && !string.IsNullOrEmpty(command.DistributorID) && (command.AgentIds is null || command.AgentIds.Length > 0))
            try { commissions = await FetchCommissions(command.ProductId, command.DistributorID, startDate.Value, command.IsRenewal, cancellationToken); }
            catch (Exception ex) { _logger.LogError(ex, "Error while fetching commissions for product {productId} and distributor {distributorId}", command.ProductId, command.DistributorID); }
        if (command.AgentIds is { } agentIds && agentIds.Length < 2) foreach (var commission in commissions) commission.Rules = [commission.Rules[0]];

        List<Campaign> campaigns = [];
        if (!string.IsNullOrEmpty(command.DistributorID) && command.CampaignCodes != null && 0 < command.CampaignCodes.Length && startDate.HasValue && endDate.HasValue)
            try { campaigns = await FetchCampaigns(command.ProductId, command.DistributorID, command.CampaignCodes.ToList(), startDate.Value, endDate.Value, cancellationToken); }
            catch (Exception ex) { _logger.LogError(ex, "Error while fetching campaigns for product {productId} and distributor {distributorId} with {campaignCodes}", command.ProductId, command.DistributorID, command.CampaignCodes); }

        string vat = command.Taxes is null or [] ? await FetchVat(product, cancellationToken) ?? string.Empty : string.Empty;

        StandardPricingCommand standardPricingCommand = new()
        {
            Script = script,
            Representation = product.Representation,
            Fields = product.Fields,
            RatingFactorsTable = product.RatingFactorsTable,
            Segments = product.Segments,
            DataInput = command.DataInput,
            UwLoadings = command.UwLoadings,
            Commissions = commissions,
            Campaigns = campaigns,
            Vat = vat,
            Taxes = command.Taxes?.ToList() ?? [],
            Modes = command.Modes,
            PricingFactors = command.PricingFactors,
        };

        return await _scriptAdapter.EvaluateStandardPricing(tenantId, standardPricingCommand, cancellationToken);
    }
    async Task<List<Commission>> FetchCommissions(Products.ProductId productId, string distributorID, DateTimeOffset date, bool isRenewal, CancellationToken cancellationToken)
    {
        IReadOnlyList<ICommissionCandidates_CommissionCandidates> cm = await _channelManagementAdapter.GetCommissionCandidates(
            new CommissionCandidatesInput
            {
                ProductID = new ProductIdInput
                {
                    Plan = productId.Plan,
                    Version = productId.Version,
                    Type = productId.Type
                },
                DistributorID = distributorID,
                Date = date,
            }, cancellationToken);

        return cm.Select(c => new Commission
        {
            CommissionID = c.CommissionID,
            Title = c.Title,
            Description = c.Description,
            Rules = isRenewal
            ? c.RenewalRules.Select(r =>
                {
                    Enum.TryParse(r.AmountUnit, true, out CommissionRuleAmountUnit amountUnit);
                    return new CommissionRule
                    {
                        Amount = r.Amount,
                        AmountUnit = amountUnit,
                        CommissionRuleID = r.CommissionRuleID,
                        PercentageUnit = r.PercentageUnit,
                    };
                }).ToList()
            : c.Rules.Select(r =>
                {
                    Enum.TryParse(r.AmountUnit, true, out CommissionRuleAmountUnit amountUnit);
                    return new CommissionRule
                    {
                        Amount = r.Amount,
                        AmountUnit = amountUnit,
                        CommissionRuleID = r.CommissionRuleID,
                        PercentageUnit = r.PercentageUnit,
                    };
                }).ToList(),
        }).ToList();
    }

    private async Task<List<Campaign>> FetchCampaigns(Products.ProductId productId, string distributorID, List<string> campaignCodes, DateTimeOffset startDate, DateTimeOffset endDate, CancellationToken cancellationToken)
    {
        IReadOnlyList<ICampaigns_Campaigns_Items?>? campaigns = await _channelManagementAdapter.GetCampaigns(new CampaignFilterInput
        {
            And = BuildCurrentlyEffectiveForDistributorCampaignFilter(productId, distributorID, campaignCodes, startDate, endDate)
        }, take: campaignCodes.Count, cancellationToken);

        return campaigns?.Where(c => c != null).Select(c => new Campaign
        {
            CampaignCode = c.CampaignCode,
            CampaignID = c.CampaignID,
            Discount = c.Discount,
            EffectiveFrom = c.EffectiveFrom.DateTime,
            EffectiveTo = c.EffectiveTo.DateTime,
            PremiumCalculationType = Enum.Parse<CampaignPremiumCalculationType>(c.PremiumCalculation.ToString())
        }).ToList() ?? [];
    }
    private static List<CampaignFilterInput> BuildCurrentlyEffectiveForDistributorCampaignFilter(Products.ProductId productId, string distributorID, List<string> campaignCodes, DateTimeOffset startDate, DateTimeOffset endDate) => [
        new CampaignFilterInput
        {
            ProductID = new ProductIdFilterInput
            {
                Type = new StringOperationFilterInput{ Eq = productId.Type }
            }
        },
        new CampaignFilterInput
        {
            ProductID = new ProductIdFilterInput
            {
                Plan = new StringOperationFilterInput{ Eq = productId.Plan }
            }
        },
        new CampaignFilterInput
        {
            Or = [
                new CampaignFilterInput {
                    ProductID = new ProductIdFilterInput
                    {
                        Version = new StringOperationFilterInput { Eq = productId.Version }
                    }
                },
                new CampaignFilterInput {
                    ProductID = new ProductIdFilterInput
                    {
                        Version = new StringOperationFilterInput { Eq = "1.0" }
                    }
                }
            ]
        },
        new CampaignFilterInput
        {
            Or = [
                new CampaignFilterInput {
                    DistributorIDs = new ListComparableGuidOperationFilterInput
                    {
                        Some = new ComparableGuidOperationFilterInput {
                            Eq = distributorID
                        }
                    }
                },
                new CampaignFilterInput {
                    IsAvailableForAllDistributors = new BooleanOperationFilterInput {
                        Eq = true
                    }
                }
            ]
        },
        new CampaignFilterInput { EffectiveFrom = new ComparableDateTimeOperationFilterInput { Lte = endDate } },
        new CampaignFilterInput { EffectiveTo = new ComparableDateTimeOperationFilterInput { Gte = startDate } },
        new CampaignFilterInput
        {
            CampaignCode = new StringOperationFilterInput
            {
                In = campaignCodes
            }
        }
    ];

    async Task<string?> FetchVat(Product product, CancellationToken cancellationToken)
    {
        var referenceMnemonic = string.IsNullOrEmpty(product.TaxConfiguration.ReferenceMnemonic) ? TaxReferenceMnemonic : product.TaxConfiguration.ReferenceMnemonic;
        var mnemonicCode = product.TaxConfiguration.MnemonicCode;
        if (referenceMnemonic == TaxReferenceMnemonic)
            if (!ProductTypeToTaxMnemonicCode.TryGetValue(product.Id.Type, out mnemonicCode)) return null;
        IReferenceTypes_ReferenceTypes_Items? rt;
        try
        {
            var res = await _referenceGraphQlClient.ReferenceTypes.ExecuteAsync(new() { ReferenceMnemonics = [referenceMnemonic], IncludeDeactivated = false }, 1, 0, null, cancellationToken);
            rt = res.Data.ReferenceTypes.Items.FirstOrDefault();
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error while fetching tax reference type {taxReferenceMnemonic}", TaxReferenceMnemonic);
            return null;
        }
        if (rt == null) return null;
        var rd = rt.ReferenceData?
            .FirstOrDefault(rd => rd.MnemonicCode == mnemonicCode && rd.ValidTo >= DateTimeOffset.UtcNow && (!rd.ValidFrom.HasValue || rd.ValidFrom.Value <= DateTimeOffset.UtcNow));

        return rd?.ProcessingRule;
    }
    const string TaxReferenceMnemonic = "X-FIN-TAXRATE";
    static readonly Dictionary<string, string> ProductTypeToTaxMnemonicCode = new() { { "gm", "GRP-HEALTH" }, { "im", "IND-HEALTH" }, { "pet", "PET-INS" } };

    async Task<string?> FetchModeProcessingRule(string mnemonicCode, CancellationToken cancellationToken)
    {
        IReferenceTypes_ReferenceTypes_Items? rt;
        try
        {
            var res = await _referenceGraphQlClient.ReferenceTypes.ExecuteAsync(new() { ReferenceMnemonics = [ModalFactorReferenceMnemonic], IncludeDeactivated = false }, 1, 0, null, cancellationToken);
            rt = res.Data.ReferenceTypes.Items.FirstOrDefault();
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error while fetching tax reference type {modalFactorReferenceMnemonic}", ModalFactorReferenceMnemonic);
            return null;
        }
        if (rt == null) return null;
        var rd = rt.ReferenceData?.FirstOrDefault(rd => rd.MnemonicCode == mnemonicCode);
        return rd?.ProcessingRule;
    }
    const string ModalFactorReferenceMnemonic = "X-PRD-MODFACT";

    public async Task<UnderwritingResult> EvaluateStandardUnderwriting(string tenantId, string clientId, EvaluateStandardUnderwritingCommand command, CancellationToken cancellationToken)
    {
        Product? product = (await _productService.GetAsync(
            tenantId,
            clientId,
            new ProductWhere { Id_in = [command.ProductId] },
            new QueryParameters() { Limit = 1 }, loadRepresentation: true, cancellationToken)).FirstOrDefault();

        if (product == null)
            throw new InvalidOperationException("Product with this id does not exist");

        Script? script = product.ScriptIds.Count < 1 ? null : (await _scriptService.GetAsync(
            tenantId,
            new ScriptWhere
            {
                And = [
                    new ScriptWhere {
                        Id_in = [.. product.ScriptIds]
                    },
                    new ScriptWhere {
                        Type = command.ScriptType
                    }
                ]
            }, cancellationToken)).FirstOrDefault();

        if (script == null) return DefaultUnderwritingResult(command);

        var standardUnderwritingCommand = new StandardUnderwritingCommand
        {
            Script = script,
            DataInput = command.DataInput,
            Representation = product.Representation,
            InsuredUnderwritingInputs = command.InsuredUnderwritingInputs,
            Modes = command.Modes,
        };

        return await _scriptAdapter.EvaluateStandardUnderwriting(tenantId, standardUnderwritingCommand, cancellationToken);
    }
    private static UnderwritingResult DefaultUnderwritingResult(EvaluateStandardUnderwritingCommand command)
    {
        List<InsuredUnderwritingResult> rInsureds = [];
        if (command.InsuredUnderwritingInputs != null)
            rInsureds = [.. command.InsuredUnderwritingInputs.Select(i => new InsuredUnderwritingResult
                    {
                        Id = i.Id,
                        Decision = UnderwritingResultDecision.Pending,
                        BenefitsUnderwriting = [],
                        Exclusions = [],
                        Loadings = [],
                        PreExistingConditions = [],
                        ReferToUnderwriter = false,
                        Remarks = [],
                        Type = UnderwritingType.Auto,
                    })];
        else
            try
            {
                var jDataInput = !string.IsNullOrEmpty(command.DataInput) ? JsonDocument.Parse(command.DataInput) : JsonDocument.Parse("{}");
                if (jDataInput.RootElement.TryGetProperty("insureds", out var insuredsElement))
                {
                    rInsureds = [.. insuredsElement.EnumerateArray().Select(insureds => new InsuredUnderwritingResult
                    {
                        Id = insureds.TryGetString("id"),
                        Decision = UnderwritingResultDecision.Pending,
                        BenefitsUnderwriting = [],
                        Exclusions = [],
                        Loadings = [],
                        PreExistingConditions = [],
                        ReferToUnderwriter = false,
                        Remarks = [],
                        Type = UnderwritingType.Auto,
                    })];
                }
            }
            catch { }

        return new UnderwritingResult
        {
            Data = new()
            {
                Insureds = rInsureds,
                Policy = new()
                {
                    Id = "policy",
                    Decision = UnderwritingResultDecision.Pending,
                    BenefitsUnderwriting = [],
                    Exclusions = [],
                    Loadings = [],
                    PreExistingConditions = [],
                    ReferToUnderwriter = false,
                    Remarks = [],
                    Type = UnderwritingType.Auto,
                }
            }
        };
    }

    private async Task<List<DiscountCode>> GetActiveDiscountCodesOfProduct(string tenantId, Products.ProductId productId, CancellationToken cancellationToken)
    {
        DateTime now = DateTime.UtcNow;
        IReadOnlyCollection<DiscountCodes.DiscountCode> discountCodes = await _discountCodeService.QueryAsync(tenantId,
            new QueryArguments<Filter<DiscountCodeFilter>>()
            {
                Where = new Filter<DiscountCodeFilter>()
                {
                    And = [
                        new() { Where = new DiscountCodeFilter() { ValidFrom_lte = now } },
                        new() { Or = [
                            new(){Where = new DiscountCodeFilter() { ValidTo_exists = false }},
                            new(){Where = new DiscountCodeFilter() { ValidTo_gte = now }}
                        ]},
                        new()
                        {
                            Where = new DiscountCodeFilter()
                            {
                                ProductIds_contains = new ProductIdWhere()
                                {
                                    Type = productId.Type
                                }
                            }
                        },
                        new()
                        {
                            Where = new DiscountCodeFilter()
                            {
                                ProductIds_contains = new ProductIdWhere()
                                {
                                    Plan = productId.Plan
                                }
                            }
                        }
                    ]
                }
            }, cancellationToken);

        return discountCodes.Select(x => new DiscountCode
        {
            Id = x.Id,
            Name = x.Name,
            Description = x.Description,
            Value = x.Value,
            ValidFrom = x.ValidFrom,
            ValidTo = x.ValidTo,
            ProductIds = x.ProductIds?.Select(pid => new CoverGo.Scripts.Client.ProductId
            {
                Plan = pid.Plan,
                Version = pid.Version,
                Type = pid.Type
            }).ToList(),
            ProductTypeId = x.ProductTypeId
        }).ToList();
    }

    public async Task<List<ModeProperties>> ProcessModeProperties(List<ModeProperties> modeProperties, CancellationToken cancellationToken)
    {
        if (modeProperties == null) return modeProperties;

        await Task.WhenAll(modeProperties.Select(async mode =>
        {
            var settings = new JsonSerializerSettings
            {
                Converters = [new StringEnumConverter()]
            };
            string billingFrequencyMnemonic = JsonConvert
                                .SerializeObject(mode.Billing.BillingFrequency, settings)
                                .Trim('"')
                                .ToLower();
            string modalFactorProcessingRule = await FetchModeProcessingRule(billingFrequencyMnemonic, cancellationToken);
            if (modalFactorProcessingRule == null) return;

            var modalOverride = JsonConvert.DeserializeObject<Billing>(modalFactorProcessingRule);
            if (modalOverride == null) return;

            mode.Billing = new Billing
            {
                BillingFrequency = mode.Billing.BillingFrequency,
                BillingMode = mode.Billing.BillingMode,
                ModalFee = mode?.Billing?.ModalFee ?? modalOverride?.ModalFee,
                ModalFactor = mode?.Billing?.ModalFactor ?? modalOverride?.ModalFactor,
                ModalRounding = modalOverride?.ModalRounding,
                ModalPrecision = modalOverride?.ModalPrecision
            };
        }));

        return modeProperties;
    }

    ScriptResult Failure(string message) => new() { Status = "failure", Value = "", Errors = [message] };
    (DateTimeOffset? startDate, DateTimeOffset? endDate) ParsePolicyDates(string dataInput)
    {
        DateTimeOffset? startDate = null;
        DateTimeOffset? endDate = null;
        try
        {
            var policy = System.Text.Json.JsonDocument.Parse(dataInput).RootElement.GetProperty("policy");
            try { startDate = policy.GetProperty("startDate").GetDateTimeOffset(); }
            catch (Exception) { _logger.LogError("Error while parsing DataInput for policy.startDate", dataInput); }

            try { endDate = policy.GetProperty("endDate").GetDateTimeOffset(); }
            catch (Exception) { _logger.LogError("Error while parsing DataInput for policy.endDate", dataInput); }
        }
        catch (Exception)
        {
            _logger.LogError("Error while parsing DataInput", dataInput);
        }
        return (startDate, endDate);
    }
}
