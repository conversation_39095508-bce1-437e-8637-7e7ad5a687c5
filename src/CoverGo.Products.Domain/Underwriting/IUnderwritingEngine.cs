﻿using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Products;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Domain.Underwriting
{
    public interface IUnderwritingEngine
    {
        string ProviderId { get; }
        Task<IEnumerable<(ProductId, Result)>> EvaluateUnderwritingsAsync(string tenantId, IEnumerable<ProductId> productIds, JToken factors, CancellationToken cancellationToken);
        Task<IEnumerable<(ProductId, string)>> GetUnderwritingJsonSchema(string tenantId, IEnumerable<ProductId> productIds, JToken factors, CancellationToken cancellationToken);
    }
}
