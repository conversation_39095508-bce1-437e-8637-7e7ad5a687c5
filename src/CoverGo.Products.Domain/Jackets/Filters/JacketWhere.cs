using System.Collections.Generic;
using CoverGo.DomainUtils;

namespace CoverGo.Products.Domain.Jackets.Filters
{
    public class JacketWhere : Where
    {
        public List<JacketWhere> Or { get; set; }

        public List<JacketWhere> And { get; set; }

        public string Id { get; set; }

        public IEnumerable<string> Id_in { get; set; }


        public string Title { get; set; }

        public string Title_contains { get; set; }

        public string Status { get; set; }

        public string Status_contains { get; set; }

        public IEnumerable<string> Status_in { get; set; }
    }
}