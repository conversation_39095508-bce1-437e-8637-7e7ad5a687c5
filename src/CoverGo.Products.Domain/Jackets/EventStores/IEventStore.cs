using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.DomainUtils;

namespace CoverGo.Products.Domain.Jackets.EventStores
{
    public interface IEventStore<TEvent, in TEventType>
    {
        Task<Result> AddEventAsync(string tenantId, TEvent @event, CancellationToken cancellationToken);
        Task<IEnumerable<TEvent>> GetEventsAsync(string tenantId, IEnumerable<TEventType> types, IEnumerable<string> ids, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    }
}