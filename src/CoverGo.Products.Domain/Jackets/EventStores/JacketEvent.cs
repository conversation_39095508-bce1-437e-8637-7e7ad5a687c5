using System;
using Newtonsoft.Json.Linq;

namespace CoverGo.Products.Domain.Jackets.EventStores
{
    public class JacketEvent
    {
        public JacketEvent()
        {

        }

        public JacketEvent(string objectId, JacketEventType type, DateTime timestamp)
        {
            JacketId = objectId;
            Type = type;
            Timestamp = timestamp;
        }

        public string Id { get; set; }

        public string JacketId { get; set; }

        public JacketEventType Type { get; set; }

        public JToken Values { get; set; }

        public DateTime Timestamp { get; set; }
    }
}