using CoverGo.Templates.Client;
using Newtonsoft.Json.Linq;

namespace CoverGo.Products.Domain.Jackets.Entities
{
    public class Clause
    {
        public string Id { get; set; }

        public int Order { get; set; }

        public string TemplateId { get; set; }

        public Template Template { get; set; }

        public RenderParameters RenderParameters { get; set; }

        public string HtmlOverride { get; set; }
    }
}