using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Jackets.Commands;
using CoverGo.Products.Domain.Jackets.Entities;
using CoverGo.Products.Domain.Jackets.Filters;

namespace CoverGo.Products.Domain.Jackets.Services
{
    public interface IJacketService
    {
        Task<long> GetTotalCountAsync(string tenantId, JacketWhere @where, CancellationToken cancellationToken);

        Task<IEnumerable<Jacket>> GetAsync(string tenantId, Filters.QueryArguments<JacketWhere> queryArguments, CancellationToken cancellationToken);

        Task<IEnumerable<string>> GetIdsAsync(string tenantId, JacketWhere where, OrderBy orderBy, int? skip, int? first, DateTime? asOf, CancellationToken cancellationToken);

        Task<Result<string>> CreateAsync(string tenantId, CreateJacketCommand command, CancellationToken cancellationToken);

        Task<Result> UpdateAsync(string tenantId, string jacketId, UpdateJacketCommand command, CancellationToken cancellationToken);
        Task<Result> DeleteAsync(string tenantId, string jacketId, DeleteCommand command, CancellationToken cancellationToken);
    }
}