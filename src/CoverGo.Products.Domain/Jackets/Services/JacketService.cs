using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Jackets.Commands;
using CoverGo.Products.Domain.Jackets.Entities;
using CoverGo.Products.Domain.Jackets.EventStores;
using CoverGo.Products.Domain.Jackets.Filters;
using CoverGo.Products.Domain.Jackets.Repositories;

using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;

namespace CoverGo.Products.Domain.Jackets.Services
{
    public class JacketService : IJacketService
    {
        private readonly JsonSerializer s_jsonSerializer;
        private readonly IJacketRepository _jacketRepository;
        private readonly IEventStore<JacketEvent, JacketEventType> _jacketEventStore;

        public JacketService(
            IJacketRepository jacketRepository,
            IEventStore<JacketEvent, JacketEventType> jacketEventStore)
        {
            var settings = new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
                TypeNameHandling = TypeNameHandling.Auto,
                NullValueHandling = NullValueHandling.Ignore,
            };
            settings.Converters.Add(new StringEnumConverter());

            s_jsonSerializer = JsonSerializer.Create(settings);
            _jacketRepository = jacketRepository;
            _jacketEventStore = jacketEventStore;
        }

        public async Task<long> GetTotalCountAsync(string tenantId, JacketWhere @where, CancellationToken cancellationToken)
        {
            return await _jacketRepository.GetTotalCountAsync(tenantId, where, cancellationToken);
        }

        public async Task<IEnumerable<string>> GetIdsAsync(string tenantId, JacketWhere where, OrderBy orderBy, int? skip, int? first, DateTime? asOf, CancellationToken cancellationToken)
        {
            return await _jacketRepository.GetIdsAsync(tenantId, where, orderBy, skip, first, asOf, cancellationToken);
        }

        public async Task<IEnumerable<Jacket>> GetAsync(string tenantId, Filters.QueryArguments<JacketWhere> queryArguments, CancellationToken cancellationToken)
        {
            var jackets = await _jacketRepository.GetAsync(tenantId, queryArguments.Where, queryArguments.OrderBy, queryArguments.Skip, queryArguments.First, cancellationToken);

            if (queryArguments.AsOf == null)
            {
                return jackets;
            }

            var events = await _jacketEventStore.GetEventsAsync(tenantId, null, jackets.Select(p => p.Id), null, queryArguments.AsOf, cancellationToken);

            return HandleJackets(events).jackets;

        }

        public async Task<Result<string>> CreateAsync(string tenantId, CreateJacketCommand command, CancellationToken cancellationToken)
        {
            var objectId = Guid.NewGuid().ToString();
            Result result = await AddEventAndReplayAsync(tenantId, new JacketEvent(objectId, JacketEventType.create, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, s_jsonSerializer)
            }, cancellationToken);

            return new Result<string> { Status = result.Status, Errors = result.Errors, Value = objectId };
        }

        public async Task<Result> UpdateAsync(string tenantId, string jacketId, UpdateJacketCommand command, CancellationToken cancellationToken)
        {
            var ev = new JacketEvent(jacketId, JacketEventType.update, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, s_jsonSerializer)
            };

            Result result = await AddEventAndReplayAsync(tenantId, ev, cancellationToken);

            return result;
        }
        
        public async Task<Result> DeleteAsync(string tenantId, string jacketId, DeleteCommand command, CancellationToken cancellationToken)
        {
            var ev = new JacketEvent(jacketId, JacketEventType.delete, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, s_jsonSerializer)
            };

            Result result = await AddEventAndReplayAsync(tenantId, ev, cancellationToken);

            return result;
        }

        private async Task<Result> AddEventAndReplayAsync(string tenantId, JacketEvent @event, CancellationToken cancellationToken)
        {
            Result result = await _jacketEventStore.AddEventAsync(tenantId, @event, cancellationToken);

            if (result.Status == "success")
            {
                await ReplayAsync(tenantId, new[] { @event.JacketId }, cancellationToken);
            }


            return result;
        }

        private async Task ReplayAsync(string tenantId, IEnumerable<string> ids, CancellationToken cancellationToken)
        {
            IEnumerable<JacketEvent> events = await _jacketEventStore.GetEventsAsync(tenantId, null, ids, cancellationToken: cancellationToken);
            await HandleAndUpsertAsync(tenantId, events, cancellationToken);
        }

        private async Task HandleAndUpsertAsync(string tenantId, IEnumerable<JacketEvent> events, CancellationToken cancellationToken)
        {
            var  result = HandleJackets(events);
            
            foreach (Jacket jacket in result.jackets)
            {
                await _jacketRepository.UpsertAsync(tenantId, jacket, cancellationToken);
            }
            
            foreach (string id in result.toDeleteIds)
            {
                await _jacketRepository.DeleteAsync(tenantId, id, cancellationToken);
            }
        }

        private static (IEnumerable<Jacket> jackets, IEnumerable<string> toDeleteIds) HandleJackets(IEnumerable<JacketEvent> events)
        {
            var jackets = new List<Jacket>();
            var toDeleteIds = new List<string>();
            foreach (JacketEvent @event in events)
            {
                switch (@event.Type)
                {
                    case JacketEventType.create:

                        CreateJacketCommand createJacketCommand = ToObjectOrDefault<CreateJacketCommand>(@event.Values);

                        var jacket = new Jacket
                        {
                            CreatedAt = @event.Timestamp,
                            LastModifiedAt = @event.Timestamp,
                            CreatedById = createJacketCommand.CreatedById,
                            Id = @event.JacketId,
                            LastModifiedById = createJacketCommand.CreatedById,
                            Status = createJacketCommand.Status,
                            Title = createJacketCommand.Title,
                            Clauses = createJacketCommand.Clauses,
                        };

                        jackets.Add(jacket);
                        break;

                    case JacketEventType.update:
                        Jacket jacketToUpdate = jackets.FirstOrDefault(c => c.Id == @event.JacketId);
                        if (jacketToUpdate == null) continue;

                        UpdateJacketCommand updateJacketCommand = ToObjectOrDefault<UpdateJacketCommand>(@event.Values);

                        jacketToUpdate.LastModifiedAt = @event.Timestamp;
                        jacketToUpdate.LastModifiedById = updateJacketCommand.ModifiedById;
                        if (updateJacketCommand.IsStatusChanged) jacketToUpdate.Status = updateJacketCommand.Status;
                        if (updateJacketCommand.IsTitleChanged) jacketToUpdate.Title = updateJacketCommand.Title;
                        if (updateJacketCommand.IsClausesChanged) jacketToUpdate.Clauses = updateJacketCommand.Clauses;
                        break;
                    
                    case JacketEventType.delete:
                        Jacket jacketToDelete = jackets.FirstOrDefault(c => c.Id == @event.JacketId);
                        if (jacketToDelete == null) continue;

                        toDeleteIds.Add(@event.JacketId);
                        jackets.Remove(jacketToDelete);
                        break;
                }
            }

            return (jackets, toDeleteIds);
        }

        private static T ToObjectOrDefault<T>(object obj) =>
            obj != null ? JToken.FromObject(obj).ToObject<T>() : default;
    }
}