using System.Collections.Generic;
using CoverGo.Products.Domain.Jackets.Entities;

namespace CoverGo.Products.Domain.Jackets.Commands
{
    public class UpdateJacketCommand
    {
        public bool IsTitleChanged { get; set; }

        public string Title { get; set; }

        public bool IsStatusChanged { get; set; }

        public string Status { get; set; }

        public bool IsClausesChanged { get; set; }

        public List<Clause> Clauses { get; set; }

        public string ModifiedById { get; set; }
    }
}