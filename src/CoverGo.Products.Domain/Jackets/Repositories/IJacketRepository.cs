using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Jackets.Entities;
using CoverGo.Products.Domain.Jackets.Filters;

namespace CoverGo.Products.Domain.Jackets.Repositories
{
    public interface IJacketRepository
    {
        Task<IEnumerable<Jacket>> GetAsync(string tenantId, JacketWhere where, OrderBy orderBy = null, int? skip = null, int? first = null, CancellationToken cancellationToken = default);

        Task<long> GetTotalCountAsync(string tenantId, JacketWhere where, CancellationToken cancellationToken);

        Task UpsertAsync(string tenantId, Jacket jacket, CancellationToken cancellationToken);

        Task<IEnumerable<string>> GetIdsAsync(string tenantId, JacketWhere where, OrderBy orderBy = null, int? skip = null, int? first = null, DateTime? asOf = null, CancellationToken cancellationToken = default);
        Task DeleteAsync(string tenantId, string jacketId, CancellationToken cancellationToken);
    }
}