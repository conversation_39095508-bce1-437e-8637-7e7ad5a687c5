﻿using CoverGo.DomainUtils;
using CoverGo.Products.Domain.BenefitDefinitionTypes;
using System.Collections.Generic;
using Newtonsoft.Json.Linq;
using HotChocolate;

namespace CoverGo.Products.Domain.BenefitDefinitions
{
    public class BenefitDefinition : SystemObject
    {
        public string Id { get; set; }
        public string BusinessId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        [GraphQLIgnore]
        public JToken Fields { get; set; }
        public List<string> BenefitDefinitionTypeIds { get; set; }
    }

    public class CreateBenefitDefinitionCommand
    {
        public string BusinessId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string Fields { get; set; }
        public List<string> BenefitDefinitionTypeIds { get; set; }
        public string CreatedById { get; set; }
    }

    public class UpdateBenefitDefinitionCommand
    {
        public string BenefitDefinitionId { get; set; }
        public string BusinessId { get; set; }
        public string Name { get; set; }
        public string Status { get; set; }
        public string Fields { get; set; }
        public List<string> BenefitDefinitionTypeIds { get; set; }
        public string Description { get; set; }
        public string ModifiedById { get; set; }
    }

    public class BatchBenefitDefinitionCommand
    {
        public List<CreateBenefitDefinitionCommand> CreateBenefitDefinitionCommands { get; set; } =
            new List<CreateBenefitDefinitionCommand>();

        public List<UpdateBenefitDefinitionCommand> UpdateBenefitDefinitionCommands { get; set; } =
            new List<UpdateBenefitDefinitionCommand>();
    }

    public class BenefitQueryArguments
    {
        public BenefitDefinitionWhere Where { get; set; }
        public OrderBy OrderBy { get; set; }
        public int? First { get; set; }
        public int? Skip { get; set; }
    }

    public class BenefitDefinitionWhere : Where
    {
        public IEnumerable<BenefitDefinitionWhere> Or { get; set; }
        public IEnumerable<BenefitDefinitionWhere> And { get; set; }
        public List<string> Id_in { get; set; }
        public List<string> BusinessId_in { get; set; }
        public BenefitDefinitionTypeWhere Type { get; set; }
        public List<string> TypeId_in { get; set; }
        public string BusinessId_contains { get; set; }
        public string Name_contains { get; set; }
        public string Status { get; set; }
        public FieldsWhere Fields { get; set; }
    }
}