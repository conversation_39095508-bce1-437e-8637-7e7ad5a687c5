﻿using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading.Tasks;
using CoverGo.Products.Domain.BenefitDefinitionTypes;
using System.Threading;


namespace CoverGo.Products.Domain.BenefitDefinitions
{
    public interface IBenefitDefinitionRepository
    {
        string ProviderId { get; }
        Task<IReadOnlyCollection<BenefitDefinition>> GetBenefitDefinitionAsync(string tenantId, BenefitDefinitionWhere where, OrderBy orderBy = null, int? skip = null, int? first = null, CancellationToken cancellationToken = default);
        Task<long> CountBenefitDefinitionAsync(string tenantId, BenefitDefinitionWhere where, CancellationToken cancellationToken = default);
        Task<IReadOnlyCollection<BenefitDefinitionType>> GetBenefitDefinitionTypeAsync(string tenantId, BenefitDefinitionTypeWhere where, CancellationToken cancellationToken);
        Task<Result<CreatedStatus>> CreateBenefitDefinitionAsync(string tenantId, string id, CreateBenefitDefinitionCommand command, CancellationToken cancellationToken);
        Task<Result> UpdateBenefitDefinitionAsync(string tenantId, UpdateBenefitDefinitionCommand command, CancellationToken cancellationToken);
        Task<Result> DeleteBenefitDefinitionAsync(string tenantId, string benefitDefinitionId, DeleteCommand command, CancellationToken cancellationToken);
        Task<Result> BatchBenefitDefinitionAsync(string tenantId, BatchBenefitDefinitionCommand command, CancellationToken cancellationToken);
    }
}