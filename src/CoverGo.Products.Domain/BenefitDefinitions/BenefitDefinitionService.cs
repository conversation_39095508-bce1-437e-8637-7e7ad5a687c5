using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.DomainUtils;
using CoverGo.Products.Domain.BenefitDefinitionTypes;

namespace CoverGo.Products.Domain.BenefitDefinitions
{
    public class BenefitDefinitionService(
        IBenefitDefinitionRepository benefitDefinitionRepository,
        IBenefitDefinitionTypeRepository benefitDefinitionTypeRepository)
    {
        public async Task<IEnumerable<BenefitDefinition>> GetBenefitDefinitionAsync(string tenantId, BenefitQueryArguments queryArguments, CancellationToken cancellationToken)
        {
            await TypeFilterPrepareAsync(tenantId, queryArguments, cancellationToken);
            return await benefitDefinitionRepository.GetBenefitDefinitionAsync(tenantId, queryArguments.Where, queryArguments.OrderBy, queryArguments.Skip, queryArguments.First, cancellationToken);
        }

        public async Task<long> CountBenefitDefinitionAsync(string tenantId, BenefitQueryArguments queryArguments, CancellationToken cancellationToken)
        {
            await TypeFilterPrepareAsync(tenantId, queryArguments, cancellationToken);
            return await benefitDefinitionRepository.CountBenefitDefinitionAsync(tenantId, queryArguments.Where, cancellationToken);
        }

        public async Task<Result<CreatedStatus>> CreateBenefitDefinitionAsync(string tenantId, CreateBenefitDefinitionCommand command, CancellationToken cancellationToken)
        {
            return await IsBusinessIdTaken(tenantId, command.BusinessId, cancellationToken)
                ? Result<CreatedStatus>.Failure("This BusinessId is taken already")
                : await benefitDefinitionRepository.CreateBenefitDefinitionAsync(tenantId, Guid.NewGuid().ToString(), command, cancellationToken);
        }

        public Task<Result> UpdateBenefitDefinitionAsync(string tenantId, UpdateBenefitDefinitionCommand command, CancellationToken cancellationToken)
        {
            return benefitDefinitionRepository.UpdateBenefitDefinitionAsync(tenantId, command, cancellationToken);
        }

        public Task<Result> DeleteBenefitDefinitionAsync(string tenantId, string benefitDefinitionId, DeleteCommand command, CancellationToken cancellationToken)
        {
            return benefitDefinitionRepository.DeleteBenefitDefinitionAsync(tenantId, benefitDefinitionId, command, cancellationToken);
        }

        public async Task<Result> BatchBenefitDefinitionAsync(string tenantId, BatchBenefitDefinitionCommand command, CancellationToken cancellationToken)
        {
            if (await IsBusinessIdTaken(tenantId,
                command.CreateBenefitDefinitionCommands.Select(x => x.BusinessId).ToList(), cancellationToken))
            {
                return Result.Failure("Some of BusinessIds are taken already");
            }

            return await benefitDefinitionRepository.BatchBenefitDefinitionAsync(tenantId, command, cancellationToken);
        }

        async Task<bool> IsBusinessIdTaken(string tenantId, string businessId, CancellationToken cancellationToken)
        {
            return (await benefitDefinitionRepository.GetBenefitDefinitionAsync(tenantId, new BenefitDefinitionWhere { BusinessId_in = new List<string> { businessId } }, cancellationToken: cancellationToken)).Any()
            || (await benefitDefinitionRepository.GetBenefitDefinitionTypeAsync(tenantId, new BenefitDefinitionTypeWhere { BusinessId_in = new List<string> { businessId } }, cancellationToken: cancellationToken)).Any();
        }

        async Task<bool> IsBusinessIdTaken(string tenantId, List<string> businessIds,
            CancellationToken cancellationToken)
        {
            if (!businessIds.Any())
            {
                return false;
            }

            return (await benefitDefinitionRepository.GetBenefitDefinitionAsync(tenantId, new BenefitDefinitionWhere { BusinessId_in = businessIds }, cancellationToken: cancellationToken)).Any()
                   || (await benefitDefinitionRepository.GetBenefitDefinitionTypeAsync(tenantId, new BenefitDefinitionTypeWhere { BusinessId_in = businessIds }, cancellationToken: cancellationToken)).Any()
                   || businessIds.Count != businessIds.Distinct().Count();
        }

        private Task TypeFilterPrepareAsync(string tenantId, BenefitQueryArguments queryArguments, CancellationToken cancellationToken)
        {
            async Task PrepareTypesFilterAsync(BenefitDefinitionWhere? where)
            {
                if (where?.Or?.Any() ?? false)
                {
                    foreach (BenefitDefinitionWhere w in where.Or) await PrepareTypesFilterAsync(w);
                }

                if (where?.And?.Any() ?? false)
                {
                    foreach (BenefitDefinitionWhere w in where.And) await PrepareTypesFilterAsync(w);
                }

                if (where?.Type == null) return;

                IReadOnlyCollection<BenefitDefinitionType> types = await benefitDefinitionTypeRepository.GetBenefitDefinitionTypeAsync(tenantId, where.Type, cancellationToken: cancellationToken);
                where.TypeId_in = types.Select(x => x.Id).ToList();
            }

            return PrepareTypesFilterAsync(queryArguments.Where);
        }
    }
}