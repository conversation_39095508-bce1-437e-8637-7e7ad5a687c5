﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <UserSecretsId>e74e56fb-c313-4174-8281-76795c9f8f5c</UserSecretsId>
    <SourceRevisionId>build$([System.DateTime]::UtcNow.ToString("yyyy-MM-ddTHH:mm:ss:fffZ"))</SourceRevisionId>
    <NoWarn>8600,8602,8603,8604,8632</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CoverGo.Multitenancy.AspNetCore" />
    <PackageReference Include="CoverGo.Users.Client" />
    <PackageReference Include="HotChocolate" />
    <PackageReference Include="HotChocolate.AspNetCore" />
    <PackageReference Include="HotChocolate.AspNetCore.Authorization" />
    <PackageReference Include="HotChocolate.Data.MongoDb" />
    <PackageReference Include="HotChocolate.Stitching" />
    <PackageReference Include="HotChocolate.Types" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" />
    <PackageReference Include="Microsoft.Extensions.Logging" />
    <PackageReference Include="Microsoft.Extensions.Logging.Configuration" />
    <PackageReference Include="Serilog.AspNetCore" />
    <PackageReference Include="Serilog.Enrichers.Span" />
    <PackageReference Include="Serilog.Extensions.Hosting" />
    <PackageReference Include="Serilog.Sinks.ElasticSearch" />
    <PackageReference Include="Swashbuckle.AspNetCore" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CoverGo.Applications.HealthCheck" />
    <PackageReference Include="CoverGo.Applications.Http.GraphQl.Services" />
    <PackageReference Include="CoverGo.Applications.Http.Rest" />
    <PackageReference Include="CoverGo.Applications.Startup" />
    <PackageReference Include="CoverGo.BuildingBlocks.Auth" />
    <PackageReference Include="CoverGo.FeatureManagement" />
    <PackageReference Include="CoverGo.Sentry" />
  </ItemGroup>

  <ItemGroup>
    <DotNetCliToolReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Tools" Version="2.1.0-preview1-final" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CoverGo.Products.Domain\CoverGo.Products.Domain.csproj" />
    <ProjectReference Include="..\CoverGo.Products.Infrastructure.GatewayClient\CoverGo.Products.Infrastructure.GatewayClient.csproj" />
    <ProjectReference Include="..\CoverGo.Products.Infrastructure\CoverGo.Products.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="secrets.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <InternalsVisibleTo Include="CoverGo.Products.Tests.Integration" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Extensions\" />
  </ItemGroup>

</Project>
