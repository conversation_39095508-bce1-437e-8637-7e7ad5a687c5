﻿using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Insurers;
using Microsoft.AspNetCore.Mvc;

using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Application.Controllers
{
    [Route("{tenantId}/api/v1/insurers")]
    [ApiController]
    public class InsurersController : ControllerBase
    {
        private readonly InsurerService _insurerService;

        public InsurersController(InsurerService insurerService)
        {
            _insurerService = insurerService;
        }

        [HttpPost("filter")]
        public async Task<ActionResult<IEnumerable<Insurer>>> GetAllAsync(string tenantId, string clientId, [FromBody] InsurerFilter filter, CancellationToken cancellationToken)
            => Ok(await _insurerService.GetInsurersAsync(tenantId, clientId, filter, cancellationToken));

        [HttpPost]
        public async Task<Result> CreateInsurerAsync(string tenantId, [FromBody] CreateInsurerCommand command, CancellationToken cancellationToken)
            => await _insurerService.CreateInsurerAsync(tenantId, command, cancellationToken);

        [HttpDelete("{id}/{deletedById}")]
        public async Task<Result> DeleteInsurerAsync(string tenantId, string id, string deletedById, CancellationToken cancellationToken)
            => await _insurerService.DeleteInsurerAsync(tenantId, id, deletedById, cancellationToken);

        [HttpPost("migrateInsurers")]
        public async Task<Result> MigrateInsurersAsync(string tenantId, [FromBody] MigrateInsurersCommand command, CancellationToken cancellationToken)
            => await _insurerService.MigrateInsurersAsync(tenantId, command, cancellationToken);
    }
}
