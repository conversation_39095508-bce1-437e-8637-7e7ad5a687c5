using CoverGo.Applications.Http.Rest;
using CoverGo.Products.Domain.DiscountCodes;
using Microsoft.AspNetCore.Mvc;

namespace CoverGo.Products.Application.Controllers;

[Controller]
[Route("{tenantId}/api/v1/discountCodes")]
public class DiscountCodesController : CrudApiControllerBase<DiscountCode, DiscountCodeUpsert, DiscountCodeFilter, IDiscountCodeService>
{
    public DiscountCodesController(IDiscountCodeService service) 
        : base(service)
    {
    }
}