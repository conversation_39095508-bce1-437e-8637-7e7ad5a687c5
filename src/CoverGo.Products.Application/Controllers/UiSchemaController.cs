﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.UiSchemas;
using Microsoft.AspNetCore.Mvc;

namespace CoverGo.Products.Application.Controllers
{
    [Route("{tenantId}/api/v1/uiSchemas")]
    [ApiController]
    public class UiSchemaController : ControllerBase
    {
        private readonly IUiSchemaService _service;

        public UiSchemaController(IUiSchemaService service) =>
            _service = service;

        [HttpPost]
        public async Task<Result<CreatedStatus>> CreateAsync(string tenantId, [FromBody] CreateUiSchemaCommand command, CancellationToken cancellationToken)
            => await _service.CreateAsync(tenantId, command, cancellationToken);

        [HttpPost("query")]
        public async Task<ActionResult<IEnumerable<UiSchema>>> GetAllAsync(string tenantId, [FromBody] UiSchemaWhere where, CancellationToken cancellationToken)
            => Ok(await _service.GetAsync(tenantId, where, cancellationToken));

        [HttpPut]
        public async Task<ActionResult<Result>> UpdateAsync(string tenantId, [FromBody] UpdateUiSchemaCommand command, CancellationToken cancellationToken)
            => await _service.UpdateAsync(tenantId, command, cancellationToken);

        [HttpDelete("{uiSchemaId}")]
        public async Task<ActionResult<Result>> DeleteAsync(string tenantId, string uiSchemaId, [FromBody] DeleteCommand command, CancellationToken cancellationToken)
            => await _service.DeleteAsync(tenantId, uiSchemaId, command, cancellationToken);
    }
}
