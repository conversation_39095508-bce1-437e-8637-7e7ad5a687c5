using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Jackets.Commands;
using CoverGo.Products.Domain.Jackets.Entities;
using CoverGo.Products.Domain.Jackets.Filters;
using CoverGo.Products.Domain.Jackets.Services;
using Microsoft.AspNetCore.Mvc;

namespace CoverGo.Products.Application.Controllers
{
    [Route("{tenantId}/api/v1/jackets")]
    public class JacketsController : ControllerBase
    {
        private readonly IJacketService _service;

        public JacketsController(IJacketService service)
        {
            _service = service;
        }

        [HttpPost("totalCount")]
        public async Task<long> GetTotalCountAsync(string tenantId, [FromBody] JacketWhere where, CancellationToken cancellationToken)
        {
            long totalCount = await _service.GetTotalCountAsync(tenantId, where, cancellationToken);
            return totalCount;
        }

        [HttpPost("query")]
        public async Task<IEnumerable<Jacket>> GetAsync(string tenantId, [FromBody] Products.Domain.Jackets.Filters.QueryArguments<JacketWhere> queryArguments, CancellationToken cancellationToken)
        {
            IEnumerable<Jacket> claims = await _service.GetAsync(tenantId, queryArguments, cancellationToken);
            return claims;
        }

        [HttpPost("queryids")]
        public async Task<ActionResult<IEnumerable<string>>> GetIds(string tenantId, [FromBody] Products.Domain.Jackets.Filters.QueryArguments<JacketWhere> queryArguments, CancellationToken cancellationToken)
        {
            IEnumerable<string> ids = await _service.GetIdsAsync(tenantId, queryArguments.Where, queryArguments.OrderBy, queryArguments.Skip, queryArguments.First, queryArguments.AsOf, cancellationToken);
            return Ok(ids);
        }

        [HttpPost("create")]
        public async Task<ActionResult<Result<string>>> CreateJacket(string tenantId, [FromBody] CreateJacketCommand command, CancellationToken cancellationToken)
        {
            Result<string> result = await _service.CreateAsync(tenantId, command, cancellationToken);
            return result;
        }

        [HttpPost("update/{jacketId}")]
        public async Task<ActionResult<Result>> UpdateJacket(string tenantId, string jacketId, [FromBody] UpdateJacketCommand command, CancellationToken cancellationToken)
        {
            Result result = await _service.UpdateAsync(tenantId, jacketId, command, cancellationToken);
            return result;
        }
        
        [HttpPost("delete/{jacketId}")]
        public async Task<ActionResult<Result>> DeleteJacket(string tenantId, string jacketId, [FromBody] DeleteCommand command, CancellationToken cancellationToken)
        {
            Result result = await _service.DeleteAsync(tenantId, jacketId, command, cancellationToken);
            return result;
        }
    }
}