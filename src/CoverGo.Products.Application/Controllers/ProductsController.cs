﻿#nullable enable

using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Facts;
using CoverGo.Products.Domain.Illustrations;
using CoverGo.Products.Domain.Products;

using Microsoft.AspNetCore.Mvc;

namespace CoverGo.Products.Application.Controllers
{
    [Route("{tenantId}/api/v1/products")]
    [ApiController]
    public class ProductsController : ControllerBase
    {
        private readonly ProductService _service;

        public ProductsController(ProductService service)
        {
            _service = service;
        }

        [HttpPost("initializeTenant")]
        public async Task<Result> InitializeTenant(string tenantId, [FromBody] InitializeTenantProductsCommand command, CancellationToken cancellationToken)
        {
            IEnumerable<Task<Result>> tasks = command.ProductConfigs.Select(c => _service.CreateProductConfig(tenantId, c, cancellationToken));
            Result[] results = await Task.WhenAll(tasks);

            IEnumerable<string> errors = results.Where(e => e.Errors?.Any() ?? false)?.SelectMany(r => r.Errors);

            return errors.Any() ? new Result { Status = "failure", Errors = errors?.ToList() } : new Result { Status = "success" };
        }

        [HttpPost("configs/query")]
        public async Task<IEnumerable<ProductConfig>> GetConfigsAsync(string tenantId, [FromBody] ProductConfigQueryArguments queryArguments, CancellationToken cancellationToken)
            => await _service.GetProductConfigsAsync(tenantId, queryArguments.Where, queryArguments.OrderBy, queryArguments.Skip, queryArguments.First, cancellationToken);

        [HttpPost("configs/create")]
        public async Task<Result<CreatedStatus>> CreateConfigAsync(string tenantId, [FromBody] CreateProductConfigCommand command, CancellationToken cancellationToken)
            => await _service.CreateProductConfig(tenantId, command, cancellationToken);

        [HttpPost("configs/{id}/update")]
        public async Task<Result> UpdateConfigAsync(string tenantId, string id, [FromBody] UpdateProductConfigCommand command, CancellationToken cancellationToken)
            => await _service.UpdateProductConfig(tenantId, id, command, cancellationToken);

        [HttpPost("configs/{id}/delete")]
        public async Task<Result> DeleteConfigAsync(string tenantId, string id, [FromBody] DeleteCommand command, CancellationToken cancellationToken)
            => await _service.DeleteProductConfig(tenantId, id, cancellationToken);

        [HttpPost("migrateProducts")]
        public async Task<Result> MigrateProducts(string tenantId, [FromBody] MigrateProductsCommand command, CancellationToken cancellationToken)
            => await _service.MigrateProductsAsync(tenantId, command, cancellationToken);

        [HttpPost("cleanProductTest")]
        public async Task<Result> CleanProductTest(string tenantId, [FromBody] string typeId, CancellationToken cancellationToken)
            => await _service.CleanProductTest(tenantId, typeId, cancellationToken);

        [HttpPost("query2")]
        public async Task<ActionResult<IEnumerable<Product>>> GetAllAsync(string tenantId, [FromBody] ProductQuery query, string? clientId, CancellationToken cancellationToken)
        {
            IEnumerable<Product> products = await _service.GetAsync(tenantId, clientId, query.Where, query.Factors,
                new QueryParameters() { OrderBy = query.OrderBy, Skip = query.Skip, Limit = query.Limit }, query.LoadRepresentation, cancellationToken);
            return Ok(products);
        }

        [HttpPost("totalCount")]
        public async Task<ActionResult<long>> GetTotalCountAsync(string tenantId, [FromBody] ProductWhere where, string? clientId, CancellationToken cancellationToken)
        {
            var productsCount = await _service.GetTotalCountAsync(tenantId, clientId, where, cancellationToken);
            return Ok(productsCount);
        }

        [HttpPost("clone")]
        public async Task<ActionResult<Result>> Clone(string tenantId, [FromBody] CloneProductCommand command, CancellationToken cancellationToken)
            => await _service.CloneAsync(tenantId, command, cancellationToken);

        [HttpPost("events")]
        public async Task<IEnumerable<Proxies.Product.EventLog>> GetEvents(string tenantId, [FromBody] Proxies.Product.EventQuery query, CancellationToken cancellationToken)
            => await _service.GetEventLogsAsync(tenantId, query, cancellationToken);

        [HttpPost("underwritingJsonLogicRules/query")]
        public async Task<ActionResult<IEnumerable<ProductUnderwritingJsonLogicRules>>> GetUnderwritingJsonLogicRulesAsync(string tenantId, [FromBody] ProductWhere where, CancellationToken cancellationToken)
        {
            IEnumerable<ProductUnderwritingJsonLogicRules> productUnderwritings = await _service.GetUnderwritingJsonLogicRulesAsync(tenantId, where, cancellationToken);
            return Ok(productUnderwritings);
        }

        [HttpPost("underwritingJsonSchema/query")]
        public async Task<ActionResult<IEnumerable<ProductUnderwritingJsonSchema>>> GetUnderwritingJsonSchemasAsync(string tenantId, [FromBody] ProductUnderwritingJsonSchemaQuery query, CancellationToken cancellationToken)
        {
            IEnumerable<ProductUnderwritingJsonSchema> productUnderwritings = await _service.GetUnderwritingJsonSchemasAsync(tenantId, query, cancellationToken);
            return Ok(productUnderwritings);
        }

        [HttpPost]
        public async Task<ActionResult<Result>> Create(string tenantId, [FromBody] CreateProductCommand command, CancellationToken cancellationToken)
            => await _service.CreateAsync(tenantId, command, cancellationToken);

        [HttpPut]
        public async Task<ActionResult<Result>> Update(string tenantId, [FromBody] UpdateProductCommand command, CancellationToken cancellationToken)
            => await _service.UpdateAsync(tenantId, command, cancellationToken);

        [HttpPost("{productIdString}/addBenefit")]
        public async Task<ActionResult<Result>> AddBenefit(string tenantId, string productIdString, [FromBody] AddBenefitCommand command, CancellationToken cancellationToken)
            => await _service.AddBenefitAsync(tenantId, ProductId.FromString(productIdString), command, cancellationToken);

        [HttpPost("{productIdString}/benefits/update")]
        public async Task<ActionResult<Result>> UpdateBenefit(string tenantId, string productIdString, [FromBody] UpdateBenefitCommand command, CancellationToken cancellationToken)
          => await _service.UpdateBenefitAsync(tenantId, ProductId.FromString(productIdString), command, cancellationToken);

        [HttpPost("{productIdString}/benefits/batch")]
        public async Task<ActionResult<Result>> BenefitBatch(string tenantId, string productIdString, [FromBody] BenefitCommandBatch command, CancellationToken cancellationToken)
        => await _service.BenefitBatchAsync(tenantId, ProductId.FromString(productIdString), command, cancellationToken);

        [HttpPost("{productIdString}/removeBenefit")]
        public async Task<ActionResult<Result>> RemoveBenefit(string tenantId, string productIdString, [FromBody] RemoveBenefitCommand command, CancellationToken cancellationToken)
            => await _service.RemoveBenefitAsync(tenantId, ProductId.FromString(productIdString), command, cancellationToken);

        [HttpPost("{productIdString}/tags/add")]
        public async Task<ActionResult<Result<CreatedStatus>>> AddTag(string tenantId, string productIdString, [FromBody] AddTagCommand command, CancellationToken cancellationToken)
          => await _service.AddTagAsync(tenantId, ProductId.FromString(productIdString), command, cancellationToken);

        [HttpPost("{productIdString}/tags/remove")]
        public async Task<ActionResult<Result>> RemoveTag(string tenantId, string productIdString, [FromBody] RemoveCommand command, CancellationToken cancellationToken)
           => await _service.RemoveTagAsync(tenantId, ProductId.FromString(productIdString), command, cancellationToken);

        [HttpPost("{productIdString}/facts/add")]
        public async Task<ActionResult<Result<CreatedStatus>>> AddFact(string tenantId, string productIdString, [FromBody] AddFactCommand command, CancellationToken cancellationToken)
         => await _service.AddFactAsync(tenantId, ProductId.FromString(productIdString), command, cancellationToken);

        [HttpPut("{productIdString}/facts/update")]
        public async Task<ActionResult<Result>> UpdateFact(string tenantId, string productIdString, [FromBody] UpdateFactCommand command, CancellationToken cancellationToken)
         => await _service.UpdateFactAsync(tenantId, ProductId.FromString(productIdString), command, cancellationToken);

        [HttpPost("{productIdString}/facts/remove")]
        public async Task<ActionResult<Result>> RemoveFact(string tenantId, string productIdString, [FromBody] RemoveCommand command, CancellationToken cancellationToken)
           => await _service.RemoveFactAsync(tenantId, ProductId.FromString(productIdString), command, cancellationToken);

        [HttpPut("{productIdString}/facts/batch")]
        public async Task<ActionResult<Result>> FactBatch(string tenantId, string productIdString, [FromBody] FactCommandBatch command, CancellationToken cancellationToken)
         => await _service.FactBatchAsync(tenantId, ProductId.FromString(productIdString), command, cancellationToken);

        [HttpPost("{productIdString}/internalreviews/add")]
        public async Task<ActionResult<Result<CreatedStatus>>> AddInternalReview(string tenantId, string productIdString, [FromBody] AddInternalReviewCommand command, CancellationToken cancellationToken)
         => await _service.AddInternalReviewAsync(tenantId, ProductId.FromString(productIdString), command, cancellationToken);

        [HttpPut("{productIdString}/internalreviews/update")]
        public async Task<ActionResult<Result>> UpdateInternalReview(string tenantId, string productIdString, [FromBody] UpdateInternalReviewCommand command, CancellationToken cancellationToken)
         => await _service.UpdateInternalReviewAsync(tenantId, ProductId.FromString(productIdString), command, cancellationToken);

        [HttpDelete("{productIdString}/internalreviews/remove")]
        public async Task<ActionResult<Result>> RemoveInternalReview(string tenantId, string productIdString, [FromBody] RemoveCommand command, CancellationToken cancellationToken)
           => await _service.RemoveInternalReviewAsync(tenantId, ProductId.FromString(productIdString), command, cancellationToken);

        [HttpPost("delete")]
        public async Task<ActionResult<Result>> Delete(string tenantId, [FromBody] DeleteProductCommand command, CancellationToken cancellationToken)
            => await _service.DeleteAsync(tenantId, command, cancellationToken);

        [HttpGet("benefits/infos")]
        public async Task<Dictionary<string, Dictionary<string, List<string>>>> GetBenefitInfos(string tenantId, string clientId, CancellationToken cancellationToken)
        {
            Dictionary<string, Dictionary<string, List<string>>> benefitInfos = await _service.GetBenefitInfosAsync(tenantId, clientId, cancellationToken);
            return benefitInfos;
        }

        [HttpGet("benefits/categories")]
        public async Task<Dictionary<string, List<string>>> GetBenefitCategoriesAsync(string tenantId, string clientId, CancellationToken cancellationToken)
        {
            Dictionary<string, List<string>> benefitCategories = await _service.GetBenefitCategoriesAsync(tenantId, clientId, cancellationToken);
            return benefitCategories;
        }

        [HttpPost("types/query")]
        public async Task<IEnumerable<ProductType>> GetTypesAsync(string tenantId, string? clientId, [FromBody] ProductTypeWhere where, CancellationToken cancellationToken)
            => await _service.GetTypesAsync(tenantId, clientId, where, cancellationToken);

        [HttpPost("types")]
        public async Task<Result> CreateTypeAsync(string tenantId, [FromBody] CreateProductTypeCommand command, CancellationToken cancellationToken)
            => await _service.CreateTypeAsync(tenantId, command, cancellationToken);

        [HttpDelete("types/{typeId}/{deletedById}")]
        public Task<Result> DeleteTypeAsync(string tenantId, string typeId, string deletedById, CancellationToken cancellationToken) =>
             _service.DeleteTypeAsync(tenantId, typeId, deletedById, cancellationToken);

        [HttpPost("{productIdString}/underwritingVariables/add")]
        public Task<Result> AddUnderwritingVariable(string tenantId, string productIdString, [FromBody] AddUnderwritingVariableCommand command, CancellationToken cancellationToken) =>
             _service.AddUnderwritingVariableAsync(tenantId, ProductId.FromString(productIdString), command, cancellationToken);

        [HttpPost("{productIdString}/underwritingVariables/update")]
        public Task<Result> UpdateUnderwritingVariable(string tenantId, string productIdString, [FromBody] UpdateUnderwritingVariableCommand command, CancellationToken cancellationToken) =>
             _service.UpdateUnderwritingVariableAsync(tenantId, ProductId.FromString(productIdString), command, cancellationToken);

        [HttpPost("{productIdString}/underwritingVariables/remove")]
        public Task<Result> RemoveUnderwritingVariable(string tenantId, string productIdString, [FromBody] RemoveCommand command, CancellationToken cancellationToken) =>
            _service.RemoveUnderwritingVariableAsync(tenantId, ProductId.FromString(productIdString), command, cancellationToken);

        [HttpPost("validate")]
        public Task<IEnumerable<ValidateResult>> ValidateProducts(string tenantId, string clientId, [FromBody] ProductQuery query, CancellationToken cancellationToken) =>
            _service.ValidateProductsAsync(tenantId, clientId, query.Where, query.Factors, cancellationToken);

        [HttpPost("illustrations")]
        public async Task<IEnumerable<Illustration>> GetIllustrations(string tenantId, [FromBody] ProductQuery query, CancellationToken cancellationToken)
        {
            Result<IEnumerable<Illustration>> illustrations = await _service.GetIllustrationsAsync(tenantId, query.Where, query.Factors, cancellationToken);
            return illustrations.Value;
        }

        [HttpPost("scripts")]
        public Task<Result> AddScriptToProductAsync(string tenantId, [FromBody] AddScriptToProductCommand command, CancellationToken cancellationToken) =>
            _service.AddScriptToProduct(tenantId, command, cancellationToken);

        [HttpDelete("scripts")]
        public Task<Result> RemoveScriptFromProductAsync(string tenantId, [FromBody] RemoveScriptFromProductCommand command, CancellationToken cancellationToken) =>
            _service.RemoveScriptFromProduct(tenantId, command, cancellationToken);

        [HttpPost("templateRelationships")]
        public Task<Result> AddTemplateRelationshipToProductAsync(string tenantId, [FromBody] AddTemplateRelationshipToProductCommand command, CancellationToken cancellationToken) =>
            _service.AddTemplateRelationshipToProduct(tenantId, command, cancellationToken);

        [HttpDelete("templateRelationships")]
        public Task<Result> RemoveTemplateRelationshipFromProductAsync(string tenantId, [FromBody] RemoveTemplateRelationshipFromProductCommand command, CancellationToken cancellationToken) =>
            _service.RemoveTemplateRelationshipFromProduct(tenantId, command, cancellationToken);

        [HttpPost("types/dataSchemas")]
        public Task<Result> AddDataSchemaToProductTypeAsync(string tenantId, [FromBody] AddDataSchemaToProductTypeCommand command, CancellationToken cancellationToken) =>
            _service.AddDataSchemaToProductTypeToProduct(tenantId, command, cancellationToken);

        [HttpDelete("types/dataSchemas")]
        public Task<Result> RemoveDataSchemaFromProductTypeAsync(string tenantId, [FromBody] RemoveDataSchemaFromProductTypeCommand command, CancellationToken cancellationToken) =>
            _service.RemoveDataSchemaToProductTypeFromProduct(tenantId, command, cancellationToken);
    }
}
