﻿using CoverGo.DomainUtils;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using CoverGo.Products.Domain.BenefitDefinitionTypes;
using System.Threading;

namespace CoverGo.Products.Application.Controllers
{
    [Route("{tenantId}/api/v1/benefitdefinitiontypes")]
    [ApiController]
    public class BenefitDefinitionTypesController : ControllerBase
    {
        readonly BenefitDefinitionTypeService _service;

        public BenefitDefinitionTypesController(BenefitDefinitionTypeService service) =>
            _service = service;

        [HttpPost("query")]
        public async Task<ActionResult<IEnumerable<BenefitDefinitionType>>> GetAllAsync(string tenantId, [FromBody] BenefitTypeQueryArguments queryArguments, CancellationToken cancellationToken)
        {
            IEnumerable<BenefitDefinitionType> benefitDefinitionTypes = await _service.GetBenefitDefinitionTypeAsync(tenantId, queryArguments, cancellationToken);
            return Ok(benefitDefinitionTypes);
        }

        [HttpPost]
        public async Task<Result<CreatedStatus>> CreateAsync(string tenantId, [FromBody] CreateBenefitDefinitionTypeCommand command, CancellationToken cancellationToken) =>
            await _service.CreateBenefitDefinitionTypeAsync(tenantId, command, cancellationToken);

        [HttpPut]
        public async Task<ActionResult<Result>> UpdateAsync(string tenantId, [FromBody] UpdateBenefitDefinitionTypeCommand command, CancellationToken cancellationToken) =>
            await _service.UpdateBenefitDefinitionTypeAsync(tenantId, command, cancellationToken);

        [HttpDelete("{benefitDefinitionTypeId}")]
        public async Task<ActionResult<Result>> DeleteAsync(string tenantId, string benefitDefinitionTypeId, [FromBody] DeleteCommand command, CancellationToken cancellationToken) =>
            await _service.DeleteBenefitDefinitionTypeAsync(tenantId, benefitDefinitionTypeId, command, cancellationToken);

        [HttpPost("batch")]
        public async Task<Result> BatchAsync(string tenantId, [FromBody] BatchBenefitDefinitionTypeCommand command, CancellationToken cancellationToken)
            => await _service.BatchBenefitDefinitionTypeAsync(tenantId, command, cancellationToken);
    }
}