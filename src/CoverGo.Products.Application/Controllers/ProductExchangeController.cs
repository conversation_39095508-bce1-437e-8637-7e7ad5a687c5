﻿using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.Commands;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Application.Controllers
{
    [Route("{tenantId}/api/v1/productexchange")]
    [ApiController]
    public class ProductExchangeController : ControllerBase
    {

        private readonly ProductService _productService;
        private readonly ProductExchangeService _productExchangeService;
        private readonly ProductEventLogsService _productEventLogsService;
        private readonly ILogger<ProductExchangeController> _logger;
        public ProductExchangeController(
             ProductService productService,
             ProductExchangeService productExchangeService,
             ProductEventLogsService productEventLogsService,
             ILogger<ProductExchangeController> logger)
        {
            _productService = productService;
            _productExchangeService = productExchangeService;
            _productEventLogsService = productEventLogsService;
            _logger = logger;
        }


        [HttpPost("import")]
        public async Task<Result> Import(string tenantId,string loginId, string fromFilePath, CancellationToken cancellationToken)
        {

            try
            {
                return await _productExchangeService.ImportAsync(tenantId, loginId, fromFilePath, skipValidateProductTreeLock: true, cancellationToken, validateFileCheckSum:false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Product import failed");
                return Result.Failure("Unable to import the product");
            }

        }

        [HttpPost("ImportInfo")]
        public async Task<Result<ProductImportInfo>> ImportInfo(string tenantId, string fromFilePath, CancellationToken cancellationToken)
        {

            try
            {
                return await _productExchangeService.GetImportInfoAsync(tenantId, fromFilePath, cancellationToken, validateFileCheckSum: false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Getting product import info failed");
                return Result<ProductImportInfo>.Failure("Unable to get the product import info");
            }

        }

        [HttpPost("export")]
        public async Task<Result<string>> Export(string tenantId, string clientId, string loginId, [FromBody]ProductId productId, CancellationToken cancellationToken) 
        {

            try
            {
                return await _productExchangeService.ExportAsync(tenantId, clientId, loginId, productId, skipValidateProductTreeLock: true, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Product export failed");
                return Result<string>.Failure("Unable to export the product");
            }

        }

        [HttpPost("clone")]
        public async Task<Result> Clone(string tenantId, string clientId, string loginId, [FromBody] CloneProductTreeCommand cloneProductCommand, string lifeCycleStage, CancellationToken cancellationToken)
        {

            try
            {
                return await _productExchangeService.CloneProductTreeAsync(tenantId, clientId, loginId, cloneProductCommand.ProductId, cloneProductCommand.Name, cloneProductCommand.CloneProductId, lifeCycleStage, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Clone Product failed");
                return Result.Failure("Unable to clone the product");
            }

        }

        [HttpPost("EventStoreLogs")]
        public async Task<Result<List<ProductEvent>>> ProductEventLogs(string tenantId, string productType, [FromBody] ProductEventWhere where, CancellationToken cancellationToken)
        {
            Result<List<ProductEvent>> result;
            try
            {
                var logs = await _productEventLogsService.GetProductEventStoreLogsAsync(tenantId, productType, where, cancellationToken);
                result = Result<List<ProductEvent>>.Success(logs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Getting product nodes event store logs failed");
                result = Result<List<ProductEvent>>.Failure("Unable to get the product nodes event store logs");
            }
            return result;
        }

        [HttpPost("ProductEvents")]
        public async Task<Result<List<ProductEvent>>> ProductEvents(string tenantId, string productType, [FromBody] ProductEventQuery query, CancellationToken cancellationToken)
        {
            Result<List<ProductEvent>> result;
            try
            {
                var logs = await _productEventLogsService.GetProductEventsAsync(tenantId, productType, query.Where, query.OrderBy, query.Skip, query.Limit, cancellationToken);
                result = Result<List<ProductEvent>>.Success(logs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Getting product event logs failed");
                result = Result<List<ProductEvent>>.Failure("Unable to get the product event logs");
            }
            return result;
        }

        [HttpPost("addEvents")]
        public async Task<Result> AddProductEvents(string tenantId, [FromBody] List<ProductEvent> events, CancellationToken cancellationToken)
        {
            if (events == null || events.Count == 0)
                return new Result { Status = "failure", Errors = new List<string> { "Event is null" } };

            foreach (var @event in events)
            {
                if (@event == null)
                    return new Result { Status = "failure", Errors = new List<string> { "Event is null" } };
                if (@event.ProductId == null)
                    return new Result { Status = "failure", Errors = new List<string> { "ProductId is null" } };

                await _productService.AddEventAsync(tenantId, @event, cancellationToken);
            }

            return new Result { Status = "success" };
        }


        
    }
}
