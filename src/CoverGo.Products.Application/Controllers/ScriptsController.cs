﻿using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Scripts;
using CoverGo.Scripts.Client;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Application.Controllers
{
    [Route("{tenantId}/api/v1/scripts")]
    [ApiController]
    public class ScriptsController : ControllerBase
    {
        readonly ScriptService _service;

        public ScriptsController(ScriptService service) =>
            _service = service;

        [HttpPost("query")]
        public async Task<ActionResult<IEnumerable<Script>>> GetAllAsync(string tenantId, [FromBody] ScriptWhere where, CancellationToken cancellationToken)
        {
            IEnumerable<Script> scripts = await _service.GetAsync(tenantId, where, cancellationToken);
            return Ok(scripts);
        }

        [HttpPost]
        public async Task<Result<CreatedStatus>> CreateAsync(string tenantId, [FromBody] CreateScriptCommand command, CancellationToken cancellationToken) =>
            await _service.CreateAsync(tenantId, command, cancellationToken);

        [HttpPut]
        public async Task<ActionResult<Result>> UpdateAsync(string tenantId, [FromBody] UpdateScriptCommand command, CancellationToken cancellationToken) =>
            await _service.UpdateAsync(tenantId, command, cancellationToken);

        [HttpDelete]
        public async Task<ActionResult<Result>> DeleteAsync(string tenantId, [FromBody] DeleteCommand command, CancellationToken cancellationToken) =>
            await _service.DeleteAsync(tenantId, command, cancellationToken);
    }
}