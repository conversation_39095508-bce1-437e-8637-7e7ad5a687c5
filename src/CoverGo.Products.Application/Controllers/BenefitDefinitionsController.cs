﻿using CoverGo.DomainUtils;
using CoverGo.Products.Domain.BenefitDefinitions;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Application.Controllers
{
    [Route("{tenantId}/api/v1/benefitdefinitions")]
    [ApiController]
    public class BenefitDefinitionsController : ControllerBase
    {
        readonly BenefitDefinitionService _service;

        public BenefitDefinitionsController(BenefitDefinitionService service) =>
            _service = service;

        [HttpPost("query")]
        public async Task<ActionResult<IEnumerable<BenefitDefinition>>> GetAllAsync(string tenantId, [FromBody] BenefitQueryArguments queryArguments, CancellationToken cancellationToken)
        {
            IEnumerable<BenefitDefinition> benefitDefinitions = await _service.GetBenefitDefinitionAsync(tenantId, queryArguments, cancellationToken);
            return Ok(benefitDefinitions);
        }

        [HttpPost("count")]
        public Task<long> CountAsync(string tenantId, [FromBody] BenefitQueryArguments queryArguments,
            CancellationToken cancellationToken) =>
            _service.CountBenefitDefinitionAsync(tenantId, queryArguments, cancellationToken);

        [HttpPost]
        public async Task<Result<CreatedStatus>> CreateAsync(string tenantId, [FromBody] CreateBenefitDefinitionCommand command, CancellationToken cancellationToken) =>
            await _service.CreateBenefitDefinitionAsync(tenantId, command, cancellationToken);

        [HttpPut]
        public async Task<ActionResult<Result>> UpdateAsync(string tenantId, [FromBody] UpdateBenefitDefinitionCommand command, CancellationToken cancellationToken) =>
            await _service.UpdateBenefitDefinitionAsync(tenantId, command, cancellationToken);

        [HttpDelete("{benefitDefinitionId}")]
        public async Task<ActionResult<Result>> DeleteAsync(string tenantId, string benefitDefinitionId, [FromBody] DeleteCommand command, CancellationToken cancellationToken) =>
            await _service.DeleteBenefitDefinitionAsync(tenantId, benefitDefinitionId, command, cancellationToken);

        [HttpPost("batch")]
        public async Task<Result> BatchAsync(string tenantId, [FromBody] BatchBenefitDefinitionCommand command, CancellationToken cancellationToken)
            => await _service.BatchBenefitDefinitionAsync(tenantId, command, cancellationToken);
    }
}