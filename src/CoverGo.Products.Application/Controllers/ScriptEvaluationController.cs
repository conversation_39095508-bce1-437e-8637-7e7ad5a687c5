#nullable enable

using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.Products.Domain.ScriptEvaluation;
using CoverGo.Scripts.Client;

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace CoverGo.Products.Application.Controllers
{
    public abstract class ScriptEvaluationControllerBase : ControllerBase
    {
        protected readonly ScriptEvaluationService _service;
        protected readonly ILogger _logger;

        protected ScriptEvaluationControllerBase(ScriptEvaluationService service, ILogger logger)
        {
            _service = service;
            _logger = logger;
        }

        protected void Log(ScriptResult? result)
        {
            if (result != null && result.Status != "success")
            {
                var joinedErrors = string.Join(", ", result.Errors ?? Enumerable.Empty<string>());
                var joinedErrors2 = result.Errors_2
                    ?.Select(e => $"{e?.Code}:{e?.Message}") ?? Enumerable.Empty<string>();
                _logger.LogError("Script execution failed : {joinedErrors} - {joinedErrors2}",
                    joinedErrors, joinedErrors2);
            }
        }
    }

    [Route("{tenantId}/api/v1/scriptevaluation")]
    [ApiController]
    public class ScriptEvaluationController : ScriptEvaluationControllerBase
    {
        public ScriptEvaluationController(ScriptEvaluationService service, ILogger<ScriptEvaluationController> logger)
            : base(service, logger)
        {
        }

        [HttpPost]
        public async Task<ScriptResult> Evaluate(string tenantId, [FromBody] EvaluateScriptCommand command, CancellationToken cancellationToken)
        {
            var result = await _service.Evaluate(tenantId, command, cancellationToken);
            Log(result);

            return result;
        }

        [HttpPost("{clientId}")]
        public async Task<ScriptResult> Evaluate(string tenantId, string clientId, [FromBody] EvaluateProductScriptCommand command, CancellationToken cancellationToken)
        {
            var result = await _service.Evaluate(tenantId, clientId, command, cancellationToken);
            Log(result);

            return result;
        }
    }

    [Route("{tenantId}/api/v2/scriptevaluation")]
    [ApiController]
    public class ScriptEvaluationControllerV2 : ScriptEvaluationControllerBase
    {
        public ScriptEvaluationControllerV2(ScriptEvaluationService service, ILogger<ScriptEvaluationController> logger)
            : base(service, logger)
        {
        }

        /// <remarks>Had to create V2 in order to allow passing nullable clientId</remarks>
        [HttpPost]
        public async Task<ScriptResult> Evaluate(string tenantId, [FromQuery] string? clientId, [FromBody] EvaluateProductScriptCommand command, CancellationToken cancellationToken)
        {
            var result = await _service.Evaluate(tenantId, clientId, command, cancellationToken);
            Log(result);

            return result;
        }
    }
}
