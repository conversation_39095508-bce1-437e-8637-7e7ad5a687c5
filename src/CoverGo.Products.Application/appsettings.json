{"Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.AspNetCore.Server.Kestrel": "Fatal", "System": "Warning"}}, "Enrich": ["FromLogContext"], "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}], "Properties": {"Service": "covergo-products"}}, "FeatureManagement": {"OptimizedProductsQueryEnabled": true, "ValidatePdtId": false, "ExtractPlansFromProductTreeEnabled": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["axaTh_test", "axaTh_preprod", "axaTh_prod", "asia_dev", "asia_preprod", "asia_uat", "asia_prod"]}}]}, "ENABLE_PERMISSION_VERSION_2_IN_GRAPHQL_INTERCEPTOR": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["covergo", "coverHealth_dev", "gms_dev"]}}]}, "UseLegacyLifecycle": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["xn_dev", "xni", "xni_qa", "xni_prod", "gms_dev", "gms_qa", "gms_sandbox", "gms_migration", "gms_training", "gms", "gms_prod"]}}]}}, "UseSentry": true, "serviceUrls": {"gateway": "http://covergo-gateway:8080/"}, "GraphQL": {"IncludeExceptionDetails": false}}