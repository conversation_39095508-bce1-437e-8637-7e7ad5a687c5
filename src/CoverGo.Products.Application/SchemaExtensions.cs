using System.Text.Json;
using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Products.Application.GraphQl;
using CoverGo.Products.Domain.Products.Commands;
using CoverGo.Products.Domain.Products.Limits;
using HotChocolate.Execution.Configuration;
using HotChocolate.Types;

using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;

namespace CoverGo.Products.Application;

public static class SchemaExtensions
{
    private const string SchemaName = "products";

    public static IRequestExecutorBuilder AddProductsGraphQLSchema(this IServiceCollection services)
    {
        return services.AddCoverGoGraphQl(SchemaName, executionTimeoutSec: 1800) // 30 min timeout
            .AddDataLoader<ProductNameBatchDataLoader>()
            .AddDataLoader<ProductPlanDetailsBatchLoader>()
            .AddDataLoader<ProductFieldsBatchLoader>()
            .AddErrorFilter<GlobalErrorFilter>((s) => new(s.GetRequiredService<IHttpContextAccessor>()))
            .AddType<AnyType>()
            .AddProductTypes()
            .AddLimitTypes()
            .AddMutationConventions(new MutationConventionOptions
            {
                ApplyToAllMutations = false,
                InputTypeNamePattern = $"{SchemaName}_{{MutationName}}Input",
                PayloadTypeNamePattern = $"{SchemaName}_{{MutationName}}Payload",
                PayloadErrorTypeNamePattern = $"{SchemaName}_{{MutationName}}Error",
            });
    }

    private static IRequestExecutorBuilder AddProductTypes(this IRequestExecutorBuilder builder)
    {
        return builder
            .AddType<ProductType>()
            .AddCommandType<UpdateProductVersionCommand>();
    }

    private static IRequestExecutorBuilder AddLimitTypes(this IRequestExecutorBuilder builder)
    {
        return builder
            .AddType<PercentageCoinsuranceValue>()
            .AddType<PercentageFormulaCoinsuranceValue>()
            .AddType<AmountCoinsuranceMaxLimitValue>()
            .AddType<AmountFormulaCoinsuranceMaxLimitValue>()
            .AddType<AmountCopaymentValue>()
            .AddType<AmountFormulaCopaymentValue>()
            .AddType<AmountDeductibleValue>()
            .AddType<AmountFormulaDeductibleValue>()
            .AddType<NumberDeferredPeriodValue>()
            .AddType<NumberFormulaDeferredPeriodValue>()
            .AddType<GroupedBenefit>()
            .AddType<GroupedBenefitType>()
            .AddType<AmountLimitValue>()
            .AddType<NumberLimitValue>()
            .AddType<AmountFormulaLimitValue>()
            .AddType<InNetworkAndOutOfNetwork>()
            .AddType<OutOfNetwork>()
            .AddType<InNetwork>()
            .AddType<AmountPanelCopaymentValue>()
            .AddType<NumberQualificationPeriodValue>()
            .AddType<NumberFormulaQualificationPeriodValue>()
            .AddType<PercentageReimbursementValue>()
            .AddType<PercentageFormulaReimbursementValue>()
            .AddType<AmountWaitingPeriodValue>()
            .AddType<AmountFormulaWaitingPeriodValue>()
            ;
    }

    private static IRequestExecutorBuilder AddCommandType<TCommand>(this IRequestExecutorBuilder builder)
    {
        return builder.AddInputObjectType(delegate (IInputObjectTypeDescriptor<TCommand> x)
        {
            x.Name($"{SchemaName}_{typeof(TCommand).Name.Replace("Command", "Input")}");
        });
    }
}
