using System;
using System.Collections.Generic;

using CoverGo.Applications.Startup;
using CoverGo.Configuration;
using CoverGo.MongoUtils;
using CoverGo.Products.Application;
using CoverGo.Sentry;

using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;

using MongoDB.Bson;
using MongoDB.Driver;

using Serilog;
using Serilog.Events;

try
{
    if (UseBootstrapLogger)
    {
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
            .Enrich.FromLogContext()
            .WriteTo.Console()
            .CreateBootstrapLogger();
    }

    var webAppBuilder = WebApplication.CreateBuilder(args);

    string appName = "covergo-products";
    if (Environment.GetEnvironmentVariable("appName") == null)
        Environment.SetEnvironmentVariable("appName", appName);
    Log.Logger.Information($"APPNAME: {appName}");

    string datacenterId = Environment.GetEnvironmentVariable("datacenterId");
    Log.Logger.Information($"DATACENTERID: {datacenterId}");

    if (webAppBuilder.Configuration.GetValue<bool>("UseSentry"))
    {
        webAppBuilder.WebHost.UseCoverGoSentry(webAppBuilder.Configuration);
    }

    string databaseDriver = Environment.GetEnvironmentVariable("DATABASE_DRIVER");

    StartupMethods.AddMongoDBMetrics();

    // initialize connectivity
    if (datacenterId == "local")
    {
        var covergoDbConfig = new DbConfig { ProviderId = "mongoDb" };
        covergoDbConfig.Load<Program>();
        DbConfig.AddConfig("default", covergoDbConfig);
        MongoTools.GetOrAddMongoClient(covergoDbConfig);
        InitMongoDbIndexes(covergoDbConfig);
    }

    if (datacenterId == "developer-machine" || datacenterId == null)
    {
        string? connectionString = Environment.GetEnvironmentVariable("DATABASE_CONNECT_STRING") ?? webAppBuilder.Configuration?.GetConnectionString("mongo");
        DbConfig defaultConfig = new() { ConnectionString = connectionString, ProviderId = "mongoDb" };
        defaultConfig.Load<Program>();
        DbConfig.AddConfig("default", defaultConfig);
        MongoTools.GetOrAddMongoClient(defaultConfig);
        InitMongoDbIndexes(defaultConfig);
    }

    if (datacenterId == "covergo-dockerComposeOnJenkins-hk")
    {
        var covergoDbConfig = new DbConfig { ProviderId = "mongoDb" };
        covergoDbConfig.Load<Program>();
        DbConfig.AddConfig("default", covergoDbConfig);
        MongoTools.GetOrAddMongoClient(covergoDbConfig);
        InitMongoDbIndexes(covergoDbConfig);
    }

    if (datacenterId == "covergo-aliyun-hk")
    {
        var covergoDbConfig = new DbConfig { ProviderId = "mongoDb" };
        covergoDbConfig.Load<Program>();
        DbConfig.AddConfig("default", covergoDbConfig);
        MongoTools.GetOrAddMongoClient(covergoDbConfig);
        InitMongoDbIndexes(covergoDbConfig);

        var tahoeUatConfig = new DbConfig
        {
            ProviderId = "eBao",
            Endpoint = "https://sandbox-hk-gw.insuremo.com/hkcloud/1.0",
        };
        DbConfig.AddConfig("tahoe_uat", "illustrations", tahoeUatConfig);

        var tahoeUatUnderwritingConfig = new DbConfig
        {
            ProviderId = "eBao",
            Endpoint = "https://sandbox-hk-gw.insuremo.com/hkcloud/1.0",
        };
        DbConfig.AddConfig("tahoe_uat", "underwriting", tahoeUatUnderwritingConfig);
    }

    if (datacenterId == "polipocket-aws-sg")
    {
        var polipocketUatDbConfig = new DbConfig { ProviderId = "mongoDb" };
        polipocketUatDbConfig.Load<Program>("aag_uat-");
        DbConfig.AddConfig("aag_uat", "products", polipocketUatDbConfig);
        MongoTools.GetOrAddMongoClient(polipocketUatDbConfig);
        InitMongoDbIndexes(polipocketUatDbConfig);

        var polipocketProdDbConfig = new DbConfig { ProviderId = "mongoDb" };
        polipocketProdDbConfig.Load<Program>("aag-");
        DbConfig.AddConfig("aag", "products", polipocketProdDbConfig);
        MongoTools.GetOrAddMongoClient(polipocketProdDbConfig);
        InitMongoDbIndexes(polipocketProdDbConfig);
    }

    if (datacenterId == "tahoe-aws-hk")
    {
        var tahoeConfig = new DbConfig { ProviderId = "mongoDb" };
        tahoeConfig.Load<Program>();
        DbConfig.AddConfig("default", tahoeConfig);
        MongoTools.GetOrAddMongoClient(tahoeConfig);
#pragma warning disable 4014 // Deliberate fire and forget
        InitMongoDbIndexes(tahoeConfig);
#pragma warning restore 4014

        var tahoeUatConfig = new DbConfig
        {
            ProviderId = "eBao",
            Endpoint = "https://sandbox-hk-gw.insuremo.com/hkcloud/1.0",
        };
        DbConfig.AddConfig("tahoe", "illustrations", tahoeUatConfig);

        var tahoeUatUnderwritingConfig = new DbConfig
        {
            ProviderId = "eBao",
            Endpoint = "https://sandbox-hk-gw.insuremo.com/hkcloud/1.0",
        };
        DbConfig.AddConfig("tahoe", "underwriting", tahoeUatUnderwritingConfig);
    }

    if (datacenterId == "12factor")
    {
        if (databaseDriver == "mongoDb")
        {
            var defaultConfig = new DbConfig { ProviderId = "mongoDb" };
            defaultConfig.Load<Program>();
            DbConfig.AddConfig("default", defaultConfig);
            MongoTools.GetOrAddMongoClient(defaultConfig);
#pragma warning disable 4014 // Deliberate fire and forget
            InitMongoDbIndexes(defaultConfig);
#pragma warning restore 4014
        }
    }

    if (datacenterId == "tcb-aws")
    {
        if (databaseDriver == "mongoDb")
        {
            var defaultConfig = new DbConfig { ProviderId = "mongoDb" };
            defaultConfig.Load<Program>();
            DbConfig.AddConfig("default", defaultConfig);
            MongoTools.GetOrAddMongoClient(defaultConfig);
            InitMongoDbIndexes(defaultConfig);
        }
    }

    var startup = new Startup(webAppBuilder.Configuration, webAppBuilder.Environment);
    startup.ConfigureServices(webAppBuilder.Services);

    webAppBuilder.Host.UseSerilog((context, services, configuration) =>
    {
        configuration
            .MinimumLevel.Override("Microsoft.AspNetCore", LogEventLevel.Warning)
            .ReadFrom.Configuration(context.Configuration)
            .ReadFrom.Services(services)
            .Enrich.FromLogContext();
    });

    var app = webAppBuilder.Build();
    startup.Configure(app, webAppBuilder.Environment);
    await app.RunAsync();
}
catch (Exception startupException)
{
    Log.Logger.Fatal(startupException, "Uncatched exception in the server");
    throw;
}
finally
{
    Log.CloseAndFlush();
}

public partial class Program
{
    public const string AppName = "covergo-products";

    internal static bool UseBootstrapLogger { get; set; } = true;

    private static void InitMongoDbIndexes(DbConfig covergoDbConfig)
    {
        MongoTools.IndexingAsync(covergoDbConfig, "products", $"/^(?=.*-)((?!_events|types|insurers).)*$/", ProductIndices());
        MongoTools.IndexingAsync(covergoDbConfig, "products", $"-types$/", TypeIndices());
        MongoTools.IndexingAsync(covergoDbConfig, "products", $"/-insurers$/", InsurerIndices());
    }

    private static List<CreateIndexModel<BsonDocument>> ProductIndices() => new List<CreateIndexModel<BsonDocument>>
        {
            new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Ascending("_id")),
            new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Ascending("productId")),
            new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Ascending("productId.type").Ascending("productId.version")),
            new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Ascending("productId.version")),
            new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Ascending("productId.plan")),
            new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Descending("lastModifiedAt")),
        };

    private static List<CreateIndexModel<BsonDocument>> TypeIndices() => new List<CreateIndexModel<BsonDocument>>
        {
            new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Ascending("_id")),
            new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Ascending("typeId")),
            new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Descending("createdAt")),
            new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Descending("lastModifiedAt")),
        };
    private static List<CreateIndexModel<BsonDocument>> InsurerIndices() => new List<CreateIndexModel<BsonDocument>>
        {
            new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Ascending("_id")),
            new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Ascending("insurerId")),
            new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Descending("createdAt")),
            new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Descending("lastModifiedAt")),
        };
}
