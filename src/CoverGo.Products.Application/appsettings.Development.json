{"serviceUrls": {"scripts": "http://localhost:61111/", "filesystem": "http://localhost:61872/", "auth": "http://localhost:60000/", "users": "http://localhost:60010/", "productbuilder": "http://localhost:60180/", "l10n": "http://localhost:60040/", "reference": "http://localhost:61910/", "channelManagement": "http://localhost:64483/", "gateway": "http://localhost:60060/", "templates": "http://localhost:63542/"}, "ConnectionStrings": {"mongo": "**********************************************************************"}, "EventStore": {"ConnectionString": "esdb+discover://admin:<EMAIL>:2113?tls=true", "ProviderId": "pb"}, "FeatureManagement": {"QueryActiveNetworks": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["coverHealth_dev", "walaa_dev"]}}]}}, "UseSentry": false, "GraphQL": {"IncludeExceptionDetails": true}}