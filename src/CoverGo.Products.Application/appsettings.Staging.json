{"Serilog": {"WriteTo": [{"Args": {"formatter": "Serilog.Formatting.Compact.RenderedCompactJsonFormatter, Serilog.Formatting.Compact"}}]}, "serviceUrls": {"filesystem": "http://covergo-filesystem:8080", "logging": "http://covergo-logging:9200/", "scripts": "http://covergo-scripts:8080/", "auth": "http://covergo-auth:8080/", "users": "http://covergo-users:8080/", "productbuilder": "http://covergo-product-builder:80/", "l10n": "http://covergo-l10n:8080/", "reference": "http://covergo-reference/", "channelManagement": "http://covergo-channel-management/", "claims2": "http://covergo-claims2:3000", "templates": "http://covergo-templates:8080/"}, "UseSentry": false}