using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Applications.Monitoring;
using CoverGo.Applications.Startup;
using CoverGo.Auth.Client;
using CoverGo.BuildingBlocks.Auth.Extensions;
using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.Configuration;
using CoverGo.FileSystem.Client;
using CoverGo.L10n.Client;
using CoverGo.MongoUtils;
using CoverGo.Multitenancy.AspNetCore;
using CoverGo.Products.Application.GraphQl;
using CoverGo.Products.Domain;
using CoverGo.Products.Domain.Auth;
using CoverGo.Products.Domain.BenefitDefinitions;
using CoverGo.Products.Domain.BenefitDefinitionTypes;
using CoverGo.Products.Domain.DiscountCodes;
using CoverGo.Products.Domain.Illustrations;
using CoverGo.Products.Domain.Insurers;
using CoverGo.Products.Domain.Jackets.EventStores;
using CoverGo.Products.Domain.Jackets.Services;
using CoverGo.Products.Domain.ProductImportHistory;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.Commands;
using CoverGo.Products.Domain.Products.Ports;
using CoverGo.Products.Domain.Products.ProductTreeParsing;
using CoverGo.Products.Domain.Sbus;
using CoverGo.Products.Domain.ScriptEvaluation;
using CoverGo.Products.Domain.Scripts;
using CoverGo.Products.Domain.Templates;
using CoverGo.Products.Domain.UiSchemas;
using CoverGo.Products.Domain.Underwriting;
using CoverGo.Products.Infrastructure.Auth;
using CoverGo.Products.Infrastructure.Auth.Adapters;
using CoverGo.Products.Infrastructure.BenefitDefinitions.Adapters.Mongo;
using CoverGo.Products.Infrastructure.BenefitDefinitionTypes.Adapters.Mongo;
using CoverGo.Products.Infrastructure.ChannelManagement;
using CoverGo.Products.Infrastructure.Claims2;
using CoverGo.Products.Infrastructure.DiscountCodes.Adapters.Mongo;
using CoverGo.Products.Infrastructure.Illustrations.Adapters.EBao;
using CoverGo.Products.Infrastructure.Insurers.Adapters.Mongo;
using CoverGo.Products.Infrastructure.Jackets.Adapters.Mongo;
using CoverGo.Products.Infrastructure.ProductImportHistory.Adapters.Mongo;
using CoverGo.Products.Infrastructure.Products;
using CoverGo.Products.Infrastructure.Products.Adapters;
using CoverGo.Products.Infrastructure.Products.Adapters.EventStore;
using CoverGo.Products.Infrastructure.Products.Adapters.Mongo;
using CoverGo.Products.Infrastructure.Sbus.Adapters.Mongo;
using CoverGo.Products.Infrastructure.Scripts.Adapters.Evaluation;
using CoverGo.Products.Infrastructure.Scripts.Adapters.Mongo;
using CoverGo.Products.Infrastructure.Templates;
using CoverGo.Products.Infrastructure.UiSchemas.Adapters.Mongo;
using CoverGo.Products.Infrastructure.Underwriting.Adapters.EBao;
using CoverGo.Products.Infrastructure.Underwriting.Adapters.JsonLogic;
using CoverGo.Products.Infrastructure.Utils.Adapters.Mongo;
using CoverGo.Proxies.Auth;
using CoverGo.Quotation.Infrastructure.ProductsClient;
using CoverGo.Scripts.Client;
using CoverGo.Users.Client;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Net.Http.Headers;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;

using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.IdGenerators;
using MongoDB.Bson.Serialization.Serializers;
using MongoDB.Driver;

using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

using Sentry;

using System;
using System.Diagnostics;
using System.Linq;
using ProductId = CoverGo.Products.Domain.Products.ProductId;
using CoverGo.Templates.Client;
using CoverGo.BuildingBlocks.Application.Core;

namespace CoverGo.Products.Application
{
    public class Startup
    {
        private readonly TimeSpan DefaultHttpClientTimeout = TimeSpan.FromMinutes(1);
        private static JTokenSerializer _jtokenSerializer = new();

        public Startup(IConfiguration configuration, IWebHostEnvironment environment)
        {
            Configuration = configuration;
            Environment = environment;
        }

        public IConfiguration Configuration { get; }
        public IWebHostEnvironment Environment { get; }

        public void ConfigureServices(IServiceCollection services)
        {
            services.AddMultiTenantFeatureManagement(Configuration);

            services
                .AddControllers()
                .AddNewtonsoftJson(
                    options =>
                    {
                        options.SerializerSettings.Converters.Add(new StringEnumConverter());
                        options.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
                        options.SerializerSettings.DateTimeZoneHandling = DateTimeZoneHandling.Utc;

                        if (Environment.IsDevelopment())
                            options.SerializerSettings.Formatting = Formatting.Indented;
                    }
                );

            services.AddHttpContextAccessor();
            services.AddCoverGoCurrentUser();

            services.AddHeaderPropagation(options =>
            {
                options.Headers.Add("Authorization");
                options.Headers.Add("Tenant");
            });

            services.AddMemoryCache();
            services.AddScoped<ProductService>();
            services.AddScoped<ScriptService>();
            services.AddScoped<BenefitDefinitionService>();
            services.AddScoped<BenefitDefinitionTypeService>();
            services.AddSingleton<SbuService>();

            services.AddScoped<ProductExchangeService>();
            services.AddSingleton<IExternalTablesAdapter, ExternalTablesAdapter>();
            services.AddSingleton<IProductExchangeAdapter, ProductExchangeAdapter>();
            services.AddSingleton<IProductBuilderAdapter, ProductBuilderAdapter>();
            services.AddSingleton<IDateTimeProvider, DateTimeProvider>();
            var pbClient = services
                .AddHttpClient(nameof(ProductBuilderAdapter), (serviceProvider, client) =>
                {
                    client.BaseAddress = new Uri($"{Configuration["serviceUrls:productbuilder"]}");
                    HttpContext context = serviceProvider.GetRequiredService<IHttpContextAccessor>()?.HttpContext;
                    if (context == null) return;
                    if (context.Request.Headers.TryGetValue(HeaderNames.Authorization, out var value))
                        client.DefaultRequestHeaders.TryAddWithoutValidation(HeaderNames.Authorization, value.ToArray());
                });
            services.AddSingleton<IProductL10NAdapter, ProductL10NAdapter>();
            var l10nClient = services.AddHttpClient<IL10nClient, L10nClient>(c => c.BaseAddress = new Uri($"{Configuration["serviceUrls:l10n"]}"));
            services.AddSingleton<IAuthClientFactory, AuthClientFactory>();
            var authClient = services.AddHttpClient(nameof(AuthClient));
            services.AddSingleton<IAuthAdapter, AuthAdapter>();

            services.AddHttpClient<IUsersClient, UsersClient>(
                c => c.BaseAddress = new($"{Configuration["serviceUrls:users"]}"));

            services.AddReferenceGraphQlClient().ConfigureHttpClient(client => client.BaseAddress = new UriBuilder(Configuration["serviceUrls:reference"]) { Path = "/graphql" }.Uri, clientBuilder => clientBuilder.AddHeaderPropagation());

            var cmServiceUrl = new UriBuilder(Configuration["serviceUrls:channelManagement"] ?? "http://localhost:64483/") { Path = "/graphql" }.Uri; ;
            services.AddChannelManagementClient()
                .ConfigureHttpClient(client => client.BaseAddress = cmServiceUrl, clientBuilder => clientBuilder.AddHeaderPropagation());
            services.AddSingleton<IChannelManagementAdapter, ChannelManagementAdapter>();

            services.AddSingleton<Claims2Adapter>();
            services.AddHttpClient<Claims2Adapter>(c => c.BaseAddress = new Uri(Configuration["serviceUrls:claims2"] ?? "https://api.claims2.dev.asia.covergo.cloud/v1/")).AddHeaderPropagation();

            services.AddSingleton<IIllustrationRepository, EBaoIllustrationRepository>();
            services.AddSingleton<IUnderwritingEngine, EBaoUnderwritingEngine>();
            services.AddScoped<IUnderwritingEngine, JsonLogicUnderwritingEngine>();

            services.Configure<EventStoreSettings>(Configuration.GetSection("EventStore"));

            services.AddScoped<ProductEventService>();
            services.AddScoped<ProductEventLogsService>();
            services.AddScoped<InsurerService>();
            services.AddScoped<InsurerEventService>();

            services.AddScoped<ScriptEvaluationService>();
            services.AddSingleton<IScriptsServiceClient, ScriptsServiceClient>();
            var scriptsClient = services.AddHttpClient<IScriptAdapter, ScriptAdapter>(c =>
            {
                c.Timeout = TimeSpan.TryParse(Configuration["timeOuts:scripts"], out var timeOut)
                    ? timeOut
                    : DefaultHttpClientTimeout;
                c.BaseAddress = new Uri($"{Configuration["serviceUrls:scripts"]}");
            })
            .ConfigurePrimaryHttpMessageHandler(serviceProvider => new System.Net.Http.HttpClientHandler
            {
                AutomaticDecompression = System.Net.DecompressionMethods.GZip
                    | System.Net.DecompressionMethods.Deflate
                    | System.Net.DecompressionMethods.Brotli
            });

            services.AddScoped<IJacketService, JacketService>();
            services.AddScoped<IUiSchemaService, UiSchemaService>();

            var eventStoreSettings = Configuration.GetSection("EventStore").Get<EventStoreSettings>();

            services.AddScoped<IEventStoreClientAdapter, EventStoreClientAdapter>(s => new EventStoreClientAdapter(eventStoreSettings));
            services.AddScoped<IEventStoreClientFactory, EventStoreClientFactory>();
            services.AddScoped<IProductBuilderEventStore, ProductBuilderEventStore>(s => new ProductBuilderEventStore(s.GetService<IEventStoreClientFactory>(), eventStoreSettings, s.GetService<ILogger<ProductBuilderEventStore>>()));

            var fileSystemClient = services.AddHttpClient<IFileSystemClient, FileSystemClient>(c => c.BaseAddress = new Uri($"{Configuration["serviceUrls:filesystem"]}"));

            services.AddSingleton<IAuthService, CoverGoAuthService>();
            services.AddHttpClient<IAuthService, CoverGoAuthService>(c => c.BaseAddress = new Uri($"{Configuration["serviceUrls:auth"]}"));
            services.AddScoped<IPermissionValidator, PermissionValidator>();

            services.AddHttpClient<ITemplatesClient, TemplatesClient>(c => c.BaseAddress = new Uri(Configuration["serviceUrls:templates"]));
            services.AddTransient<ITemplatesAdapter, TemplatesAdapter>();

            var useSentry = Configuration.GetValue<bool>("UseSentry");
            if (useSentry)
            {
                services.AddTransient<SentryHttpMessageHandler>();
                pbClient.AddHttpMessageHandler<SentryHttpMessageHandler>();
                l10nClient.AddHttpMessageHandler<SentryHttpMessageHandler>();
                authClient.AddHttpMessageHandler<SentryHttpMessageHandler>();
                scriptsClient.AddHttpMessageHandler<SentryHttpMessageHandler>();
                fileSystemClient.AddHttpMessageHandler<SentryHttpMessageHandler>();
            }

            if (DbConfig.GetConfigs().Any(c => c.ProviderId == "mongoDb"))
            {
                services.AddSingleton<MongoProductRepository>();
                services.AddSingleton<MongoDbReferenceGenerator>();
                services.AddSingleton<MongoScriptRepository>();
                services.AddSingleton<MongoBenefitDefinitionTypeRepository>();
                services.AddSingleton<MongoBenefitDefinitionRepository>();
                services.AddSingleton<ISbuRepository, MongoSbuRepository>();
                services.AddSingleton<ISbuService, SbuService>();
                services.AddSingleton<MongoProductEventStore>();
                services.AddSingleton<IDiscountCodeRepository, MongoDiscountCodeRepository>();
                services.AddSingleton<IDiscountCodeService, DiscountCodeService>();

                services.AddSingleton<IProductImportHistoryRepository, MongoProductImportHistoryRepository>();
                services.AddSingleton<IProductImportHistoryService, ProductImportHistoryService>();

                services.AddSingleton<MongoInsurerRepository>();
                services.AddSingleton<MongoInsurerEventStore>();

                services.AddSingleton<MongoDbJacketRepository>();
                services.AddSingleton<MongoDbJacketEventStore>();

                services.AddSingleton<MongoUiSchemaRepository>();

                services.AddSingleton<MongoProductImportHistoryRepository>();

                MongoSetup.RegisterConventions();

                BsonClassMap.TryRegisterClassMap<ProductId>(cm => { cm.AutoMap(); cm.MapMember(i => i.Version).SetIgnoreIfNull(false); });

                BsonClassMap.TryRegisterClassMap<ProductEvent>(cm =>
                {
                    cm.AutoMap();
                    cm.MapIdProperty(c => c.Id)
                        .SetIdGenerator(StringObjectIdGenerator.Instance)
                        .SetSerializer(new StringSerializer(BsonType.ObjectId));
                });

                BsonClassMap.TryRegisterClassMap<ProductTypeEvent>(cm =>
                {
                    cm.AutoMap();
                    cm.MapIdProperty(c => c.Id)
                        .SetIdGenerator(StringObjectIdGenerator.Instance)
                        .SetSerializer(new StringSerializer(BsonType.ObjectId));
                });

                BsonClassMap.TryRegisterClassMap<InsurerEvent>(cm =>
                {
                    cm.AutoMap();
                    cm.MapIdProperty(c => c.Id)
                        .SetIdGenerator(StringObjectIdGenerator.Instance)
                        .SetSerializer(new StringSerializer(BsonType.ObjectId));
                });

                BsonClassMap.TryRegisterClassMap<MongoProductDao2>(cm =>
                {
                    cm.AutoMap();
                    cm.MapIdProperty(c => c.Id)
                        .SetIdGenerator(StringObjectIdGenerator.Instance)
                        .SetSerializer(new StringSerializer(BsonType.ObjectId));
                });

                BsonClassMap.TryRegisterClassMap<JacketEvent>(cm =>
                {
                    cm.AutoMap();
                    cm.MapIdProperty(c => c.Id)
                        .SetIdGenerator(StringObjectIdGenerator.Instance)
                        .SetSerializer(new StringSerializer(BsonType.ObjectId));
                });

                BsonClassMap.TryRegisterClassMap<ProductConfig>(cm =>
                {
                    cm.AutoMap();
                    cm.MapIdProperty(c => c.Id)
                        .SetIdGenerator(StringObjectIdGenerator.Instance)
                        .SetSerializer(new StringSerializer(BsonType.ObjectId));
                });

                BsonClassMap.TryRegisterClassMap<ProductImportHistoryRecord>(cm =>
                {
                    cm.AutoMap();
                    cm.MapIdProperty(c => c.Id)
                        .SetIdGenerator(StringObjectIdGenerator.Instance)
                        .SetSerializer(new StringSerializer(BsonType.ObjectId));
                });

                BsonSerializer.TryRegisterSerializer(_jtokenSerializer);
                TryRegisterTimeSpanBsonMapper();
            }

            services.AddSwaggerGen(options =>
            {
                options.SwaggerDoc("v1", new OpenApiInfo { Title = "CoverGo Products", Version = "v1" });
                options.CustomSchemaIds(type => type.ToString());
                options.MapType<TimeSpan>(() => new OpenApiSchema
                {
                    Type = "string",
                    Example = new OpenApiString("00:00:00")
                });

                options.MapType<TimeSpan?>(() => new OpenApiSchema
                {
                    Type = "string",
                    Example = new OpenApiString("00:00:00")
                });
            });

            services.AddHostedService<ApplicationLifetimeService>();
            services.Configure<HostOptions>(opts => opts.ShutdownTimeout = TimeSpan.FromSeconds(45));

            AppContext.SetSwitch("System.Net.Http.SocketsHttpHandler.Http2UnencryptedSupport", true); // Needed this if .Net <5.0
            services.AddCoverGoOpenTelemetryTracingIfEnabled();
            services.AddCoverGoAuthorization(Configuration);

            var graphqlConfiguration =
                Configuration.GetRequiredSection("GraphQL").Get<GraphQLOptions>()
                ?? new();
            services.AddProductsGraphQLSchema()
                .ModifyRequestOptions(options =>
                {
                    if (!Debugger.IsAttached)
                    {
                        options.ExecutionTimeout = TimeSpan.FromMinutes(2);
                    }
                    options.IncludeExceptionDetails = graphqlConfiguration.IncludeExceptionDetails;
                });
            services.AddCoverGoApplicationMonitoring();

            services.AddMediatR(delegate (MediatRServiceConfiguration configuration)
            {
                configuration.RegisterServicesFromAssemblies([
                    typeof(CloneProductVersionCommandHandler).Assembly,
                    typeof(ProductClonedHandler).Assembly,
                    typeof(PlanDetailsQueryHandler).Assembly
                ]);
            });

            services.AddGatewayClient(Configuration["serviceUrls:gateway"] + "graphql")
                .AddHeaderPropagation();

            services
                .AddMultitenancy()
                .AddTenantProvider<FromClaimTenantProvider>()
                .AddTenantProvider<FromHeaderTenantProvider>()
                .AddTenantProvider<FromRouteTenantProvider>();

            services.AddUserContextProviders();

            services.AddProductsInfrastructure();
        }

        private static void TryRegisterTimeSpanBsonMapper()
        {
            try
            {
                BsonTypeMapper.RegisterCustomTypeMapper(typeof(TimeSpan), new TimeSpanBsonMapper());
            }
            catch (ArgumentException _)
            {
                //This line fails for some of the integrated tests and there's no TryAddMethod
            }
        }

        public void Configure(
            IApplicationBuilder app,
            IWebHostEnvironment env)
        {
            app.UseHeaderPropagation();

            app.UseCoverGoApplicationMonitoring();
            app.UseCoverGoMetrics();

            var useSentry = Configuration.GetValue<bool>("UseSentry");

            if (env.IsDevelopment())
                app.UseDeveloperExceptionPage();

            app.UseSwagger();
            app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "CoverGo Products v1"));

            app.UseLogs();

            app.UseCoverGoGraphQl();

            if (useSentry)
            {
                app.UseSentryTracing();
            }

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                endpoints.MapVersionEndpoint();
            });
        }
    }
}
