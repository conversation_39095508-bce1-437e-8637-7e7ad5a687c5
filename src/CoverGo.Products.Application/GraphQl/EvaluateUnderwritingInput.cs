﻿using CoverGo.Scripts.Client;
using HotChocolate;
using HotChocolate.Types;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using ProductId = CoverGo.Products.Domain.Products.ProductId;

namespace CoverGo.Products.Application.GraphQl;

public class EvaluateUnderwritingInput
{
    public ProductId ProductId { get; set; }
    public string DataInput { get; set; }
    public List<InsuredUnderwritingGraphqlInput> InsuredUnderwritingInputs { get; set; }
    [GraphQLNonNullType]
    public UnderwritingModes Modes { get; set; }
}

[GraphQLName("InsuredUnderwritingInput")]
public class InsuredUnderwritingGraphqlInput
{
    public string Id { get; set; }
    public string Type { get; set; }

    public DateOnly? DateOfBirth { get; set; }

    public HealthQuestionnaireResponseInput InsuredSimplifiedMedicalUnderwritingResponse { get; set; }

    public NonMedicalMaterialFactsInput InsuredNonMedicalMaterialFacts { get; set; }

    [GraphQLType(typeof(AnyType))]
    public Dictionary<string, object> Fields { get; set; }

    [GraphQLType(typeof(AnyType))]
    public Dictionary<string, object> Meta { get; set; }
}

public class HealthQuestionnaireResponseInput
{
    [JsonProperty("template", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
    public Template Template { get; set; }

    [GraphQLType(typeof(AnyType))]
    public Dictionary<string, object> Answers { get; set; }
}

public class NonMedicalMaterialFactsInput
{
    public string Id { get; set; }

    [GraphQLType(typeof(AnyType))]
    public Dictionary<string, object> Meta { get; set; }
}

