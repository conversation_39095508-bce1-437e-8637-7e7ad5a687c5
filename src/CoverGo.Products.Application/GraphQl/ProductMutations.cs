using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.BuildingBlocks.Application.Core.Ports;
using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.Commands;

using HotChocolate;
using HotChocolate.Types;

namespace CoverGo.Products.Application.GraphQl;

[ExtendObjectType(CoverGoGraphQl.MutationsRoot)]
public class ProductMutation
{
    private readonly ProductService _productService;

    public ProductMutation(ProductService productService)
    {
        _productService = productService;
    }

    [GraphQLName("SetTermsAndConditions")]
    public async Task<Result> SetTermsAndConditions(
        [GlobalState] string tenantId,
        [GlobalState] ClaimsIdentity identity,
        [Service] IPermissionValidator permissionValidator,
        [Service] IUserContextProvider userContextProvider,
        SetTermsAndConditionsCommand input,
        CancellationToken cancellationToken)
    {
        await permissionValidator.AuthorizeAsync(identity, new PermissionRequest("writeProducts"));
        input.SetById = userContextProvider.GetUserId();
        Result result = await _productService.SetTermsAndConditionsTemplateToProduct(tenantId, input, cancellationToken);

        return result;
    }

    [GraphQLName("RemoveTermsAndConditions")]
    public async Task<Result> RemoveTermsAndConditions(
        [GlobalState] string tenantId,
        [GlobalState] ClaimsIdentity identity,
        [Service] IPermissionValidator permissionValidator,
        [Service] IUserContextProvider userContextProvider,
        RemoveTermsAndConditionsCommand input,
        CancellationToken cancellationToken)
    {
        await permissionValidator.AuthorizeAsync(identity, new PermissionRequest("writeProducts"));
        input.RemovedById = userContextProvider.GetUserId();
        Result result = await _productService.RemoveTermsAndConditionsTemplateFromProduct(tenantId, input, cancellationToken);

        return result;
    }

    [GraphQLName("SetRatingFactorsTable")]
    public async Task<Result> SetRatingFactorsTable(
        [GlobalState] string tenantId,
        [GlobalState] ClaimsIdentity identity,
        [Service] IPermissionValidator permissionValidator,
        [Service] IUserContextProvider userContextProvider,
        SetRatingFactorsTableCommand input,
        CancellationToken cancellationToken)
    {
        await permissionValidator.AuthorizeAsync(identity, new PermissionRequest("writeProducts"));
        input.SetById = userContextProvider.GetUserId();
        Result result = await _productService.SetRatingFactorsTableToProduct(tenantId, input, cancellationToken);

        return result;
    }

    [GraphQLName("RemoveRatingFactorsTable")]
    public async Task<Result> RemoveRatingFactorsTable(
        [GlobalState] string tenantId,
        [GlobalState] ClaimsIdentity identity,
        [Service] IPermissionValidator permissionValidator,
        [Service] IUserContextProvider userContextProvider,
        RemoveRatingFactorsTableCommand input,
        CancellationToken cancellationToken)
    {
        await permissionValidator.AuthorizeAsync(identity, new PermissionRequest("writeProducts"));
        input.RemovedById = userContextProvider.GetUserId();
        Result result = await _productService.RemoveRatingFactorsTableFromProduct(tenantId, input, cancellationToken);

        return result;
    }

    [GraphQLName("SetSegments")]
    public async Task<Result> SetSegments(
        [GlobalState] string tenantId,
        [GlobalState] ClaimsIdentity identity,
        [Service] IPermissionValidator permissionValidator,
        [Service] IUserContextProvider userContextProvider,
        SetSegmentsCommand input,
        CancellationToken cancellationToken)
    {
        await permissionValidator.AuthorizeAsync(identity, new PermissionRequest("writeProducts"));
        input.SetById = userContextProvider.GetUserId();
        Result result = await _productService.SetSegmentsToProduct(tenantId, input, cancellationToken);

        return result;
    }

    [GraphQLName("AttachDocuments")]
    public async Task<Result> AttachDocuments(
        [GlobalState] string tenantId,
        [Service] IPermissionValidator permissionValidator,
        [GlobalState] ClaimsIdentity identity,
        [Service] IUserContextProvider userContextProvider,
        AttachDocumentsCommand input,
        CancellationToken cancellationToken)
    {
        await permissionValidator.AuthorizeAsync(identity, new PermissionRequest("writeProducts"));
        input.SetById = userContextProvider.GetUserId();
        Result result = await _productService.AttachDocumentsToProduct(tenantId, input, cancellationToken);
        return result;
    }

    [GraphQLName("RemoveAttachedDocument")]
    public async Task<Result> RemoveDocument(
        [GlobalState] string tenantId,
        [Service] IPermissionValidator permissionValidator,
        [GlobalState] ClaimsIdentity identity,
        [Service] IUserContextProvider userContextProvider,
        RemoveAttachedDocumentCommand input,
        CancellationToken cancellationToken)
    {
        await permissionValidator.AuthorizeAsync(identity, new PermissionRequest("writeProducts"));
        input.RemovedById = userContextProvider.GetUserId();
        Result result = await _productService.RemoveAttachedDocument(tenantId, input, cancellationToken);
        return result;
    }
}
