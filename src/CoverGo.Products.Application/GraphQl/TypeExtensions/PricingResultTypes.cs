﻿using CoverGo.Scripts.Client;
using HotChocolate.Types;
using System.Linq;

namespace CoverGo.Products.Application.GraphQl.TypeExtensions;

public class PricingDataType : ObjectType<PricingData>
{
    protected override void Configure(
        IObjectTypeDescriptor<PricingData> descriptor)
    {
        descriptor.Name("products_PricingData");
        descriptor.BindFieldsImplicitly();
        descriptor.Field(p => p.Meta)
            .Type<AnyType>()
            .Resolve(p => p.Parent<PricingData>()?.Meta?.AdditionalProperties);
    }
}

public class ModeType : ObjectType<Mode>
{
    protected override void Configure(
        IObjectTypeDescriptor<Mode> descriptor)
    {
        descriptor.BindFieldsImplicitly();
        descriptor.Field(p => p.Meta)
            .Type<AnyType>()
            .Resolve(p => p.Parent<Mode>()?.Meta?.AdditionalProperties);
    }
}

public class ModePropertiesType : ObjectType<ModeProperties>
{
    protected override void Configure(
        IObjectTypeDescriptor<ModeProperties> descriptor)
    {
        descriptor.BindFieldsImplicitly();
        descriptor.Field(p => p.Meta)
            .Type<AnyType>()
            .Resolve(p => p.Parent<ModeProperties>()?.Meta?.AdditionalProperties);
    }
}

public class InsuredType : ObjectType<Insured>
{
    protected override void Configure(
        IObjectTypeDescriptor<Insured> descriptor)
    {
        descriptor.BindFieldsImplicitly();
        descriptor.Field(p => p.Meta)
            .Type<AnyType>()
            .Resolve(p => p.Parent<Insured>()?.Meta?.AdditionalProperties);
    }
}

public class BenefitType : ObjectType<Benefit>
{
    protected override void Configure(
        IObjectTypeDescriptor<Benefit> descriptor)
    {
        descriptor.BindFieldsImplicitly();
        descriptor.Field(p => p.Meta)
            .Type<AnyType>()
            .Resolve(p => p.Parent<Benefit>()?.Meta?.AdditionalProperties);
    }
}

public class ScriptEvalutationErrorType : InterfaceType<ScriptEvaluationError>
{
    protected override void Configure(
        IInterfaceTypeDescriptor<ScriptEvaluationError> descriptor)
    {
    }
}

public class ScriptExecutionErrorType : ObjectType<ScriptExecutionError>
{
    protected override void Configure(
        IObjectTypeDescriptor<ScriptExecutionError> descriptor)
    {
        descriptor.Implements<ScriptEvalutationErrorType>();
        descriptor.Field(f => f.Message).Resolve(r => r.Parent<ScriptExecutionError>().Message ?? $"Type: {r.Parent<ScriptExecutionError>().Details.ErrorType}, Message: {r.Parent<ScriptExecutionError>().Details.ErrorMessage}, Trace: {r.Parent<ScriptExecutionError>().Details.Trace}");
        descriptor.Field(f => f.Type).Resolve(r => ScriptEvaluationErrorType.SCRIPT_ERROR);
    }
}

public class InputDataValidationErrorType : ObjectType<InputDataValidationError>
{
    protected override void Configure(
       IObjectTypeDescriptor<InputDataValidationError> descriptor)
    {
        descriptor.Implements<ScriptEvalutationErrorType>();
        descriptor.Field(f => f.Message).Resolve(r => r.Parent<InputDataValidationError>().Message ?? string.Join(',', r.Parent<InputDataValidationError>().Issues.Select(i => $"Code:{i.Code}, Message:{i.Message}, Path:{ string.Join('.',i.Path)}")));
        descriptor.Field(f => f.Type).Resolve(r => ScriptEvaluationErrorType.INPUT_DATA_VALIDATION_ERROR);
    }
}

public class IssueType : InterfaceType<Issue>
{
    protected override void Configure(
        IInterfaceTypeDescriptor<Issue> descriptor)
    {
    }
}

public class InvalidTypeIssueType : ObjectType<InvalidTypeIssue>
{
    protected override void Configure(
       IObjectTypeDescriptor<InvalidTypeIssue> descriptor)
    {
        descriptor.Implements<IssueType>();
    }
}

public class InvalidLiteralIssueType : ObjectType<InvalidLiteralIssue>
{
    protected override void Configure(
       IObjectTypeDescriptor<InvalidLiteralIssue> descriptor)
    {
        descriptor.Implements<IssueType>();
    }
}

public class CustomIssueType : ObjectType<CustomIssue>
{
    protected override void Configure(
       IObjectTypeDescriptor<CustomIssue> descriptor)
    {
        descriptor.Implements<IssueType>();
    }
}

public class InvalidUnionDiscriminatorIssueType : ObjectType<InvalidUnionDiscriminatorIssue>
{
    protected override void Configure(
       IObjectTypeDescriptor<InvalidUnionDiscriminatorIssue> descriptor)
    {
        descriptor.Implements<IssueType>();
    }
}

public class InvalidEnumValueIssueType : ObjectType<InvalidEnumValueIssue>
{
    protected override void Configure(
       IObjectTypeDescriptor<InvalidEnumValueIssue> descriptor)
    {
        descriptor.Implements<IssueType>();
    }
}

public class UnrecognizedKeysIssueType : ObjectType<UnrecognizedKeysIssue>
{
    protected override void Configure(
       IObjectTypeDescriptor<UnrecognizedKeysIssue> descriptor)
    {
        descriptor.Implements<IssueType>();
    }
}

public class InvalidStringIssueType : ObjectType<InvalidStringIssue>
{
    protected override void Configure(
       IObjectTypeDescriptor<InvalidStringIssue> descriptor)
    {
        descriptor.Implements<IssueType>();
    }
}

public class TooBigIssueIssueType : ObjectType<TooBigIssue>
{
    protected override void Configure(
       IObjectTypeDescriptor<TooBigIssue> descriptor)
    {
        descriptor.Implements<IssueType>();
    }
}

public class TooSmallIssueType : ObjectType<TooSmallIssue>
{
    protected override void Configure(
       IObjectTypeDescriptor<TooSmallIssue> descriptor)
    {
        descriptor.Implements<IssueType>();
    }
}

public class NotMultipleOfIssueType : ObjectType<NotMultipleOfIssue>
{
    protected override void Configure(
       IObjectTypeDescriptor<NotMultipleOfIssue> descriptor)
    {
        descriptor.Implements<IssueType>();
    }
}