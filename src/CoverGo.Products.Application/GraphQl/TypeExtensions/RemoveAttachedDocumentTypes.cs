using CoverGo.Products.Domain.Products.Commands;
using HotChocolate.Types;

namespace CoverGo.Products.Application.GraphQl.TypeExtensions;

public class RemoveAttachedDocumentCommandType : InputObjectType<RemoveAttachedDocumentCommand>
{
	protected override void Configure(
			IInputObjectTypeDescriptor<RemoveAttachedDocumentCommand> descriptor)
	{
		descriptor.Name("products_RemoveAttachedDocumentInput");
		descriptor.BindFieldsImplicitly();
		descriptor.Ignore(p => p.RemovedById);
	}
}