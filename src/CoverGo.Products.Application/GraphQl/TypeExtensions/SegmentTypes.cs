using CoverGo.Products.Domain.Products.Commands;
using HotChocolate.Types;

namespace CoverGo.Products.Application.GraphQl.TypeExtensions;

public class SetSegmentsCommandType : InputObjectType<SetSegmentsCommand>
{
	protected override void Configure(
			IInputObjectTypeDescriptor<SetSegmentsCommand> descriptor)
	{
		descriptor.Name("products_SetSegmentsInput");
		descriptor.BindFieldsImplicitly();
		descriptor.Ignore(p => p.SetById);
	}
}