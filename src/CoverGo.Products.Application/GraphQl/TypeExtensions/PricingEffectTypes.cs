using CoverGo.Scripts.Client;
using HotChocolate.Types;

namespace CoverGo.Products.Application.GraphQl.TypeExtensions;

public class PricingEffectType : InterfaceType<PricingEffect>
{
    protected override void Configure(
        IInterfaceTypeDescriptor<PricingEffect> descriptor)
    {
        descriptor.Field(p => p.Meta)
            .Type<AnyType>();
    }
}

public class LoadingEffectType : ObjectType<LoadingEffect>
{
    protected override void Configure(
        IObjectTypeDescriptor<LoadingEffect> descriptor)
    {
        descriptor.Implements<PricingEffectType>();
        descriptor.Field(p => p.Meta)
            .Type<AnyType>()
            .Resolve(p => p.Parent<LoadingEffect>()?.Meta?.AdditionalProperties);
    }
}

public class FactorEffectType : ObjectType<FactorEffect>
{
    protected override void Configure(
        IObjectTypeDescriptor<FactorEffect> descriptor)
    {
        descriptor.Implements<PricingEffectType>();
        descriptor.Field(f => f.Value).Type<AdjustmentValueFlatFactorType>();
        descriptor.Field(p => p.Meta)
            .Type<AnyType>()
            .Resolve(p => p.Parent<FactorEffect>()?.Meta?.AdditionalProperties);
    }
}

public class DiscountEffectType : ObjectType<DiscountEffect>
{
    protected override void Configure(
        IObjectTypeDescriptor<DiscountEffect> descriptor)
    {
        descriptor.Implements<PricingEffectType>();
        descriptor.Field(f => f.Value).Type<AdjustmentValueFlatFactorType>();
        descriptor.Field(p => p.Meta)
            .Type<AnyType>()
            .Resolve(p => p.Parent<DiscountEffect>()?.Meta?.AdditionalProperties);
    }
}

public class FeeEffectType : ObjectType<FeeEffect>
{
    protected override void Configure(
        IObjectTypeDescriptor<FeeEffect> descriptor)
    {
        descriptor.Implements<PricingEffectType>();
        descriptor.Field(f => f.Value).Type<AdjustmentValueFlatFactorType>();
        descriptor.Field(p => p.Meta)
            .Type<AnyType>()
            .Resolve(p => p.Parent<FeeEffect>()?.Meta?.AdditionalProperties);
    }
}

public class TaxEffectType : ObjectType<TaxEffect>
{
    protected override void Configure(
        IObjectTypeDescriptor<TaxEffect> descriptor)
    {
        descriptor.BindFieldsImplicitly();
        descriptor.Field(f => f.Value).Type<AdjustmentValueFlatFactorPercentageType>();
    }
}

public class RoundEffectType : ObjectType<RoundEffect>
{
    protected override void Configure(
        IObjectTypeDescriptor<RoundEffect> descriptor)
    {
        descriptor.Implements<PricingEffectType>();
        descriptor.Field(p => p.Meta)
        .Type<AnyType>()
        .Resolve(p => p.Parent<RoundEffect>()?.Meta?.AdditionalProperties);
    }
}

public class PremiumEffectType : ObjectType<PremiumEffect>
{
    protected override void Configure(
        IObjectTypeDescriptor<PremiumEffect> descriptor)
    {
        descriptor.Implements<PricingEffectType>();
        descriptor.Field(p => p.Meta)
            .Type<AnyType>()
            .Resolve(p => p.Parent<PremiumEffect>()?.Meta?.AdditionalProperties);
    }
}

public class PricingFactorsEffectType : ObjectType<PricingFactorsEffect>
{
    protected override void Configure(
        IObjectTypeDescriptor<PricingFactorsEffect> descriptor)
    {
        descriptor.Implements<PricingEffectType>();
        descriptor.Field(f => f.Value).Type<AdjustmentValueFlatFactorType>();
        descriptor.Field(p => p.Meta)
            .Type<AnyType>()
            .Resolve(p => p.Parent<PricingFactorsEffect>()?.Meta?.AdditionalProperties);
    }
}