using CoverGo.Scripts.Client;
using HotChocolate.Types;

namespace CoverGo.Products.Application.GraphQl.TypeExtensions;

public class AdjustmentValueFlatType : ObjectType<AdjustmentValueFlat>
{
    protected override void Configure(IObjectTypeDescriptor<AdjustmentValueFlat> descriptor)
    {
    }
}

public class AdjustmentValuePercentageType : ObjectType<AdjustmentValuePercentage>
{
    protected override void Configure(IObjectTypeDescriptor<AdjustmentValuePercentage> descriptor)
    {
    }
}

public class AdjustmentValueFactorType : ObjectType<AdjustmentValueFactor>
{
    protected override void Configure(IObjectTypeDescriptor<AdjustmentValueFactor> descriptor)
    {
    }
}

public class AdjustmentValueFlatFactorType : UnionType
{
    protected override void Configure(IUnionTypeDescriptor descriptor)
    {
        descriptor.Type<AdjustmentValueFlatType>();
        descriptor.Type<AdjustmentValueFactorType>();
    }
}

public class AdjustmentValueFlatFactorPercentageType : UnionType
{
    protected override void Configure(IUnionTypeDescriptor descriptor)
    {
        descriptor.Type<AdjustmentValueFlatType>();
        descriptor.Type<AdjustmentValueFactorType>();
        descriptor.Type<AdjustmentValuePercentageType>();
    }
}


public class AdjustmentValuePrimaryCommissionType : ObjectType<AdjustmentValuePrimaryCommission>
{
    protected override void Configure(IObjectTypeDescriptor<AdjustmentValuePrimaryCommission> descriptor)
    {
    }
}

public class AdjustmentValueSecondaryCommissionType : ObjectType<AdjustmentValueSecondaryCommission>
{
    protected override void Configure(IObjectTypeDescriptor<AdjustmentValueSecondaryCommission> descriptor)
    {
    }
}

public class AdjustmentValueDiscountType: ObjectType<AdjustmentValueDiscount>
{
    protected override void Configure(IObjectTypeDescriptor<AdjustmentValueDiscount> descriptor)
    {
    }
}

public class AdjustmentValueCommissionFactorType : UnionType
{
    protected override void Configure(IUnionTypeDescriptor descriptor)
    {
        descriptor.Type<AdjustmentValuePrimaryCommissionType>();
        descriptor.Type<AdjustmentValueSecondaryCommissionType>();
    }
}