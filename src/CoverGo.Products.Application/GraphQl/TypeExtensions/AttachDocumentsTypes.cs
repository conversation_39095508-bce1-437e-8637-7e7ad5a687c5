using CoverGo.Products.Domain.Products.Commands;
using HotChocolate.Types;

namespace CoverGo.Products.Application.GraphQl.TypeExtensions;

public class AttachDocumentCommandType : InputObjectType<AttachDocumentsCommand>
{
	protected override void Configure(
			IInputObjectTypeDescriptor<AttachDocumentsCommand> descriptor)
	{
		descriptor.Name("products_AttachDocumentsInput");
		descriptor.BindFieldsImplicitly();
		descriptor.Ignore(p => p.SetById);
	}
}