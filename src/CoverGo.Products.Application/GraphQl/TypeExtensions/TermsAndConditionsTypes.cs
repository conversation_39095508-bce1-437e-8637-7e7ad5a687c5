﻿using CoverGo.Products.Domain.Products.Commands;
using HotChocolate.Types;

namespace CoverGo.Products.Application.GraphQl.TypeExtensions;

public class SetTermsAndConditionsCommandType : InputObjectType<SetTermsAndConditionsCommand>
{
    protected override void Configure(
        IInputObjectTypeDescriptor<SetTermsAndConditionsCommand> descriptor)
    {
        descriptor.Name("products_SetTermsAndConditionsInput");
        descriptor.BindFieldsImplicitly();
        descriptor.Ignore(p => p.SetById);
    }
}

public class RemoveTermsAndConditionsCommandType : InputObjectType<RemoveTermsAndConditionsCommand>
{
    protected override void Configure(
        IInputObjectTypeDescriptor<RemoveTermsAndConditionsCommand> descriptor)
    {
        descriptor.Name("products_RemoveTermsAndConditionsInput");
        descriptor.BindFieldsImplicitly();
        descriptor.Ignore(p => p.RemovedById);
    }
}
