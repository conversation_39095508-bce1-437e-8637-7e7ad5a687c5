using System.Collections.Generic;
using CoverGo.Products.Domain.Products.ProductTreeParsing;
using HotChocolate.Types;
using Newtonsoft.Json.Linq;

namespace CoverGo.Products.Application.GraphQl.TypeExtensions;

public class ProductRepresentationNodeExtensions : ObjectType<ProductRepresentationNode>
{
    protected override void Configure(IObjectTypeDescriptor<ProductRepresentationNode> descriptor)
    {
        descriptor.BindFieldsImplicitly();

        descriptor
            .Field(x => x.Percentage)
            .Type<AnyType>()
            .Resolve<object>(x => x.Parent<ProductRepresentationNode>().Percentage.ToAnyValue());
        ;

        descriptor
            .Field(x => x.Resolved)
            .Type<AnyType>()
            .Resolve<object>(x => x.Parent<ProductRepresentationNode>().Resolved.ToAnyValue());

        descriptor
            .Field(x => x.Order)
            .Type<AnyType>()
            .Resolve<object>(x => x.Parent<ProductRepresentationNode>().Order.ToAnyValue());

        descriptor
            .Field(x => x.Summary)
            .Type<AnyType>()
            .Resolve<object>(x => x.Parent<ProductRepresentationNode>().Summary.ToAnyValue());

        descriptor
            .Field(x => x.ExtraSummary)
            .Type<AnyType>()
            .Resolve<object>(x => x.Parent<ProductRepresentationNode>().ExtraSummary.ToAnyValue());

        descriptor
            .Field(x => x.MasterPlan)
            .Type<AnyType>()
            .Resolve<object>(x => x.Parent<ProductRepresentationNode>().MasterPlan.ToAnyValue());
    }
}

public class ProductRepresentationNodeParameterExtension
    : ObjectType<ProductRepresentationNodeParameter>
{
    protected override void Configure(
        IObjectTypeDescriptor<ProductRepresentationNodeParameter> descriptor
    )
    {
        descriptor.BindFieldsImplicitly();
        descriptor
            .Field(x => x.Value)
            .Type<AnyType>()
            .Resolve<object>(x =>
                x.Parent<ProductRepresentationNodeParameter>()?.Value.ToAnyValue()
            );
    }
}

public class ProductRepresentationNodePropsExtension : ObjectType<ProductRepresentationNodeProps>
{
    protected override void Configure(
        IObjectTypeDescriptor<ProductRepresentationNodeProps> descriptor
    )
    {
        descriptor.BindFieldsImplicitly();
        descriptor
            .Field(x => x.Value)
            .Type<StringType>()
            .Resolve(x => x.Parent<ProductRepresentationNodeProps>()?.Value.ToAnyValue());
    }
}

internal static class GraphQlAnyTypeResolveExtensions
{
    public static object ToAnyValue(this object obj)
    {
        if (obj == null)
        {
            return null;
        }

        if (obj is JObject jObject)
        {
            var dict = new Dictionary<string, object>();

            foreach (JProperty property in jObject.Properties())
            {
                dict.Add(property.Name, property.Value.ToAnyValue());
            }

            return dict;
        }

        if (obj is JArray jArray)
        {
            var list = new List<object>();

            foreach (var item in jArray)
            {
                list.Add(item.ToAnyValue());
            }

            return list;
        }

        return obj.ToString();
    }
}
