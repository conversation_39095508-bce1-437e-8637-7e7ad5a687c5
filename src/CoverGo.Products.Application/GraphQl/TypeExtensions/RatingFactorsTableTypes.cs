﻿using CoverGo.Products.Domain.Products.Commands;
using HotChocolate.Types;

namespace CoverGo.Products.Application.GraphQl.TypeExtensions;

public class SetRatingFactorsTableCommandType : InputObjectType<SetRatingFactorsTableCommand>
{
    protected override void Configure(
        IInputObjectTypeDescriptor<SetRatingFactorsTableCommand> descriptor)
    {
        descriptor.Name("products_SetRatingFactorsTableInput");
        descriptor.BindFieldsImplicitly();
        descriptor.Ignore(p => p.SetById);
    }
}

public class RemoveRatingFactorsTableCommandType : InputObjectType<RemoveRatingFactorsTableCommand>
{
    protected override void Configure(
        IInputObjectTypeDescriptor<RemoveRatingFactorsTableCommand> descriptor)
    {
        descriptor.Name("products_RemoveRatingFactorsTableInput");
        descriptor.BindFieldsImplicitly();
        descriptor.Ignore(p => p.RemovedById);
    }
}
