using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.Applications.Domain;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.DataModels;
using CoverGo.Products.Domain.Scripts;

using GreenDonut;

using HotChocolate;
using HotChocolate.Types;

using Microsoft.Extensions.Logging;

namespace CoverGo.Products.Application.GraphQl;

[ExtendObjectType(typeof(ProductPlanDetail))]
public class ProductPlanDetailExtensions
{
    [BindMember("fields")]
    public async Task<Dictionary<string, ScriptSchemaPlanField>> GetFields(
        [Parent] ProductPlanDetail productPlanDetail,
        [Service] ProductPlanDetailsBatchLoader batchLoader,
        CancellationToken cancellationToken = default)
    {
        var loadedEntry = await batchLoader.LoadAsync(
            new ProductPlanKey()
            {
                ProductId = new ProductId
                {
                    Plan = productPlanDetail.ProductId.Plan,
                    Type = productPlanDetail.ProductId.Type,
                    Version = productPlanDetail.ProductId.Version
                },
                PlanId = productPlanDetail.Id,
            },
            cancellationToken);
        return loadedEntry?.Fields ?? [];
    }
}

public record ProductPlanKey
{
    public required ProductId ProductId { get; set; }

    public required string PlanId { get; set; }
}

public class ProductPlanDetailsBatchLoader : BatchDataLoader<ProductPlanKey, PlanFields>
{
    private readonly ProductService _productService;
    private readonly ScriptService _scriptService;
    private readonly ICurrentUser _currentUser;
    private readonly ILogger<ProductPlanDetailsBatchLoader> _logger;

    public ProductPlanDetailsBatchLoader(
        ProductService productService,
        ScriptService scriptService,
        ICurrentUser currentUser,
        IBatchScheduler batchScheduler,
        ILogger<ProductPlanDetailsBatchLoader> logger,
        DataLoaderOptions options = null) : base(batchScheduler, options)
    {
        _productService = productService;
        _scriptService = scriptService;
        _currentUser = currentUser;
        _logger = logger;
    }

    protected override async Task<IReadOnlyDictionary<ProductPlanKey, PlanFields>> LoadBatchAsync(IReadOnlyList<ProductPlanKey> keys, CancellationToken cancellationToken)
    {
        var tenantId = _currentUser.TenantId;

        var products = await _productService.GetAsync(
            tenantId,
            null,
            new ProductWhere
            {
                Id_in = keys.Select(it => it.ProductId).Distinct().ToList()
            },
            null,
            null,
            false,
            cancellationToken);
        _logger.LogInformation("Products loaded {@Products}", products);

        var scripts = await _scriptService.GetAsync(
            tenantId,
            new ScriptWhere
            {
                And = new List<ScriptWhere>
                {
                    new ScriptWhere
                    {
                        Id_in = products.SelectMany(it => it.ScriptIds ?? []).ToList(),
                    }
                }
            },
            cancellationToken);

        var pricingScripts = scripts.Where(x => x.Type == Scripts.Client.ScriptTypeEnum.Pricing_standard || x.Type == Scripts.Client.ScriptTypeEnum.Pricing);

        _logger.LogInformation("Scripts loaded {@Scripts}", pricingScripts.Select(x => new { x.Id, x.Type }));

        var productPlanFields = products
            .Select(product =>
            {
                var script =
                    pricingScripts
                        .Where(it => product.ScriptIds.Contains(it.Id))
                        .FirstOrDefault(it => it.Type == Scripts.Client.ScriptTypeEnum.Pricing_standard)
                    ??
                    pricingScripts
                        .Where(it => product.ScriptIds.Contains(it.Id))
                        .FirstOrDefault(it => it.Type == Scripts.Client.ScriptTypeEnum.Pricing);

                var planFields = script is not null
                    ? PlanFieldsParser.Parse(script.InputSchema)
                    : [];
                return (product, planFields);
            })
            .ToList();
        _logger.LogInformation("productPlanFields {@ProductPlanFields}", productPlanFields);

        var result = productPlanFields
            .SelectMany(loadResult => loadResult.planFields.Select(planFields => (loadResult.product, planFields)))
            .ToDictionary(
                x => new ProductPlanKey() { ProductId = x.product.Id, PlanId = x.planFields.PlanId },
                x => x.planFields);
        _logger.LogInformation("Result {@Result}", result);
        _logger.LogInformation("Keys {@Keys}", keys);
        return result;
    }
}
