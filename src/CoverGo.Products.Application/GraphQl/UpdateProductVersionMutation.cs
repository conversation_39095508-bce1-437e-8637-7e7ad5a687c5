#nullable enable

using System.Threading;
using System.Threading.Tasks;

using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.Products.Domain.Products.Commands;

using HotChocolate;
using HotChocolate.Types;

using MediatR;

namespace CoverGo.Products.Application.GraphQl;

[ExtendObjectType(CoverGoGraphQl.MutationsRoot)]
[CoverGoGraphQlIgnoreClassName]
public class UpdateProductVersionMutation
{
    //Not restricted by permissions to allow quotation flow via cloning. Will be obsolete when product lifecycle management is implemented
    [UseMutationConvention(PayloadFieldName = "product")]
    public async Task<Domain.Products.Product> UpdateProductVersion(
        [Service] IMediator mediator,
        UpdateProductVersionCommand input,
        CancellationToken cancellationToken = default)
    {
        var result = await mediator.Send(
            input,
            cancellationToken);
        return result;
    }
}
