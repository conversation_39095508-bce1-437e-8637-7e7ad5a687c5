using System.Security.Claims;
using System.Threading;

using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.DiscountCodes;

using HotChocolate;
using HotChocolate.Types;

namespace CoverGo.Products.Application.GraphQl.DiscountCodes;

[ExtendObjectType(CoverGoGraphQl.QueriesRoot)]
public class DiscountCodes : CoverGoGraphQlQueryBase<DiscountCode, DiscountCodeUpsert, DiscountCodeFilter, IDiscountCodeService>
{
    private readonly IPermissionValidator _permissionValidator;

    public DiscountCodes(IPermissionValidator permissionValidator)
    {
        _permissionValidator = permissionValidator;
    }

    //TODO: Why this function is sync ? Can we make it async ? 
    public  CoverGoGraphQlQueryInterface<DiscountCode, DiscountCodeUpsert, DiscountCodeUpsert, DiscountCodeUpsert, EntityBatch<DiscountCodeUpsert, DiscountCodeUpsert, DiscountCodeUpsert>, QueryArguments<Filter<DiscountCodeFilter>>, DiscountCodeFilter, IDiscountCodeService> Query([GlobalState]string tenantId, [GlobalState] ClaimsIdentity identity, QueryArguments<Filter<DiscountCodeFilter>> @where,
        CancellationToken cancellation = default)
    {
        //If we can/should change function signature to async, then we should prefer async authorize function here
        _permissionValidator.AuthorizeWith(identity, "readDiscountCodes");
        return base.Query(tenantId, @where, cancellation);
    }
    
    public class DiscountCodeType : ObjectType<DiscountCode>
    {
        protected override void Configure(IObjectTypeDescriptor<DiscountCode> descriptor)
        {
            descriptor.Field(x => x.IsActive()).Ignore();
            descriptor.Field(x => x.IsExpired()).Ignore();
        }
    }
}
