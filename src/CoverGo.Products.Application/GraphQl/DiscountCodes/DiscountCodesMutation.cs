using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.DiscountCodes;

using HotChocolate;
using HotChocolate.Types;

namespace CoverGo.Products.Application.GraphQl.DiscountCodes;

[ExtendObjectType(CoverGoGraphQl.MutationsRoot)]
public class DiscountCodesMutation : CoverGoGraphQlMutationsBase<DiscountCode, DiscountCodeUpsert, DiscountCodeFilter, IDiscountCodeService>
{
    private readonly IDiscountCodeService _service;
    private readonly IPermissionValidator _permissionValidator;

    public DiscountCodesMutation(
        [Service] IDiscountCodeService service,
        IPermissionValidator permissionValidator)
        : base(service)
    {
        _service = service;
        _permissionValidator = permissionValidator;
    }

    public async Task<Result> AddEligibleProductAsync([GlobalState] string tenantId, [GlobalState] string loginId, [GlobalState] ClaimsIdentity identity, DiscountCodeProductsUpsert input,
        CancellationToken cancellation = default)
    {
        await _permissionValidator.AuthorizeAsync(identity, "writeDiscountCodes", input.DiscountCodeId);

        input.ById = loginId;
        return await _service.AddEligibleProducts(tenantId, input, cancellation);
    }

    public async Task<Result> RemoveEligibleProductAsync([GlobalState] string tenantId, [GlobalState] string loginId, [GlobalState] ClaimsIdentity identity, DiscountCodeProductsUpsert input,
        CancellationToken cancellation = default)
    {
        await _permissionValidator.AuthorizeAsync(identity, "writeDiscountCodes", input.DiscountCodeId);

        input.ById = loginId;
        return await _service.RemoveEligibleProducts(tenantId, input, cancellation);
    }

    public async Task<Result> Batch([GlobalState] string tenantId, [GlobalState] string loginId, [GlobalState] ClaimsIdentity identity, EntityBatch<DiscountCodeUpsert, DiscountCodeUpsert, DiscountCodeUpsert> batch, CancellationToken cancellationToken = default)
    {

        if (batch.Create?.Any() ?? false)
        {
            await _permissionValidator.AuthorizeWithAsync(identity, "writeDiscountCodes");

            foreach (DiscountCodeUpsert discountCodeUpsert in batch.Create)
                discountCodeUpsert.ById = loginId;
        }

        if (batch.Update?.Any() ?? false)
        {
            foreach (DiscountCodeUpsert discountCodeUpsert in batch.Update)
            {
                discountCodeUpsert.ById = loginId;
                await _permissionValidator.AuthorizeAsync(identity, "writeDiscountCodes", discountCodeUpsert.Id);
            }
        }


        if (batch.Delete?.Any() ?? false)
        {
            foreach (DiscountCodeUpsert discountCodeUpsert in batch.Delete)
            {
                discountCodeUpsert.ById = loginId;
                await _permissionValidator.AuthorizeAsync(identity, "writeDiscountCodes", discountCodeUpsert.Id);
            }
        }

        return await base.Batch(tenantId, batch, cancellationToken);
    }

    public async Task<Result<CreatedStatus>> Create([GlobalState] string tenantId, [GlobalState] string loginId, [GlobalState] ClaimsIdentity identity, DiscountCodeUpsert create, CancellationToken cancellation = default)
    {
        await _permissionValidator.AuthorizeWithAsync(identity, "writeDiscountCodes");
        create.ById = loginId;
        return await base.Create(tenantId, create, cancellation);
    }

    public async Task<Result> UpdateAsync([GlobalState] string tenantId, [GlobalState] string loginId, [GlobalState] ClaimsIdentity identity, DiscountCodeUpsert update, CancellationToken cancellation = default)
    {
        await _permissionValidator.AuthorizeAsync(identity, "writeDiscountCodes", update.Id);
        update.ById = loginId;
        return await base.UpdateAsync(tenantId, update, cancellation);
    }

    public async Task<Result> DeleteAsync([GlobalState] string tenantId, [GlobalState] string loginId, [GlobalState] ClaimsIdentity identity, DiscountCodeUpsert delete, CancellationToken cancellation = new CancellationToken())
    {
        await _permissionValidator.AuthorizeAsync(identity, "writeDiscountCodes", delete.Id);

        delete.ById = loginId;
        return await base.DeleteAsync(tenantId, delete, cancellation);
    }

    public Task<Result> ApplyCodeAsync([GlobalState] string tenantId, [GlobalState] string loginId, [GlobalState] ClaimsIdentity identity, DiscountCodeApply input,
    CancellationToken cancellation = default)
    {
        return _service.ApplyCodeAsync(tenantId, input.DiscountCode, cancellation);
    }
}
public class DiscountCodeUpsertInputType
    : InputObjectType<DiscountCodeUpsert>
{
    protected override void Configure(IInputObjectTypeDescriptor<DiscountCodeUpsert> descriptor) => descriptor.Ignore(x => x.ProductIds);
}

public class DiscountCodeApplyInputType
    : InputObjectType<DiscountCodeApply>
{
}