using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.Applications.Domain;
using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.DataModels;
using CoverGo.Products.Domain.Scripts;

using GreenDonut;

using HotChocolate;
using HotChocolate.Types;

using Microsoft.Extensions.Logging;

using Newtonsoft.Json.Linq;

namespace CoverGo.Products.Application.GraphQl;

[ExtendObjectType(typeof(ProductPlanDetails))]
[CoverGoGraphQlIgnoreClassName]
public class ProductPlanDetailsExtensions
{
    public async Task<Dictionary<string, ScriptSchemaPlanField>> InsuredsFields(
        [Parent] ProductPlanDetails productPlanDetail,
        [Service] ProductFieldsBatchLoader loader,
        CancellationToken cancellationToken = default)
    {
        var result = await loader.LoadAsync(
            new ProductFieldsKey
            {
                ProductId = productPlanDetail.ProductId,
                Type = ProductFieldsType.Insured,
            },
            cancellationToken);
        return result ?? [];
    }

    public async Task<Dictionary<string, ScriptSchemaPlanField>> MemberCustomSchema(
        [Parent] ProductPlanDetails productPlanDetail,
        [Service] ProductFieldsBatchLoader loader,
        CancellationToken cancellationToken = default)
    {
        var result = await loader.LoadAsync(
            new ProductFieldsKey
            {
                ProductId = productPlanDetail.ProductId,
                Type = ProductFieldsType.MemberCustomSchema,
            },
            cancellationToken);
        return result ?? [];
    }

    public async Task<Dictionary<string, ScriptSchemaPlanField>> PolicyFields(
        [Parent] ProductPlanDetails productPlanDetail,
        [Service] ProductFieldsBatchLoader loader,
        CancellationToken cancellationToken = default)
    {
        var result = await loader.LoadAsync(
            new ProductFieldsKey
            {
                ProductId = productPlanDetail.ProductId,
                Type = ProductFieldsType.Policy,
            },
            cancellationToken);
        return result ?? [];
    }
}

public enum ProductFieldsType { Insured, MemberCustomSchema, Policy }

public record ProductFieldsKey
{
    public required ProductId ProductId { get; set; }

    public required ProductFieldsType Type { get; set; }
}

public class ProductFieldsBatchLoader(
    ProductService productService,
    ScriptService scriptService,
    ICurrentUser currentUser,
    IBatchScheduler batchScheduler,
    ILogger<ProductFieldsBatchLoader> logger,
    DataLoaderOptions options = null) : BatchDataLoader<ProductFieldsKey, Dictionary<string, ScriptSchemaPlanField>>(batchScheduler, options)
{
    protected override async Task<IReadOnlyDictionary<ProductFieldsKey, Dictionary<string, ScriptSchemaPlanField>>> LoadBatchAsync(IReadOnlyList<ProductFieldsKey> keys, CancellationToken cancellationToken)
    {
        var tenantId = currentUser.TenantId;

        var products = await productService.GetAsync(
            tenantId,
            null,
            new ProductWhere
            {
                Id_in = keys.Select(it => it.ProductId).Distinct().ToList()
            },
            null,
            null,
            false,
            cancellationToken);
        logger.LogInformation("Products loaded {@Products}", products);

        var scripts = await scriptService.GetAsync(
            tenantId,
            new ScriptWhere
            {
                And = new List<ScriptWhere>
                {
                    new ScriptWhere
                    {
                        Id_in = products.SelectMany(it => it.ScriptIds ?? []).ToList(),
                    }
                }
            },
            cancellationToken);

        var pricingScripts = scripts.Where(x => x.Type == Scripts.Client.ScriptTypeEnum.Pricing_standard || x.Type == Scripts.Client.ScriptTypeEnum.Pricing);

        logger.LogInformation("Scripts loaded {@Scripts}", pricingScripts.Select(x => new { x.Id, x.Type }));

        var productPlanFields = products
            .Select(product =>
            {
                var script =
                    pricingScripts
                        .Where(it => product.ScriptIds.Contains(it.Id))
                        .FirstOrDefault(it => it.Type == Scripts.Client.ScriptTypeEnum.Pricing_standard)
                    ??
                    pricingScripts
                        .Where(it => product.ScriptIds.Contains(it.Id))
                        .FirstOrDefault(it => it.Type == Scripts.Client.ScriptTypeEnum.Pricing);

                if (script is not null)
                {
                    var jobject = JObject.Parse(script.InputSchema);
                    var insuredFields = ProductDataModelParser.Parse(jobject, "insuredsFields");
                    var memberCustomSchema = ProductDataModelParser.Parse(jobject, "memberCustomSchema");
                    var policyFields = ProductDataModelParser.Parse(jobject, "policyFields");
                    return (Product: product, InsuredFields: insuredFields, MemberCustomSchema: memberCustomSchema, PolicyFields: policyFields);
                }
                return (product, [], [], []);
            })
            .ToList();
        logger.LogInformation("productPlanFields {@ProductPlanFields}", productPlanFields);

        var result = productPlanFields
            .SelectMany(it => new List<(ProductFieldsKey Key, Dictionary<string, ScriptSchemaPlanField> Value)>
            {
                (Key: new ProductFieldsKey { ProductId = it.Product.Id, Type = ProductFieldsType.Insured }, Value: it.InsuredFields),
                (Key: new ProductFieldsKey { ProductId = it.Product.Id, Type = ProductFieldsType.MemberCustomSchema }, Value: it.MemberCustomSchema),
                (Key: new ProductFieldsKey { ProductId = it.Product.Id, Type = ProductFieldsType.Policy }, Value: it.PolicyFields),
            })
            .ToDictionary(
                x => x.Key,
                x => x.Value);
        logger.LogInformation("Result {@Result}", result);
        logger.LogInformation("Keys {@Keys}", keys);
        return result;
    }
}
