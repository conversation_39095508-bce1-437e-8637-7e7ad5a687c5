using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Products;
using HotChocolate;
using HotChocolate.Types;
using Microsoft.Extensions.Logging;

namespace CoverGo.Products.Application.GraphQl.ProductEventLogs;

[ExtendObjectType(CoverGoGraphQl.QueriesRoot)]
[CoverGoGraphQlIgnoreClassName]
public class ProductEventLogs
{
    private readonly IPermissionValidator _permissionValidator;
    private readonly ProductEventLogsService _productEventLogsService;
    private readonly ILogger<ProductEventLogs> _logger;

    private readonly int defaultPageSize = 100;

    public ProductEventLogs(
        IPermissionValidator permissionValidator, 
        ProductEventLogsService productEventLogsService,
        ILogger<ProductEventLogs> logger)
    {
        _permissionValidator = permissionValidator;
        _productEventLogsService = productEventLogsService;
        _logger = logger;
    }

    [GraphQLName("ProductEventLogs")]
    public async Task<List<ProductEventLog>> Query(
        [GlobalState] string tenantId,
        [GlobalState] ClaimsIdentity identity,
        ProductId productId,
        DateTime? fromDate,
        DateTime? toDate,
        OrderBy? orderBy = null,
        int? skip = null,
        int? first = null,
        CancellationToken cancellation = default)
    {
         _permissionValidator.AuthorizeWith(identity, "readProducts");
        var productEventLogs = await _productEventLogsService.GetProductEventLogsAsync(tenantId, productId.Type, new ProductEventWhere
        {
            ProductIds_in = new List<ProductId> { productId },
            FromDate = fromDate,
            ToDate = toDate
        },
        orderBy, skip ?? 0, first ?? defaultPageSize, cancellation);
        
        return productEventLogs;
    }




}