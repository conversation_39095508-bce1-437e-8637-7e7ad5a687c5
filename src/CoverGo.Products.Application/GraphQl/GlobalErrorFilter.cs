using HotChocolate;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;

namespace CoverGo.Products.Application.GraphQl;

public class GlobalErrorFilter : IErrorFilter
{
    private readonly IHttpContextAccessor httpContextAccessor;

    public GlobalErrorFilter(IHttpContextAccessor httpContextAccessor)
    {
        this.httpContextAccessor = httpContextAccessor;
    }

    private static Dictionary<Type, string> exeptionTypeCodeDictionary = new()
    {
        { typeof(NullReferenceException), "NULL_REFERENCE" },
        { typeof(InvalidOperationException), "INVALID_OPERATION" },
        { typeof(ArgumentException), "INVALID_ARGUMENT" },
        { typeof(AggregateException), "AGGREGATE_EXCEPTION" },
    };

    public IError OnError(IError error)
    {
        if (error.Exception != null && exeptionTypeCodeDictionary.ContainsKey(error.Exception.GetType()))
        {
            Microsoft.Extensions.Primitives.StringValues? traceId = httpContextAccessor.HttpContext?.Response.Headers["X-CoverGo-Ref"];
            return error
                .WithCode(exeptionTypeCodeDictionary[error.Exception.GetType()])
                .WithMessage(error.Exception.Message)
                .WithExtensions(new Dictionary<string, object>
                {
                    { "traceId", traceId?.ToString() }
                });
        }
        return error;
    }
}
