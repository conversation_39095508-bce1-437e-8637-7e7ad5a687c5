#nullable enable

using System.Threading;
using System.Threading.Tasks;

using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.ProductTreeParsing;

using HotChocolate;
using HotChocolate.AspNetCore.Authorization;
using HotChocolate.Types;

using MediatR;

namespace CoverGo.Products.Application.GraphQl;

[ExtendObjectType(CoverGoGraphQl.QueriesRoot)]
[CoverGoGraphQlIgnoreClassName]
public sealed class ProductProductPlanDetailsQuery
{
    //Not restricted by permissions to allow product query on UI
    // TODO: This name is not good and query itself needs to extend some Product object
    [Authorize]
    public async Task<ProductPlanDetails> ProductProductPlanDetails(
        ProductPlanDetailFactorsInput input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken)
    {
        var result = await mediator.Send(new PlanDetailsQuery { ProductId = input.ProductId }, cancellationToken);
        return result;
    }
}
