using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.ScriptEvaluation;
using CoverGo.Products.Domain.Scripts;
using CoverGo.Scripts.Client;
using HotChocolate;
using HotChocolate.Types;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ProductId = CoverGo.Products.Domain.Products.ProductId;

namespace CoverGo.Products.Application.GraphQl;

[ExtendObjectType(CoverGoGraphQl.QueriesRoot)]
public sealed class Product
{
    private readonly ScriptEvaluationService _scriptEvaluationService;
    private readonly ProductService _productService;
    private readonly ScriptService _scriptService;

    public Product(ScriptEvaluationService scriptEvaluationService, ProductService productService, ScriptService scriptService)
    {
        _scriptEvaluationService = scriptEvaluationService;
        _productService = productService;
        _scriptService = scriptService;
    }

    [GraphQLDeprecated("Use 'CalculatePricing' API instead")]
    public async Task<ProductPricingResult> Pricing(
         [GlobalState] string tenantId,
         [GlobalState] string clientId,
         [GraphQLNonNullType] ProductId productId,
         [GraphQLNonNullType] string dataInput,
         [GraphQLType(typeof(string))] string? distributorID,
         [GraphQLType(typeof(ListType<StringType>))] string[]? campaignCodes,
         [GraphQLType(typeof(DateTimeOffset?))] DateTimeOffset? startDate,
         [GraphQLType(typeof(ScriptTypeEnum?))] ScriptTypeEnum? scriptType,
         CancellationToken cancellationToken)
    {
        if (startDate is null)
        {
            try
            {
                var jDataInput = System.Text.Json.JsonDocument.Parse(dataInput).RootElement;
                startDate = jDataInput.GetProperty("policy").GetProperty("startDate").GetDateTimeOffset();
            }
            catch { }
        }

        ScriptResult scriptResult = await _scriptEvaluationService.Evaluate(
            tenantId,
            clientId,
            new EvaluateProductScriptCommand
            {
                DataInput = dataInput,
                ProductId = productId,
                DistributorID = distributorID,
                CampaignCodes = campaignCodes?.ToList() ?? [],
                StartDate = startDate,
                ScriptType = scriptType ?? ScriptTypeEnum.Pricing
            },
            cancellationToken);

        var result = new ProductPricingResult
        {
            Errors = scriptResult.Errors,
            Errors_2 = scriptResult.Errors_2?.Select(e => new DomainUtils.Error
            {
                Code = e.Code,
                Message = e.Message
            }),
            Pricing = scriptResult.Value,
            Status = scriptResult.Status
        };

        return result;
    }

    [GraphQLName("CalculatePricing")]
    public async Task<PricingResult> CalculatePricing(
     [GlobalState] string tenantId,
     [GlobalState] string clientId,
     CalculatePricingInput input,
     CancellationToken cancellationToken)
    {
        PricingResult scriptResult = await _scriptEvaluationService.EvaluateStandardPricing(
            tenantId,
            clientId,
            new EvaluateStandardPricingCommand
            {
                DataInput = input.DataInput,
                ProductId = input.ProductId,
                ScriptType = ScriptTypeEnum.Pricing_standard,
                Modes = input.Modes?.Select(m => new ModeProperties
                {
                    Count = m.Count,
                    Meta = new Scripts.Client.Meta { AdditionalProperties = m.Meta },
                    Billing = m.Billing,
                }).ToList(),
                DistributorID = input.DistributorID,
                AgentIds = input.AgentIds,
                StartDate = input.StartDate,
                CampaignCodes = input.CampaignCodes,
                IsRenewal = input.IsRenewal ?? false,
                UwLoadings = input.UwLoadings?.ToUwLoadingsInput(),
                Taxes = input.Taxes,
                PricingFactors = input.PricingFactors,
            },
            cancellationToken);

        return scriptResult;
    }

    [GraphQLName("EvaluateUnderwriting")]
    public async Task<UnderwritingResult> EvaluateUnderwriting(
     [GlobalState] string tenantId,
     [GlobalState] string clientId,
     EvaluateUnderwritingInput input,
     CancellationToken cancellationToken)
    {
        EvaluateStandardUnderwritingCommand command = new()
        {
            DataInput = input.DataInput,
            ProductId = input.ProductId,
            ScriptType = ScriptTypeEnum.Underwriting_standard,
            Modes = input.Modes,
            InsuredUnderwritingInputs = input.InsuredUnderwritingInputs?.Select(i =>
            {
                return new InsuredUnderwritingInput
                {
                    Id = i.Id,
                    Type = i.Type,
                    DateOfBirth = i.DateOfBirth?.ToDateTime(TimeOnly.MinValue),
                    InsuredSimplifiedMedicalUnderwritingResponse = i.InsuredSimplifiedMedicalUnderwritingResponse != null
                    ? new HealthQuestionnaireResponse
                    {
                        Template = i.InsuredSimplifiedMedicalUnderwritingResponse.Template,
                        Answers = new Scripts.Client.Meta
                        {
                            AdditionalProperties = i.InsuredSimplifiedMedicalUnderwritingResponse.Answers,
                        }
                    } : null,
                    InsuredNonMedicalMaterialFacts = i.InsuredNonMedicalMaterialFacts != null
                    ? new NonMedicalMaterialFacts
                    {
                        Id = i.InsuredNonMedicalMaterialFacts.Id,
                        Meta = new Scripts.Client.Meta { AdditionalProperties = i.InsuredNonMedicalMaterialFacts.Meta },
                    } : null,
                    Fields = new Scripts.Client.Meta { AdditionalProperties = i.Fields },
                    Meta = new Scripts.Client.Meta { AdditionalProperties = i.Meta },
                };
            }).ToList()
        };
        command.DataInput = MergeStandardizedInputsIntoDataInput();

        UnderwritingResult scriptResult = await _scriptEvaluationService.EvaluateStandardUnderwriting(
            tenantId,
            clientId,
            command,
            cancellationToken);

        return scriptResult;

        string MergeStandardizedInputsIntoDataInput()
        {
            JObject dataInputJObject = !string.IsNullOrEmpty(input.DataInput) ? JObject.Parse(input.DataInput) : JObject.Parse("{}");
            IEnumerable<JObject> insuredArray = command.InsuredUnderwritingInputs?.Select(i =>
            {
                var insuredJObjectAdditionalProperties = JObject.FromObject(i.Meta);
                var insuredJObject = JObject.FromObject(i);
                insuredJObject.Merge(insuredJObjectAdditionalProperties);
                return insuredJObject;
            });
            if (insuredArray != null)
                dataInputJObject.TryAdd("insureds", new JArray(insuredArray));

            return dataInputJObject.ToString();
        }
    }

    [GraphQLName("LatestPricingScriptType")]
    public async Task<Result<ScriptTypeEnum>> LatestPricingScriptType(
        [GlobalState] string tenantId,
        [GlobalState] string clientId,
        ProductId productId,
        CancellationToken cancellationToken)
    {
        Domain.Products.Product product = (await _productService.GetAsync(tenantId, clientId, new ProductWhere
        {
            Id_in =
            [
                productId
            ]
        }, null, new QueryParameters { Limit = 1 }, false, cancellationToken)).FirstOrDefault();
        if (product == null)
            return Result<ScriptTypeEnum>.Failure($"No Product found `{productId}`.");

        IEnumerable<Script> scripts = await _scriptService.GetAsync(tenantId, new ScriptWhere { And = [new() { Id_in = product.ScriptIds?.ToList() ?? [] }] }, cancellationToken);
        if (!scripts?.Any() ?? true)
            return Result<ScriptTypeEnum>.Failure($"No Scripts found for `{productId}`.");

        return scripts.Any(s => s.Type == ScriptTypeEnum.Pricing_standard)
            ? Result<ScriptTypeEnum>.Success(ScriptTypeEnum.Pricing_standard)
            : scripts.Any(s => s.Type == ScriptTypeEnum.Pricing)
            ? Result<ScriptTypeEnum>.Success(ScriptTypeEnum.Pricing)
            : Result<ScriptTypeEnum>.Failure($"No Pricing Scripts found for `{productId}`."); ;
    }
}

public class ProductPlanDetailFactorsInput
{
    public ProductId ProductId { get; set; }
}


public class ProductPricingResult : Result
{
    public string Pricing { get; set; }
}
