using System.Security.Claims;
using System.Threading;

using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Sbus;

using HotChocolate;
using HotChocolate.Types;

namespace CoverGo.Products.Application.GraphQl.Sbus;

[ExtendObjectType(CoverGoGraphQl.QueriesRoot)]
public class Sbus : CoverGoGraphQlQueryBase<Sbu, SbuUpsert, SbuFilter, ISbuRepository>
{
    private readonly IPermissionValidator _permissionValidator;

    public Sbus(IPermissionValidator permissionValidator)
    {
        _permissionValidator = permissionValidator;
    }

    public CoverGoGraphQlQueryInterface<
        Sbu,
        SbuUpsert,
        SbuUpsert,
        SbuUpsert,
        EntityBatch<
            SbuUpsert, SbuUpsert, SbuUpsert
        >,
        QueryArguments<
            Filter<SbuFilter>
        >,
        SbuFilter,
        ISbuRepository>
        Query(
            [GlobalState] string tenantId,
            [GlobalState] ClaimsIdentity identity,
            QueryArguments<Filter<SbuFilter>> where,
            CancellationToken cancellation = default)
    {
        _permissionValidator.AuthorizeWith(identity, "readSbus");
        return base.Query(tenantId, where, cancellation);
    }
}
