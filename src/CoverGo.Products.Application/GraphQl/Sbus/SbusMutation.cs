using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Sbus;

using HotChocolate;
using HotChocolate.Types;

namespace CoverGo.Products.Application.GraphQl.Sbus;

[ExtendObjectType(CoverGoGraphQl.MutationsRoot)]
public class SbusMutation : CoverGoGraphQlMutationsBase<Sbu, SbuUpsert, SbuFilter, ISbuService>
{
    private readonly ISbuService _service;
    private readonly IPermissionValidator _permissionValidator;

    public SbusMutation(
        [Service] ISbuService service,
        IPermissionValidator permissionValidator)
        : base(service)
    {
        _service = service;
        _permissionValidator = permissionValidator;
    }

    public async Task<Result<CreatedStatus>> Create(
        [GlobalState] string tenantId,
        [GlobalState] string loginId,
        [GlobalState] ClaimsIdentity identity,
        SbuUpsert create,
        CancellationToken cancellation = default)
    {
        await _permissionValidator.AuthorizeWithAsync(identity, "writeSbus");
        create.ById = loginId;
        return await base.Create(tenantId, create, cancellation);
    }

    public async Task<Result> UpdateAsync(
        [GlobalState] string tenantId,
        [GlobalState] string loginId,
        [GlobalState] ClaimsIdentity identity,
        SbuUpsert update,
        CancellationToken cancellation = default)
    {
        await _permissionValidator.AuthorizeAsync(identity, "writeSbus", update.Id);
        update.ById = loginId;
        return await base.UpdateAsync(tenantId, update, cancellation);
    }

    public async Task<Result> DeleteAsync(
        [GlobalState] string tenantId,
        [GlobalState] string loginId,
        [GlobalState] ClaimsIdentity identity,
        SbuUpsert delete,
        CancellationToken cancellation = new CancellationToken())
    {
        await _permissionValidator.AuthorizeAsync(identity, "writeSbus", delete.Id);

        delete.ById = loginId;
        return await base.DeleteAsync(tenantId, delete, cancellation);
    }
}
