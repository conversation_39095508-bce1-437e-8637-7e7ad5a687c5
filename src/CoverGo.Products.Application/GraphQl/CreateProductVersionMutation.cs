#nullable enable

using System.Diagnostics.CodeAnalysis;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.Products.Domain.Products;
using CoverGo.Products.Domain.Products.Commands;

using HotChocolate;
using HotChocolate.Types;

using MediatR;

using ProductId = CoverGo.Products.Domain.Products.ProductId;

namespace CoverGo.Products.Application.GraphQl;

[ExcludeFromCodeCoverage]
[ExtendObjectType(CoverGoGraphQl.MutationsRoot)]
[CoverGoGraphQlIgnoreClassName]
public class CreateProductVersionMutation
{
    [Error<ProductVersioningException>]
    [UseMutationConvention(PayloadFieldName = "result")]
    public async Task<CreateProductVersionCommandResult> CreateProductVersion(
        [Service] IMediator mediator,
        ProductId originalProductId,
        CancellationToken cancellationToken = default)
    {
        var result = await mediator.Send(
            new CreateProductVersionCommand()
            {
                OriginalProductId = originalProductId,
                IsReadonly = true,
            },
            cancellationToken);
        return result;
    }
}
