using System.Security.Claims;
using System.Threading.Tasks;

using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.BuildingBlocks.Auth.Permissions;

using HotChocolate;

namespace CoverGo.Products.Application.GraphQl.ProductExchange;

public static class PermissionValidatorExtensions
{
    public static ValueTask AuthorizeWithAccessDeniedHandledAsync(this IPermissionValidator permissionValidator, ClaimsIdentity identity, string permissionType)
        => permissionValidator.AuthorizeWithAsync(identity, permissionType).WithAccessDeniedHandled(permissionType);

    public static ValueTask AuthorizeWithAccessDeniedHandledAsync(this IPermissionValidator permissionValidator, ClaimsIdentity identity, string claimType, string id)
        => permissionValidator.AuthorizeAsync(identity, claimType, id).WithAccessDeniedHandled(claimType);

    private static async ValueTask WithAccessDeniedHandled(this ValueTask valueTask, string permissionType)
    {
        try
        {
            await valueTask;
        }
        catch (CoverGoGraphQlAuthorizationException ex) when (ex.Message == "Access denied")
        {
            throw new GraphQLException(ErrorBuilder.New()
                .SetMessage($"You are not authorized to run this. You are missing '{permissionType}' permission.")
                .SetCode("authorization")
                .Build()
            );
        }
    }
}