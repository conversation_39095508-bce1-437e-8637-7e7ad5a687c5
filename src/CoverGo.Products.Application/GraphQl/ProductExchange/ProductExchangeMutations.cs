using System;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Products;

using HotChocolate;
using HotChocolate.Types;

using Microsoft.Extensions.Logging;

namespace CoverGo.Products.Application.GraphQl.ProductExchange;

[ExtendObjectType(CoverGoGraphQl.MutationsRoot)]
public class Export
{
    private readonly ProductExchangeService _service;
    private readonly IPermissionValidator _permissionValidator;
    private readonly ILogger<Export> _logger;

    public Export([Service] ProductExchangeService service, IPermissionValidator permissionValidator, ILogger<Export> logger)
    {
        _service = service;
        _permissionValidator = permissionValidator;
        _logger = logger;
    }

    public async Task<FilePathResult> Product(
        [GlobalState] string tenantId,
        [GlobalState] string clientId,
        [GlobalState] string loginId,
        [GlobalState] ClaimsIdentity identity,
        [GraphQLNonNullType] ProductId productId,
        CancellationToken cancellation = default)
    {
        await _permissionValidator.AuthorizeWithAccessDeniedHandledAsync(identity, "exportProducts");
        try
        {
            Result<string> result = await _service.ExportAsync(tenantId, clientId, loginId, productId, skipValidateProductTreeLock: false, cancellation);
            return FilePathResult.From(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Product export failed");
            return FilePathResult.From(Result<string>.Failure("Unable to export the product"));
        }
    }
}

[ExtendObjectType(CoverGoGraphQl.MutationsRoot)]
public class Import
{
    private readonly ProductExchangeService _service;
    private readonly IPermissionValidator _permissionValidator;
    private readonly ILogger<Import> _logger;

    public Import([Service] ProductExchangeService service, IPermissionValidator permissionValidator, ILogger<Import> logger)
    {
        _service = service;
        _permissionValidator = permissionValidator;
        _logger = logger;
    }

    public async Task<Result> Product(
        [GlobalState] string tenantId,
        [GlobalState] string loginId,
        [GlobalState] ClaimsIdentity identity,
        [GraphQLNonNullType] string fromFilePath,
        CancellationToken cancellation = default)
    {
        await _permissionValidator.AuthorizeWithAccessDeniedHandledAsync(identity, "importProducts");
        try
        {
            return await _service.ImportAsync(tenantId, loginId, fromFilePath, skipValidateProductTreeLock: false, cancellation);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Product import failed");
            return Result.Failure("Unable to import the product");
        }
    }
}

public class FilePathResult : Result
{
    public string FilePath { get; set; }

    public static FilePathResult From(Result<string> source) => new FilePathResult
    {
        FilePath = source.Value,
        Status = source.Status,
        Errors = source.Errors,
        Errors_2 = source.Errors_2
    };
}