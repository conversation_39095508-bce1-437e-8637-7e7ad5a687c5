using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.ProductImportHistory;

using HotChocolate;
using HotChocolate.Types;

namespace CoverGo.Products.Application.GraphQl.ProductExchange;

[ExtendObjectType(CoverGoGraphQl.QueriesRoot)]
public class ProductImportHistory : CoverGoGraphQlQueryBase<ProductImportHistoryRecord, ProductImportHistoryRecord, ProductImportHistoryRecordFilter, IProductImportHistoryRepository>
{
    private readonly IPermissionValidator _permissionValidator;

    public ProductImportHistory(IPermissionValidator permissionValidator) => _permissionValidator = permissionValidator;

    public async Task<CoverGoGraphQlQueryInterface<
        ProductImportHistoryRecord,
        ProductImportHistoryRecord,
        ProductImportHistoryRecord,
        ProductImportHistoryRecord,
        EntityBatch<ProductImportHistoryRecord, ProductImportHistoryRecord, ProductImportHistoryRecord>,
        QueryArguments<Filter<ProductImportHistoryRecordFilter>>,
        ProductImportHistoryRecordFilter,
        IProductImportHistoryRepository>>
        Query(
            [GlobalState] string tenantId,
            [GlobalState] ClaimsIdentity identity,
            QueryArguments<Filter<ProductImportHistoryRecordFilter>> where,
            CancellationToken cancellation = default)
    {
        await _permissionValidator.AuthorizeWithAccessDeniedHandledAsync(identity, "readProducts", "all");
        return base.Query(tenantId, OrderByDefault(where, "importedAt", OrderByType.DSC), cancellation);
    }

    private static QueryArguments<Filter<ProductImportHistoryRecordFilter>> OrderByDefault(QueryArguments<Filter<ProductImportHistoryRecordFilter>> where, string fieldName, OrderByType orderByType)
    {
        where ??= new QueryArguments<Filter<ProductImportHistoryRecordFilter>>();
        if (where.OrderBy == null && where.OrderBy2 == null)
            where.OrderBy = new OrderBy { FieldName = fieldName, Type = orderByType };
        return where;
    }
}