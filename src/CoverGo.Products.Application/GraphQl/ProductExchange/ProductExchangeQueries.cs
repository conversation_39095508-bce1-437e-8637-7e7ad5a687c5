using System;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Products;

using HotChocolate;
using HotChocolate.Types;

using Microsoft.Extensions.Logging;

namespace CoverGo.Products.Application.GraphQl.ProductExchange;

[ExtendObjectType(CoverGoGraphQl.QueriesRoot)]
public class ProductImport
{
    private readonly ProductExchangeService _service;
    private readonly IPermissionValidator _permissionValidator;
    private readonly ILogger<ProductImport> _logger;

    public ProductImport([Service] ProductExchangeService service, IPermissionValidator permissionValidator, ILogger<ProductImport> logger)
    {
        _service = service;
        _permissionValidator = permissionValidator;
        _logger = logger;
    }

    public async Task<ProductImportInfo> Info(
        [GlobalState] string tenantId,
        [GlobalState] ClaimsIdentity identity,
        [GraphQLNonNullType] string filePath,
        CancellationToken cancellation = default)
    {
        await _permissionValidator.AuthorizeWithAccessDeniedHandledAsync(identity, "importProducts");
        try
        {
            Result<ProductImportInfo> result = await _service.GetImportInfoAsync(tenantId, filePath, cancellation);
            if (!result.IsSuccess)
            {
                DomainUtils.Error error = result.Errors_2?.FirstOrDefault(e => !string.IsNullOrEmpty(e.Code));
                if (error != null) throw new GraphQLException(ErrorBuilder.New()
                    .SetMessage(error.Message)
                    .SetCode(error.Code)
                    .Build());

                throw new GraphQLException(string.Join(", ", result.Errors));
            }
            return result.Value;
        }
        catch (Exception ex) when (ex is not GraphQLException)
        {
            _logger.LogError(ex, "Getting product import info failed");
            throw new GraphQLException("Unable to get the product import info");
        }
    }
}