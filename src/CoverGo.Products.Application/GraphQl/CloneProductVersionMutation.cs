#nullable enable

using System.Threading;
using System.Threading.Tasks;

using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.Products.Domain.Products.Commands;

using HotChocolate;
using HotChocolate.Types;

using MediatR;

using ProductId = CoverGo.Products.Domain.Products.ProductId;

namespace CoverGo.Products.Application.GraphQl;

[ExtendObjectType(CoverGoGraphQl.MutationsRoot)]
[CoverGoGraphQlIgnoreClassName]
public class CloneProductVersionMutation
{
    //Not restricted by permissions to allow quotation flow via cloning. Will be obsolete when product lifecycle management is implemented
    [UseMutationConvention(PayloadFieldName = "clonedProductId")]
    public async Task<ProductId> CloneProductVersion(
        [Service] IMediator mediator,
        ProductId originalProductId,
        string cloneProductVersion,
        bool isReadonly = true,
        CancellationToken cancellationToken = default)
    {
        var result = await mediator.Send(
            new CloneProductVersionCommand()
            {
                OriginalProductId = originalProductId,
                CloneProductVersion = cloneProductVersion,
                IsReadonly = isReadonly,
            },
            cancellationToken);
        return result.ClonedProduct.Id;
    }
}
