using System.Threading;
using System.Threading.Tasks;

using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.DomainUtils;
using CoverGo.Products.Domain.Products;

using HotChocolate;
using HotChocolate.Types;

namespace CoverGo.Products.Application.GraphQl;

[ExtendObjectType(CoverGoGraphQl.QueriesRoot)]
public class IssuerProductId
{
    private readonly ProductService _productService;
    
    public IssuerProductId(ProductService productService)
    {
        _productService = productService;
    }

    public Task<Result<string>> GenerateNext([GlobalState] string tenantId,
        CancellationToken cancellationToken) =>
        _productService.GetNextIssuerProductId(tenantId, cancellationToken);
}