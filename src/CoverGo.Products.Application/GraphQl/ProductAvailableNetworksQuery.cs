﻿using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.FeatureManagement;
using CoverGo.Products.Domain.Claims2;
using CoverGo.Products.Infrastructure.Claims2;
using HotChocolate;
using HotChocolate.AspNetCore.Authorization;
using HotChocolate.Types;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Products.Application.GraphQl;

[ExtendObjectType(CoverGoGraphQl.QueriesRoot)]
[CoverGoGraphQlIgnoreClassName]
public class ProductAvailableNetworksQuery
{
    //Not restricted by permissions to allow product query on UI
    [Authorize]
    public async Task<List<Network>> ProductActiveNetworks(
            [GlobalState] string tenantId,
            [Service] Claims2Adapter claims2Adapter,
            [Service] IMultiTenantFeatureManager featureManager,
            string? searchInput,
            CancellationToken cancellationToken
        ) => await featureManager.IsEnabled("QueryActiveNetworks",tenantId) ? await claims2Adapter.GetActiveNetworks(searchInput, cancellationToken) : [];
}
