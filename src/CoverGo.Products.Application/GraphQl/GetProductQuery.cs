#nullable enable

using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.Products.Domain.Products.Ports;
using CoverGo.Products.Domain.Scripts;
using CoverGo.Scripts.Client;
using GreenDonut;
using HotChocolate;
using HotChocolate.AspNetCore.Authorization;
using HotChocolate.Types;
using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using ProductId = CoverGo.Products.Domain.Products.ProductId;

namespace CoverGo.Products.Application.GraphQl;

[ExtendObjectType(CoverGoGraphQl.QueriesRoot)]
[CoverGoGraphQlIgnoreClassName]
public sealed class GetProductQuery
{
    //Not restricted by permissions to allow product query on UI
    [Authorize]
    public async Task<Domain.Products.Product?> Product(
        ProductId productId,
        [Service] IMediator mediator,
        CancellationToken cancellationToken = default)
    {
        return await mediator.Send(new Domain.Products.Queries.ProductQuery { ProductId = productId }, cancellationToken);
    }
}

public sealed class ProductType : ObjectType<Domain.Products.Product>
{
    protected override void Configure(IObjectTypeDescriptor<Domain.Products.Product> descriptor)
    {
        descriptor.BindFieldsExplicitly();
        descriptor.Field(it => it.Id);
        descriptor.Field(it => it.LifecycleStage);
        descriptor.Field(it => it.PolicyIssuanceMethod);
        descriptor.Field(it => it.OfferValidityPeriod);
        descriptor.Field(it => it.AutoRenewal);
        descriptor.Field(it => it.RenewalNotification);
        descriptor.Field(it => it.TaxConfiguration);
        descriptor
            .Field("fields")
            .Type<NonNullType<ListType<NonNullType<ProductFieldType>>>>()
            .Resolve(it =>
            {
                var fields = it.Parent<Domain.Products.Product>().Fields;
                var result = FieldsHelper.JsonStringToCustomFieldDtoList<Domain.Products.Product>(
                    !string.IsNullOrEmpty(fields)
                        ? it.Parent<Domain.Products.Product>().Fields
                        : "{}");
                return result;
            });
        descriptor.Field(it => it.TermsAndConditionsTemplateId);
        descriptor.Field(it => it.TermsAndConditionsJacketId);
        descriptor.Field("name").Type<StringType>().Resolve(async (ctx, ct) =>
        {
            var product = ctx.Parent<Domain.Products.Product>();
            var productNameLoader = ctx.Service<ProductNameBatchDataLoader>();
            var productName = await productNameLoader.LoadAsync(ct, product.Id);
            return productName;
        });
        descriptor
            .Field("scripts")
            .Type<NonNullType<ListType<NonNullType<ObjectType<Script>>>>>()
            .Argument("where", a => a.Type<InputObjectType<ScriptWhere>>())
            .Resolve(async (ctx, ct) =>
            {
                string tenantId = ctx.ContextData.TryGetValue("tenantId", out var tenantValue) ? tenantValue?.ToString() ?? string.Empty : string.Empty;
                ScriptService service = ctx.Service<ScriptService>();
                Domain.Products.Product product = ctx.Parent<Domain.Products.Product>();

                if (!product.ScriptIds?.ToList().Any() ?? true)
                    return [];

                ScriptWhere whereClause;
                var scriptIdFromProductFilter = new ScriptWhere { Id_in = product.ScriptIds.ToList() };
                ScriptWhere? userWhere = ctx.ArgumentValue<ScriptWhere?>("where");
                whereClause = userWhere != null
                    ? new ScriptWhere { And = [scriptIdFromProductFilter, userWhere] }
                    : scriptIdFromProductFilter;

                return (await service.GetAsync(tenantId, whereClause, ct)).ToList();
            });
    }
}

public class ProductNameBatchDataLoader : BatchDataLoader<ProductId, string?>
{
    private readonly IProductNameRepository _repository;

    public ProductNameBatchDataLoader(
        IProductNameRepository repository,
        IBatchScheduler batchScheduler,
        DataLoaderOptions? options = null)
        : base(batchScheduler, options)
    {
        _repository = repository;
    }

    protected override async Task<IReadOnlyDictionary<ProductId, string?>> LoadBatchAsync(
        IReadOnlyList<ProductId> keys,
        CancellationToken cancellationToken)
    {
        var productNames = await _repository.GetOrDefaultByIds(keys, cancellationToken);
        return productNames.Select((x, index) => (Key: keys[index], Value: x)).ToDictionary(x => x.Key, x => x.Value);
    }
}

#region to be moved to separate repository next sprint

public abstract record CustomFieldDto(string Key, [GraphQLType(typeof(AnyType))] object? Value)
{
    public virtual bool Equals(CustomFieldDto? other)
    {
        return other is not null
            && Key == other.Key
            && JsonSerializer.Serialize(Value) == JsonSerializer.Serialize(other.Value);
    }

    public override int GetHashCode() => HashCode.Combine(Key, JsonSerializer.Serialize(Value));
}

public record CustomFieldDto<T>(string Key, [GraphQLType(typeof(AnyType))] object? Value) : CustomFieldDto(Key, Value)
{
}

public sealed class ProductFieldType : ObjectType<CustomFieldDto<Domain.Products.Product>>
{
    protected override void Configure(IObjectTypeDescriptor<CustomFieldDto<Domain.Products.Product>> descriptor)
    {
        descriptor.Name("ProductField");
        descriptor.Field(it => it.Value).Type<AnyType>();
    }
}

public static class JsonElementToObjectConverter
{
    public static object? FromJsonElementToObject(JsonElement jsonElement)
    {
        return jsonElement.ValueKind switch
        {
            JsonValueKind.True => jsonElement.GetBoolean(),
            JsonValueKind.False => jsonElement.GetBoolean(),
            JsonValueKind.Null => null,
            JsonValueKind.Undefined => null,
            JsonValueKind.Number => jsonElement.GetDecimal(),
            JsonValueKind.String => jsonElement.GetString(),
            JsonValueKind.Object => jsonElement.EnumerateObject().ToDictionary(it => it.Name, it => FromJsonElementToObject(it.Value)),
            JsonValueKind.Array => jsonElement.EnumerateArray().Select(it => FromJsonElementToObject(it))
        };
    }
}

public static class FieldsHelper
{
    public static IReadOnlyDictionary<string, object?> JsonStringToDictionary(string fields)
    {
        return JsonSerializer.Deserialize<Dictionary<string, object?>>(fields).ToDictionary(
            it => it.Key,
            it => it.Value is JsonElement jsonElement ? JsonElementToObjectConverter.FromJsonElementToObject(jsonElement) : it.Value);
    }

    public static List<CustomFieldDto<T>> JsonStringToCustomFieldDtoList<T>(string fields)
    {
        return [.. JsonStringToDictionary(fields).Select(it => new CustomFieldDto<T>(it.Key, it.Value))];
    }
}

#endregion
