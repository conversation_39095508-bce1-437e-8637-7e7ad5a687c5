using CoverGo.Scripts.Client;
using HotChocolate;
using HotChocolate.Types;
using System;
using System.Collections.Generic;
using System.Linq;
using ProductId = CoverGo.Products.Domain.Products.ProductId;
using UwLoadingInput = CoverGo.Scripts.Client.UwLoadingInput;
using PricingFactorsInput = CoverGo.Scripts.Client.PricingFactorsInput;

namespace CoverGo.Products.Application.GraphQl;

public class CalculatePricingInput
{
    public ProductId ProductId { get; set; }
    [GraphQLNonNullType]
    public string DataInput { get; set; }
    public List<ModePropertiesInput> Modes { get; set; }
    public string DistributorID { get; set; }
    public string[]? AgentIds { get; set; }
    public string[]? CampaignCodes { get; set; }
    public DateTimeOffset? StartDate { get; set; }
    public bool? IsRenewal { get; set; } = false;
    public UwLoadings? UwLoadings { get; set; }
    public TaxInput[] Taxes { get; set; }
    public PricingFactorsInput PricingFactors { get; set; }
}

public class ModePropertiesInput
{
    public int Count { get; set; }
    [GraphQLType(typeof(AnyType))]
    public Dictionary<string, object> Meta { get; set; }
    public Billing Billing { get; set; }
}

public class UwLoadings
{
    public UwLoading[]? Loadings { get; set; }
    public MemberUwLoading[]? Members { get; set; }

    public UwLoadingInput ToUwLoadingsInput() => new() { Loadings = Loadings?.ToList() ?? [], Members = Members?.ToList() ?? [], };
}
