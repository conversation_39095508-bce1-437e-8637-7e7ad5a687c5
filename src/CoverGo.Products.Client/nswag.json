{"runtime": "Net60", "defaultVariables": "Configuration=Debug", "documentGenerator": {"webApiToOpenApi": {"isAspNetCore": true, "assemblyPaths": ["../CoverGo.Products.Application/bin/$(Configuration)/net7.0/CoverGo.Products.Application.dll"], "referencePaths": ["../CoverGo.Products.Application/bin/$(Configuration)/net7.0/"], "output": null}}, "codeGenerators": {"openApiToCSharpClient": {"clientBaseClass": null, "configurationClass": null, "generateClientClasses": true, "generateClientInterfaces": true, "clientBaseInterface": null, "injectHttpClient": true, "disposeHttpClient": false, "protectedMethods": [], "generateExceptionClasses": true, "exceptionClass": "ProductsClientException", "wrapDtoExceptions": false, "useHttpClientCreationMethod": false, "httpClientType": "System.Net.Http.HttpClient", "useHttpRequestMessageCreationMethod": false, "useBaseUrl": false, "generateBaseUrlProperty": true, "generateSyncMethods": false, "generatePrepareRequestAndProcessResponseAsAsyncMethods": false, "exposeJsonSerializerSettings": false, "clientClassAccessModifier": "public", "typeAccessModifier": "public", "generateContractsOutput": false, "contractsNamespace": "CoverGo.Products.Client", "contractsOutputFilePath": "./ProductsClientContracts.cs", "parameterDateTimeFormat": "s", "parameterDateFormat": "yyyy-MM-dd", "generateUpdateJsonSerializerSettingsMethod": true, "useRequestAndResponseSerializationSettings": false, "serializeTypeInformation": false, "queryNullValue": "", "className": "ProductsClient", "operationGenerationMode": "SingleClientFromOperationId", "additionalNamespaceUsages": ["CoverGo.DomainUtils", "Newtonsoft.Json.Linq"], "additionalContractNamespaceUsages": [], "generateOptionalParameters": false, "generateJsonMethods": false, "enforceFlagEnums": false, "parameterArrayType": "System.Collections.Generic.IEnumerable", "parameterDictionaryType": "System.Collections.Generic.IDictionary", "responseArrayType": "System.Collections.Generic.List", "responseDictionaryType": "System.Collections.Generic.Dictionary", "wrapResponses": false, "wrapResponseMethods": [], "generateResponseClasses": true, "responseClass": "SwaggerResponse", "namespace": "CoverGo.Products.Client", "requiredPropertiesMustBeDefined": true, "dateType": "System.DateTime", "jsonConverters": null, "anyType": "JToken", "dateTimeType": "System.DateTime", "timeType": "System.TimeSpan", "timeSpanType": "System.TimeSpan", "arrayType": "System.Collections.Generic.List", "arrayInstanceType": "System.Collections.Generic.List", "dictionaryType": "System.Collections.Generic.Dictionary", "dictionaryInstanceType": "System.Collections.Generic.Dictionary", "arrayBaseType": "System.Collections.Generic.List", "dictionaryBaseType": "System.Collections.Generic.Dictionary", "classStyle": "Poco", "jsonLibrary": "NewtonsoftJson", "generateDefaultValues": true, "generateDataAnnotations": false, "excludedTypeNames": ["EventLog", "DomainUtils.EventQuery", "CurrencyCode", "DateRange", "FieldsWhere", "FieldsWhereCondition", "DeleteCommand", "RemoveCommand", "QueryArguments", "OrderBy", "OrderByType", "Result", "CreatedStatus", "Error", "ScalarValue", "KeyScalarValue", "SystemObject", "Where"], "excludedParameterNames": [], "handleReferences": true, "generateImmutableArrayProperties": false, "generateImmutableDictionaryProperties": false, "jsonSerializerSettingsTransformationMethod": null, "inlineNamedArrays": false, "inlineNamedDictionaries": false, "inlineNamedTuples": true, "inlineNamedAny": false, "generateDtoTypes": true, "generateOptionalPropertiesAsNullable": true, "generateNullableReferenceTypes": true, "templateDirectory": null, "typeNameGeneratorType": null, "propertyNameGeneratorType": null, "enumNameGeneratorType": null, "serviceHost": null, "serviceSchemes": null, "output": "./ProductsClient.cs", "newLineBehavior": "Auto"}}}