<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <PackageId>CoverGo.Products.Client</PackageId>
    <Company>CoverGo</Company>
    <PackageDescription>CoverGo.Products service REST client</PackageDescription>
    <RepositoryUrl>https://github.com/CoverGo/Products</RepositoryUrl>
    <Version>1.0.0</Version>
    <Authors>CoverGo</Authors>
    <PackageRequireLicenseAcceptance>false</PackageRequireLicenseAcceptance>
    <Description>CoverGo.Products service REST client</Description>
    <PackageProjectUrl>https://github.com/CoverGo/Products</PackageProjectUrl>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CoverGo.DomainUtils" />
    <PackageReference Include="NSwag.MSBuild">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <Target Name="NSwag" BeforeTargets="Build" Condition="'$(SonarQubeBuildDirectory)' == ''">
      <Exec Command="dotnet build ../CoverGo.Products.Application/CoverGo.Products.Application.csproj -c $(Configuration)" />
      <Exec Command="$(NSwagExe_Net60) run nswag.json /variables:Configuration=$(Configuration)" />
  </Target>

</Project>
