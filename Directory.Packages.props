<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <CoverGoInternalsVersion>3.74.8</CoverGoInternalsVersion>
    <HotChocolateVersion>12.22.2</HotChocolateVersion>
  </PropertyGroup>
  <!-- CoverGo -->
  <ItemGroup>
    <PackageVersion Include="AutoFixture" Version="4.18.1" />
    <PackageVersion Include="AutoFixture.AutoMoq" Version="4.18.1" />
    <PackageVersion Include="AutoFixture.Xunit2" Version="4.18.1" />
    <PackageVersion Include="CoverGo.Applications.Domain" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Applications.HealthCheck" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Applications.Http.GraphQl.Services" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Applications.Http.Rest" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Applications.Infrastructure" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Applications.Startup" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.BuildingBlocks.Application.Core" Version="11.0.1" />
    <PackageVersion Include="CoverGo.BuildingBlocks.Auth" Version="1.2.7" />
    <PackageVersion Include="CoverGo.Configuration" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.DomainUtils" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.FeatureManagement" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.GraphQL.Client" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.HttpUtils" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.JsonUtils" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.MongoUtils" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Multitenancy" Version="3.74.4" />
    <PackageVersion Include="CoverGo.Multitenancy.AspNetCore" Version="3.74.4" />
    <PackageVersion Include="CoverGo.Proxies.Auth" Version="3.74.4" />
    <PackageVersion Include="CoverGo.Proxies.Product" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Sentry" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.SettableValues" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Threading.Tasks" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Users.Client" Version="2.63.1" />
    <PackageVersion Include="EventStore.Client.Grpc.Streams" Version="23.1.0" />
    <PackageVersion Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="7.0.13" />
    <PackageVersion Include="Moq" Version="4.18.1" />
    <PackageVersion Include="Serilog.Extensions.Hosting" Version="7.0.0" />
  </ItemGroup>
  <!-- CoverGo Clients -->
  <ItemGroup>
    <PackageVersion Include="CoverApp.ProductBuilder.Client" Version="2.22.0" />
    <PackageVersion Include="CoverGo.Auth.Client" Version="2.75.0" />
    <PackageVersion Include="CoverGo.FileSystem.Client" Version="2.27.0" />
    <PackageVersion Include="CoverGo.Gateway.Client" Version="2.242.2" />
    <PackageVersion Include="CoverGo.L10n.Client" Version="2.11.0" />
    <PackageVersion Include="CoverGo.Scripts.Client" Version="2.55.0-rc.2" />
    <PackageVersion Include="CoverGo.Templates.Client.Rest" Version="2.1.141" />
    <PackageVersion Include="CoverGo.Reference.Client" Version="1.5.0" />
    <PackageVersion Include="CoverGo.ChannelManagement.Client" Version="1.34.0-rc.1" />
  </ItemGroup>
  <!-- .NET -->
  <ItemGroup>
    <PackageVersion Include="Microsoft.Extensions.Configuration.Binder" Version="7.0.4" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="7.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Configuration" Version="7.0.0" />
    <PackageVersion Include="Microsoft.SourceLink.GitHub" Version="8.0.0" />
    <PackageVersion Include="System.Text.Encoding.CodePages" Version="7.0.0" />
  </ItemGroup>
  <!-- 3rd party -->
  <ItemGroup>
    <PackageVersion Include="DotNetCore.NPOI" Version="1.2.2" />
    <PackageVersion Include="DotNetZip" Version="1.16.0" />
    <PackageVersion Include="JsonLogic.Net" Version="1.1.11" />
    <PackageVersion Include="GraphQL.Client" Version="5.1.0" />
    <PackageVersion Include="GraphQL.Client.Serializer.Newtonsoft" Version="5.1.0" />
    <PackageVersion Include="HotChocolate" Version="$(HotChocolateVersion)" />
    <PackageVersion Include="HotChocolate.AspNetCore" Version="$(HotChocolateVersion)" />
    <PackageVersion Include="HotChocolate.AspNetCore.Authorization" Version="$(HotChocolateVersion)" />
    <PackageVersion Include="HotChocolate.Data.MongoDb" Version="$(HotChocolateVersion)" />
    <PackageVersion Include="HotChocolate.Stitching" Version="$(HotChocolateVersion)" />
    <PackageVersion Include="HotChocolate.Types" Version="$(HotChocolateVersion)" />
    <PackageVersion Include="IdentityModel" Version="6.2.0" />
    <PackageVersion Include="Manatee.Json" Version="13.0.2" />
    <PackageVersion Include="NSwag.MSBuild" Version="13.20.0" />
    <PackageVersion Include="Serilog.AspNetCore" Version="3.4.0" />
    <PackageVersion Include="Serilog.Enrichers.Span" Version="1.3.0" />
    <PackageVersion Include="Serilog.Sinks.ElasticSearch" Version="8.1.0" />
    <PackageVersion Include="StrawberryShake.Server" Version="13.5.1" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="5.5.0" />
  </ItemGroup>
  <!-- Tests -->
  <ItemGroup>
    <PackageVersion Include="FluentAssertions" Version="6.12.0" />
    <PackageVersion Include="JunitXml.TestLogger" Version="3.1.12" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="7.0.18" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.9.0" />
    <PackageVersion Include="xunit" Version="2.8.0" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.8.0" />
    <PackageVersion Include="coverlet.collector" Version="6.0.2" />
    <PackageVersion Include="Snapshooter.Xunit" Version="0.14.1" />
  </ItemGroup>
</Project>
